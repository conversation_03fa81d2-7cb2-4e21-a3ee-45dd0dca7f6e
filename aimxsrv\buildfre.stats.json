{"built": [{"name": "e:\\os\\src\\onecore\\ds\\ds\\src\\aimx\\prod\\aimxsrv\\idl", "pass": "PASS_MISC", "returncode": 0, "stats": [{"execution times": [{"job start time": "2025-07-15 21:02:47.596", "pip start time": "0-00-00 00:00:00.000", "pip end time": "0-00-00 00:00:00.000", "job end time": "2025-07-15 21:02:47.601"}], "i/o": [{"bytes read": 0, "bytes written": 0, "bytes transferred (non-read/write)": 0, "read operation count": 0, "write operation count": 0, "non-read/write operations count": 0}], "peak memory": [{"job": 0, "process": 0}], "network": [{"bandwidth": 0}]}]}, {"name": "e:\\os\\src\\onecore\\ds\\ds\\src\\aimx\\prod\\aimxsrv\\client\\lib", "pass": "PASS_MISC", "returncode": 0, "stats": [{"execution times": [{"job start time": "2025-07-15 21:02:47.709", "pip start time": "0-00-00 00:00:00.000", "pip end time": "0-00-00 00:00:00.000", "job end time": "2025-07-15 21:02:47.713"}], "i/o": [{"bytes read": 0, "bytes written": 0, "bytes transferred (non-read/write)": 0, "read operation count": 0, "write operation count": 0, "non-read/write operations count": 0}], "peak memory": [{"job": 0, "process": 0}], "network": [{"bandwidth": 0}]}]}, {"name": "e:\\os\\src\\onecore\\ds\\ds\\src\\aimx\\prod\\aimxsrv\\client\\dll", "pass": "PASS_MISC", "returncode": 0, "stats": [{"execution times": [{"job start time": "2025-07-15 21:02:47.723", "pip start time": "0-00-00 00:00:00.000", "pip end time": "0-00-00 00:00:00.000", "job end time": "2025-07-15 21:02:47.739"}], "i/o": [{"bytes read": 0, "bytes written": 0, "bytes transferred (non-read/write)": 0, "read operation count": 0, "write operation count": 0, "non-read/write operations count": 0}], "peak memory": [{"job": 0, "process": 0}], "network": [{"bandwidth": 0}]}]}, {"name": "e:\\os\\src\\onecore\\ds\\ds\\src\\aimx\\prod\\aimxsrv\\powershell", "pass": "PASS_MISC", "returncode": 0, "stats": [{"execution times": [{"job start time": "2025-07-15 21:02:47.739", "pip start time": "0-00-00 00:00:00.000", "pip end time": "0-00-00 00:00:00.000", "job end time": "2025-07-15 21:02:47.755"}], "i/o": [{"bytes read": 0, "bytes written": 0, "bytes transferred (non-read/write)": 0, "read operation count": 0, "write operation count": 0, "non-read/write operations count": 0}], "peak memory": [{"job": 0, "process": 0}], "network": [{"bandwidth": 0}]}]}, {"name": "e:\\os\\src\\onecore\\ds\\ds\\src\\aimx\\prod\\aimxsrv\\serviceinstaller", "pass": "PASS_MISC", "returncode": 0, "stats": [{"execution times": [{"job start time": "2025-07-15 21:02:47.787", "pip start time": "0-00-00 00:00:00.000", "pip end time": "0-00-00 00:00:00.000", "job end time": "2025-07-15 21:02:47.791"}], "i/o": [{"bytes read": 0, "bytes written": 0, "bytes transferred (non-read/write)": 0, "read operation count": 0, "write operation count": 0, "non-read/write operations count": 0}], "peak memory": [{"job": 0, "process": 0}], "network": [{"bandwidth": 0}]}]}, {"name": "e:\\os\\src\\onecore\\ds\\ds\\src\\aimx\\prod\\aimxsrv\\server", "pass": "PASS_MISC", "returncode": 0, "stats": [{"execution times": [{"job start time": "2025-07-15 21:02:47.729", "pip start time": "0-00-00 00:00:00.000", "pip end time": "0-00-00 00:00:00.000", "job end time": "2025-07-15 21:02:47.803"}], "i/o": [{"bytes read": 0, "bytes written": 0, "bytes transferred (non-read/write)": 0, "read operation count": 0, "write operation count": 0, "non-read/write operations count": 0}], "peak memory": [{"job": 0, "process": 0}], "network": [{"bandwidth": 0}]}]}, {"name": "e:\\os\\src\\onecore\\ds\\ds\\src\\aimx\\prod\\aimxsrv\\dll", "pass": "PASS_MISC", "returncode": 0, "stats": [{"execution times": [{"job start time": "2025-07-15 21:02:47.771", "pip start time": "0-00-00 00:00:00.000", "pip end time": "0-00-00 00:00:00.000", "job end time": "2025-07-15 21:02:47.819"}], "i/o": [{"bytes read": 0, "bytes written": 0, "bytes transferred (non-read/write)": 0, "read operation count": 0, "write operation count": 0, "non-read/write operations count": 0}], "peak memory": [{"job": 0, "process": 0}], "network": [{"bandwidth": 0}]}]}, {"name": "e:\\os\\src\\onecore\\ds\\ds\\src\\aimx\\prod\\mcpserversample", "pass": "PASS_MISC", "returncode": 0, "stats": [{"execution times": [{"job start time": "2025-07-15 21:02:47.898", "pip start time": "0-00-00 00:00:00.000", "pip end time": "0-00-00 00:00:00.000", "job end time": "2025-07-15 21:02:47.916"}], "i/o": [{"bytes read": 0, "bytes written": 0, "bytes transferred (non-read/write)": 0, "read operation count": 0, "write operation count": 0, "non-read/write operations count": 0}], "peak memory": [{"job": 0, "process": 0}], "network": [{"bandwidth": 0}]}]}, {"name": "e:\\os\\src\\onecore\\ds\\ds\\src\\aimx\\prod\\mcpserversample", "pass": "PASS_MISC", "returncode": 0, "stats": [{"execution times": [{"job start time": "2025-07-15 21:02:47.918", "pip start time": "0-00-00 00:00:00.000", "pip end time": "0-00-00 00:00:00.000", "job end time": "2025-07-15 21:02:47.930"}], "i/o": [{"bytes read": 0, "bytes written": 0, "bytes transferred (non-read/write)": 0, "read operation count": 0, "write operation count": 0, "non-read/write operations count": 0}], "peak memory": [{"job": 0, "process": 0}], "network": [{"bandwidth": 0}]}]}, {"name": "e:\\os\\src\\onecore\\ds\\ds\\src\\aimx\\prod\\cpprestsdk", "pass": "PASS_MISC", "returncode": 0, "stats": [{"execution times": [{"job start time": "2025-07-15 21:02:48.197", "pip start time": "0-00-00 00:00:00.000", "pip end time": "0-00-00 00:00:00.000", "job end time": "2025-07-15 21:02:49.916"}], "i/o": [{"bytes read": 0, "bytes written": 0, "bytes transferred (non-read/write)": 0, "read operation count": 0, "write operation count": 0, "non-read/write operations count": 0}], "peak memory": [{"job": 0, "process": 0}], "network": [{"bandwidth": 0}]}]}, {"name": "e:\\os\\src\\onecore\\ds\\ds\\src\\aimx\\prod\\adpsmcpsvr", "pass": "PASS_MISC", "returncode": 0, "stats": [{"execution times": [{"job start time": "2025-07-15 21:02:49.923", "pip start time": "0-00-00 00:00:00.000", "pip end time": "0-00-00 00:00:00.000", "job end time": "2025-07-15 21:02:49.923"}], "i/o": [{"bytes read": 0, "bytes written": 0, "bytes transferred (non-read/write)": 0, "read operation count": 0, "write operation count": 0, "non-read/write operations count": 0}], "peak memory": [{"job": 0, "process": 0}], "network": [{"bandwidth": 0}]}]}, {"name": "e:\\os\\src\\onecore\\ds\\ds\\src\\aimx\\prod\\adpsmcpsvr", "pass": "PASS_MISC", "returncode": 0, "stats": [{"execution times": [{"job start time": "2025-07-15 21:02:49.923", "pip start time": "0-00-00 00:00:00.000", "pip end time": "0-00-00 00:00:00.000", "job end time": "2025-07-15 21:02:49.955"}], "i/o": [{"bytes read": 0, "bytes written": 0, "bytes transferred (non-read/write)": 0, "read operation count": 0, "write operation count": 0, "non-read/write operations count": 0}], "peak memory": [{"job": 0, "process": 0}], "network": [{"bandwidth": 0}]}]}, {"name": "e:\\os\\src\\onecore\\ds\\ds\\src\\aimx\\prod\\llmclientlib", "pass": "PASS_MISC", "returncode": 0, "stats": [{"execution times": [{"job start time": "2025-07-15 21:02:49.955", "pip start time": "0-00-00 00:00:00.000", "pip end time": "0-00-00 00:00:00.000", "job end time": "2025-07-15 21:02:49.955"}], "i/o": [{"bytes read": 0, "bytes written": 0, "bytes transferred (non-read/write)": 0, "read operation count": 0, "write operation count": 0, "non-read/write operations count": 0}], "peak memory": [{"job": 0, "process": 0}], "network": [{"bandwidth": 0}]}]}, {"name": "e:\\os\\src\\onecore\\ds\\ds\\src\\aimx\\prod\\llmclientlib", "pass": "PASS_MISC", "returncode": 0, "stats": [{"execution times": [{"job start time": "2025-07-15 21:02:49.971", "pip start time": "0-00-00 00:00:00.000", "pip end time": "0-00-00 00:00:00.000", "job end time": "2025-07-15 21:02:49.977"}], "i/o": [{"bytes read": 0, "bytes written": 0, "bytes transferred (non-read/write)": 0, "read operation count": 0, "write operation count": 0, "non-read/write operations count": 0}], "peak memory": [{"job": 0, "process": 0}], "network": [{"bandwidth": 0}]}]}, {"name": "e:\\os\\src\\onecore\\ds\\ds\\src\\aimx\\prod\\aimxsrv\\client\\lib", "pass": "PASS0", "returncode": 0, "stats": [{"execution times": [{"job start time": "2025-07-15 21:02:50.353", "pip start time": "2025-07-15 21:02:50.401", "pip end time": "2025-07-15 21:02:52.180", "job end time": "2025-07-15 21:02:52.190"}], "i/o": [{"bytes read": 1735401, "bytes written": 447246, "bytes transferred (non-read/write)": 2045938, "read operation count": 547, "write operation count": 47, "non-read/write operations count": 11299}], "peak memory": [{"job": 60817408, "process": 30859264}], "network": [{"bandwidth": 0}]}]}, {"name": "e:\\os\\src\\onecore\\ds\\ds\\src\\aimx\\prod\\aimxsrv\\client\\dll", "pass": "PASS0", "returncode": 0, "stats": [{"execution times": [{"job start time": "2025-07-15 21:02:50.353", "pip start time": "2025-07-15 21:02:50.401", "pip end time": "2025-07-15 21:02:52.201", "job end time": "2025-07-15 21:02:52.212"}], "i/o": [{"bytes read": 1742008, "bytes written": 449530, "bytes transferred (non-read/write)": 2063992, "read operation count": 568, "write operation count": 54, "non-read/write operations count": 11508}], "peak memory": [{"job": 60760064, "process": 30859264}], "network": [{"bandwidth": 0}]}]}, {"name": "e:\\os\\src\\onecore\\ds\\ds\\src\\aimx\\prod\\llmclientlib", "pass": "PASS0", "returncode": 0, "stats": [{"execution times": [{"job start time": "2025-07-15 21:02:50.369", "pip start time": "2025-07-15 21:02:50.409", "pip end time": "2025-07-15 21:02:52.239", "job end time": "2025-07-15 21:02:52.261"}], "i/o": [{"bytes read": 1735597, "bytes written": 713151, "bytes transferred (non-read/write)": 2018132, "read operation count": 547, "write operation count": 15194, "non-read/write operations count": 10965}], "peak memory": [{"job": 60878848, "process": 30597120}], "network": [{"bandwidth": 0}]}]}, {"name": "e:\\os\\src\\onecore\\ds\\ds\\src\\aimx\\prod\\mcpserversample", "pass": "PASS0", "returncode": 0, "stats": [{"execution times": [{"job start time": "2025-07-15 21:02:50.369", "pip start time": "2025-07-15 21:02:50.409", "pip end time": "2025-07-15 21:02:52.362", "job end time": "2025-07-15 21:02:52.382"}], "i/o": [{"bytes read": 1741831, "bytes written": 141800, "bytes transferred (non-read/write)": 2037880, "read operation count": 553, "write operation count": 68, "non-read/write operations count": 11860}], "peak memory": [{"job": 61136896, "process": 31154176}], "network": [{"bandwidth": 0}]}]}, {"name": "e:\\os\\src\\onecore\\ds\\ds\\src\\aimx\\prod\\aimxsrv\\server", "pass": "PASS0", "returncode": 0, "stats": [{"execution times": [{"job start time": "2025-07-15 21:02:50.353", "pip start time": "2025-07-15 21:02:50.409", "pip end time": "2025-07-15 21:02:52.392", "job end time": "2025-07-15 21:02:52.401"}], "i/o": [{"bytes read": 1742672, "bytes written": 3011449, "bytes transferred (non-read/write)": 2063114, "read operation count": 555, "write operation count": 120, "non-read/write operations count": 12279}], "peak memory": [{"job": 61517824, "process": 30625792}], "network": [{"bandwidth": 0}]}]}, {"name": "e:\\os\\src\\onecore\\ds\\ds\\src\\aimx\\prod\\aimxsrv\\dll", "pass": "PASS0", "returncode": 0, "stats": [{"execution times": [{"job start time": "2025-07-15 21:02:50.368", "pip start time": "2025-07-15 21:02:50.409", "pip end time": "2025-07-15 21:02:52.398", "job end time": "2025-07-15 21:02:52.409"}], "i/o": [{"bytes read": 1746911, "bytes written": 520716, "bytes transferred (non-read/write)": 2058328, "read operation count": 575, "write operation count": 81, "non-read/write operations count": 12224}], "peak memory": [{"job": 61231104, "process": 31027200}], "network": [{"bandwidth": 0}]}]}, {"name": "e:\\os\\src\\onecore\\ds\\ds\\src\\aimx\\prod\\adpsmcpsvr", "pass": "PASS0", "returncode": 0, "stats": [{"execution times": [{"job start time": "2025-07-15 21:02:50.369", "pip start time": "2025-07-15 21:02:50.409", "pip end time": "2025-07-15 21:02:52.417", "job end time": "2025-07-15 21:02:52.437"}], "i/o": [{"bytes read": 1741968, "bytes written": 426135, "bytes transferred (non-read/write)": 2035766, "read operation count": 553, "write operation count": 15169, "non-read/write operations count": 11809}], "peak memory": [{"job": 61247488, "process": 30851072}], "network": [{"bandwidth": 0}]}]}, {"name": "e:\\os\\src\\onecore\\ds\\ds\\src\\aimx\\prod\\aimxsrv\\idl", "pass": "PASS0", "returncode": 0, "stats": [{"execution times": [{"job start time": "2025-07-15 21:02:50.353", "pip start time": "2025-07-15 21:02:50.385", "pip end time": "2025-07-15 21:02:52.733", "job end time": "2025-07-15 21:02:52.749"}], "i/o": [{"bytes read": 5992515, "bytes written": 313472, "bytes transferred (non-read/write)": 2139614, "read operation count": 975, "write operation count": 15265, "non-read/write operations count": 13807}], "peak memory": [{"job": 60043264, "process": 30007296}], "network": [{"bandwidth": 0}]}]}, {"name": "e:\\os\\src\\onecore\\ds\\ds\\src\\aimx\\prod\\aimxsrv\\powershell", "pass": "PASS0", "returncode": 0, "stats": [{"execution times": [{"job start time": "2025-07-15 21:02:50.353", "pip start time": "2025-07-15 21:02:50.409", "pip end time": "2025-07-15 21:02:53.478", "job end time": "2025-07-15 21:02:53.478"}], "i/o": [{"bytes read": 3769667, "bytes written": 155436, "bytes transferred (non-read/write)": 2206440, "read operation count": 2596, "write operation count": 15313, "non-read/write operations count": 20731}], "peak memory": [{"job": 77377536, "process": 31211520}], "network": [{"bandwidth": 0}]}]}, {"name": "e:\\os\\src\\onecore\\ds\\ds\\src\\aimx\\prod\\aimxsrv\\serviceinstaller", "pass": "PASS1", "returncode": 0, "stats": [{"execution times": [{"job start time": "2025-07-15 21:02:54.418", "pip start time": "2025-07-15 21:02:54.469", "pip end time": "2025-07-15 21:02:58.947", "job end time": "2025-07-15 21:02:58.953"}], "i/o": [{"bytes read": 17997326, "bytes written": 3899656, "bytes transferred (non-read/write)": 3350628, "read operation count": 1947, "write operation count": 20268, "non-read/write operations count": 20706}], "peak memory": [{"job": 83259392, "process": 53284864}], "network": [{"bandwidth": 0}]}]}, {"name": "e:\\os\\src\\onecore\\ds\\ds\\src\\aimx\\prod\\aimxsrv\\powershell", "pass": "PASS1", "returncode": 0, "stats": [{"execution times": [{"job start time": "2025-07-15 21:02:54.418", "pip start time": "2025-07-15 21:02:54.469", "pip end time": "2025-07-15 21:03:00.579", "job end time": "2025-07-15 21:03:00.595"}], "i/o": [{"bytes read": 13885168, "bytes written": 543868, "bytes transferred (non-read/write)": 2333679, "read operation count": 2770, "write operation count": 16300, "non-read/write operations count": 27900}], "peak memory": [{"job": 92815360, "process": 62386176}], "network": [{"bandwidth": 0}]}]}, {"name": "e:\\os\\src\\onecore\\ds\\ds\\src\\aimx\\prod\\llmclientlib", "pass": "PASS1", "returncode": 0, "stats": [{"execution times": [{"job start time": "2025-07-15 21:02:54.432", "pip start time": "2025-07-15 21:02:54.469", "pip end time": "2025-07-15 21:03:00.713", "job end time": "2025-07-15 21:03:00.736"}], "i/o": [{"bytes read": 21002470, "bytes written": 11203240, "bytes transferred (non-read/write)": 3391652, "read operation count": 2776, "write operation count": 21195, "non-read/write operations count": 21932}], "peak memory": [{"job": 209432576, "process": 178765824}], "network": [{"bandwidth": 0}]}]}, {"name": "e:\\os\\src\\onecore\\ds\\ds\\src\\aimx\\prod\\aimxsrv\\client\\lib", "pass": "PASS1", "returncode": 0, "stats": [{"execution times": [{"job start time": "2025-07-15 21:02:54.415", "pip start time": "2025-07-15 21:02:54.461", "pip end time": "2025-07-15 21:03:07.500", "job end time": "2025-07-15 21:03:07.525"}], "i/o": [{"bytes read": 31369087, "bytes written": 35680179, "bytes transferred (non-read/write)": 5210154, "read operation count": 4003, "write operation count": 25744, "non-read/write operations count": 30211}], "peak memory": [{"job": 196513792, "process": 165736448}], "network": [{"bandwidth": 0}]}]}, {"name": "e:\\os\\src\\onecore\\ds\\ds\\src\\aimx\\prod\\aimxsrv\\client\\dll", "pass": "PASS1", "returncode": 0, "stats": [{"execution times": [{"job start time": "2025-07-15 21:02:54.416", "pip start time": "2025-07-15 21:02:54.461", "pip end time": "2025-07-15 21:03:07.709", "job end time": "2025-07-15 21:03:07.733"}], "i/o": [{"bytes read": 31296304, "bytes written": 35679975, "bytes transferred (non-read/write)": 5206686, "read operation count": 4020, "write operation count": 25757, "non-read/write operations count": 30003}], "peak memory": [{"job": 196165632, "process": 165834752}], "network": [{"bandwidth": 0}]}]}, {"name": "e:\\os\\src\\onecore\\ds\\ds\\src\\aimx\\prod\\mcpserversample", "pass": "PASS1", "returncode": 0, "stats": [{"execution times": [{"job start time": "2025-07-15 21:02:54.418", "pip start time": "2025-07-15 21:02:54.461", "pip end time": "2025-07-15 21:03:08.180", "job end time": "2025-07-15 21:03:08.205"}], "i/o": [{"bytes read": 32105371, "bytes written": 26600767, "bytes transferred (non-read/write)": 3791842, "read operation count": 4813, "write operation count": 23218, "non-read/write operations count": 25079}], "peak memory": [{"job": 240992256, "process": 210640896}], "network": [{"bandwidth": 0}]}]}, {"name": "e:\\os\\src\\onecore\\ds\\ds\\src\\aimx\\prod\\aimxsrv\\dll", "pass": "PASS1", "returncode": 0, "stats": [{"execution times": [{"job start time": "2025-07-15 21:02:54.418", "pip start time": "2025-07-15 21:02:54.461", "pip end time": "2025-07-15 21:03:10.181", "job end time": "2025-07-15 21:03:10.200"}], "i/o": [{"bytes read": 54818130, "bytes written": 90711219, "bytes transferred (non-read/write)": 5661768, "read operation count": 7500, "write operation count": 29139, "non-read/write operations count": 35515}], "peak memory": [{"job": 264261632, "process": 233340928}], "network": [{"bandwidth": 0}]}]}, {"name": "e:\\os\\src\\onecore\\ds\\ds\\src\\aimx\\prod\\adpsmcpsvr", "pass": "PASS1", "returncode": 0, "stats": [{"execution times": [{"job start time": "2025-07-15 21:02:54.426", "pip start time": "2025-07-15 21:02:54.469", "pip end time": "2025-07-15 21:03:12.479", "job end time": "2025-07-15 21:03:12.495"}], "i/o": [{"bytes read": 70319635, "bytes written": 155147579, "bytes transferred (non-read/write)": 5169082, "read operation count": 10028, "write operation count": 31787, "non-read/write operations count": 36804}], "peak memory": [{"job": 261558272, "process": 230821888}], "network": [{"bandwidth": 0}]}]}, {"name": "e:\\os\\src\\onecore\\ds\\ds\\src\\aimx\\prod\\cpprestsdk", "pass": "PASS1", "returncode": 0, "stats": [{"execution times": [{"job start time": "2025-07-15 21:02:54.418", "pip start time": "2025-07-15 21:02:54.434", "pip end time": "2025-07-15 21:03:34.909", "job end time": "2025-07-15 21:03:36.024"}], "i/o": [{"bytes read": 391680059, "bytes written": 287439069, "bytes transferred (non-read/write)": 9740808, "read operation count": 110827, "write operation count": 34690, "non-read/write operations count": 569552}], "peak memory": [{"job": 466321408, "process": 184074240}], "network": [{"bandwidth": 0}]}]}, {"name": "e:\\os\\src\\onecore\\ds\\ds\\src\\aimx\\prod\\aimxsrv\\server", "pass": "PASS1", "returncode": 0, "stats": [{"execution times": [{"job start time": "2025-07-15 21:03:36.041", "pip start time": "2025-07-15 21:03:36.058", "pip end time": "2025-07-15 21:03:59.144", "job end time": "2025-07-15 21:03:59.156"}], "i/o": [{"bytes read": 210405920, "bytes written": 339405893, "bytes transferred (non-read/write)": 5887508, "read operation count": 35447, "write operation count": 57318, "non-read/write operations count": 74382}], "peak memory": [{"job": 347594752, "process": 316547072}], "network": [{"bandwidth": 0}]}]}, {"name": "e:\\os\\src\\onecore\\ds\\ds\\src\\aimx\\prod\\aimxsrv\\powershell", "pass": "PASS2", "returncode": 0, "stats": [{"execution times": [{"job start time": "2025-07-15 21:03:59.241", "pip start time": "2025-07-15 21:03:59.273", "pip end time": "2025-07-15 21:04:02.908", "job end time": "2025-07-15 21:04:02.914"}], "i/o": [{"bytes read": 6449222, "bytes written": 282120, "bytes transferred (non-read/write)": 4135452, "read operation count": 2030, "write operation count": 32800, "non-read/write operations count": 26595}], "peak memory": [{"job": 99557376, "process": 33939456}], "network": [{"bandwidth": 0}]}]}, {"name": "e:\\os\\src\\onecore\\ds\\ds\\src\\aimx\\prod\\aimxsrv\\client\\dll", "pass": "PASS2", "returncode": 0, "stats": [{"execution times": [{"job start time": "2025-07-15 21:03:59.239", "pip start time": "2025-07-15 21:03:59.273", "pip end time": "2025-07-15 21:04:05.522", "job end time": "2025-07-15 21:04:05.538"}], "i/o": [{"bytes read": 10917705, "bytes written": 7522000, "bytes transferred (non-read/write)": 4243192, "read operation count": 2310, "write operation count": 34976, "non-read/write operations count": 30002}], "peak memory": [{"job": 303235072, "process": 272650240}], "network": [{"bandwidth": 0}]}]}, {"name": "e:\\os\\src\\onecore\\ds\\ds\\src\\aimx\\prod\\aimxsrv\\serviceinstaller", "pass": "PASS2", "returncode": 0, "stats": [{"execution times": [{"job start time": "2025-07-15 21:03:59.243", "pip start time": "2025-07-15 21:03:59.273", "pip end time": "2025-07-15 21:04:06.400", "job end time": "2025-07-15 21:04:06.415"}], "i/o": [{"bytes read": 12840585, "bytes written": 3143264, "bytes transferred (non-read/write)": 4352764, "read operation count": 5002, "write operation count": 35310, "non-read/write operations count": 39145}], "peak memory": [{"job": 236650496, "process": 205209600}], "network": [{"bandwidth": 0}]}]}, {"name": "e:\\os\\src\\onecore\\ds\\ds\\src\\aimx\\prod\\aimxsrv\\dll", "pass": "PASS2", "returncode": 0, "stats": [{"execution times": [{"job start time": "2025-07-15 21:03:59.243", "pip start time": "2025-07-15 21:03:59.283", "pip end time": "2025-07-15 21:04:10.949", "job end time": "2025-07-15 21:04:10.978"}], "i/o": [{"bytes read": 48336138, "bytes written": 56541280, "bytes transferred (non-read/write)": 4382972, "read operation count": 15026, "write operation count": 37385, "non-read/write operations count": 52193}], "peak memory": [{"job": 955465728, "process": 924884992}], "network": [{"bandwidth": 0}]}]}]}