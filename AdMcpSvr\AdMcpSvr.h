/*++

Copyright (c) Microsoft Corporation. All rights reserved.

Module Name:

    AdMcpSvr.h

Abstract:

    Active Directory in-process MCP server that provides comprehensive AD query tools.
    Implements native Win32 LDAP API calls while maintaining PowerShell cmdlet parameter
    compatibility for seamless integration with existing AD management workflows.

    This server provides read-only access to Active Directory information including:
    - User accounts (Get-ADUser)
    - Groups (Get-ADGroup) 
    - Computer accounts (Get-ADComputer)
    - Domain information (Get-ADDomain)
    - Forest information (Get-ADForest)

    All tools maintain strict compatibility with PowerShell Active Directory module
    parameter structures and descriptions as documented in MSDN.

Author:

    <PERSON><PERSON><PERSON> (rizhang) 2025-7-16

--*/

#pragma once

#include "../aimxsrv/server/InProcessMcpServerBase.h"
#include <windows.h>
#include <winldap.h>
#include <ntldap.h>
#include <dsgetdc.h>
#include <lm.h>

// Active Directory MCP Server
// Provides comprehensive AD query tools using native Win32 LDAP APIs
class AdMcpSvr : public InProcessMcpServerBase
{
public:
    // Constructor
    AdMcpSvr();

    // Destructor
    virtual ~AdMcpSvr();

protected:
    // Override initialization to register AD tools
    HRESULT OnInitialize() override;

    // Override cleanup for LDAP connection management
    void OnUninitialize() override;

private:
    // LDAP connection management
    LDAP* m_pLdapConnection;
    std::wstring m_domainController;
    std::wstring m_defaultNamingContext;
    
    // Tool handler methods - each accepts and returns JSON blobs
    // Implementations are in separate .cpp files for better maintainability

    // Get-ADUser: Gets one or more Active Directory users
    // Supports Identity, Filter, LDAPFilter, Properties, SearchBase, SearchScope, Server parameters
    HRESULT GetADUserTool(
        _In_ const nlohmann::json& parameters,
        _Out_ nlohmann::json& result
        );

    // Get-ADGroup: Gets one or more Active Directory groups
    // Supports Identity, Filter, LDAPFilter, Properties, SearchBase, SearchScope, Server parameters
    HRESULT GetADGroupTool(
        _In_ const nlohmann::json& parameters,
        _Out_ nlohmann::json& result
        );

    // Get-ADComputer: Gets one or more Active Directory computers
    // Supports Identity, Filter, LDAPFilter, Properties, SearchBase, SearchScope, Server parameters
    HRESULT GetADComputerTool(
        _In_ const nlohmann::json& parameters,
        _Out_ nlohmann::json& result
        );

    // Get-ADDomain: Gets an Active Directory domain
    // Supports Identity, Server parameters
    HRESULT GetADDomainTool(
        _In_ const nlohmann::json& parameters,
        _Out_ nlohmann::json& result
        );

    // Get-ADForest: Gets an Active Directory forest
    // Supports Identity, Server parameters
    HRESULT GetADForestTool(
        _In_ const nlohmann::json& parameters,
        _Out_ nlohmann::json& result
        );

    // Helper methods for LDAP operations

    // Initialize LDAP connection to domain controller
    HRESULT InitializeLdapConnection(
        _In_opt_ const std::wstring& serverName = L""
        );

    // Cleanup LDAP connection
    void CleanupLdapConnection();

    // Execute LDAP search with specified parameters
    HRESULT ExecuteLdapSearch(
        _In_ const std::wstring& searchBase,
        _In_ const std::wstring& filter,
        _In_ const std::vector<std::wstring>& attributes,
        _In_ ULONG searchScope,
        _Out_ std::vector<nlohmann::json>& results
        );

    // Convert LDAP search result to JSON object
    HRESULT ConvertLdapEntryToJson(
        _In_ LDAP* pLdap,
        _In_ LDAPMessage* pEntry,
        _In_ const std::vector<std::wstring>& requestedAttributes,
        _Out_ nlohmann::json& jsonObject
        );

    // Parameter validation and conversion helpers

    // Validate and parse Filter parameter to LDAP filter
    HRESULT ParseFilterParameter(
        _In_ const nlohmann::json& filterParam,
        _Out_ std::wstring& ldapFilter
        );

    // Advanced PowerShell filter parsing helpers
    HRESULT ParsePowerShellExpression(
        _In_ const std::wstring& psExpression,
        _Out_ std::wstring& ldapFilter
        );

    HRESULT ParseComparisonExpression(
        _In_ const std::wstring& expression,
        _Out_ std::wstring& ldapFilter
        );

    HRESULT ParseLogicalExpression(
        _In_ const std::wstring& expression,
        _Out_ std::wstring& ldapFilter
        );

    std::wstring EscapeLdapFilterValue(const std::wstring& value);
    std::wstring ConvertPowerShellOperatorToLdap(const std::wstring& psOperator);
    std::vector<std::wstring> TokenizePowerShellExpression(const std::wstring& expression);

    // Validate and parse Properties parameter
    HRESULT ParsePropertiesParameter(
        _In_ const nlohmann::json& propertiesParam,
        _Out_ std::vector<std::wstring>& attributes
        );

    // Validate and parse SearchScope parameter
    HRESULT ParseSearchScopeParameter(
        _In_ const nlohmann::json& scopeParam,
        _Out_ ULONG& searchScope
        );

    // Get default attributes for each object type
    std::vector<std::wstring> GetDefaultUserAttributes();
    std::vector<std::wstring> GetDefaultGroupAttributes();
    std::vector<std::wstring> GetDefaultComputerAttributes();
    std::vector<std::wstring> GetDefaultDomainAttributes();
    std::vector<std::wstring> GetDefaultForestAttributes();
    std::vector<std::wstring> GetDefaultPasswordPolicyAttributes();

    // Tool-specific FGPP attribute functions (each tool has its own implementation)
    std::vector<std::wstring> GetDefaultFGPPAttributes();  // For GetADFineGrainedPasswordPolicy tool
    std::vector<std::wstring> GetDefaultFGPPAttributesForUserTool();  // For GetADUserResultantPasswordPolicy tool

    // Binary attribute conversion helpers
    std::string ConvertGuidToString(_In_ const berval* pBerVal);
    std::string ConvertSidToString(_In_ const berval* pBerVal);

    // Error handling helpers
    HRESULT CreateLdapErrorResponse(
        _In_ ULONG ldapError,
        _In_ const std::wstring& operation,
        _Out_ nlohmann::json& errorResponse
        );

    // Create error response for HRESULT errors
    HRESULT CreateHResultErrorResponse(
        _In_ HRESULT hr,
        _In_ const std::wstring& operation,
        _Out_ nlohmann::json& errorResponse
        );

    // Domain controller discovery
    HRESULT DiscoverDomainController(
        _In_opt_ const std::wstring& domainName,
        _Out_ std::wstring& dcName
        );
};

// Factory for creating AD MCP server instances
DECLARE_INPROCESS_MCP_SERVER(AdMcpSvr)

// Export functions for AIMXSrv to register this server
extern "C" __declspec(dllexport) HRESULT RegisterAdMcpSvr();
extern "C" __declspec(dllexport) HRESULT UnregisterAdMcpSvr();
