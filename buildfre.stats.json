{"built": [{"name": "e:\\os\\src\\onecore\\ds\\ds\\src\\aimx\\prod\\admcpsvr", "pass": "PASS_MISC", "returncode": 0, "stats": [{"execution times": [{"job start time": "2025-07-24 19:41:32.591", "pip start time": "0-00-00 00:00:00.000", "pip end time": "0-00-00 00:00:00.000", "job end time": "2025-07-24 19:41:32.607"}], "i/o": [{"bytes read": 0, "bytes written": 0, "bytes transferred (non-read/write)": 0, "read operation count": 0, "write operation count": 0, "non-read/write operations count": 0}], "peak memory": [{"job": 0, "process": 0}], "network": [{"bandwidth": 0}]}]}, {"name": "e:\\os\\src\\onecore\\ds\\ds\\src\\aimx\\prod\\aimxsrv\\idl", "pass": "PASS_MISC", "returncode": 0, "stats": [{"execution times": [{"job start time": "2025-07-24 19:41:32.607", "pip start time": "0-00-00 00:00:00.000", "pip end time": "0-00-00 00:00:00.000", "job end time": "2025-07-24 19:41:32.607"}], "i/o": [{"bytes read": 0, "bytes written": 0, "bytes transferred (non-read/write)": 0, "read operation count": 0, "write operation count": 0, "non-read/write operations count": 0}], "peak memory": [{"job": 0, "process": 0}], "network": [{"bandwidth": 0}]}]}, {"name": "e:\\os\\src\\onecore\\ds\\ds\\src\\aimx\\prod\\mcpserversample\\exe", "pass": "PASS_MISC", "returncode": 0, "stats": [{"execution times": [{"job start time": "2025-07-24 19:41:32.653", "pip start time": "0-00-00 00:00:00.000", "pip end time": "0-00-00 00:00:00.000", "job end time": "2025-07-24 19:41:32.653"}], "i/o": [{"bytes read": 0, "bytes written": 0, "bytes transferred (non-read/write)": 0, "read operation count": 0, "write operation count": 0, "non-read/write operations count": 0}], "peak memory": [{"job": 0, "process": 0}], "network": [{"bandwidth": 0}]}]}, {"name": "e:\\os\\src\\onecore\\ds\\ds\\src\\aimx\\prod\\aimxsrv\\server", "pass": "PASS_MISC", "returncode": 0, "stats": [{"execution times": [{"job start time": "2025-07-24 19:41:32.653", "pip start time": "0-00-00 00:00:00.000", "pip end time": "0-00-00 00:00:00.000", "job end time": "2025-07-24 19:41:32.684"}], "i/o": [{"bytes read": 0, "bytes written": 0, "bytes transferred (non-read/write)": 0, "read operation count": 0, "write operation count": 0, "non-read/write operations count": 0}], "peak memory": [{"job": 0, "process": 0}], "network": [{"bandwidth": 0}]}]}, {"name": "e:\\os\\src\\onecore\\ds\\ds\\src\\aimx\\prod\\llmclientlib", "pass": "PASS_MISC", "returncode": 0, "stats": [{"execution times": [{"job start time": "2025-07-24 19:41:33.008", "pip start time": "0-00-00 00:00:00.000", "pip end time": "0-00-00 00:00:00.000", "job end time": "2025-07-24 19:41:33.018"}], "i/o": [{"bytes read": 0, "bytes written": 0, "bytes transferred (non-read/write)": 0, "read operation count": 0, "write operation count": 0, "non-read/write operations count": 0}], "peak memory": [{"job": 0, "process": 0}], "network": [{"bandwidth": 0}]}]}, {"name": "e:\\os\\src\\onecore\\ds\\ds\\src\\aimx\\prod\\aimxsrv\\serviceinstaller", "pass": "PASS_MISC", "returncode": 0, "stats": [{"execution times": [{"job start time": "2025-07-24 19:41:33.018", "pip start time": "0-00-00 00:00:00.000", "pip end time": "0-00-00 00:00:00.000", "job end time": "2025-07-24 19:41:33.033"}], "i/o": [{"bytes read": 0, "bytes written": 0, "bytes transferred (non-read/write)": 0, "read operation count": 0, "write operation count": 0, "non-read/write operations count": 0}], "peak memory": [{"job": 0, "process": 0}], "network": [{"bandwidth": 0}]}]}, {"name": "e:\\os\\src\\onecore\\ds\\ds\\src\\aimx\\prod\\mcpprotocollib", "pass": "PASS_MISC", "returncode": 0, "stats": [{"execution times": [{"job start time": "2025-07-24 19:41:33.033", "pip start time": "0-00-00 00:00:00.000", "pip end time": "0-00-00 00:00:00.000", "job end time": "2025-07-24 19:41:33.033"}], "i/o": [{"bytes read": 0, "bytes written": 0, "bytes transferred (non-read/write)": 0, "read operation count": 0, "write operation count": 0, "non-read/write operations count": 0}], "peak memory": [{"job": 0, "process": 0}], "network": [{"bandwidth": 0}]}]}, {"name": "e:\\os\\src\\onecore\\ds\\ds\\src\\aimx\\prod\\aimxsrv\\client\\lib", "pass": "PASS_MISC", "returncode": 0, "stats": [{"execution times": [{"job start time": "2025-07-24 19:41:33.066", "pip start time": "0-00-00 00:00:00.000", "pip end time": "0-00-00 00:00:00.000", "job end time": "2025-07-24 19:41:33.066"}], "i/o": [{"bytes read": 0, "bytes written": 0, "bytes transferred (non-read/write)": 0, "read operation count": 0, "write operation count": 0, "non-read/write operations count": 0}], "peak memory": [{"job": 0, "process": 0}], "network": [{"bandwidth": 0}]}]}, {"name": "e:\\os\\src\\onecore\\ds\\ds\\src\\aimx\\prod\\aimxsrv\\dll", "pass": "PASS_MISC", "returncode": 0, "stats": [{"execution times": [{"job start time": "2025-07-24 19:41:33.059", "pip start time": "0-00-00 00:00:00.000", "pip end time": "0-00-00 00:00:00.000", "job end time": "2025-07-24 19:41:33.066"}], "i/o": [{"bytes read": 0, "bytes written": 0, "bytes transferred (non-read/write)": 0, "read operation count": 0, "write operation count": 0, "non-read/write operations count": 0}], "peak memory": [{"job": 0, "process": 0}], "network": [{"bandwidth": 0}]}]}, {"name": "e:\\os\\src\\onecore\\ds\\ds\\src\\aimx\\prod\\aimxsrv\\client\\dll", "pass": "PASS_MISC", "returncode": 0, "stats": [{"execution times": [{"job start time": "2025-07-24 19:41:33.108", "pip start time": "0-00-00 00:00:00.000", "pip end time": "0-00-00 00:00:00.000", "job end time": "2025-07-24 19:41:33.122"}], "i/o": [{"bytes read": 0, "bytes written": 0, "bytes transferred (non-read/write)": 0, "read operation count": 0, "write operation count": 0, "non-read/write operations count": 0}], "peak memory": [{"job": 0, "process": 0}], "network": [{"bandwidth": 0}]}]}, {"name": "e:\\os\\src\\onecore\\ds\\ds\\src\\aimx\\prod\\mcpserversample\\lib", "pass": "PASS_MISC", "returncode": 0, "stats": [{"execution times": [{"job start time": "2025-07-24 19:41:33.122", "pip start time": "0-00-00 00:00:00.000", "pip end time": "0-00-00 00:00:00.000", "job end time": "2025-07-24 19:41:33.129"}], "i/o": [{"bytes read": 0, "bytes written": 0, "bytes transferred (non-read/write)": 0, "read operation count": 0, "write operation count": 0, "non-read/write operations count": 0}], "peak memory": [{"job": 0, "process": 0}], "network": [{"bandwidth": 0}]}]}, {"name": "e:\\os\\src\\onecore\\ds\\ds\\src\\aimx\\prod\\aimxsrv\\powershell", "pass": "PASS_MISC", "returncode": 0, "stats": [{"execution times": [{"job start time": "2025-07-24 19:41:33.131", "pip start time": "0-00-00 00:00:00.000", "pip end time": "0-00-00 00:00:00.000", "job end time": "2025-07-24 19:41:33.135"}], "i/o": [{"bytes read": 0, "bytes written": 0, "bytes transferred (non-read/write)": 0, "read operation count": 0, "write operation count": 0, "non-read/write operations count": 0}], "peak memory": [{"job": 0, "process": 0}], "network": [{"bandwidth": 0}]}]}, {"name": "e:\\os\\src\\onecore\\ds\\ds\\src\\aimx\\prod\\cpprestsdk", "pass": "PASS_MISC", "returncode": 0, "stats": [{"execution times": [{"job start time": "2025-07-24 19:41:33.609", "pip start time": "0-00-00 00:00:00.000", "pip end time": "0-00-00 00:00:00.000", "job end time": "2025-07-24 19:41:35.448"}], "i/o": [{"bytes read": 0, "bytes written": 0, "bytes transferred (non-read/write)": 0, "read operation count": 0, "write operation count": 0, "non-read/write operations count": 0}], "peak memory": [{"job": 0, "process": 0}], "network": [{"bandwidth": 0}]}]}, {"name": "e:\\os\\src\\onecore\\ds\\ds\\src\\aimx\\prod\\mcpserversample", "pass": "PASS_MISC", "returncode": 0, "stats": [{"execution times": [{"job start time": "2025-07-24 19:41:35.559", "pip start time": "0-00-00 00:00:00.000", "pip end time": "0-00-00 00:00:00.000", "job end time": "2025-07-24 19:41:35.559"}], "i/o": [{"bytes read": 0, "bytes written": 0, "bytes transferred (non-read/write)": 0, "read operation count": 0, "write operation count": 0, "non-read/write operations count": 0}], "peak memory": [{"job": 0, "process": 0}], "network": [{"bandwidth": 0}]}]}, {"name": "e:\\os\\src\\onecore\\ds\\ds\\src\\aimx\\prod\\semanticrag\\examples", "pass": "PASS_MISC", "returncode": 0, "stats": [{"execution times": [{"job start time": "2025-07-24 19:41:35.591", "pip start time": "0-00-00 00:00:00.000", "pip end time": "0-00-00 00:00:00.000", "job end time": "2025-07-24 19:41:35.591"}], "i/o": [{"bytes read": 0, "bytes written": 0, "bytes transferred (non-read/write)": 0, "read operation count": 0, "write operation count": 0, "non-read/write operations count": 0}], "peak memory": [{"job": 0, "process": 0}], "network": [{"bandwidth": 0}]}]}, {"name": "e:\\os\\src\\onecore\\ds\\ds\\src\\aimx\\prod\\semanticrag\\examples", "pass": "PASS_MISC", "returncode": 0, "stats": [{"execution times": [{"job start time": "2025-07-24 19:41:35.611", "pip start time": "0-00-00 00:00:00.000", "pip end time": "0-00-00 00:00:00.000", "job end time": "2025-07-24 19:41:35.611"}], "i/o": [{"bytes read": 0, "bytes written": 0, "bytes transferred (non-read/write)": 0, "read operation count": 0, "write operation count": 0, "non-read/write operations count": 0}], "peak memory": [{"job": 0, "process": 0}], "network": [{"bandwidth": 0}]}]}, {"name": "e:\\os\\src\\onecore\\ds\\ds\\src\\aimx\\prod\\semanticrag\\examples", "pass": "PASS_MISC", "returncode": 0, "stats": [{"execution times": [{"job start time": "2025-07-24 19:41:35.637", "pip start time": "0-00-00 00:00:00.000", "pip end time": "0-00-00 00:00:00.000", "job end time": "2025-07-24 19:41:35.637"}], "i/o": [{"bytes read": 0, "bytes written": 0, "bytes transferred (non-read/write)": 0, "read operation count": 0, "write operation count": 0, "non-read/write operations count": 0}], "peak memory": [{"job": 0, "process": 0}], "network": [{"bandwidth": 0}]}]}, {"name": "e:\\os\\src\\onecore\\ds\\ds\\src\\aimx\\prod\\aimxsrv\\server", "pass": "PASS0", "returncode": 0, "stats": [{"execution times": [{"job start time": "2025-07-24 19:41:35.927", "pip start time": "2025-07-24 19:41:35.974", "pip end time": "2025-07-24 19:41:38.312", "job end time": "2025-07-24 19:41:38.333"}], "i/o": [{"bytes read": 1774774, "bytes written": 3383606, "bytes transferred (non-read/write)": 2092854, "read operation count": 572, "write operation count": 98, "non-read/write operations count": 11674}], "peak memory": [{"job": 61612032, "process": 31186944}], "network": [{"bandwidth": 0}]}]}, {"name": "e:\\os\\src\\onecore\\ds\\ds\\src\\aimx\\prod\\llmclientlib", "pass": "PASS0", "returncode": 0, "stats": [{"execution times": [{"job start time": "2025-07-24 19:41:35.927", "pip start time": "2025-07-24 19:41:35.974", "pip end time": "2025-07-24 19:41:38.318", "job end time": "2025-07-24 19:41:38.337"}], "i/o": [{"bytes read": 1770430, "bytes written": 640752, "bytes transferred (non-read/write)": 2067344, "read operation count": 569, "write operation count": 49, "non-read/write operations count": 11232}], "peak memory": [{"job": 61485056, "process": 31174656}], "network": [{"bandwidth": 0}]}]}, {"name": "e:\\os\\src\\onecore\\ds\\ds\\src\\aimx\\prod\\mcpserversample\\exe", "pass": "PASS0", "returncode": 0, "stats": [{"execution times": [{"job start time": "2025-07-24 19:41:35.919", "pip start time": "2025-07-24 19:41:35.974", "pip end time": "2025-07-24 19:41:38.318", "job end time": "2025-07-24 19:41:38.337"}], "i/o": [{"bytes read": 1786603, "bytes written": 97922, "bytes transferred (non-read/write)": 2084186, "read operation count": 608, "write operation count": 49, "non-read/write operations count": 11685}], "peak memory": [{"job": 60821504, "process": 30597120}], "network": [{"bandwidth": 0}]}]}, {"name": "e:\\os\\src\\onecore\\ds\\ds\\src\\aimx\\prod\\admcpsvr", "pass": "PASS0", "returncode": 0, "stats": [{"execution times": [{"job start time": "2025-07-24 19:41:35.919", "pip start time": "2025-07-24 19:41:35.974", "pip end time": "2025-07-24 19:41:38.320", "job end time": "2025-07-24 19:41:38.340"}], "i/o": [{"bytes read": 1773541, "bytes written": 548923, "bytes transferred (non-read/write)": 2070052, "read operation count": 570, "write operation count": 15980, "non-read/write operations count": 11309}], "peak memory": [{"job": 61259776, "process": 31297536}], "network": [{"bandwidth": 0}]}]}, {"name": "e:\\os\\src\\onecore\\ds\\ds\\src\\aimx\\prod\\aimxsrv\\client\\lib", "pass": "PASS0", "returncode": 0, "stats": [{"execution times": [{"job start time": "2025-07-24 19:41:35.931", "pip start time": "2025-07-24 19:41:35.974", "pip end time": "2025-07-24 19:41:38.324", "job end time": "2025-07-24 19:41:38.347"}], "i/o": [{"bytes read": 1772735, "bytes written": 447180, "bytes transferred (non-read/write)": 2096834, "read operation count": 570, "write operation count": 47, "non-read/write operations count": 11559}], "peak memory": [{"job": 61276160, "process": 30838784}], "network": [{"bandwidth": 0}]}]}, {"name": "e:\\os\\src\\onecore\\ds\\ds\\src\\aimx\\prod\\mcpserversample\\lib", "pass": "PASS0", "returncode": 0, "stats": [{"execution times": [{"job start time": "2025-07-24 19:41:35.934", "pip start time": "2025-07-24 19:41:35.991", "pip end time": "2025-07-24 19:41:38.363", "job end time": "2025-07-24 19:41:38.383"}], "i/o": [{"bytes read": 1770563, "bytes written": 72133, "bytes transferred (non-read/write)": 2075774, "read operation count": 569, "write operation count": 40, "non-read/write operations count": 11381}], "peak memory": [{"job": 61681664, "process": 31383552}], "network": [{"bandwidth": 0}]}]}, {"name": "e:\\os\\src\\onecore\\ds\\ds\\src\\aimx\\prod\\aimxsrv\\dll", "pass": "PASS0", "returncode": 0, "stats": [{"execution times": [{"job start time": "2025-07-24 19:41:35.933", "pip start time": "2025-07-24 19:41:35.974", "pip end time": "2025-07-24 19:41:38.438", "job end time": "2025-07-24 19:41:38.447"}], "i/o": [{"bytes read": 1778576, "bytes written": 576396, "bytes transferred (non-read/write)": 2087050, "read operation count": 592, "write operation count": 15874, "non-read/write operations count": 11620}], "peak memory": [{"job": 61374464, "process": 31715328}], "network": [{"bandwidth": 0}]}]}, {"name": "e:\\os\\src\\onecore\\ds\\ds\\src\\aimx\\prod\\aimxsrv\\client\\dll", "pass": "PASS0", "returncode": 0, "stats": [{"execution times": [{"job start time": "2025-07-24 19:41:35.934", "pip start time": "2025-07-24 19:41:35.995", "pip end time": "2025-07-24 19:41:38.529", "job end time": "2025-07-24 19:41:38.545"}], "i/o": [{"bytes read": 1774034, "bytes written": 523812, "bytes transferred (non-read/write)": 2104608, "read operation count": 589, "write operation count": 15824, "non-read/write operations count": 11768}], "peak memory": [{"job": 61595648, "process": 31408128}], "network": [{"bandwidth": 0}]}]}, {"name": "e:\\os\\src\\onecore\\ds\\ds\\src\\aimx\\prod\\aimxsrv\\idl", "pass": "PASS0", "returncode": 0, "stats": [{"execution times": [{"job start time": "2025-07-24 19:41:35.919", "pip start time": "2025-07-24 19:41:35.981", "pip end time": "2025-07-24 19:41:39.105", "job end time": "2025-07-24 19:41:39.122"}], "i/o": [{"bytes read": 6033862, "bytes written": 315371, "bytes transferred (non-read/write)": 2187546, "read operation count": 996, "write operation count": 15944, "non-read/write operations count": 13966}], "peak memory": [{"job": 60506112, "process": 30965760}], "network": [{"bandwidth": 0}]}]}, {"name": "e:\\os\\src\\onecore\\ds\\ds\\src\\aimx\\prod\\cpprestsdk", "pass": "PASS0", "returncode": 0, "stats": [{"execution times": [{"job start time": "2025-07-24 19:41:35.941", "pip start time": "2025-07-24 19:41:35.955", "pip end time": "2025-07-24 19:41:39.472", "job end time": "2025-07-24 19:41:39.478"}], "i/o": [{"bytes read": 1330543, "bytes written": 177256, "bytes transferred (non-read/write)": 2037194, "read operation count": 594, "write operation count": 18759, "non-read/write operations count": 7821}], "peak memory": [{"job": 77414400, "process": 42790912}], "network": [{"bandwidth": 0}]}]}, {"name": "e:\\os\\src\\onecore\\ds\\ds\\src\\aimx\\prod\\aimxsrv\\powershell", "pass": "PASS0", "returncode": 0, "stats": [{"execution times": [{"job start time": "2025-07-24 19:41:35.941", "pip start time": "2025-07-24 19:41:36.001", "pip end time": "2025-07-24 19:41:39.934", "job end time": "2025-07-24 19:41:39.950"}], "i/o": [{"bytes read": 3817493, "bytes written": 157822, "bytes transferred (non-read/write)": 2253412, "read operation count": 2618, "write operation count": 16050, "non-read/write operations count": 20977}], "peak memory": [{"job": 77955072, "process": 32399360}], "network": [{"bandwidth": 0}]}]}, {"name": "e:\\os\\src\\onecore\\ds\\ds\\src\\aimx\\prod\\aimxsrv\\serviceinstaller", "pass": "PASS1", "returncode": 0, "stats": [{"execution times": [{"job start time": "2025-07-24 19:41:39.971", "pip start time": "2025-07-24 19:41:40.028", "pip end time": "2025-07-24 19:41:45.096", "job end time": "2025-07-24 19:41:45.113"}], "i/o": [{"bytes read": 18050223, "bytes written": 3901169, "bytes transferred (non-read/write)": 3411270, "read operation count": 1969, "write operation count": 20818, "non-read/write operations count": 20624}], "peak memory": [{"job": 84164608, "process": 53587968}], "network": [{"bandwidth": 0}]}]}, {"name": "e:\\os\\src\\onecore\\ds\\ds\\src\\aimx\\prod\\mcpserversample\\lib", "pass": "PASS1", "returncode": 0, "stats": [{"execution times": [{"job start time": "2025-07-24 19:41:39.982", "pip start time": "2025-07-24 19:41:40.028", "pip end time": "2025-07-24 19:41:46.841", "job end time": "2025-07-24 19:41:46.861"}], "i/o": [{"bytes read": 22294689, "bytes written": 11090910, "bytes transferred (non-read/write)": 3485814, "read operation count": 2855, "write operation count": 4975, "non-read/write operations count": 22398}], "peak memory": [{"job": 209219584, "process": 178692096}], "network": [{"bandwidth": 0}]}]}, {"name": "e:\\os\\src\\onecore\\ds\\ds\\src\\aimx\\prod\\llmclientlib", "pass": "PASS1", "returncode": 0, "stats": [{"execution times": [{"job start time": "2025-07-24 19:41:39.971", "pip start time": "2025-07-24 19:41:40.028", "pip end time": "2025-07-24 19:41:46.911", "job end time": "2025-07-24 19:41:46.931"}], "i/o": [{"bytes read": 21059280, "bytes written": 11216982, "bytes transferred (non-read/write)": 3447568, "read operation count": 2801, "write operation count": 21759, "non-read/write operations count": 21936}], "peak memory": [{"job": 209784832, "process": 178786304}], "network": [{"bandwidth": 0}]}]}, {"name": "e:\\os\\src\\onecore\\ds\\ds\\src\\aimx\\prod\\aimxsrv\\powershell", "pass": "PASS1", "returncode": 0, "stats": [{"execution times": [{"job start time": "2025-07-24 19:41:39.984", "pip start time": "2025-07-24 19:41:40.028", "pip end time": "2025-07-24 19:41:47.639", "job end time": "2025-07-24 19:41:47.657"}], "i/o": [{"bytes read": 13959470, "bytes written": 578551, "bytes transferred (non-read/write)": 2373806, "read operation count": 2801, "write operation count": 16965, "non-read/write operations count": 28161}], "peak memory": [{"job": 89030656, "process": 58404864}], "network": [{"bandwidth": 0}]}]}, {"name": "e:\\os\\src\\onecore\\ds\\ds\\src\\aimx\\prod\\aimxsrv\\client\\lib", "pass": "PASS1", "returncode": 0, "stats": [{"execution times": [{"job start time": "2025-07-24 19:41:39.971", "pip start time": "2025-07-24 19:41:40.019", "pip end time": "2025-07-24 19:41:47.836", "job end time": "2025-07-24 19:41:47.852"}], "i/o": [{"bytes read": 31435260, "bytes written": 35620262, "bytes transferred (non-read/write)": 5266374, "read operation count": 4027, "write operation count": 9027, "non-read/write operations count": 30322}], "peak memory": [{"job": 196751360, "process": 165801984}], "network": [{"bandwidth": 0}]}]}, {"name": "e:\\os\\src\\onecore\\ds\\ds\\src\\aimx\\prod\\aimxsrv\\client\\dll", "pass": "PASS1", "returncode": 0, "stats": [{"execution times": [{"job start time": "2025-07-24 19:41:39.971", "pip start time": "2025-07-24 19:41:40.028", "pip end time": "2025-07-24 19:41:47.850", "job end time": "2025-07-24 19:41:47.865"}], "i/o": [{"bytes read": 31439761, "bytes written": 35703088, "bytes transferred (non-read/write)": 5266652, "read operation count": 4046, "write operation count": 26271, "non-read/write operations count": 30183}], "peak memory": [{"job": 196374528, "process": 165830656}], "network": [{"bandwidth": 0}]}]}, {"name": "e:\\os\\src\\onecore\\ds\\ds\\src\\aimx\\prod\\mcpprotocollib", "pass": "PASS1", "returncode": 0, "stats": [{"execution times": [{"job start time": "2025-07-24 19:41:39.971", "pip start time": "2025-07-24 19:41:40.028", "pip end time": "2025-07-24 19:41:50.092", "job end time": "2025-07-24 19:41:50.103"}], "i/o": [{"bytes read": 39292063, "bytes written": 22434413, "bytes transferred (non-read/write)": 3518174, "read operation count": 4821, "write operation count": 23909, "non-read/write operations count": 26500}], "peak memory": [{"job": 208306176, "process": 177565696}], "network": [{"bandwidth": 0}]}]}, {"name": "e:\\os\\src\\onecore\\ds\\ds\\src\\aimx\\prod\\mcpserversample\\exe", "pass": "PASS1", "returncode": 0, "stats": [{"execution times": [{"job start time": "2025-07-24 19:41:39.966", "pip start time": "2025-07-24 19:41:40.028", "pip end time": "2025-07-24 19:41:50.338", "job end time": "2025-07-24 19:41:50.347"}], "i/o": [{"bytes read": 43211362, "bytes written": 24389415, "bytes transferred (non-read/write)": 4858390, "read operation count": 5277, "write operation count": 27181, "non-read/write operations count": 31829}], "peak memory": [{"job": 176447488, "process": 147554304}], "network": [{"bandwidth": 0}]}]}, {"name": "e:\\os\\src\\onecore\\ds\\ds\\src\\aimx\\prod\\admcpsvr", "pass": "PASS1", "returncode": 0, "stats": [{"execution times": [{"job start time": "2025-07-24 19:41:39.966", "pip start time": "2025-07-24 19:41:40.028", "pip end time": "2025-07-24 19:42:00.653", "job end time": "2025-07-24 19:42:00.670"}], "i/o": [{"bytes read": 154107002, "bytes written": 76422790, "bytes transferred (non-read/write)": 3927460, "read operation count": 15966, "write operation count": 31139, "non-read/write operations count": 54479}], "peak memory": [{"job": 201207808, "process": 170287104}], "network": [{"bandwidth": 0}]}]}, {"name": "e:\\os\\src\\onecore\\ds\\ds\\src\\aimx\\prod\\cpprestsdk", "pass": "PASS1", "returncode": 0, "stats": [{"execution times": [{"job start time": "2025-07-24 19:41:39.986", "pip start time": "2025-07-24 19:41:39.997", "pip end time": "2025-07-24 19:42:08.862", "job end time": "2025-07-24 19:42:09.766"}], "i/o": [{"bytes read": 381518759, "bytes written": 281540915, "bytes transferred (non-read/write)": 9792702, "read operation count": 109892, "write operation count": 34799, "non-read/write operations count": 560205}], "peak memory": [{"job": 505503744, "process": 217108480}], "network": [{"bandwidth": 0}]}]}, {"name": "e:\\os\\src\\onecore\\ds\\ds\\src\\aimx\\prod\\aimxsrv\\dll", "pass": "PASS1", "returncode": 0, "stats": [{"execution times": [{"job start time": "2025-07-24 19:42:09.782", "pip start time": "2025-07-24 19:42:09.814", "pip end time": "2025-07-24 19:42:15.665", "job end time": "2025-07-24 19:42:15.681"}], "i/o": [{"bytes read": 50666517, "bytes written": 87945319, "bytes transferred (non-read/write)": 5336622, "read operation count": 7067, "write operation count": 29342, "non-read/write operations count": 34683}], "peak memory": [{"job": 273317888, "process": 243924992}], "network": [{"bandwidth": 0}]}]}, {"name": "e:\\os\\src\\onecore\\ds\\ds\\src\\aimx\\prod\\aimxsrv\\server", "pass": "PASS1", "returncode": 1, "stats": [{"execution times": [{"job start time": "2025-07-24 19:42:09.782", "pip start time": "2025-07-24 19:42:09.814", "pip end time": "2025-07-24 19:42:22.429", "job end time": "2025-07-24 19:42:22.440"}], "i/o": [{"bytes read": 110040344, "bytes written": 212518049, "bytes transferred (non-read/write)": 3369406, "read operation count": 18043, "write operation count": 22989, "non-read/write operations count": 44436}], "peak memory": [{"job": 283422720, "process": 252534784}], "network": [{"bandwidth": 0}]}]}, {"name": "e:\\os\\src\\onecore\\ds\\ds\\src\\aimx\\prod\\cpprestsdk", "pass": "PASS2", "returncode": 0, "stats": [{"execution times": [{"job start time": "2025-07-24 19:42:22.924", "pip start time": "2025-07-24 19:42:22.935", "pip end time": "2025-07-24 19:42:25.010", "job end time": "2025-07-24 19:42:25.390"}], "i/o": [{"bytes read": 1300557, "bytes written": 176855, "bytes transferred (non-read/write)": 2034650, "read operation count": 593, "write operation count": 18782, "non-read/write operations count": 7816}], "peak memory": [{"job": 77971456, "process": 42975232}], "network": [{"bandwidth": 0}]}]}, {"name": "e:\\os\\src\\onecore\\ds\\ds\\src\\aimx\\prod\\aimxsrv\\powershell", "pass": "PASS2", "returncode": 0, "stats": [{"execution times": [{"job start time": "2025-07-24 19:42:22.922", "pip start time": "2025-07-24 19:42:22.959", "pip end time": "2025-07-24 19:42:26.495", "job end time": "2025-07-24 19:42:26.507"}], "i/o": [{"bytes read": 6512379, "bytes written": 285669, "bytes transferred (non-read/write)": 4240227, "read operation count": 2074, "write operation count": 34037, "non-read/write operations count": 27117}], "peak memory": [{"job": 101568512, "process": 35319808}], "network": [{"bandwidth": 0}]}]}, {"name": "e:\\os\\src\\onecore\\ds\\ds\\src\\aimx\\prod\\aimxsrv\\client\\dll", "pass": "PASS2", "returncode": 0, "stats": [{"execution times": [{"job start time": "2025-07-24 19:42:22.921", "pip start time": "2025-07-24 19:42:22.959", "pip end time": "2025-07-24 19:42:27.508", "job end time": "2025-07-24 19:42:27.527"}], "i/o": [{"bytes read": 11008814, "bytes written": 7529366, "bytes transferred (non-read/write)": 4344819, "read operation count": 2404, "write operation count": 36175, "non-read/write operations count": 31035}], "peak memory": [{"job": 299515904, "process": 270831616}], "network": [{"bandwidth": 0}]}]}, {"name": "e:\\os\\src\\onecore\\ds\\ds\\src\\aimx\\prod\\aimxsrv\\serviceinstaller", "pass": "PASS2", "returncode": 0, "stats": [{"execution times": [{"job start time": "2025-07-24 19:42:22.915", "pip start time": "2025-07-24 19:42:22.959", "pip end time": "2025-07-24 19:42:27.918", "job end time": "2025-07-24 19:42:27.934"}], "i/o": [{"bytes read": 12889106, "bytes written": 3145385, "bytes transferred (non-read/write)": 4453784, "read operation count": 5052, "write operation count": 36469, "non-read/write operations count": 41193}], "peak memory": [{"job": 246259712, "process": 215580672}], "network": [{"bandwidth": 0}]}]}]}