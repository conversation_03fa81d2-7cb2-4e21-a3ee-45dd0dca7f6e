# Fix for LLM Parameter Fabrication Issue

## Problem Description

The user reported that when asking "find user rizhang", the LLM was constructing fake Distinguished Names like:
```json
{"Identity": "CN=Rizhang,OU=Users,DC=example,DC=com"}
```

Instead of using the simple username as provided:
```json
{"Identity": "rizhang"}
```

This caused issues because:
1. The constructed DN was fake and didn't match the actual domain structure
2. The LLM was making up parameter values that weren't specified by the user
3. Active Directory cmdlets work perfectly fine with simple usernames

## Root Cause Analysis

The issue was in the LLM prompts in `SystemPromptManager.cpp`:

1. **Tool Analysis Prompt**: Instructed to "infer parameters" without clear guidance about not making up values
2. **Parameter Refinement Prompt**: Instructed to "infer missing parameters from context when possible"
3. **Tool Schema Descriptions**: Mentioned DN format first, leading LLM to prefer constructed DNs

## Solution Implemented

### 1. Updated Tool Analysis Prompt (`GetToolAnalysisPrompt`)

**Added explicit instructions:**
```cpp
2. **Parameter Extraction**: Extract parameters ONLY from what the user explicitly provided
   - **CRITICAL**: Do NOT make up, construct, or infer parameter values that weren't specified
   - **CRITICAL**: Do NOT construct Distinguished Names (DN) like "CN=User,OU=Users,DC=domain,DC=com" unless the user provided the full DN
   - For simple names like "rizhang", use them as-is: `"Identity": "rizhang"`
   - Only include parameters that are explicitly mentioned or clearly derivable from the user's request
```

**Added concrete examples:**
```cpp
### Parameter Examples:

**CORRECT**: User says "find user rizhang"
```json
"parameters": {"Identity": "rizhang"}
```

**INCORRECT**: Do NOT construct fake DNs
```json
"parameters": {"Identity": "CN=rizhang,OU=Users,DC=example,DC=com"}
```
```

### 2. Updated Parameter Refinement Prompt (`GetParameterRefinementPrompt`)

**Replaced problematic guidance:**
- Removed: "Infer missing parameters from context when possible"
- Added: "Do NOT make up or construct parameter values that weren't explicitly provided"

**Added critical rules:**
```cpp
### Critical Rules:
- **NEVER** construct Distinguished Names (DN) unless user provided the full DN
- **NEVER** make up domain structures like "DC=example,DC=com"
- Use simple names exactly as provided: "rizhang" stays "rizhang", not "CN=rizhang,OU=Users,DC=domain,DC=com"
- Only refine parameters that were explicitly mentioned by the user
```

### 3. Updated Tool Schema Descriptions

**Modified Identity parameter descriptions in:**
- `ADPsMCPSvr/Tools/UserManagement.ps1`
- `ADPsMCPSvr/Tools/GroupManagement.ps1`

**Changed from:**
```powershell
Identity = @{ type = "string"; description = "User identity (DN, GUID, SID, or SAM account name)" }
```

**Changed to:**
```powershell
Identity = @{ type = "string"; description = "User identity - use simple username/SAM account name (e.g., 'jdoe'), or full DN, GUID, or SID if needed" }
```

## Expected Behavior After Fix

### Before Fix:
```
User: "find user rizhang"
LLM: {"Identity": "CN=Rizhang,OU=Users,DC=example,DC=com"}
Result: Error - fake DN doesn't exist
```

### After Fix:
```
User: "find user rizhang"
LLM: {"Identity": "rizhang"}
PowerShell: Get-ADUser -Identity "rizhang"
Result: Success - finds user by SAM account name
```

## Test Cases Created

Created `Test-ParameterExtraction.ps1` with test cases showing correct behavior:

1. **Simple username**: "rizhang" → `{"Identity": "rizhang"}`
2. **Username with dots**: "john.doe" → `{"Identity": "john.doe"}`
3. **Group name**: "Administrators" → `{"Identity": "Administrators"}`
4. **User-provided DN**: Use as-is when user provides full DN
5. **Filter queries**: Extract filter expressions properly
6. **Property requests**: Extract Properties array when user asks for specific attributes

## Benefits

1. **Eliminates fake parameter construction**: LLM won't make up DNs or other complex values
2. **Improves reliability**: Simple usernames work consistently with AD cmdlets
3. **Reduces errors**: No more "object not found" errors due to fake DNs
4. **Better user experience**: Users can use natural language without worrying about exact AD syntax
5. **Maintains flexibility**: Still supports full DNs when users provide them

## Files Modified

1. `aimxsrv/server/SystemPromptManager.cpp` - Updated LLM prompts
2. `ADPsMCPSvr/Tools/UserManagement.ps1` - Updated tool schema descriptions
3. `Test-ParameterExtraction.ps1` - Created test cases (new file)
4. `Fix-ParameterFabrication-Summary.md` - This documentation (new file)

## Impact on Direct PowerShell Execution

This fix is particularly important for the direct PowerShell execution feature because:

1. **Simpler command construction**: No need to handle fake DN validation
2. **Better error handling**: Real errors vs. fabricated parameter errors
3. **Improved performance**: Direct execution with correct parameters
4. **Enhanced reliability**: PowerShell cmdlets handle simple names natively

The combination of direct PowerShell execution + corrected parameter extraction provides a much more robust and user-friendly experience.
