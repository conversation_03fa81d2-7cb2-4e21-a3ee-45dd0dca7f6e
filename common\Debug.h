/*++

Copyright (c) Microsoft Corporation. All rights reserved.

Module Name:

    Debug.h

Abstract:

    This module defines the debug logging infrastructure.
    Provides functions for logging debug information and errors.
    Implemented as a header-only library for cross-project use.

Author:

    <PERSON><PERSON><PERSON> (rizhang) 03/21/2025

--*/

#pragma once

#include <iostream>
#include <fstream>
#include <mutex>
#include <string>
#include <ctime>
#include <chrono>
#include <sstream>
#include <thread>
#include <filesystem>
#include <windows.h>
#include "StringUtils.h"

class Debug {
public:
    enum class LogLevel {
        VERBOSE,
        DEBUG,
        INFO,
        WARNING,
        ERR
    };

    static Debug&
    GetInstance()
    {
        static Debug instance;
        return instance;
    }
    
    // New method to get instance with a specific log file name
    static Debug&
    GetInstance(
        _In_ const std::string& logFileName
        )
    {
        Debug& instance = GetInstance();
        instance.SetCurrentLogFileName(logFileName);
        return instance;
    }

    // New method for wide string version
    static Debug&
    GetInstance(
        _In_ const std::wstring& logFileName
        )
    {
        Debug& instance = GetInstance();
        instance.SetCurrentLogFileName(logFileName);
        return instance;
    }

    void
    SetLogFileSizeLimit(
        _In_ size_t bytes
        )
    {
        logFileSizeLimit = bytes;
    }

    void
    SetLogDirectory(
        _In_ const std::string& dir
        )
    {
        logDirectory = dir;
        std::filesystem::create_directories(logDirectory);
        
        // Update log file path with new directory
        if (logFile.is_open()) {
            logFile.close();
        }
        
        logFilePath = logDirectory + "/" + currentLogFileName;
        logFile.open(logFilePath, std::ios::out | std::ios::app);
        
        if (logFile.is_open()) {
            WriteLogHeader();
        }
    }

    void
    SetLogLevel(
        _In_ LogLevel level
        )
    {
        currentLogLevel = level;
    }

    void
    SetCurrentLogFileName(
        _In_ const std::string& logFileName
        )
    {
        std::lock_guard<std::mutex> lock(logMutex);
        
        // Check if filename is actually changing
        if (currentLogFileName == logFileName)
        {
            return;
        }
        
        currentLogFileName = logFileName;
        
        // Close existing log file if open
        if (logFile.is_open())
        {
            WriteLogFooter();
            logFile.close();
        }
        
        // Update log file path and open new log file
        logFilePath = logDirectory + "/" + currentLogFileName;
        logFile.open(logFilePath, std::ios::out | std::ios::app);
        
        if (logFile.is_open())
        {
            WriteLogHeader();
        }
    }

    void
    SetCurrentLogFileName(
        _In_ const std::wstring& logFileName
        )
    {
        SetCurrentLogFileName(WideToUtf8(logFileName));
    }

    std::string
    GetCurrentLogFileName()
    {
        std::lock_guard<std::mutex> lock(logMutex);
        return currentLogFileName;
    }

    // Log functions
    template<typename... Args>
    void
    LogVerbose(
        _In_ Args... args
        );

    template<typename... Args>
    void
    LogDebug(
        _In_ Args... args
        );

    template<typename... Args>
    void
    LogInfo(
        _In_ Args... args
        );

    template<typename... Args>
    void
    LogWarning(
        _In_ Args... args
        );

    template<typename... Args>
    void
    LogError(
        _In_ Args... args
        );

private:
    Debug()
        : logDirectory("./logs")
    {
        // Ensure log directory exists
        EnsureLogDirectoryExists();
        
        // Initialize log file path
        logFilePath = logDirectory + "/" + currentLogFileName;

        // Read registry settings
        ReadLogLevelFromRegistry();
        ReadLogFileSizeFromRegistry();
        
        // Open initial log file
        logFile.open(logFilePath, std::ios::out | std::ios::app);
        if (logFile.is_open()) {
            WriteLogHeader();
        }
    }

    ~Debug()
    {
        if (logFile.is_open())
        {
            WriteLogFooter();
            logFile.close();
        }
    }

    Debug(
        _In_ const Debug&
        ) = delete;

    Debug&
    operator=(
        _In_ const Debug&
        ) = delete;

    void
    RotateLogFile()
    {
        // Check if a log file already exists
        if (logFile.is_open())
        {
            logFile.close();
        }

        // Rename current log file with a timestamp    
        std::filesystem::rename(logFilePath, GenerateLogFileName());

        // open original log file for logging
        logFile.open(logFilePath, std::ios::out | std::ios::app);
    }

    void
    WriteLogHeader()
    {
        if (!logFile.is_open())
        {
            return;
        }

        logFile << "================================================================\n";
        logFile << "          Debug Log Started at " << GetCurrentTimestamp() << "\n";
        logFile << "================================================================\n";

        logFile.flush();
    }

    void
    WriteLogFooter()
    {
        if (!logFile.is_open())
        {
            return;
        }

        logFile << "===============================================================\n";
        logFile << "           Debug Log ended at " << GetCurrentTimestamp() << "\n";
        logFile << "===============================================================\n";
        logFile.flush();
    }

    void
    EnsureLogDirectoryExists()
    {
        // Check if the directory exists, if not, create it
        if (!std::filesystem::exists(logDirectory))
        {
            std::filesystem::create_directories(logDirectory);
        }
    }

    std::string
    GetCurrentTimestamp()
    {
        auto now = std::chrono::system_clock::now();
        auto time_value = std::chrono::system_clock::to_time_t(now);

        struct tm timeStruct;
        localtime_s(&timeStruct, reinterpret_cast<const time_t*>(&time_value));

        char buffer[20];
        strftime(buffer, sizeof(buffer), "%Y-%m-%d %H:%M:%S", &timeStruct);
        return std::string(buffer);
    }

    std::string
    GenerateLogFileName()
    {
        auto now = std::chrono::system_clock::now();
        auto time_value = std::chrono::system_clock::to_time_t(now);

        struct tm timeStruct;
        localtime_s(&timeStruct, reinterpret_cast<const time_t*>(&time_value));

        char buffer[40];
        strftime(buffer, sizeof(buffer), "%Y-%m-%d_%H-%M-%S", &timeStruct);

        return logDirectory + "/" + currentLogFileName + "_" + std::string(buffer) + ".log";
    }

    std::string
    LevelToString(
        _In_ LogLevel level
        )
    {
        switch (level) {
            case LogLevel::VERBOSE:
                return "VERBOSE";

            case LogLevel::DEBUG:
                return "DEBUG";

            case LogLevel::INFO:
                return "INFO";

            case LogLevel::WARNING:
                return "WARNING";

            case LogLevel::ERR:
                return "ERROR";

            default:
                return "UNKNOWN";
        }
    }

    bool
    ReadRegistryValue(
        _In_ const std::string& subKey,
        _In_ const std::string& valueName,
        _Out_ DWORD& outValue
        )
    {
        HKEY hKey;
        DWORD bufferSize = sizeof(DWORD);
        DWORD value = 0;

        // Open registry key
        if (RegOpenKeyExA(HKEY_LOCAL_MACHINE, subKey.c_str(), 0, KEY_READ, &hKey) == ERROR_SUCCESS)
        {
            if (RegQueryValueExA(hKey, valueName.c_str(), NULL, NULL, reinterpret_cast<LPBYTE>(&value), &bufferSize) == ERROR_SUCCESS)
            {
                outValue = value;
                RegCloseKey(hKey);
                return true;
            }
            RegCloseKey(hKey);
        }
        return false; 
    }

    void
    ReadLogFileSizeFromRegistry()
    {
        const std::string registryPath = "SOFTWARE\\Microsoft\\Aimx\\Logging";
        // Value in MB
        DWORD logSizeMB = 0;

        if (ReadRegistryValue(registryPath, "LogFileSizeLimitMB", logSizeMB))
        {
            // Convert MB to bytes
            logFileSizeLimit = static_cast<size_t>(logSizeMB) * 1024 * 1024;
        }
    }

    void
    ReadLogLevelFromRegistry()
    {
        const std::string registryPath = "SOFTWARE\\Microsoft\\Aimx\\Logging";
        // Default to INFO
        DWORD logLevel = 0;

        if (ReadRegistryValue(registryPath, "LogLevel", logLevel))
        {
            if (logLevel <= static_cast<DWORD>(LogLevel::ERR))
            {
                currentLogLevel = static_cast<LogLevel>(logLevel);
            }
        }
    }

    // Helper function to convert wide strings when needed
    void FormatArg(std::ostringstream& oss, const std::wstring& arg) {
        oss << WideToUtf8(arg);
    }

    void FormatArg(std::ostringstream& oss, wchar_t arg) {
        std::wstring wstr(1, arg);
        oss << WideToUtf8(wstr);
    }

    // For non-wstring types, just stream directly
    template<typename T>
    void FormatArg(std::ostringstream& oss, const T& arg) {
        oss << arg;
    }

    // Process a pack of arguments recursively
    template<typename T, typename... Rest>
    void FormatArgs(std::ostringstream& oss, const T& arg, const Rest&... rest) {
        FormatArg(oss, arg);
        if constexpr (sizeof...(rest) > 0) {
            FormatArgs(oss, rest...);
        }
    }

    template<typename... Args>
    void
    Log(
        _In_ LogLevel level,
        _In_ Args... args
        );

    std::ofstream logFile;
    std::mutex logMutex;
    std::string logDirectory;
    std::string logFilePath;
    std::string currentLogFileName;

    // Default: 5MB
    size_t logFileSizeLimit = 5 * 1024 * 1024;

    // Default log level to INFO
    LogLevel currentLogLevel = LogLevel::INFO; 
};

// Template function definitions
template<typename... Args>
void
Debug::Log(
    _In_ LogLevel level,
    _In_ Args... args
    )
{
    if (level < currentLogLevel) return;

    std::lock_guard<std::mutex> lock(logMutex);

    // Ensure the file still exists
    if (!logFile.is_open()) {
        logFile.open(logFilePath, std::ios::out | std::ios::app);
    }

    // Rotate log file if it exceeds the limit
    if (std::filesystem::exists(logFilePath) && 
        std::filesystem::file_size(logFilePath) >= logFileSizeLimit) {
        RotateLogFile();
    }

    std::ostringstream formattedMessage;
    FormatArgs(formattedMessage, args...);

    logFile << "[" << GetCurrentTimestamp() << "] [" << LevelToString(level) << "] "
        << formattedMessage.str() << std::endl;

    logFile.flush();
}

template<typename... Args>
void
Debug::LogVerbose(
    _In_ Args... args
    )
{
    Log(LogLevel::VERBOSE, args...);
}

template<typename... Args>
void
Debug::LogDebug(
    _In_ Args... args
    )
{
    Log(LogLevel::DEBUG, args...);
}

template<typename... Args>
void
Debug::LogInfo(
    _In_ Args... args
    )
{
    Log(LogLevel::INFO, args...);
}

template<typename... Args>
void
Debug::LogWarning(
    _In_ Args... args
    )
{
    Log(LogLevel::WARNING, args...);
}

template<typename... Args>
void
Debug::LogError(
    _In_ Args... args
    )
{
    Log(LogLevel::ERR, args...);
}

// Macros for easy logging
#define LOGVERBOSE(...) Debug::GetInstance().LogVerbose(__VA_ARGS__)
#define LOGDEBUG(...) Debug::GetInstance().LogDebug(__VA_ARGS__)
#define LOGINFO(...) Debug::GetInstance().LogInfo(__VA_ARGS__)
#define LOGWARNING(...) Debug::GetInstance().LogWarning(__VA_ARGS__)
#define LOGERROR(...) Debug::GetInstance().LogError(__VA_ARGS__)
