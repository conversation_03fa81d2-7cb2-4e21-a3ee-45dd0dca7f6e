/*++

Copyright (c) Microsoft Corporation. All rights reserved.

Module Name:

    HelloMcpServer.h

Abstract:

    Hello in-process MCP server that provides a simple greeting tool.
    Demonstrates the JSON blob interface architecture while maintaining
    full MCP protocol compliance through direct API calls.

    This is a standalone library that can be linked into AIMXSrv to provide
    a simple sample MCP server via the in-process MCP server framework.

Author:

    <PERSON><PERSON><PERSON> (rizhang) 07/12/2025

--*/

#pragma once

#include "../aimxsrv/server/InProcessMcpServerBase.h"
#include <windows.h>

// Hello MCP Server
// Provides a simple greeting tool for demonstration purposes
class HelloMcpServer : public InProcessMcpServerBase
{
public:
    // Constructor
    HelloMcpServer();

    // Destructor
    virtual ~HelloMcpServer();

public:
    // Tool handler method - accepts and returns JSON blobs
    // Made public to allow access from stdio server executable

    // Simple hello greeting tool
    HRESULT HelloFromMcpTool(
        _In_ const nlohmann::json& parameters,
        _Out_ nlohmann::json& result
        );

protected:
    // Override initialization to register tools
    HRESULT OnInitialize() override;
};

// Factory for creating Hello MCP server instances
DECLARE_INPROCESS_MCP_SERVER(HelloMcpServer)

// Export function for AIMXSrv to register this server
extern "C" __declspec(dllexport) HRESULT RegisterHelloMcpServer();
extern "C" __declspec(dllexport) HRESULT UnregisterHelloMcpServer();
