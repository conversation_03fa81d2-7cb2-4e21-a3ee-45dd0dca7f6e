<#
.SYNOPSIS
    Active Directory Computer Management Tools for MCP Server
    
.DESCRIPTION
    This module registers MCP tools for Active Directory computer management operations.
    Each tool is a direct wrapper around the corresponding AD PowerShell cmdlet with
    exact parameter passthrough and no output formatting.

.AUTHOR
    Rupo Zhang (rizhang)
#>

# Import required modules
Import-Module ActiveDirectory -ErrorAction SilentlyContinue

function Register-ComputerManagementTools {
    [CmdletBinding()]
    param()

    # Get-ADComputer - Gets one or more Active Directory computers
    Register-McpTool -Name "Get-ADComputer" -Description "Gets one or more Active Directory computers. Supports filtering by various criteria and retrieving specific properties." -ScriptBlock {
        param($Arguments)
        
        $params = @{}
        if ($Arguments.Filter) { $params.Filter = $Arguments.Filter }
        if ($Arguments.Identity) { $params.Identity = $Arguments.Identity }
        if ($Arguments.LDAPFilter) { $params.LDAPFilter = $Arguments.LDAPFilter }
        if ($Arguments.AuthType) { $params.AuthType = $Arguments.AuthType }
        if ($Arguments.Credential) { $params.Credential = $Arguments.Credential }
        if ($Arguments.Properties) { $params.Properties = $Arguments.Properties }
        if ($Arguments.ResultPageSize) { $params.ResultPageSize = $Arguments.ResultPageSize }
        if ($Arguments.ResultSetSize) { $params.ResultSetSize = $Arguments.ResultSetSize }
        if ($Arguments.SearchBase) { $params.SearchBase = $Arguments.SearchBase }
        if ($Arguments.SearchScope) { $params.SearchScope = $Arguments.SearchScope }
        if ($Arguments.Server) { $params.Server = $Arguments.Server }
        if ($Arguments.Partition) { $params.Partition = $Arguments.Partition }
        
        Get-ADComputer @params
    } -InputSchema @{
        type = "object"
        properties = @{
            Filter = @{ type = "string"; description = "PowerShell Expression Language filter string" }
            Identity = @{ type = "string"; description = "Computer identity (DN, GUID, SID, or SAM account name)" }
            LDAPFilter = @{ type = "string"; description = "LDAP query string for filtering" }
            AuthType = @{ type = "string"; enum = @("Negotiate", "Basic"); description = "Authentication method" }
            Credential = @{ type = "string"; description = "User credentials for authentication" }
            Properties = @{ type = "array"; items = @{ type = "string" }; description = "Additional properties to retrieve" }
            ResultPageSize = @{ type = "integer"; description = "Number of objects per page" }
            ResultSetSize = @{ type = "integer"; description = "Maximum number of objects to return" }
            SearchBase = @{ type = "string"; description = "Active Directory path to search under" }
            SearchScope = @{ type = "string"; enum = @("Base", "OneLevel", "Subtree"); description = "Scope of the search" }
            Server = @{ type = "string"; description = "Domain controller to connect to" }
            Partition = @{ type = "string"; description = "Distinguished name of AD partition" }
        }
    }

    # New-ADComputer - Creates a new Active Directory computer
    Register-McpTool -Name "New-ADComputer" -Description "Creates a new Active Directory computer account with specified properties and attributes." -ScriptBlock {
        param($Arguments)
        
        $params = @{}
        if ($Arguments.Name) { $params.Name = $Arguments.Name }
        if ($Arguments.SamAccountName) { $params.SamAccountName = $Arguments.SamAccountName }
        if ($Arguments.DisplayName) { $params.DisplayName = $Arguments.DisplayName }
        if ($Arguments.Description) { $params.Description = $Arguments.Description }
        if ($Arguments.Path) { $params.Path = $Arguments.Path }
        if ($Arguments.Enabled) { $params.Enabled = $Arguments.Enabled }
        if ($Arguments.Location) { $params.Location = $Arguments.Location }
        if ($Arguments.ManagedBy) { $params.ManagedBy = $Arguments.ManagedBy }
        if ($Arguments.OperatingSystem) { $params.OperatingSystem = $Arguments.OperatingSystem }
        if ($Arguments.OperatingSystemVersion) { $params.OperatingSystemVersion = $Arguments.OperatingSystemVersion }
        if ($Arguments.ServicePrincipalNames) { $params.ServicePrincipalNames = $Arguments.ServicePrincipalNames }
        if ($Arguments.TrustedForDelegation) { $params.TrustedForDelegation = $Arguments.TrustedForDelegation }
        if ($Arguments.AccountPassword) { $params.AccountPassword = $Arguments.AccountPassword }
        if ($Arguments.OtherAttributes) { $params.OtherAttributes = $Arguments.OtherAttributes }
        if ($Arguments.Server) { $params.Server = $Arguments.Server }
        if ($Arguments.AuthType) { $params.AuthType = $Arguments.AuthType }
        if ($Arguments.Credential) { $params.Credential = $Arguments.Credential }
        if ($Arguments.PassThru) { $params.PassThru = $Arguments.PassThru }
        
        New-ADComputer @params
    } -InputSchema @{
        type = "object"
        properties = @{
            Name = @{ type = "string"; description = "Name of the computer account (required)" }
            SamAccountName = @{ type = "string"; description = "SAM account name for the computer" }
            DisplayName = @{ type = "string"; description = "Display name for the computer" }
            Description = @{ type = "string"; description = "Description of the computer account" }
            Path = @{ type = "string"; description = "Distinguished name of the container for the computer" }
            Enabled = @{ type = "boolean"; description = "Whether the account is enabled" }
            Location = @{ type = "string"; description = "Physical location of the computer" }
            ManagedBy = @{ type = "string"; description = "Distinguished name of the computer manager" }
            OperatingSystem = @{ type = "string"; description = "Operating system name" }
            OperatingSystemVersion = @{ type = "string"; description = "Operating system version" }
            ServicePrincipalNames = @{ type = "array"; items = @{ type = "string" }; description = "Service principal names" }
            TrustedForDelegation = @{ type = "boolean"; description = "Whether the computer is trusted for delegation" }
            AccountPassword = @{ type = "string"; description = "Password for the computer account" }
            OtherAttributes = @{ type = "object"; description = "Additional attributes as hashtable" }
            Server = @{ type = "string"; description = "Domain controller to connect to" }
            AuthType = @{ type = "string"; enum = @("Negotiate", "Basic"); description = "Authentication method" }
            Credential = @{ type = "string"; description = "User credentials for authentication" }
            PassThru = @{ type = "boolean"; description = "Return the created computer object" }
        }
        required = @("Name")
    }

    # Set-ADComputer - Modifies an Active Directory computer
    Register-McpTool -Name "Set-ADComputer" -Description "Modifies properties of an existing Active Directory computer account." -ScriptBlock {
        param($Arguments)
        
        $params = @{}
        if ($Arguments.Identity) { $params.Identity = $Arguments.Identity }
        if ($Arguments.Add) { $params.Add = $Arguments.Add }
        if ($Arguments.Clear) { $params.Clear = $Arguments.Clear }
        if ($Arguments.Remove) { $params.Remove = $Arguments.Remove }
        if ($Arguments.Replace) { $params.Replace = $Arguments.Replace }
        if ($Arguments.DisplayName) { $params.DisplayName = $Arguments.DisplayName }
        if ($Arguments.Description) { $params.Description = $Arguments.Description }
        if ($Arguments.Location) { $params.Location = $Arguments.Location }
        if ($Arguments.ManagedBy) { $params.ManagedBy = $Arguments.ManagedBy }
        if ($Arguments.OperatingSystem) { $params.OperatingSystem = $Arguments.OperatingSystem }
        if ($Arguments.OperatingSystemVersion) { $params.OperatingSystemVersion = $Arguments.OperatingSystemVersion }
        if ($Arguments.ServicePrincipalNames) { $params.ServicePrincipalNames = $Arguments.ServicePrincipalNames }
        if ($Arguments.TrustedForDelegation) { $params.TrustedForDelegation = $Arguments.TrustedForDelegation }
        if ($Arguments.Server) { $params.Server = $Arguments.Server }
        if ($Arguments.AuthType) { $params.AuthType = $Arguments.AuthType }
        if ($Arguments.Credential) { $params.Credential = $Arguments.Credential }
        if ($Arguments.PassThru) { $params.PassThru = $Arguments.PassThru }
        if ($Arguments.Partition) { $params.Partition = $Arguments.Partition }
        
        Set-ADComputer @params
    } -InputSchema @{
        type = "object"
        properties = @{
            Identity = @{ type = "string"; description = "Computer identity (DN, GUID, SID, or SAM account name)" }
            Add = @{ type = "object"; description = "Attributes to add as hashtable" }
            Clear = @{ type = "array"; items = @{ type = "string" }; description = "Attributes to clear" }
            Remove = @{ type = "object"; description = "Attributes to remove as hashtable" }
            Replace = @{ type = "object"; description = "Attributes to replace as hashtable" }
            DisplayName = @{ type = "string"; description = "Display name for the computer" }
            Description = @{ type = "string"; description = "Description of the computer account" }
            Location = @{ type = "string"; description = "Physical location of the computer" }
            ManagedBy = @{ type = "string"; description = "Distinguished name of the computer manager" }
            OperatingSystem = @{ type = "string"; description = "Operating system name" }
            OperatingSystemVersion = @{ type = "string"; description = "Operating system version" }
            ServicePrincipalNames = @{ type = "array"; items = @{ type = "string" }; description = "Service principal names" }
            TrustedForDelegation = @{ type = "boolean"; description = "Whether the computer is trusted for delegation" }
            Server = @{ type = "string"; description = "Domain controller to connect to" }
            AuthType = @{ type = "string"; enum = @("Negotiate", "Basic"); description = "Authentication method" }
            Credential = @{ type = "string"; description = "User credentials for authentication" }
            PassThru = @{ type = "boolean"; description = "Return the modified computer object" }
            Partition = @{ type = "string"; description = "Distinguished name of AD partition" }
        }
        required = @("Identity")
    }

    # Remove-ADComputer - Removes an Active Directory computer
    Register-McpTool -Name "Remove-ADComputer" -Description "Removes an Active Directory computer account from the directory." -ScriptBlock {
        param($Arguments)
        
        $params = @{}
        if ($Arguments.Identity) { $params.Identity = $Arguments.Identity }
        if ($Arguments.Server) { $params.Server = $Arguments.Server }
        if ($Arguments.AuthType) { $params.AuthType = $Arguments.AuthType }
        if ($Arguments.Credential) { $params.Credential = $Arguments.Credential }
        if ($Arguments.Partition) { $params.Partition = $Arguments.Partition }
        if ($Arguments.Confirm) { $params.Confirm = $Arguments.Confirm }
        if ($Arguments.WhatIf) { $params.WhatIf = $Arguments.WhatIf }
        
        Remove-ADComputer @params
    } -InputSchema @{
        type = "object"
        properties = @{
            Identity = @{ type = "string"; description = "Computer identity (DN, GUID, SID, or SAM account name)" }
            Server = @{ type = "string"; description = "Domain controller to connect to" }
            AuthType = @{ type = "string"; enum = @("Negotiate", "Basic"); description = "Authentication method" }
            Credential = @{ type = "string"; description = "User credentials for authentication" }
            Partition = @{ type = "string"; description = "Distinguished name of AD partition" }
            Confirm = @{ type = "boolean"; description = "Prompt for confirmation before removing" }
            WhatIf = @{ type = "boolean"; description = "Show what would happen without executing" }
        }
        required = @("Identity")
    }

    # Add-ADComputerServiceAccount - Adds one or more service accounts to an Active Directory computer
    Register-McpTool -Name "Add-ADComputerServiceAccount" -Description "Adds one or more service accounts to an Active Directory computer." -ScriptBlock {
        param($Arguments)
        
        $params = @{}
        if ($Arguments.Identity) { $params.Identity = $Arguments.Identity }
        if ($Arguments.ServiceAccount) { $params.ServiceAccount = $Arguments.ServiceAccount }
        if ($Arguments.Server) { $params.Server = $Arguments.Server }
        if ($Arguments.AuthType) { $params.AuthType = $Arguments.AuthType }
        if ($Arguments.Credential) { $params.Credential = $Arguments.Credential }
        if ($Arguments.Partition) { $params.Partition = $Arguments.Partition }
        if ($Arguments.PassThru) { $params.PassThru = $Arguments.PassThru }
        
        Add-ADComputerServiceAccount @params
    } -InputSchema @{
        type = "object"
        properties = @{
            Identity = @{ type = "string"; description = "Computer identity (DN, GUID, SID, or SAM account name)" }
            ServiceAccount = @{ type = "array"; items = @{ type = "string" }; description = "Service account identities to add" }
            Server = @{ type = "string"; description = "Domain controller to connect to" }
            AuthType = @{ type = "string"; enum = @("Negotiate", "Basic"); description = "Authentication method" }
            Credential = @{ type = "string"; description = "User credentials for authentication" }
            Partition = @{ type = "string"; description = "Distinguished name of AD partition" }
            PassThru = @{ type = "boolean"; description = "Return the computer object" }
        }
        required = @("Identity", "ServiceAccount")
    }

    # Remove-ADComputerServiceAccount - Removes one or more service accounts from a computer
    Register-McpTool -Name "Remove-ADComputerServiceAccount" -Description "Removes one or more service accounts from an Active Directory computer." -ScriptBlock {
        param($Arguments)
        
        $params = @{}
        if ($Arguments.Identity) { $params.Identity = $Arguments.Identity }
        if ($Arguments.ServiceAccount) { $params.ServiceAccount = $Arguments.ServiceAccount }
        if ($Arguments.Server) { $params.Server = $Arguments.Server }
        if ($Arguments.AuthType) { $params.AuthType = $Arguments.AuthType }
        if ($Arguments.Credential) { $params.Credential = $Arguments.Credential }
        if ($Arguments.Partition) { $params.Partition = $Arguments.Partition }
        if ($Arguments.PassThru) { $params.PassThru = $Arguments.PassThru }
        
        Remove-ADComputerServiceAccount @params
    } -InputSchema @{
        type = "object"
        properties = @{
            Identity = @{ type = "string"; description = "Computer identity (DN, GUID, SID, or SAM account name)" }
            ServiceAccount = @{ type = "array"; items = @{ type = "string" }; description = "Service account identities to remove" }
            Server = @{ type = "string"; description = "Domain controller to connect to" }
            AuthType = @{ type = "string"; enum = @("Negotiate", "Basic"); description = "Authentication method" }
            Credential = @{ type = "string"; description = "User credentials for authentication" }
            Partition = @{ type = "string"; description = "Distinguished name of AD partition" }
            PassThru = @{ type = "boolean"; description = "Return the computer object" }
        }
        required = @("Identity", "ServiceAccount")
    }

    # Get-ADComputerServiceAccount - Gets the service accounts hosted by a computer
    Register-McpTool -Name "Get-ADComputerServiceAccount" -Description "Gets the service accounts that are hosted by an Active Directory computer." -ScriptBlock {
        param($Arguments)
        
        $params = @{}
        if ($Arguments.Identity) { $params.Identity = $Arguments.Identity }
        if ($Arguments.Server) { $params.Server = $Arguments.Server }
        if ($Arguments.AuthType) { $params.AuthType = $Arguments.AuthType }
        if ($Arguments.Credential) { $params.Credential = $Arguments.Credential }
        if ($Arguments.Partition) { $params.Partition = $Arguments.Partition }
        
        Get-ADComputerServiceAccount @params
    } -InputSchema @{
        type = "object"
        properties = @{
            Identity = @{ type = "string"; description = "Computer identity (DN, GUID, SID, or SAM account name)" }
            Server = @{ type = "string"; description = "Domain controller to connect to" }
            AuthType = @{ type = "string"; enum = @("Negotiate", "Basic"); description = "Authentication method" }
            Credential = @{ type = "string"; description = "User credentials for authentication" }
            Partition = @{ type = "string"; description = "Distinguished name of AD partition" }
        }
        required = @("Identity")
    }

    # Get-ADComputerServiceAccount - Gets the service accounts hosted by a computer
    Register-McpTool -Name "Get-ADComputerServiceAccount" -Description "Gets the service accounts hosted by a computer." -ScriptBlock {
        param($Arguments)

        $params = @{}
        if ($Arguments.Identity) { $params.Identity = $Arguments.Identity }
        if ($Arguments.Server) { $params.Server = $Arguments.Server }
        if ($Arguments.AuthType) { $params.AuthType = $Arguments.AuthType }
        if ($Arguments.Credential) { $params.Credential = $Arguments.Credential }

        Get-ADComputerServiceAccount @params
    } -InputSchema @{
        type = "object"
        properties = @{
            Identity = @{ type = "string"; description = "Computer identity (DN, GUID, SID, or SAM account name)" }
            Server = @{ type = "string"; description = "Domain controller to connect to" }
            AuthType = @{ type = "string"; enum = @("Negotiate", "Basic"); description = "Authentication method" }
            Credential = @{ type = "string"; description = "User credentials for authentication" }
        }
        required = @("Identity")
    }

    # Add-ADComputerServiceAccount - Adds one or more service accounts to an Active Directory computer
    Register-McpTool -Name "Add-ADComputerServiceAccount" -Description "Adds one or more service accounts to an Active Directory computer." -ScriptBlock {
        param($Arguments)

        $params = @{}
        if ($Arguments.Identity) { $params.Identity = $Arguments.Identity }
        if ($Arguments.ServiceAccount) { $params.ServiceAccount = $Arguments.ServiceAccount }
        if ($Arguments.Server) { $params.Server = $Arguments.Server }
        if ($Arguments.AuthType) { $params.AuthType = $Arguments.AuthType }
        if ($Arguments.Credential) { $params.Credential = $Arguments.Credential }
        if ($Arguments.PassThru) { $params.PassThru = $Arguments.PassThru }

        Add-ADComputerServiceAccount @params
    } -InputSchema @{
        type = "object"
        properties = @{
            Identity = @{ type = "string"; description = "Computer identity (DN, GUID, SID, or SAM account name)" }
            ServiceAccount = @{ type = "array"; items = @{ type = "string" }; description = "Service accounts to add" }
            Server = @{ type = "string"; description = "Domain controller to connect to" }
            AuthType = @{ type = "string"; enum = @("Negotiate", "Basic"); description = "Authentication method" }
            Credential = @{ type = "string"; description = "User credentials for authentication" }
            PassThru = @{ type = "boolean"; description = "Return the modified computer object" }
        }
        required = @("Identity", "ServiceAccount")
    }

    # Remove-ADComputerServiceAccount - Removes one or more service accounts from a computer
    Register-McpTool -Name "Remove-ADComputerServiceAccount" -Description "Removes one or more service accounts from a computer." -ScriptBlock {
        param($Arguments)

        $params = @{}
        if ($Arguments.Identity) { $params.Identity = $Arguments.Identity }
        if ($Arguments.ServiceAccount) { $params.ServiceAccount = $Arguments.ServiceAccount }
        if ($Arguments.Server) { $params.Server = $Arguments.Server }
        if ($Arguments.AuthType) { $params.AuthType = $Arguments.AuthType }
        if ($Arguments.Credential) { $params.Credential = $Arguments.Credential }
        if ($Arguments.PassThru) { $params.PassThru = $Arguments.PassThru }

        Remove-ADComputerServiceAccount @params
    } -InputSchema @{
        type = "object"
        properties = @{
            Identity = @{ type = "string"; description = "Computer identity (DN, GUID, SID, or SAM account name)" }
            ServiceAccount = @{ type = "array"; items = @{ type = "string" }; description = "Service accounts to remove" }
            Server = @{ type = "string"; description = "Domain controller to connect to" }
            AuthType = @{ type = "string"; enum = @("Negotiate", "Basic"); description = "Authentication method" }
            Credential = @{ type = "string"; description = "User credentials for authentication" }
            PassThru = @{ type = "boolean"; description = "Return the modified computer object" }
        }
        required = @("Identity", "ServiceAccount")
    }
}

# Function is available after dot-sourcing
