{"mcpServers": {"ADPSMcpServer": {"enabled": true, "type": "stdio", "version": "1.0.0", "description": "Active Directory and PowerShell MCP Server providing system administration and directory services tools", "command": "powershell.exe", "args": ["-ExecutionPolicy", "Bypass", "-NoProfile", "-File", "C:\\ProgramData\\Microsoft\\AIMX\\MCPServers\\ADPsMCPSvr\\Start-ADPsMCPSvr.ps1"], "workingDirectory": "C:\\ProgramData\\Microsoft\\AIMX\\MCPServers\\ADPsMCPSvr", "env": {"MCP_SERVER_MODE": "stdio", "LOG_LEVEL": "Info"}}}}