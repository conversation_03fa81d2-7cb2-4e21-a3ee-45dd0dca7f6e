{"building": [{"path": "onecore\\ds\\ds\\src\\aimx\\prod\\admcpsvr", "pass": "PASS0", "cmd": "e:\\os\\tools\\NMakeRust\\x64\\bin\\nmake.exe /nologo BUILDMSG=Stop. BUILD_PASS=PASS0 /nologo /f e:\\os\\src\\tools\\makefile.def NOLINK=1 PASS0ONLY=1 MAKEDIR_RELATIVE_TO_BASEDIR=onecore\\ds\\ds\\src\\aimx\\prod\\admcpsvr TARGET_PLATFORM=windows CODE_SETS_BUILDING=cs_windows,cs_xbox,cs_phone CODE_SETS_TAGGED=cs_windows,cs_xbox", "inputs": ["e:\\os\\src\\onecore\\ds\\ds\\src\\aimx\\prod\\admcpsvr\\sources", "e:\\os\\src\\onecore\\ds\\project.mk", "e:\\os\\src\\tools\\makefile.def", "e:\\os\\src\\tools\\projects.inc"]}, {"path": "onecore\\ds\\ds\\src\\aimx\\prod\\admcpsvr", "pass": "PASS1", "cmd": "e:\\os\\tools\\NMakeRust\\x64\\bin\\nmake.exe /nologo BUILDMSG=Stop. /nologo /f e:\\os\\src\\tools\\makefile.def BUILD_PASS=PASS1 NOLINK=1 MAKEDIR_RELATIVE_TO_BASEDIR=onecore\\ds\\ds\\src\\aimx\\prod\\admcpsvr TARGET_PLATFORM=windows CODE_SETS_BUILDING=cs_windows,cs_xbox,cs_phone CODE_SETS_TAGGED=cs_windows,cs_xbox", "inputs": ["e:\\os\\src\\onecore\\ds\\ds\\src\\aimx\\prod\\admcpsvr\\admcpsvr.cpp", "e:\\os\\src\\onecore\\ds\\ds\\src\\aimx\\prod\\admcpsvr\\getadcomputertool.cpp", "e:\\os\\src\\onecore\\ds\\ds\\src\\aimx\\prod\\admcpsvr\\getaddefaultdomainpasswordpolicytool.cpp", "e:\\os\\src\\onecore\\ds\\ds\\src\\aimx\\prod\\admcpsvr\\getaddomaintool.cpp", "e:\\os\\src\\onecore\\ds\\ds\\src\\aimx\\prod\\admcpsvr\\getadforesttool.cpp", "e:\\os\\src\\onecore\\ds\\ds\\src\\aimx\\prod\\admcpsvr\\getadgrouptool.cpp", "e:\\os\\src\\onecore\\ds\\ds\\src\\aimx\\prod\\admcpsvr\\getadusertool.cpp", "e:\\os\\src\\onecore\\ds\\ds\\src\\aimx\\prod\\admcpsvr\\powershellfilterparser.cpp", "e:\\os\\src\\onecore\\ds\\ds\\src\\aimx\\prod\\admcpsvr\\sources", "e:\\os\\src\\onecore\\ds\\project.mk", "e:\\os\\src\\tools\\makefile.def", "e:\\os\\src\\tools\\projects.inc"]}]}