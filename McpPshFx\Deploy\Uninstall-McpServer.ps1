<#
.SYNOPSIS
    Uninstall script for PowerShell MCP Server (AdPsMcpSvr)
    
.DESCRIPTION
    This script removes the PowerShell MCP Server configuration from the Aimx
    MCP server configuration file. It handles backing up the configuration
    before removal and validates the changes.

.AUTHOR
    <PERSON><PERSON><PERSON> (rizhang) 07-21-2025
    
.PARAMETER ServerName
    Name of the MCP server entry to remove (default: PSMcpServer)
    
.PARAMETER BackupConfig
    Create a backup of the existing configuration file (default: true)
    
.PARAMETER Force
    Remove the server without confirmation prompt
    
.EXAMPLE
    .\Uninstall-McpServer.ps1
    
.EXAMPLE
    .\Uninstall-McpServer.ps1 -ServerName "MyPSServer" -Force
#>

[CmdletBinding()]
param(
    [string]$ServerName = "PSMcpServer",
    
    [bool]$BackupConfig = $true,
    
    [switch]$Force
)

# Configuration paths
$ConfigPath = "C:\ProgramData\Microsoft\AIMX\aimx_mcpsvr.json"

Write-Host "=== PowerShell MCP Server Uninstall ===" -ForegroundColor Red
Write-Host ""

# Check if configuration file exists
if (-not (Test-Path $ConfigPath)) {
    Write-Host "Configuration file not found: $ConfigPath" -ForegroundColor Yellow
    Write-Host "The server may not be installed or the configuration file has been moved." -ForegroundColor Gray
    exit 0
}

# Load existing configuration
Write-Host "Loading configuration: $ConfigPath" -ForegroundColor Yellow
try {
    $configContent = Get-Content $ConfigPath -Raw -Encoding UTF8
    $config = $configContent | ConvertFrom-Json -AsHashtable
    Write-Host "Configuration loaded" -ForegroundColor Green
}
catch {
    Write-Host "ERROR: Failed to parse configuration: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}

# Check if server exists
if (-not $config.mcpServers -or -not $config.mcpServers.ContainsKey($ServerName)) {
    Write-Host "Server '$ServerName' not found in configuration." -ForegroundColor Yellow
    Write-Host "Available servers:" -ForegroundColor Gray
    if ($config.mcpServers) {
        foreach ($server in $config.mcpServers.Keys) {
            Write-Host "  - $server" -ForegroundColor Gray
        }
    }
    else {
        Write-Host "  (none)" -ForegroundColor Gray
    }
    exit 0
}

# Show server details
$serverConfig = $config.mcpServers[$ServerName]
Write-Host "Found server configuration:" -ForegroundColor Yellow
Write-Host "  Name: $ServerName" -ForegroundColor Cyan
Write-Host "  Description: $($serverConfig.description)" -ForegroundColor Cyan
Write-Host "  Enabled: $($serverConfig.enabled)" -ForegroundColor Cyan
Write-Host "  Command: $($serverConfig.command)" -ForegroundColor Cyan

# Confirmation prompt
if (-not $Force) {
    Write-Host ""
    $confirmation = Read-Host "Are you sure you want to remove this server? (y/N)"
    if ($confirmation -notmatch '^[Yy]') {
        Write-Host "Uninstall cancelled." -ForegroundColor Yellow
        exit 0
    }
}

# Create backup if requested
if ($BackupConfig) {
    $backupPath = $ConfigPath + ".backup." + (Get-Date -Format "yyyyMMdd-HHmmss")
    try {
        Copy-Item $ConfigPath $backupPath
        Write-Host "Configuration backed up to: $backupPath" -ForegroundColor Green
    }
    catch {
        Write-Host "WARNING: Failed to create backup: $($_.Exception.Message)" -ForegroundColor Yellow
    }
}

# Remove server from configuration
Write-Host "Removing server '$ServerName' from configuration..." -ForegroundColor Yellow
$config.mcpServers.Remove($ServerName)

# Save updated configuration
try {
    $configJson = $config | ConvertTo-Json -Depth 10
    $configJson | Set-Content $ConfigPath -Encoding UTF8
    Write-Host "Configuration updated" -ForegroundColor Green
}
catch {
    Write-Host "ERROR: Failed to save configuration: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}

# Validate the configuration
Write-Host "Validating updated configuration..." -ForegroundColor Yellow
try {
    $testConfig = Get-Content $ConfigPath -Raw | ConvertFrom-Json
    if (-not $testConfig.mcpServers.$ServerName) {
        Write-Host "Server successfully removed from configuration" -ForegroundColor Green
    }
    else {
        Write-Host "ERROR: Server still exists in configuration" -ForegroundColor Red
        exit 1
    }
}
catch {
    Write-Host "ERROR: Configuration validation failed: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}

Write-Host ""
Write-Host "=== Uninstall Complete ===" -ForegroundColor Green
Write-Host ""
Write-Host "PowerShell MCP Server '$ServerName' has been removed successfully!" -ForegroundColor White
Write-Host ""
Write-Host "Next steps:" -ForegroundColor Yellow
Write-Host "1. Restart the Aimx service to apply the configuration changes" -ForegroundColor Cyan
Write-Host "2. The server will no longer be available in Aimx" -ForegroundColor Cyan
Write-Host ""
Write-Host "Note: The server files have not been deleted." -ForegroundColor Gray
Write-Host "You can manually delete the AdPsMcpSvr folder if no longer needed." -ForegroundColor Gray
