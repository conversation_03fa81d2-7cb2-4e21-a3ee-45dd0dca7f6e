/*++

Copyright (c) Microsoft Corporation. All rights reserved.

Module Name:

    AimxCommon.h

Abstract:

    Common definitions, structures, and enums shared across AIMX orchestrator/planner and request handler.

--*/

#pragma once
#include <nlohmann/json.hpp>
#include <mutex>
#include <shared_mutex>
#include <vector>
#include <map>
#include <string>
#include "aimxrpc.h"
//
// GuidHash GuidEqual functions for unordered_map comparison
// borrowed from
//  %SDXROOT%\onecoreuap\shell\auth\credprov2fahelper\dll\CredProv2faHelper.h
//
struct GuidHash
{
    __forceinline
        size_t operator()(const GUID &guid) const
    {
        const ULONGLONG* p = reinterpret_cast<const ULONGLONG*>(&guid);
        std::hash<ULONGLONG> hash;

        return hash(p[0]) ^ hash(p[1]);
    }
};

// specify an equal function for unordered_map
struct GuidEqual
{
    __forceinline
        bool operator()(REFGUID refGuidLeft, REFGUID refGuidRight) const
    {
        return (0 < IsEqualGUID(refGuidLeft, refGuidRight));
    }
};


// Protocol definitions for AIMX request/response
enum AIMX_REQUEST_TYPE
{
    AIMX_CHATBOT_QUERY = 1,
    AIMX_DIRECT_QUERY = 2,
    AIMX_PLAN_STATUS = 3,
    AIMX_EXECUTE_PLAN = 4,
    AIMX_CANCEL_OPERATION = 5
};

enum AIMX_EXECUTION_MODE
{
    AIMX_MODE_AUTOMATED = 1,
    AIMX_MODE_INTERACTIVE = 2
};

enum AIMX_OPERATION_STATUS
{
    AIMX_STATUS_PLANNING = 1,
    AIMX_STATUS_PLAN_READY = 2,
    AIMX_STATUS_EXECUTING = 3,
    AIMX_STATUS_COMPLETED = 4,
    AIMX_STATUS_FAILED = 5,
    AIMX_STATUS_CANCELLED = 6
};

// Structure for operation tracking
struct AIMX_OPERATION
{
    GUID OperationId;
    AIMX_REQUEST_TYPE RequestType;
    AIMX_EXECUTION_MODE ExecutionMode;
    AIMX_OPERATION_STATUS Status;
    std::wstring OriginalQuery;
    nlohmann::json ExecutionPlan;
    std::wstring Result;
    FILETIME CreationTime;
    FILETIME CompletionTime;
};



// AIMX Action Types for planning and Orchestration
enum class AimxAction
{
    ExecuteTool,
    ProcessNaturalLanguage,
    GenerateResponse
    // Add more here
};

inline const char* ToString(AimxAction action)
{
    switch (action)
    {
        case AimxAction::ExecuteTool: return "execute_tool";
        case AimxAction::ProcessNaturalLanguage: return "process_natural_language";
        case AimxAction::GenerateResponse: return "generate_response";
        default: return "";
    }
}

// AIMX Target Types
enum class AimxTarget
{
    AdToolsAgent,
    AiLlmAgent,
    // Add more here
};

inline const char* ToString(AimxTarget target)
{
    switch (target)
    {
        case AimxTarget::AdToolsAgent: return "ad_tools_agent";
        case AimxTarget::AiLlmAgent: return "ai_llm_agent";
        default: return "";
    }
}

// Planning context structure for worker threads
struct PlanningContext
{
    GUID OperationId;
    std::wstring Query;
    AIMX_EXECUTION_MODE ExecutionMode;
    std::atomic<bool> cancelRequested{false};
};

// Execution context structure for worker threads
struct ExecutionContext
{
    GUID OperationId;
    nlohmann::json ExecutionPlan;
    std::atomic<bool> cancelRequested{false};
};

// Global operation tracking declarations

_Guarded_by_(g_OperationMapMutex)
extern std::unordered_map<GUID, std::shared_ptr<AIMX_OPERATION>, GuidHash, GuidEqual> g_OperationMap;
extern std::mutex g_OperationMapMutex;

// Global map to track active planning operations (declared but defined in Planner.cpp)
_Guarded_by_(g_PlanningThreadMapMutex)
extern std::unordered_map<GUID, HANDLE, GuidHash, GuidEqual> g_PlanningThreadMap;
extern std::mutex g_PlanningThreadMapMutex;

// Global map to track active execution operations (declared but defined in Orchestrator.cpp)
_Guarded_by_(g_ExecutionThreadMapMutex)
extern std::unordered_map<GUID, HANDLE, GuidHash, GuidEqual> g_ExecutionThreadMap;
extern std::mutex g_ExecutionThreadMapMutex;


//
// AIMX client info structure
// Stored in a global map to track AIMX server connections.
// Returns a string ID to abstract the RPC handle, enabling remoting and other ops securely.
//
struct AIMX_CLIENT_INFO
{
    GUID ConnectionId;
    std::wstring ConnectionIdStr;
    AIMXR_HANDLE RpcHandle;
};

//
// MCP Configuration Structures for JSON-based server management
//

// MCP Server type enumeration
enum class MCP_SERVER_TYPE
{
    OUT_OF_PROCESS = 0,  // Traditional stdio/pipe servers
    IN_PROCESS = 1       // Built-in API servers
};



// Complete MCP server configuration
struct MCP_SERVER_CONFIG
{
    // Basic server information
    std::wstring serverName;
    std::wstring description;
    std::wstring version;
    MCP_SERVER_TYPE serverType = MCP_SERVER_TYPE::OUT_OF_PROCESS;
    bool enabled = true;
    bool autoStart = true;
    bool restartOnFailure = true;
    int priority = 100;

    // STDIO server configuration
    std::wstring command;
    std::vector<std::wstring> arguments;
    std::wstring workingDirectory;
    std::map<std::string, std::string> environment;

    // SSE server configuration (for future use)
    std::wstring url;
    std::map<std::string, std::string> headers;

    // In-process server configuration (for future use)
    std::wstring assembly;
    std::wstring className;
    nlohmann::json initParams;

    MCP_SERVER_CONFIG() = default;
};