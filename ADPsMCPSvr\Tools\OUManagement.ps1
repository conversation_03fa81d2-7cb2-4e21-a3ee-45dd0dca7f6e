<#
.SYNOPSIS
    Active Directory Organizational Unit Management Tools for MCP Server
    
.DESCRIPTION
    This module registers MCP tools for Active Directory organizational unit management operations.
    Each tool is a direct wrapper around the corresponding AD PowerShell cmdlet with
    exact parameter passthrough and no output formatting.

.AUTHOR
    Rupo Zhang (rizhang)
#>

# Import required modules
Import-Module ActiveDirectory -ErrorAction SilentlyContinue

function Register-OUManagementTools {
    [CmdletBinding()]
    param()

    # Get-ADOrganizationalUnit - Gets one or more Active Directory organizational units
    Register-McpTool -Name "Get-ADOrganizationalUnit" -Description "Gets one or more Active Directory organizational units. Supports filtering by various criteria and retrieving specific properties." -ScriptBlock {
        param($Arguments)
        
        $params = @{}
        if ($Arguments.Filter) { $params.Filter = $Arguments.Filter }
        if ($Arguments.Identity) { $params.Identity = $Arguments.Identity }
        if ($Arguments.LDAPFilter) { $params.LDAPFilter = $Arguments.LDAPFilter }
        if ($Arguments.AuthType) { $params.AuthType = $Arguments.AuthType }
        if ($Arguments.Credential) { $params.Credential = $Arguments.Credential }
        if ($Arguments.Properties) { $params.Properties = $Arguments.Properties }
        if ($Arguments.ResultPageSize) { $params.ResultPageSize = $Arguments.ResultPageSize }
        if ($Arguments.ResultSetSize) { $params.ResultSetSize = $Arguments.ResultSetSize }
        if ($Arguments.SearchBase) { $params.SearchBase = $Arguments.SearchBase }
        if ($Arguments.SearchScope) { $params.SearchScope = $Arguments.SearchScope }
        if ($Arguments.Server) { $params.Server = $Arguments.Server }
        if ($Arguments.Partition) { $params.Partition = $Arguments.Partition }
        
        Get-ADOrganizationalUnit @params
    } -InputSchema @{
        type = "object"
        properties = @{
            Filter = @{ type = "string"; description = "PowerShell Expression Language filter string" }
            Identity = @{ type = "string"; description = "OU identity (DN, GUID, or canonical name)" }
            LDAPFilter = @{ type = "string"; description = "LDAP query string for filtering" }
            AuthType = @{ type = "string"; enum = @("Negotiate", "Basic"); description = "Authentication method" }
            Credential = @{ type = "string"; description = "User credentials for authentication" }
            Properties = @{ type = "array"; items = @{ type = "string" }; description = "Additional properties to retrieve" }
            ResultPageSize = @{ type = "integer"; description = "Number of objects per page" }
            ResultSetSize = @{ type = "integer"; description = "Maximum number of objects to return" }
            SearchBase = @{ type = "string"; description = "Active Directory path to search under" }
            SearchScope = @{ type = "string"; enum = @("Base", "OneLevel", "Subtree"); description = "Scope of the search" }
            Server = @{ type = "string"; description = "Domain controller to connect to" }
            Partition = @{ type = "string"; description = "Distinguished name of AD partition" }
        }
    }

    # New-ADOrganizationalUnit - Creates an Active Directory organizational unit
    Register-McpTool -Name "New-ADOrganizationalUnit" -Description "Creates a new Active Directory organizational unit with specified properties and attributes." -ScriptBlock {
        param($Arguments)
        
        $params = @{}
        if ($Arguments.Name) { $params.Name = $Arguments.Name }
        if ($Arguments.Path) { $params.Path = $Arguments.Path }
        if ($Arguments.DisplayName) { $params.DisplayName = $Arguments.DisplayName }
        if ($Arguments.Description) { $params.Description = $Arguments.Description }
        if ($Arguments.City) { $params.City = $Arguments.City }
        if ($Arguments.Country) { $params.Country = $Arguments.Country }
        if ($Arguments.PostalCode) { $params.PostalCode = $Arguments.PostalCode }
        if ($Arguments.State) { $params.State = $Arguments.State }
        if ($Arguments.StreetAddress) { $params.StreetAddress = $Arguments.StreetAddress }
        if ($Arguments.ManagedBy) { $params.ManagedBy = $Arguments.ManagedBy }
        if ($Arguments.ProtectedFromAccidentalDeletion) { $params.ProtectedFromAccidentalDeletion = $Arguments.ProtectedFromAccidentalDeletion }
        if ($Arguments.OtherAttributes) { $params.OtherAttributes = $Arguments.OtherAttributes }
        if ($Arguments.Server) { $params.Server = $Arguments.Server }
        if ($Arguments.AuthType) { $params.AuthType = $Arguments.AuthType }
        if ($Arguments.Credential) { $params.Credential = $Arguments.Credential }
        if ($Arguments.PassThru) { $params.PassThru = $Arguments.PassThru }
        
        New-ADOrganizationalUnit @params
    } -InputSchema @{
        type = "object"
        properties = @{
            Name = @{ type = "string"; description = "Name of the organizational unit (required)" }
            Path = @{ type = "string"; description = "Distinguished name of the parent container" }
            DisplayName = @{ type = "string"; description = "Display name for the OU" }
            Description = @{ type = "string"; description = "Description of the organizational unit" }
            City = @{ type = "string"; description = "City location" }
            Country = @{ type = "string"; description = "Country location" }
            PostalCode = @{ type = "string"; description = "Postal code" }
            State = @{ type = "string"; description = "State or province" }
            StreetAddress = @{ type = "string"; description = "Street address" }
            ManagedBy = @{ type = "string"; description = "Distinguished name of the OU manager" }
            ProtectedFromAccidentalDeletion = @{ type = "boolean"; description = "Protect from accidental deletion" }
            OtherAttributes = @{ type = "object"; description = "Additional attributes as hashtable" }
            Server = @{ type = "string"; description = "Domain controller to connect to" }
            AuthType = @{ type = "string"; enum = @("Negotiate", "Basic"); description = "Authentication method" }
            Credential = @{ type = "string"; description = "User credentials for authentication" }
            PassThru = @{ type = "boolean"; description = "Return the created OU object" }
        }
        required = @("Name")
    }

    # Set-ADOrganizationalUnit - Modifies an Active Directory organizational unit
    Register-McpTool -Name "Set-ADOrganizationalUnit" -Description "Modifies properties of an existing Active Directory organizational unit." -ScriptBlock {
        param($Arguments)
        
        $params = @{}
        if ($Arguments.Identity) { $params.Identity = $Arguments.Identity }
        if ($Arguments.Add) { $params.Add = $Arguments.Add }
        if ($Arguments.Clear) { $params.Clear = $Arguments.Clear }
        if ($Arguments.Remove) { $params.Remove = $Arguments.Remove }
        if ($Arguments.Replace) { $params.Replace = $Arguments.Replace }
        if ($Arguments.DisplayName) { $params.DisplayName = $Arguments.DisplayName }
        if ($Arguments.Description) { $params.Description = $Arguments.Description }
        if ($Arguments.City) { $params.City = $Arguments.City }
        if ($Arguments.Country) { $params.Country = $Arguments.Country }
        if ($Arguments.PostalCode) { $params.PostalCode = $Arguments.PostalCode }
        if ($Arguments.State) { $params.State = $Arguments.State }
        if ($Arguments.StreetAddress) { $params.StreetAddress = $Arguments.StreetAddress }
        if ($Arguments.ManagedBy) { $params.ManagedBy = $Arguments.ManagedBy }
        if ($Arguments.ProtectedFromAccidentalDeletion) { $params.ProtectedFromAccidentalDeletion = $Arguments.ProtectedFromAccidentalDeletion }
        if ($Arguments.Server) { $params.Server = $Arguments.Server }
        if ($Arguments.AuthType) { $params.AuthType = $Arguments.AuthType }
        if ($Arguments.Credential) { $params.Credential = $Arguments.Credential }
        if ($Arguments.PassThru) { $params.PassThru = $Arguments.PassThru }
        if ($Arguments.Partition) { $params.Partition = $Arguments.Partition }
        
        Set-ADOrganizationalUnit @params
    } -InputSchema @{
        type = "object"
        properties = @{
            Identity = @{ type = "string"; description = "OU identity (DN, GUID, or canonical name)" }
            Add = @{ type = "object"; description = "Attributes to add as hashtable" }
            Clear = @{ type = "array"; items = @{ type = "string" }; description = "Attributes to clear" }
            Remove = @{ type = "object"; description = "Attributes to remove as hashtable" }
            Replace = @{ type = "object"; description = "Attributes to replace as hashtable" }
            DisplayName = @{ type = "string"; description = "Display name for the OU" }
            Description = @{ type = "string"; description = "Description of the organizational unit" }
            City = @{ type = "string"; description = "City location" }
            Country = @{ type = "string"; description = "Country location" }
            PostalCode = @{ type = "string"; description = "Postal code" }
            State = @{ type = "string"; description = "State or province" }
            StreetAddress = @{ type = "string"; description = "Street address" }
            ManagedBy = @{ type = "string"; description = "Distinguished name of the OU manager" }
            ProtectedFromAccidentalDeletion = @{ type = "boolean"; description = "Protect from accidental deletion" }
            Server = @{ type = "string"; description = "Domain controller to connect to" }
            AuthType = @{ type = "string"; enum = @("Negotiate", "Basic"); description = "Authentication method" }
            Credential = @{ type = "string"; description = "User credentials for authentication" }
            PassThru = @{ type = "boolean"; description = "Return the modified OU object" }
            Partition = @{ type = "string"; description = "Distinguished name of AD partition" }
        }
        required = @("Identity")
    }

    # Remove-ADOrganizationalUnit - Removes an Active Directory organizational unit
    Register-McpTool -Name "Remove-ADOrganizationalUnit" -Description "Removes an Active Directory organizational unit from the directory." -ScriptBlock {
        param($Arguments)
        
        $params = @{}
        if ($Arguments.Identity) { $params.Identity = $Arguments.Identity }
        if ($Arguments.Recursive) { $params.Recursive = $Arguments.Recursive }
        if ($Arguments.Server) { $params.Server = $Arguments.Server }
        if ($Arguments.AuthType) { $params.AuthType = $Arguments.AuthType }
        if ($Arguments.Credential) { $params.Credential = $Arguments.Credential }
        if ($Arguments.Partition) { $params.Partition = $Arguments.Partition }
        if ($Arguments.Confirm) { $params.Confirm = $Arguments.Confirm }
        if ($Arguments.WhatIf) { $params.WhatIf = $Arguments.WhatIf }
        
        Remove-ADOrganizationalUnit @params
    } -InputSchema @{
        type = "object"
        properties = @{
            Identity = @{ type = "string"; description = "OU identity (DN, GUID, or canonical name)" }
            Recursive = @{ type = "boolean"; description = "Remove OU and all child objects recursively" }
            Server = @{ type = "string"; description = "Domain controller to connect to" }
            AuthType = @{ type = "string"; enum = @("Negotiate", "Basic"); description = "Authentication method" }
            Credential = @{ type = "string"; description = "User credentials for authentication" }
            Partition = @{ type = "string"; description = "Distinguished name of AD partition" }
            Confirm = @{ type = "boolean"; description = "Prompt for confirmation before removing" }
            WhatIf = @{ type = "boolean"; description = "Show what would happen without executing" }
        }
        required = @("Identity")
    }
}

# Function is available after dot-sourcing
