<#
.SYNOPSIS
    Active Directory Domain and Forest Management Tools for MCP Server
    
.DESCRIPTION
    This module registers MCP tools for Active Directory domain and forest management operations.
    Each tool is a direct wrapper around the corresponding AD PowerShell cmdlet with
    exact parameter passthrough and no output formatting.

.AUTHOR
    Rup<PERSON> (rizhang)
#>

# Import required modules
Import-Module ActiveDirectory -ErrorAction SilentlyContinue

function Register-DomainForestManagementTools {
    [CmdletBinding()]
    param()

    # Get-ADDomain - Gets an Active Directory domain
    Register-McpTool -Name "Get-ADDomain" -Description "Gets an Active Directory domain and its properties." -ScriptBlock {
        param($Arguments)
        
        $params = @{}
        if ($Arguments.Identity) { $params.Identity = $Arguments.Identity }
        if ($Arguments.Current) { $params.Current = $Arguments.Current }
        if ($Arguments.Server) { $params.Server = $Arguments.Server }
        if ($Arguments.AuthType) { $params.AuthType = $Arguments.AuthType }
        if ($Arguments.Credential) { $params.Credential = $Arguments.Credential }
        
        Get-ADDomain @params
    } -InputSchema @{
        type = "object"
        properties = @{
            Identity = @{ type = "string"; description = "Domain identity (FQDN, NetBIOS name, or DN)" }
            Current = @{ type = "string"; enum = @("LocalComputer", "LoggedOnUser"); description = "Get current domain context" }
            Server = @{ type = "string"; description = "Domain controller to connect to" }
            AuthType = @{ type = "string"; enum = @("Negotiate", "Basic"); description = "Authentication method" }
            Credential = @{ type = "string"; description = "User credentials for authentication" }
        }
    }

    # Set-ADDomain - Modifies an Active Directory domain
    Register-McpTool -Name "Set-ADDomain" -Description "Modifies properties of an Active Directory domain." -ScriptBlock {
        param($Arguments)
        
        $params = @{}
        if ($Arguments.Identity) { $params.Identity = $Arguments.Identity }
        if ($Arguments.Add) { $params.Add = $Arguments.Add }
        if ($Arguments.Clear) { $params.Clear = $Arguments.Clear }
        if ($Arguments.Remove) { $params.Remove = $Arguments.Remove }
        if ($Arguments.Replace) { $params.Replace = $Arguments.Replace }
        if ($Arguments.AllowedDNSSuffixes) { $params.AllowedDNSSuffixes = $Arguments.AllowedDNSSuffixes }
        if ($Arguments.ManagedBy) { $params.ManagedBy = $Arguments.ManagedBy }
        if ($Arguments.Server) { $params.Server = $Arguments.Server }
        if ($Arguments.AuthType) { $params.AuthType = $Arguments.AuthType }
        if ($Arguments.Credential) { $params.Credential = $Arguments.Credential }
        if ($Arguments.PassThru) { $params.PassThru = $Arguments.PassThru }
        
        Set-ADDomain @params
    } -InputSchema @{
        type = "object"
        properties = @{
            Identity = @{ type = "string"; description = "Domain identity (FQDN, NetBIOS name, or DN)" }
            Add = @{ type = "object"; description = "Attributes to add as hashtable" }
            Clear = @{ type = "array"; items = @{ type = "string" }; description = "Attributes to clear" }
            Remove = @{ type = "object"; description = "Attributes to remove as hashtable" }
            Replace = @{ type = "object"; description = "Attributes to replace as hashtable" }
            AllowedDNSSuffixes = @{ type = "array"; items = @{ type = "string" }; description = "Allowed DNS suffixes" }
            ManagedBy = @{ type = "string"; description = "Distinguished name of the domain manager" }
            Server = @{ type = "string"; description = "Domain controller to connect to" }
            AuthType = @{ type = "string"; enum = @("Negotiate", "Basic"); description = "Authentication method" }
            Credential = @{ type = "string"; description = "User credentials for authentication" }
            PassThru = @{ type = "boolean"; description = "Return the modified domain object" }
        }
        required = @("Identity")
    }

    # Set-ADDomainMode - Sets the domain mode for an Active Directory domain
    Register-McpTool -Name "Set-ADDomainMode" -Description "Sets the domain functional level for an Active Directory domain." -ScriptBlock {
        param($Arguments)
        
        $params = @{}
        if ($Arguments.Identity) { $params.Identity = $Arguments.Identity }
        if ($Arguments.DomainMode) { $params.DomainMode = $Arguments.DomainMode }
        if ($Arguments.Server) { $params.Server = $Arguments.Server }
        if ($Arguments.AuthType) { $params.AuthType = $Arguments.AuthType }
        if ($Arguments.Credential) { $params.Credential = $Arguments.Credential }
        if ($Arguments.PassThru) { $params.PassThru = $Arguments.PassThru }
        if ($Arguments.Force) { $params.Force = $Arguments.Force }
        
        Set-ADDomainMode @params
    } -InputSchema @{
        type = "object"
        properties = @{
            Identity = @{ type = "string"; description = "Domain identity (FQDN, NetBIOS name, or DN)" }
            DomainMode = @{ type = "string"; enum = @("Windows2000Domain", "Windows2003InterimDomain", "Windows2003Domain", "Windows2008Domain", "Windows2008R2Domain", "Windows2012Domain", "Windows2012R2Domain", "Windows2016Domain", "Windows2019Domain", "Windows2022Domain"); description = "Domain functional level" }
            Server = @{ type = "string"; description = "Domain controller to connect to" }
            AuthType = @{ type = "string"; enum = @("Negotiate", "Basic"); description = "Authentication method" }
            Credential = @{ type = "string"; description = "User credentials for authentication" }
            PassThru = @{ type = "boolean"; description = "Return the domain object" }
            Force = @{ type = "boolean"; description = "Force the operation without confirmation" }
        }
        required = @("Identity", "DomainMode")
    }

    # Get-ADForest - Gets an Active Directory forest
    Register-McpTool -Name "Get-ADForest" -Description "Gets an Active Directory forest and its properties." -ScriptBlock {
        param($Arguments)
        
        $params = @{}
        if ($Arguments.Identity) { $params.Identity = $Arguments.Identity }
        if ($Arguments.Current) { $params.Current = $Arguments.Current }
        if ($Arguments.Server) { $params.Server = $Arguments.Server }
        if ($Arguments.AuthType) { $params.AuthType = $Arguments.AuthType }
        if ($Arguments.Credential) { $params.Credential = $Arguments.Credential }
        
        Get-ADForest @params
    } -InputSchema @{
        type = "object"
        properties = @{
            Identity = @{ type = "string"; description = "Forest identity (FQDN or DN)" }
            Current = @{ type = "string"; enum = @("LocalComputer", "LoggedOnUser"); description = "Get current forest context" }
            Server = @{ type = "string"; description = "Domain controller to connect to" }
            AuthType = @{ type = "string"; enum = @("Negotiate", "Basic"); description = "Authentication method" }
            Credential = @{ type = "string"; description = "User credentials for authentication" }
        }
    }

    # Set-ADForest - Modifies an Active Directory forest
    Register-McpTool -Name "Set-ADForest" -Description "Modifies properties of an Active Directory forest." -ScriptBlock {
        param($Arguments)
        
        $params = @{}
        if ($Arguments.Identity) { $params.Identity = $Arguments.Identity }
        if ($Arguments.Add) { $params.Add = $Arguments.Add }
        if ($Arguments.Clear) { $params.Clear = $Arguments.Clear }
        if ($Arguments.Remove) { $params.Remove = $Arguments.Remove }
        if ($Arguments.Replace) { $params.Replace = $Arguments.Replace }
        if ($Arguments.SPNSuffixes) { $params.SPNSuffixes = $Arguments.SPNSuffixes }
        if ($Arguments.UPNSuffixes) { $params.UPNSuffixes = $Arguments.UPNSuffixes }
        if ($Arguments.Server) { $params.Server = $Arguments.Server }
        if ($Arguments.AuthType) { $params.AuthType = $Arguments.AuthType }
        if ($Arguments.Credential) { $params.Credential = $Arguments.Credential }
        if ($Arguments.PassThru) { $params.PassThru = $Arguments.PassThru }
        
        Set-ADForest @params
    } -InputSchema @{
        type = "object"
        properties = @{
            Identity = @{ type = "string"; description = "Forest identity (FQDN or DN)" }
            Add = @{ type = "object"; description = "Attributes to add as hashtable" }
            Clear = @{ type = "array"; items = @{ type = "string" }; description = "Attributes to clear" }
            Remove = @{ type = "object"; description = "Attributes to remove as hashtable" }
            Replace = @{ type = "object"; description = "Attributes to replace as hashtable" }
            SPNSuffixes = @{ type = "array"; items = @{ type = "string" }; description = "Service Principal Name suffixes" }
            UPNSuffixes = @{ type = "array"; items = @{ type = "string" }; description = "User Principal Name suffixes" }
            Server = @{ type = "string"; description = "Domain controller to connect to" }
            AuthType = @{ type = "string"; enum = @("Negotiate", "Basic"); description = "Authentication method" }
            Credential = @{ type = "string"; description = "User credentials for authentication" }
            PassThru = @{ type = "boolean"; description = "Return the modified forest object" }
        }
        required = @("Identity")
    }

    # Set-ADForestMode - Sets the forest mode for an Active Directory forest
    Register-McpTool -Name "Set-ADForestMode" -Description "Sets the forest functional level for an Active Directory forest." -ScriptBlock {
        param($Arguments)
        
        $params = @{}
        if ($Arguments.Identity) { $params.Identity = $Arguments.Identity }
        if ($Arguments.ForestMode) { $params.ForestMode = $Arguments.ForestMode }
        if ($Arguments.Server) { $params.Server = $Arguments.Server }
        if ($Arguments.AuthType) { $params.AuthType = $Arguments.AuthType }
        if ($Arguments.Credential) { $params.Credential = $Arguments.Credential }
        if ($Arguments.PassThru) { $params.PassThru = $Arguments.PassThru }
        if ($Arguments.Force) { $params.Force = $Arguments.Force }
        
        Set-ADForestMode @params
    } -InputSchema @{
        type = "object"
        properties = @{
            Identity = @{ type = "string"; description = "Forest identity (FQDN or DN)" }
            ForestMode = @{ type = "string"; enum = @("Windows2000Forest", "Windows2003InterimForest", "Windows2003Forest", "Windows2008Forest", "Windows2008R2Forest", "Windows2012Forest", "Windows2012R2Forest", "Windows2016Forest", "Windows2019Forest", "Windows2022Forest"); description = "Forest functional level" }
            Server = @{ type = "string"; description = "Domain controller to connect to" }
            AuthType = @{ type = "string"; enum = @("Negotiate", "Basic"); description = "Authentication method" }
            Credential = @{ type = "string"; description = "User credentials for authentication" }
            PassThru = @{ type = "boolean"; description = "Return the forest object" }
            Force = @{ type = "boolean"; description = "Force the operation without confirmation" }
        }
        required = @("Identity", "ForestMode")
    }

    # Get-ADDefaultDomainPasswordPolicy - Gets the default password policy for an Active Directory domain
    Register-McpTool -Name "Get-ADDefaultDomainPasswordPolicy" -Description "Gets the default password policy for an Active Directory domain." -ScriptBlock {
        param($Arguments)
        
        $params = @{}
        if ($Arguments.Identity) { $params.Identity = $Arguments.Identity }
        if ($Arguments.Current) { $params.Current = $Arguments.Current }
        if ($Arguments.Server) { $params.Server = $Arguments.Server }
        if ($Arguments.AuthType) { $params.AuthType = $Arguments.AuthType }
        if ($Arguments.Credential) { $params.Credential = $Arguments.Credential }
        
        Get-ADDefaultDomainPasswordPolicy @params
    } -InputSchema @{
        type = "object"
        properties = @{
            Identity = @{ type = "string"; description = "Domain identity (FQDN, NetBIOS name, or DN)" }
            Current = @{ type = "string"; enum = @("LocalComputer", "LoggedOnUser"); description = "Get current domain context" }
            Server = @{ type = "string"; description = "Domain controller to connect to" }
            AuthType = @{ type = "string"; enum = @("Negotiate", "Basic"); description = "Authentication method" }
            Credential = @{ type = "string"; description = "User credentials for authentication" }
        }
    }

    # Set-ADDefaultDomainPasswordPolicy - Modifies the default password policy for an Active Directory domain
    Register-McpTool -Name "Set-ADDefaultDomainPasswordPolicy" -Description "Modifies the default password policy for an Active Directory domain." -ScriptBlock {
        param($Arguments)
        
        $params = @{}
        if ($Arguments.Identity) { $params.Identity = $Arguments.Identity }
        if ($Arguments.ComplexityEnabled) { $params.ComplexityEnabled = $Arguments.ComplexityEnabled }
        if ($Arguments.LockoutDuration) { $params.LockoutDuration = $Arguments.LockoutDuration }
        if ($Arguments.LockoutObservationWindow) { $params.LockoutObservationWindow = $Arguments.LockoutObservationWindow }
        if ($Arguments.LockoutThreshold) { $params.LockoutThreshold = $Arguments.LockoutThreshold }
        if ($Arguments.MaxPasswordAge) { $params.MaxPasswordAge = $Arguments.MaxPasswordAge }
        if ($Arguments.MinPasswordAge) { $params.MinPasswordAge = $Arguments.MinPasswordAge }
        if ($Arguments.MinPasswordLength) { $params.MinPasswordLength = $Arguments.MinPasswordLength }
        if ($Arguments.PasswordHistoryCount) { $params.PasswordHistoryCount = $Arguments.PasswordHistoryCount }
        if ($Arguments.ReversibleEncryptionEnabled) { $params.ReversibleEncryptionEnabled = $Arguments.ReversibleEncryptionEnabled }
        if ($Arguments.Server) { $params.Server = $Arguments.Server }
        if ($Arguments.AuthType) { $params.AuthType = $Arguments.AuthType }
        if ($Arguments.Credential) { $params.Credential = $Arguments.Credential }
        if ($Arguments.PassThru) { $params.PassThru = $Arguments.PassThru }
        
        Set-ADDefaultDomainPasswordPolicy @params
    } -InputSchema @{
        type = "object"
        properties = @{
            Identity = @{ type = "string"; description = "Domain identity (FQDN, NetBIOS name, or DN)" }
            ComplexityEnabled = @{ type = "boolean"; description = "Enable password complexity requirements" }
            LockoutDuration = @{ type = "string"; description = "Account lockout duration (timespan)" }
            LockoutObservationWindow = @{ type = "string"; description = "Lockout observation window (timespan)" }
            LockoutThreshold = @{ type = "integer"; description = "Account lockout threshold" }
            MaxPasswordAge = @{ type = "string"; description = "Maximum password age (timespan)" }
            MinPasswordAge = @{ type = "string"; description = "Minimum password age (timespan)" }
            MinPasswordLength = @{ type = "integer"; description = "Minimum password length" }
            PasswordHistoryCount = @{ type = "integer"; description = "Password history count" }
            ReversibleEncryptionEnabled = @{ type = "boolean"; description = "Enable reversible encryption" }
            Server = @{ type = "string"; description = "Domain controller to connect to" }
            AuthType = @{ type = "string"; enum = @("Negotiate", "Basic"); description = "Authentication method" }
            Credential = @{ type = "string"; description = "User credentials for authentication" }
            PassThru = @{ type = "boolean"; description = "Return the modified policy object" }
        }
        required = @("Identity")
    }
}

# Function is available after dot-sourcing
