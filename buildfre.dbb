DBBAPI : The CDG file is e:\os\cdg\amd64fre\build.cdg with size (104884609) and LastWriteTime [UTC 2000//1//1 8:0:0] 
DBBAPI : Package metadata file: e
Invalid inter-pass relationship - check sources.dep or LDG or CDG
Producer Pass: e:\os\src\onecore\ds\ds\src\aimx\prod\aimxsrv\server (Pass1)
Consumer Pass: e:\os\src\onecore\ds\ds\src\aimx\prod\aimxsrv\server (Pass0)
Invalid inter-pass relationship - check sources.dep or LDG or CDG
Producer Pass: e:\os\src\onecore\ds\ds\src\aimx\prod\aimxsrv\dll (Pass1)
Consumer Pass: e:\os\src\onecore\ds\ds\src\aimx\prod\aimxsrv\dll (Pass0)

DIR:
	PATH: onecore\ds\ds\src\aimx\prod\aimxsrv\idl (IN FOCUS)
	PASS: PASS0
PRIORITY: 0
DEPENDENTS:
	onecore\ds\ds\src\aimx\prod\admcpsvr (PASS1, 0, 0)
	onecore\ds\ds\src\aimx\prod\aimxsrv\client\dll (PASS1, 0, 0)
	onecore\ds\ds\src\aimx\prod\aimxsrv\client\lib (PASS1, 0, 0)
	onecore\ds\ds\src\aimx\prod\aimxsrv\dll (PASS1, 1, 0)
	onecore\ds\ds\src\aimx\prod\aimxsrv\server (PASS1, 1, 0)
	onecore\ds\ds\src\aimx\prod\llmclientlib (PASS1, 0, 0)
	onecore\ds\ds\src\aimx\prod\mcpserversample\exe (PASS1, 0, 0)
	onecore\ds\ds\src\aimx\prod\mcpserversample\lib (PASS1, 0, 0)
PRODUCERS:
	NONE

DIR:
	PATH: onecore\ds\ds\src\aimx\prod\aimxsrv\server (IN FOCUS)
	PASS: PASS1
PRIORITY: 0
DEPENDENTS:
	onecore\ds\ds\src\aimx\prod\aimxsrv\dll (PASS2, 0, 0)
	onecore\ds\ds\src\aimx\prod\mcpserversample\exe (PASS2, 0, 0)
PRODUCERS:
	onecore\ds\ds\src\aimx\prod\aimxsrv\idl (Pass0, 0, 0)
	onecore\ds\ds\src\aimx\prod\aimxsrv\server (Pass0, 0, 0)
	onecore\ds\ds\src\aimx\prod\cpprestsdk (Pass1, 0, 0)

DIR:
	PATH: onecore\ds\ds\src\aimx\prod\aimxsrv\server (IN FOCUS)
	PASS: PASS0
PRIORITY: 0
DEPENDENTS:
	onecore\ds\ds\src\aimx\prod\aimxsrv\server (PASS1, 1, 0)
PRODUCERS:
	NONE

DIR:
	PATH: onecore\ds\ds\src\aimx\prod\mcpprotocollib (IN FOCUS)
	PASS: PASS1
PRIORITY: 0
DEPENDENTS:
	onecore\ds\ds\src\aimx\prod\aimxsrv\dll (PASS2, 0, 0)
	onecore\ds\ds\src\aimx\prod\mcpserversample\exe (PASS2, 0, 0)
PRODUCERS:
	NONE

DIR:
	PATH: onecore\ds\ds\src\aimx\prod\mcpserversample\exe (IN FOCUS)
	PASS: PASS2
PRIORITY: 0
DEPENDENTS:
	NONE
PRODUCERS:
	onecore\ds\ds\src\aimx\prod\admcpsvr (Pass1, 0, 0)
	onecore\ds\ds\src\aimx\prod\aimxsrv\client\lib (Pass1, 0, 0)
	onecore\ds\ds\src\aimx\prod\aimxsrv\server (Pass1, 1, 0)
	onecore\ds\ds\src\aimx\prod\mcpprotocollib (Pass1, 0, 0)
	onecore\ds\ds\src\aimx\prod\mcpserversample\exe (Pass0, 0, 0)
	onecore\ds\ds\src\aimx\prod\mcpserversample\exe (Pass1, 0, 0)

DIR:
	PATH: onecore\ds\ds\src\aimx\prod\mcpserversample\exe (IN FOCUS)
	PASS: PASS1
PRIORITY: 0
DEPENDENTS:
	onecore\ds\ds\src\aimx\prod\mcpserversample\exe (PASS2, 0, 0)
PRODUCERS:
	onecore\ds\ds\src\aimx\prod\aimxsrv\idl (Pass0, 0, 0)
	onecore\ds\ds\src\aimx\prod\mcpserversample\exe (Pass0, 0, 0)

DIR:
	PATH: onecore\ds\ds\src\aimx\prod\mcpserversample\exe (IN FOCUS)
	PASS: PASS0
PRIORITY: 0
DEPENDENTS:
	onecore\ds\ds\src\aimx\prod\mcpserversample\exe (PASS1, 0, 0)
	onecore\ds\ds\src\aimx\prod\mcpserversample\exe (PASS2, 0, 0)
PRODUCERS:
	NONE

DIR:
	PATH: onecore\ds\ds\src\aimx\prod\mcpserversample\lib (IN FOCUS)
	PASS: PASS0
PRIORITY: 0
DEPENDENTS:
	onecore\ds\ds\src\aimx\prod\mcpserversample\lib (PASS1, 0, 0)
PRODUCERS:
	NONE

DIR:
	PATH: onecore\ds\ds\src\aimx\prod\mcpserversample\lib (IN FOCUS)
	PASS: PASS1
PRIORITY: 0
DEPENDENTS:
	onecore\ds\ds\src\aimx\prod\aimxsrv\dll (PASS2, 0, 0)
PRODUCERS:
	onecore\ds\ds\src\aimx\prod\aimxsrv\idl (Pass0, 0, 0)
	onecore\ds\ds\src\aimx\prod\mcpserversample\lib (Pass0, 0, 0)
no dependencies: onecore\ds\ds\src\aimx\prod\cpprestsdk PASS2
no dependencies: onecore\ds\ds\src\aimx\prod\cpprestsdk PASS0

DIR:
	PATH: onecore\ds\ds\src\aimx\prod\cpprestsdk (IN FOCUS)
	PASS: PASS1
PRIORITY: 0
DEPENDENTS:
	onecore\ds\ds\src\aimx\prod\aimxsrv\dll (PASS1, 1, 0)
	onecore\ds\ds\src\aimx\prod\aimxsrv\dll (PASS2, 0, 0)
	onecore\ds\ds\src\aimx\prod\aimxsrv\server (PASS1, 1, 0)
PRODUCERS:
	NONE

DIR:
	PATH: onecore\ds\ds\src\aimx\prod\aimxsrv\client\lib (IN FOCUS)
	PASS: PASS1
PRIORITY: 0
DEPENDENTS:
	onecore\ds\ds\src\aimx\prod\aimxsrv\dll (PASS2, 0, 0)
	onecore\ds\ds\src\aimx\prod\mcpserversample\exe (PASS2, 0, 0)
PRODUCERS:
	onecore\ds\ds\src\aimx\prod\aimxsrv\client\lib (Pass0, 0, 0)
	onecore\ds\ds\src\aimx\prod\aimxsrv\idl (Pass0, 0, 0)

DIR:
	PATH: onecore\ds\ds\src\aimx\prod\aimxsrv\client\lib (IN FOCUS)
	PASS: PASS0
PRIORITY: 0
DEPENDENTS:
	onecore\ds\ds\src\aimx\prod\aimxsrv\client\lib (PASS1, 0, 0)
PRODUCERS:
	NONE

DIR:
	PATH: onecore\ds\ds\src\aimx\prod\aimxsrv\client\dll (IN FOCUS)
	PASS: PASS2
PRIORITY: 0
DEPENDENTS:
	NONE
PRODUCERS:
	onecore\ds\ds\src\aimx\prod\aimxsrv\client\dll (Pass0, 0, 0)
	onecore\ds\ds\src\aimx\prod\aimxsrv\client\dll (Pass1, 0, 0)

DIR:
	PATH: onecore\ds\ds\src\aimx\prod\aimxsrv\client\dll (IN FOCUS)
	PASS: PASS1
PRIORITY: 0
DEPENDENTS:
	onecore\ds\ds\src\aimx\prod\aimxsrv\client\dll (PASS2, 0, 0)
PRODUCERS:
	onecore\ds\ds\src\aimx\prod\aimxsrv\client\dll (Pass0, 0, 0)
	onecore\ds\ds\src\aimx\prod\aimxsrv\idl (Pass0, 0, 0)

DIR:
	PATH: onecore\ds\ds\src\aimx\prod\aimxsrv\client\dll (IN FOCUS)
	PASS: PASS0
PRIORITY: 0
DEPENDENTS:
	onecore\ds\ds\src\aimx\prod\aimxsrv\client\dll (PASS1, 0, 0)
	onecore\ds\ds\src\aimx\prod\aimxsrv\client\dll (PASS2, 0, 0)
PRODUCERS:
	NONE

DIR:
	PATH: onecore\ds\ds\src\aimx\prod\aimxsrv\serviceinstaller (IN FOCUS)
	PASS: PASS2
PRIORITY: 0
DEPENDENTS:
	NONE
PRODUCERS:
	onecore\ds\ds\src\aimx\prod\aimxsrv\serviceinstaller (Pass1, 0, 0)

DIR:
	PATH: onecore\ds\ds\src\aimx\prod\aimxsrv\serviceinstaller (IN FOCUS)
	PASS: PASS1
PRIORITY: 0
DEPENDENTS:
	onecore\ds\ds\src\aimx\prod\aimxsrv\serviceinstaller (PASS2, 0, 0)
PRODUCERS:
	NONE

DIR:
	PATH: onecore\ds\ds\src\aimx\prod\llmclientlib (IN FOCUS)
	PASS: PASS1
PRIORITY: 0
DEPENDENTS:
	onecore\ds\ds\src\aimx\prod\aimxsrv\dll (PASS2, 0, 0)
PRODUCERS:
	onecore\ds\ds\src\aimx\prod\aimxsrv\idl (Pass0, 0, 0)
	onecore\ds\ds\src\aimx\prod\llmclientlib (Pass0, 0, 0)

DIR:
	PATH: onecore\ds\ds\src\aimx\prod\llmclientlib (IN FOCUS)
	PASS: PASS0
PRIORITY: 0
DEPENDENTS:
	onecore\ds\ds\src\aimx\prod\llmclientlib (PASS1, 0, 0)
PRODUCERS:
	NONE

DIR:
	PATH: onecore\ds\ds\src\aimx\prod\admcpsvr (IN FOCUS)
	PASS: PASS1
PRIORITY: 0
DEPENDENTS:
	onecore\ds\ds\src\aimx\prod\aimxsrv\dll (PASS2, 0, 0)
	onecore\ds\ds\src\aimx\prod\mcpserversample\exe (PASS2, 0, 0)
PRODUCERS:
	onecore\ds\ds\src\aimx\prod\admcpsvr (Pass0, 0, 0)
	onecore\ds\ds\src\aimx\prod\aimxsrv\idl (Pass0, 0, 0)

DIR:
	PATH: onecore\ds\ds\src\aimx\prod\admcpsvr (IN FOCUS)
	PASS: PASS0
PRIORITY: 0
DEPENDENTS:
	onecore\ds\ds\src\aimx\prod\admcpsvr (PASS1, 0, 0)
PRODUCERS:
	NONE

DIR:
	PATH: onecore\ds\ds\src\aimx\prod\aimxsrv\powershell (IN FOCUS)
	PASS: PASS2
PRIORITY: 0
DEPENDENTS:
	NONE
PRODUCERS:
	onecore\ds\ds\src\aimx\prod\aimxsrv\powershell (Pass0, 0, 0)
	onecore\ds\ds\src\aimx\prod\aimxsrv\powershell (Pass1, 0, 0)

DIR:
	PATH: onecore\ds\ds\src\aimx\prod\aimxsrv\powershell (IN FOCUS)
	PASS: PASS1
PRIORITY: 0
DEPENDENTS:
	onecore\ds\ds\src\aimx\prod\aimxsrv\powershell (PASS2, 0, 0)
PRODUCERS:
	onecore\ds\ds\src\aimx\prod\aimxsrv\powershell (Pass0, 0, 0)

DIR:
	PATH: onecore\ds\ds\src\aimx\prod\aimxsrv\powershell (IN FOCUS)
	PASS: PASS0
PRIORITY: 0
DEPENDENTS:
	onecore\ds\ds\src\aimx\prod\aimxsrv\powershell (PASS1, 0, 0)
	onecore\ds\ds\src\aimx\prod\aimxsrv\powershell (PASS2, 0, 0)
PRODUCERS:
	NONE

DIR:
	PATH: onecore\ds\ds\src\aimx\prod\aimxsrv\dll (IN FOCUS)
	PASS: PASS2
PRIORITY: 0
DEPENDENTS:
	NONE
PRODUCERS:
	onecore\ds\ds\src\aimx\prod\admcpsvr (Pass1, 0, 0)
	onecore\ds\ds\src\aimx\prod\aimxsrv\client\lib (Pass1, 0, 0)
	onecore\ds\ds\src\aimx\prod\aimxsrv\dll (Pass0, 0, 0)
	onecore\ds\ds\src\aimx\prod\aimxsrv\dll (Pass1, 1, 0)
	onecore\ds\ds\src\aimx\prod\aimxsrv\server (Pass1, 1, 0)
	onecore\ds\ds\src\aimx\prod\cpprestsdk (Pass1, 0, 0)
	onecore\ds\ds\src\aimx\prod\llmclientlib (Pass1, 0, 0)
	onecore\ds\ds\src\aimx\prod\mcpprotocollib (Pass1, 0, 0)
	onecore\ds\ds\src\aimx\prod\mcpserversample\lib (Pass1, 0, 0)

DIR:
	PATH: onecore\ds\ds\src\aimx\prod\aimxsrv\dll (IN FOCUS)
	PASS: PASS1
PRIORITY: 0
DEPENDENTS:
	onecore\ds\ds\src\aimx\prod\aimxsrv\dll (PASS2, 0, 0)
PRODUCERS:
	onecore\ds\ds\src\aimx\prod\aimxsrv\dll (Pass0, 0, 0)
	onecore\ds\ds\src\aimx\prod\aimxsrv\idl (Pass0, 0, 0)
	onecore\ds\ds\src\aimx\prod\cpprestsdk (Pass1, 0, 0)

DIR:
	PATH: onecore\ds\ds\src\aimx\prod\aimxsrv\dll (IN FOCUS)
	PASS: PASS0
PRIORITY: 0
DEPENDENTS:
	onecore\ds\ds\src\aimx\prod\aimxsrv\dll (PASS1, 1, 0)
	onecore\ds\ds\src\aimx\prod\aimxsrv\dll (PASS2, 0, 0)
PRODUCERS:
	NONE


TOP 10 PIP WITH MAX PRODUCER COUNT 
onecore\ds\ds\src\aimx\prod\aimxsrv\dll|PASS2 (Producer Count = 9)
onecore\ds\ds\src\aimx\prod\mcpserversample\exe|PASS2 (Producer Count = 6)
onecore\ds\ds\src\aimx\prod\aimxsrv\dll|PASS1 (Producer Count = 3)
onecore\ds\ds\src\aimx\prod\aimxsrv\server|PASS1 (Producer Count = 3)
onecore\ds\ds\src\aimx\prod\aimxsrv\powershell|PASS2 (Producer Count = 2)
onecore\ds\ds\src\aimx\prod\admcpsvr|PASS1 (Producer Count = 2)
onecore\ds\ds\src\aimx\prod\llmclientlib|PASS1 (Producer Count = 2)
onecore\ds\ds\src\aimx\prod\aimxsrv\client\dll|PASS1 (Producer Count = 2)
onecore\ds\ds\src\aimx\prod\aimxsrv\client\dll|PASS2 (Producer Count = 2)
onecore\ds\ds\src\aimx\prod\aimxsrv\client\lib|PASS1 (Producer Count = 2)

