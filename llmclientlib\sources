!if 0
Copyright (c) Microsoft Corporation.  All rights reserved
!endif

TARGETNAME = llmclientlib
TARGETTYPE = LIBRARY
UMTYPE = windows
TARGET_DESTINATION = retail

MSC_WARNING_LEVEL=/W4 /WX

USE_MSVCRT                  = 1
USE_UNICRT                  = 1
USE_STL                     = 1
STL_VER                     = STL_VER_CURRENT
USE_DEFAULT_WIN32_LIBS      = 0
USE_NATIVE_EH               = 1

# 
# https://github.com/microsoft/STL/issues/4978
# workaround for STL bug 4978.. sigh
#
C_DEFINES = $(C_DEFINES) -D_DISABLE_CONSTEXPR_MUTEX_CONSTRUCTOR -DUNICODE

#
# Set it to 1 for the time being, remove it when productzation code is ready
#
MUI_VERIFY_NO_LOC_RESOURCE  = 1

SOURCES=\
    llmclientlib.cpp \

INCLUDES=\
    $(INCLUDES); \
    $(MINWIN_INTERNAL_PRIV_SDK_INC_PATH_L); \
    $(ONECORE_EXTERNAL_SDK_INC_PATH_L); \
    $(PROJECT_INTERNAL_SDK_METADATA_PATH)\cppwinrt; \
    $(ONECOREUAPWINDOWS_RESTRICTED_INC_PATH_L); \
    $(PROJECT_INTERNAL_SDK_METADATA_PATH)\cppwinrt; \
    ..\common; \
    ..\common\nlohmann-json\include; \
    ..\common\hnswlib; \
    ..\aimxsrv\inc; \
    $(OBJ_PATH)\..\aimxsrv\idl\$(O); \

TARGETLIBS=\
    $(ONECORE_EXTERNAL_SDK_LIB_PATH)\oleaut32.lib \
    $(ONECORE_EXTERNAL_SDK_LIB_PATH)\onecore.lib \
    $(ONECORE_EXTERNAL_SDK_LIB_PATH)\ws2_32.lib \
    $(ONECORE_INTERNAL_PRIV_SDK_LIB_PATH_L)\OneCore_Forwarder_ole32.lib \
    $(ONECORE_INTERNAL_PRIV_SDK_LIB_PATH_L)\OneCore_Forwarder_shlwapi.lib \
    $(ONECORE_INTERNAL_PRIV_SDK_LIB_PATH_L)\OneCore_Forwarder_shell32.lib \
    $(ONECORE_INTERNAL_PRIV_SDK_LIB_PATH_L)\OneCore_Forwarder_user32.lib \
    $(ONECORE_INTERNAL_PRIV_SDK_LIB_PATH_L)\OneCore_Forwarder_gdi32.lib \
    $(MINWIN_INTERNAL_PRIV_SDK_LIB_PATH_L)\api-ms-win-core-misc-l1-1-0.lib \
    $(MINCORE_INTERNAL_PRIV_SDK_LIB_PATH_L)\ext-ms-win-ntuser-window-l1-1-0.lib \

RUN_WPP = \
    -ext:.cpp.h.hxx                                                   \
    -preserveext:.cpp.h.hxx                                           \
    -scan:..\aimxsrv\inc\wpp.h                                            \
    -DWPP_CHECK_INIT                                              \
    -p:llmclientlib                                                   \
    -func:TraceCrit{LEVEL=TRACE_LEVEL_CRITICAL}(FLAGS,MSG,...)    \
    -func:TraceErr{LEVEL=TRACE_LEVEL_ERROR}(FLAGS,MSG,...)        \
    -func:TraceWarn{LEVEL=TRACE_LEVEL_WARNING}(FLAGS,MSG,...)     \
    -func:TraceInfo{LEVEL=TRACE_LEVEL_INFORMATION}(FLAGS,MSG,...) \
    -func:TraceVerb{LEVEL=TRACE_LEVEL_VERBOSE}(FLAGS,MSG,...)     \
    $(SOURCES)