# Direct PowerShell Execution Implementation

## Overview

This implementation adds direct PowerShell execution capability to the AIMX MCP Tool Manager, bypassing the MCP server protocol overhead for PowerShell-based tools. Instead of communicating through the MCP server using stdio pipes and JSON-RPC, PowerShell cmdlets are executed directly using `CreateProcess`.

## Benefits

1. **Performance**: Eliminates MCP protocol overhead and JSON serialization/deserialization
2. **Simplicity**: Direct process execution is simpler than maintaining MCP server connections
3. **Reliability**: Reduces points of failure by removing the intermediate MCP server process
4. **Resource Efficiency**: Lower memory and CPU usage without the MCP server layer

## Implementation Details

### Files Modified

#### 1. `aimxsrv/server/McpToolManager.h`
- Added `ExecutePowerShellToolDirect` method declaration
- Method signature:
  ```cpp
  HRESULT ExecutePowerShellToolDirect(
      _In_ const std::wstring& toolName,
      _In_ const nlohmann::json& parameters,
      _Out_ MCP_TOOL_EXECUTION_RESULT& result
  );
  ```

#### 2. `aimxsrv/server/McpToolManager.cpp`
- Modified `ExecuteToolInternal` to detect PowerShell servers and route to direct execution
- Implemented `ExecutePowerShellToolDirect` method with full CreateProcess implementation
- Added server name detection for `ADPSMcpServer` and `PSMcpServer`

### Key Features

#### Server Detection
```cpp
if (serverName == L"ADPSMcpServer" || serverName == L"PSMcpServer")
{
    TraceInfo(AimxMcpToolManager, "Using direct PowerShell execution for server: %ws", serverName.c_str());
    return ExecutePowerShellToolDirect(toolName, parameters, result);
}
```

#### Command Building
- Constructs PowerShell command: `powershell.exe -ExecutionPolicy Bypass -NoProfile -Command "..."`
- Converts JSON parameters to PowerShell parameter syntax
- Handles different parameter types:
  - Strings: Wrapped in single quotes
  - Numbers: Direct conversion
  - Booleans: Converted to `$true`/`$false`
  - Objects: JSON serialized and quoted

#### Process Execution
- Uses `CreateProcess` with proper pipe setup for stdout/stderr capture
- Implements 30-second timeout for process execution
- Handles process termination on timeout
- Captures both stdout and stderr output

#### Result Formatting
- Returns results in MCP-compatible format:
  ```json
  {
    "content": "stdout output",
    "exitCode": 0,
    "error": "stderr output (if any)"
  }
  ```

## Testing

### Test Script: `Test-DirectPowerShellExecution.ps1`
Created comprehensive test script that validates:
- Simple cmdlets (Get-Date)
- Cmdlets with parameters (Get-Process, Get-Service)
- Complex cmdlets (Get-ComputerInfo)
- Error handling (access denied scenarios)

### Test Results
All 5 test cases passed successfully:
- ✅ Get-Date (no parameters)
- ✅ Get-Process (string parameter)
- ✅ Get-ChildItem (path and boolean parameters)
- ✅ Get-Service (service lookup)
- ✅ Get-ComputerInfo (system information)

## Configuration

### Supported Servers
Currently configured to work with:
- `ADPSMcpServer` - Active Directory PowerShell MCP Server
- `PSMcpServer` - General PowerShell MCP Server

### Adding New Servers
To add support for additional PowerShell-based servers, modify the detection logic in `ExecuteToolInternal`:
```cpp
if (serverName == L"ADPSMcpServer" || serverName == L"PSMcpServer" || serverName == L"YourNewServer")
```

## Error Handling

- Process creation failures are properly logged and returned as HRESULT errors
- Timeout scenarios terminate the process and return appropriate error codes
- PowerShell execution errors are captured in stderr and included in results
- Exception handling covers both C++ exceptions and system errors

## Performance Considerations

- 30-second timeout prevents hanging processes
- Pipes are properly closed to prevent resource leaks
- Process handles are cleaned up after execution
- Memory allocation is minimal with direct string operations

## Future Enhancements

1. **Configurable Timeout**: Make the 30-second timeout configurable
2. **Parameter Validation**: Add schema validation for PowerShell parameters
3. **Execution Context**: Support for different PowerShell execution policies and profiles
4. **Async Execution**: Add support for asynchronous PowerShell execution
5. **Output Streaming**: Stream large outputs instead of buffering everything

## Backward Compatibility

This implementation maintains full backward compatibility:
- Non-PowerShell servers continue to use the existing MCP protocol
- PowerShell servers can still use MCP protocol if direct execution is disabled
- All existing APIs and interfaces remain unchanged
- Results are formatted in the same structure as MCP responses

## Security Considerations

- Uses `-ExecutionPolicy Bypass` for reliable execution
- No shell injection vulnerabilities due to proper parameter escaping
- Process isolation through CreateProcess
- Timeout prevents resource exhaustion attacks
- Error messages are sanitized before logging
