<#
.SYNOPSIS
    Test script to verify parameter omission behavior
    
.DESCRIPTION
    This script demonstrates how parameters should be omitted entirely
    when not specified by the user, rather than using placeholder values.
    
.EXAMPLE
    .\Test-ParameterOmission.ps1
#>

[CmdletBinding()]
param()

function Test-ParameterBehavior {
    param(
        [string]$UserQuery,
        [hashtable]$CorrectParameters,
        [hashtable]$IncorrectParameters,
        [string]$Description
    )
    
    Write-Host "Testing: $Description" -ForegroundColor Cyan
    Write-Host "User Query: '$UserQuery'" -ForegroundColor Yellow
    Write-Host ""
    
    Write-Host "✅ CORRECT Parameters (omit unspecified):" -ForegroundColor Green
    foreach ($key in $CorrectParameters.Keys) {
        if ($CorrectParameters[$key] -is [array]) {
            Write-Host "  $key = @('$($CorrectParameters[$key] -join "', '")')" -ForegroundColor White
        } else {
            Write-Host "  $key = '$($CorrectParameters[$key])'" -ForegroundColor White
        }
    }
    
    Write-Host ""
    Write-Host "❌ INCORRECT Parameters (with placeholders):" -ForegroundColor Red
    foreach ($key in $IncorrectParameters.Keys) {
        if ($IncorrectParameters[$key] -is [array]) {
            Write-Host "  $key = @('$($IncorrectParameters[$key] -join "', '")')" -ForegroundColor White
        } else {
            Write-Host "  $key = '$($IncorrectParameters[$key])'" -ForegroundColor White
        }
    }
    
    Write-Host ""
    Write-Host "PowerShell Command Results:" -ForegroundColor Magenta
    
    # Build correct command
    $correctCmd = "Get-ADUser"
    foreach ($key in $CorrectParameters.Keys) {
        if ($CorrectParameters[$key] -is [array]) {
            $correctCmd += " -$key '$($CorrectParameters[$key] -join "','")'"
        } else {
            $correctCmd += " -$key '$($CorrectParameters[$key])'"
        }
    }
    Write-Host "✅ WORKS: $correctCmd" -ForegroundColor Green
    
    # Build incorrect command
    $incorrectCmd = "Get-ADUser"
    foreach ($key in $IncorrectParameters.Keys) {
        if ($IncorrectParameters[$key] -is [array]) {
            $incorrectCmd += " -$key '$($IncorrectParameters[$key] -join "','")'"
        } else {
            $incorrectCmd += " -$key '$($IncorrectParameters[$key])'"
        }
    }
    Write-Host "❌ FAILS: $incorrectCmd" -ForegroundColor Red
    Write-Host "   Error: 'Not specified' is not a valid parameter value" -ForegroundColor Red
    
    Write-Host ("-" * 80) -ForegroundColor Gray
    Write-Host ""
}

Write-Host "=== Parameter Omission Test Cases ===" -ForegroundColor Magenta
Write-Host "Testing how to handle unspecified parameters correctly" -ForegroundColor Cyan
Write-Host ""

# Test cases showing correct parameter omission
$testCases = @(
    @{
        UserQuery = "find user rizhang"
        CorrectParameters = @{ Identity = "rizhang" }
        IncorrectParameters = @{ 
            Identity = "rizhang"
            Server = "Not specified"
            Credential = "Not specified"
            ResultPageSize = "Not specified"
        }
        Description = "Simple user lookup - omit unspecified parameters"
    },
    @{
        UserQuery = "get user alice with email and phone"
        CorrectParameters = @{ 
            Identity = "alice"
            Properties = @("mail", "telephoneNumber")
        }
        IncorrectParameters = @{ 
            Identity = "alice"
            Properties = @("mail", "telephoneNumber")
            Server = "Not specified"
            SearchBase = "Not specified"
        }
        Description = "User with properties - omit optional parameters"
    },
    @{
        UserQuery = "find group Administrators"
        CorrectParameters = @{ Identity = "Administrators" }
        IncorrectParameters = @{ 
            Identity = "Administrators"
            Server = "Not specified"
            AuthType = "Not specified"
        }
        Description = "Group lookup - omit authentication parameters"
    },
    @{
        UserQuery = "get all users from Marketing"
        CorrectParameters = @{ Filter = "Department -eq 'Marketing'" }
        IncorrectParameters = @{ 
            Filter = "Department -eq 'Marketing'"
            Identity = "Not specified"
            LDAPFilter = "Not specified"
        }
        Description = "Filter-based query - omit unused filter types"
    }
)

foreach ($testCase in $testCases) {
    Test-ParameterBehavior -UserQuery $testCase.UserQuery -CorrectParameters $testCase.CorrectParameters -IncorrectParameters $testCase.IncorrectParameters -Description $testCase.Description
}

Write-Host "=== Key Rules for Parameter Handling ===" -ForegroundColor Magenta
Write-Host ""
Write-Host "✅ ALWAYS DO:" -ForegroundColor Green
Write-Host "  - Include ONLY parameters explicitly mentioned by user" -ForegroundColor White
Write-Host "  - Omit parameters entirely if not specified" -ForegroundColor White
Write-Host "  - Use simple values exactly as provided" -ForegroundColor White
Write-Host "  - Derive obvious parameters (Properties from 'with email')" -ForegroundColor White
Write-Host ""
Write-Host "❌ NEVER DO:" -ForegroundColor Red
Write-Host "  - Include parameters with 'Not specified' values" -ForegroundColor White
Write-Host "  - Include parameters with null or empty string values" -ForegroundColor White
Write-Host "  - Add parameters the user didn't mention" -ForegroundColor White
Write-Host "  - Construct fake Distinguished Names" -ForegroundColor White
Write-Host ""

Write-Host "=== Impact on Direct PowerShell Execution ===" -ForegroundColor Magenta
Write-Host ""
Write-Host "This fix ensures that:" -ForegroundColor Cyan
Write-Host "1. Only valid parameters are passed to PowerShell cmdlets" -ForegroundColor White
Write-Host "2. No 'Not specified' errors occur" -ForegroundColor White
Write-Host "3. Commands execute successfully with minimal parameters" -ForegroundColor White
Write-Host "4. PowerShell uses default values for omitted optional parameters" -ForegroundColor White
Write-Host ""

Write-Host "The updated prompts will prevent parameter pollution!" -ForegroundColor Green
