// -------------------------------------------------------------------------------
// <copyright file="FoundryLocalWizard.cs" company="Microsoft">
//  Copyright (c) Microsoft Corporation.  All rights reserved.
// </copyright>
//
// <summary>
// Implements Initialize-AimxServiceWizard cmdlet to start interactive setup for the AIMXsrv service.
// </summary>
//
// Owner:       jadonlau
// History:     (jadonlau) Created 6/19/2025
// -------------------------------------------------------------------------------

using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Management.Automation;
using Microsoft.Win32;
using System.IO;

namespace Microsoft.Windows.AIMX
{
    [Cmdlet(VerbsData.Initialize, "AimxServiceWizard")]
    public class InitializeAimxServiceWizard : PSCmdlet
    {

        // these reflect the C++ constants in AimxConstants.h
        private const string AIMX_REGISTRY_PARAMETERS = @"SYSTEM\CurrentControlSet\Services\AIMXSrv\Parameters";
        private const string AIMX_REGISTRY_KEYNAME_FOUNDRYLOCALINSTALLED = "FoundryLocalInstalled";
        private const string AIMX_REGISTRY_KEYNAME_ADMODULELOADED = "ActiveDirectoryModuleLoaded";
        private const string AIMX_REGISTRY_KEYNAME_FOUNDRYLOCALPORT = "FoundryLocalPort";
        private const string AIMX_REGISTRY_KEYNAME_FOUNDRYLOCALMODELIDLIST = "FoundryLocalModelIdList";
        private const string AIMX_REGISTRY_KEYNAME_FOUNDRYLOCALMODELID = "FoundryLocalModelId";
        private const string FOUNDRY_EXECUTABLE = "foundry";
        private const string WHOAMI_EXECUTABLE = "whoami";
        private const string DOMAIN_ADMINS_GROUP = "Domain Admins";
        private const int AIMX_MIN_PORT = 1024;
        private const int AIMX_MAX_PORT = 65535;

        // Available Foundry Local models
        private readonly string[] AIMX_AVAILABLE_MODELS = new string[]
        {
            "phi-4",
            "phi-4-mini",
            "phi-3.5-mini"
        };

        // not including these constants in AimxConstants.h as they are not being used in the wizard for now
        private const string API_KEY_VALUE = "OpenAiApiKey";
        private const string GEMINI_API_KEY = "GeminiApiKey";
        private const string AZURE_FOUNDRY_API_KEY = "AzureFoundryApiKey";
        private const string AZURE_FOUNDRY_ENDPOINT = "AzureFoundryEndpoint";
        private const string FOUNDRY_TASK_FOLDER = @"\Microsoft\Windows\AimxSrv";
        private const string FOUNDRY_TASK_FOLDER_NAME = "AimxSrv";
        private const string FOUNDRY_TASK_PARENT_FOLDER = @"\Microsoft\Windows";

        // PowerShell script to create Foundry scheduled task
        private const string FOUNDRY_SCHEDULED_TASK_SCRIPT = $@"
            # Find foundry.exe only under Program Files\WindowsApps on the system drive

            $windowsAppsDir = Join-Path ""$($env:SystemDrive)\Program Files"" ""WindowsApps""
            if (-not (Test-Path $windowsAppsDir)) {{
                Write-Host ""WindowsApps directory not found: $windowsAppsDir""
                exit 1
            }}

            $foundryPath = Get-ChildItem -Path $windowsAppsDir -Filter 'foundry.exe' -Recurse -ErrorAction SilentlyContinue | Select-Object -ExpandProperty FullName -First 1
            if (-not $foundryPath) {{
                Write-Host ""Could not find foundry.exe in $windowsAppsDir or its subdirectories.""
                exit 1
            }}

            # Create scheduled task components
            $action = New-ScheduledTaskAction -Execute $foundryPath -Argument ""service start""
            $trigger = New-ScheduledTaskTrigger -AtStartup
            $principal = New-ScheduledTaskPrincipal -UserId ""$env:USERNAME"" -LogonType S4U -RunLevel Limited

            # Ensure the folder Microsoft\Windows\AimxSrv exists in Task Scheduler
            $taskFolder = ""{FOUNDRY_TASK_FOLDER}""
            $service = New-Object -ComObject ""Schedule.Service""
            $service.Connect()

            try {{
                $rootFolder = $service.GetFolder(""{FOUNDRY_TASK_PARENT_FOLDER}"")
                try {{
                    $aimxFolder = $service.GetFolder($taskFolder)
                }} catch {{
                    $aimxFolder = $rootFolder.CreateFolder(""{FOUNDRY_TASK_FOLDER_NAME}"")
                }}
            }} catch {{
                Write-Host ""Failed to create or access Task Scheduler folder $taskFolder.""
                exit 1
            }}

            # Remove the existing task if it exists
            try {{
                Unregister-ScheduledTask -TaskName ""AimxSrvStartFoundry"" -TaskPath $taskFolder -Confirm:$false -ErrorAction SilentlyContinue
            }} catch {{}}

            # Register the new scheduled task
            Register-ScheduledTask -TaskName ""AimxSrvStartFoundry"" -Action $action -Trigger $trigger -Principal $principal -Description ""Start Foundry as a service at system startup"" -TaskPath $taskFolder -Force
        ";


        /// <summary>
        /// BeginProcessing
        /// </summary>
        protected override void BeginProcessing()
        {
            WriteVerbose("Initialize-AimxServiceWizard starting...");
        }

        /// <summary>
        /// ProcessRecord - Main wizard logic
        /// </summary>
        protected override void ProcessRecord()
        {
            try
            {
                if (IsCurrentUserDomainAdmin())
                {
                    WriteWarning("Current user is a domain administrator. This wizard is intended for local administrators only. Some features may not be available. Please run as a local administrator.");
                }
                WriteHost("=== Welcome to the Active Directory Management Service Wizard ===");
                WriteHost("");
                WriteHost("Please choose your LLM model provider:");

                if (!IsFoundryLocalInstalled())
                {
                    WriteHost("1. Download and use Microsoft Foundry Local (Recommended)");
                    // assume we don't have any models downloaded/port configured/api keys set
                    RemoveFoundryRegistryValues();
                }
                else
                {
                    WriteHost("1. Setup Microsoft Foundry Local");
                }
                // WriteHost("2. Choose cloud provider");
                // WriteHost("");

                // string choice = PromptForChoice();
                string choice = PromptForChoice(1, "Enter your choice"); // for now we only have Foundry Local option

                //
                // TODO: Add back cloud provider option with encryption to store in registry for M2
                //

                switch (choice.ToLower())
                {
                    case "1":
                        SetupFoundryLocal();
                        break;
                    // case "2":
                    //     SetupCloudProvider();
                    //     break;
                    default:
                        WriteWarning("Invalid selection. Please run the wizard again and choose 1.");
                        return;
                }

                WriteHost("");
                WriteHost("=== Setup Complete ===");
                WriteHost("Active Directory Management Service has been configured successfully.");
            }
            catch (Exception ex)
            {
                ThrowTerminatingError(new ErrorRecord(
                    ex,
                    "WizardSetupFailed",
                    ErrorCategory.NotSpecified,
                    null));
            }
        }

        // Helper methods for common functionality

        /// <summary>
        /// Check if input is an exit command
        /// </summary>
        private bool IsExitCommand(string input)
        {
            return !string.IsNullOrWhiteSpace(input) &&
                   (input.Trim().ToLower() == "exit" ||
                    input.Trim().ToLower() == "quit" ||
                    input.Trim().ToLower() == "cancel" ||
                    input.Trim().ToLower() == "q");
        }

        /// <summary>
        /// Handle user cancellation with consistent error messaging
        /// </summary>
        private void HandleUserCancellation()
        {
            WriteHost("Setup wizard cancelled by user.");
            ThrowTerminatingError(new ErrorRecord(
                new OperationCanceledException("User cancelled the setup wizard"),
                "UserCancelled",
                ErrorCategory.OperationStopped,
                null));
        }

        /// <summary>
        /// EndProcessing
        /// </summary>
        protected override void EndProcessing()
        {
            WriteVerbose("Initialize-AimxServiceWizard completed, please ensure the AIMXsrv service is started.");
        }

        /// <summary>
        /// Prompt user for their choice with customizable options and message
        /// </summary>
        /// <param name="maxChoices">The number of valid choices (1, 2, 3, etc.)</param>
        /// <param name="promptMessage">The message to display to the user</param>
        /// <param name="defaultChoice">Optional default choice (1-based). If null, no default is used.</param>
        /// <returns>Returns the selected choice as a string</returns>
        private string PromptForChoice(int maxChoices, string promptMessage, int? defaultChoice = 1)
        {
            while (true)
            {
                // Build the prompt message
                string fullPrompt = promptMessage;

                fullPrompt += ", or type 'exit' to quit:";

                WriteHost(fullPrompt, true);

                var input = Host.UI.ReadLine();

                // Handle exit command
                if (IsExitCommand(input))
                {
                    HandleUserCancellation();
                }

                // Handle default case (empty input)
                if (string.IsNullOrWhiteSpace(input) && defaultChoice.HasValue)
                {
                    return defaultChoice.Value.ToString();
                }

                // Validate numeric input
                if (int.TryParse(input.Trim(), out int choice) && choice >= 1 && choice <= maxChoices)
                {
                    return choice.ToString();
                }

                // Show error message for invalid input
                if (maxChoices == 1)
                {
                    WriteHost("Please enter 1.");
                }
                else
                {
                    WriteHost($"Please enter a number between 1 and {maxChoices}.");
                }
            }
        }


        /// <summary>
        /// Setup Foundry Local installation
        /// </summary>
        private void SetupFoundryLocal()
        {
            WriteHost("");
            WriteHost("Setting up Microsoft Foundry Local...");
            WriteHost("");

            // Check if Foundry Local is already installed
            if (IsFoundryLocalInstalled())
            {
                WriteHost("Microsoft Foundry Local is already installed.");
                StartFoundry();
                SetRegistryValue(AIMX_REGISTRY_KEYNAME_FOUNDRYLOCALINSTALLED, true);

                // Now prompt for model selection and download
                SetupFoundryLocalModel();
                return;
            }

            WriteHost("Installing Microsoft Foundry Local via winget...");

            try
            {
                // Execute winget install command
                var processStartInfo = new ProcessStartInfo
                {
                    FileName = "winget",
                    Arguments = "install Microsoft.FoundryLocal --accept-source-agreements --accept-package-agreements --silent",
                    UseShellExecute = false,
                    RedirectStandardOutput = true,
                    RedirectStandardError = true,
                    CreateNoWindow = true
                };

                using (var process = Process.Start(processStartInfo))
                {
                    // Use WriteProgress to show installation progress
                    WriteProgress(new ProgressRecord(1, "Foundry Local Installation", "Starting installation..."));

                    string line;
                    bool installSuccess = false;

                    // Read output line by line to capture real-time progress
                    while ((line = process.StandardOutput.ReadLine()) != null)
                    {
                        if (!string.IsNullOrWhiteSpace(line))
                        {
                            // Check for progress percentage in the line
                            var progressMatch = System.Text.RegularExpressions.Regex.Match(line, @"(\d+)%");
                            if (progressMatch.Success)
                            {
                                string percentage = progressMatch.Groups[1].Value;
                                WriteProgress(new ProgressRecord(1, "Foundry Local Installation", $"Installing... {percentage}%"));
                            }
                        }
                    }

                    // Wait for process to complete
                    process.WaitForExit();

                    // Read any remaining output and errors
                    var remainingOutputTask = process.StandardOutput.ReadToEndAsync();
                    var errorTask = process.StandardError.ReadToEndAsync();

                    var remainingOutput = remainingOutputTask.Result;
                    var error = errorTask.Result;

                    // Check final result
                    if (process.ExitCode == 0 || installSuccess ||
                        (!string.IsNullOrWhiteSpace(remainingOutput) && remainingOutput.Contains("Successfully installed")))
                    {
                        SetRegistryValue(AIMX_REGISTRY_KEYNAME_FOUNDRYLOCALINSTALLED, true);
                        WriteHost("Microsoft Foundry Local installed successfully!");

                        // If Foundry Local is installed, we can start the service immediately to avoid issues with downloading models and getting stuck (foundry will start service auto to download models)
                        StartFoundry();
                        // Now prompt for model selection and download
                        SetupFoundryLocalModel();
                    }
                    else
                    {
                        WriteWarning($"Foundry Local installation may not have completed successfully (Exit code: {process.ExitCode})");
                        if (!string.IsNullOrWhiteSpace(error))
                        {
                            WriteHost($"Error: {error.Trim()}");
                        }

                        if (!string.IsNullOrWhiteSpace(remainingOutput))
                        {
                            WriteHost($"Output: {remainingOutput.Trim()}");
                        }
                        WriteHost("You can try to manually install Foundry Local using: winget install Microsoft.FoundryLocal");
                    }
                }
            }
            catch (Exception ex)
            {
                WriteError(new ErrorRecord(
                    ex,
                    "FoundryLocalInstallFailed",
                    ErrorCategory.NotSpecified,
                    null));

                WriteHost("Installation encountered an error.");
                WriteHost("You can try to manually install Foundry Local using: winget install Microsoft.FoundryLocal");
            }
        }

        /// <summary>
        /// Setup Foundry Local model selection and download
        /// </summary>
        private void SetupFoundryLocalModel()
        {
            WriteHost("");
            WriteHost("=== Model Selection ===");
            WriteHost("");
            WriteHost("Please choose which AI model to download and use:");
            WriteHost("");

            // Display available models with numbers
            for (int i = 0; i < AIMX_AVAILABLE_MODELS.Length; i++)
            {
                string displayText = $"{i + 1}. {AIMX_AVAILABLE_MODELS[i]}";
                if (i == 0) // phi-4 is the default/recommended
                {
                    displayText += " (Recommended)";
                }
                WriteHost(displayText);
            }
            WriteHost("");
            string selectedModel = PromptForModelChoice();

            if (!string.IsNullOrEmpty(selectedModel))
            {
                SetupModelDownloadLocation();
                DownloadFoundryModel(selectedModel);

                // Check if port is already configured
                if (IsFoundryPortConfigured())
                {
                    WriteHost("");
                    string result = PromptForYesNoChoice("Do you want to change the Foundry Local service port?");

                    if (result == "Y")
                    {
                        ConfigureFoundryPort();
                    }
                }
                else
                {
                    // Port not configured, configure it automatically
                    WriteHost("");
                    WriteHost("Foundry Local service port is not configured.");
                    ConfigureFoundryPort();
                }

                // Ask the user to download the AD PS module if not already done
                if (!IsActiveDirectoryModuleLoaded())
                {
                    InstallActiveDirectoryModule();
                }
                else
                {
                    WriteHost("Active Directory PowerShell module is already loaded, skipping installation...");
                }

                // Add the user to turn on the AIMXsrv service
                ConfigureAimxService();

                // Ask the user if they want to create a scheduled task for Foundry startup
                if (!IsFoundryScheduledTaskExists())
                {
                    ConfigureFoundryScheduledTask();
                }
                else
                {
                    WriteHost("AimxSrvStartFoundry scheduled task already exists, skipping creation...");
                }
            }
        }

        /// <summary>
        /// Ask the user if they want to change the location of the directory where models are stored
        /// </summary>
        private void SetupModelDownloadLocation()
        {
            WriteHost("");
            WriteHost("=== Model Download Location ===");
            WriteHost("");

            // Get current cache location
            string currentCacheLocation = GetFoundryCacheLocation();
            if (!string.IsNullOrEmpty(currentCacheLocation))
            {
                WriteHost($"Current cache location: {currentCacheLocation}");
                WriteHost("");
            }

            WriteHost("Please choose where to download the models:");
            WriteHost("1. Keep current location (default)");
            WriteHost("2. Custom location (you will be prompted to enter a path)");
            WriteHost("");

            var input = PromptForChoice(2, "Enter your choice (1 or 2)");

            if (input.Trim() == "1")
            {
                return;
            }
            else if (input.Trim() == "2")
            {
                GetCustomPathFromUser();
                return;
            }
        }

        /// <summary>
        /// Prompt user for custom path until a valid path is entered
        /// </summary>
        private void GetCustomPathFromUser()
        {
            while (true)
            {
                WriteHost("Please enter the custom path for model downloads:", true);
                string customPath = Host.UI.ReadLine().Trim();

                // Handle exit command for custom path
                if (IsExitCommand(customPath))
                {
                    HandleUserCancellation();
                }

                // Validate the cache location path for invalid characters
                if (customPath.IndexOfAny(System.IO.Path.GetInvalidPathChars()) >= 0)
                {
                    WriteWarning("The provided cache location contains invalid characters. Please try again.");
                    WriteHost("");
                    continue;
                }

                // Additional validation: check if path is empty or just whitespace
                if (string.IsNullOrWhiteSpace(customPath))
                {
                    WriteWarning("Path cannot be empty. Please enter a valid path.");
                    WriteHost("");
                    continue;
                }

                // Additional validation: check for obviously invalid paths
                try
                {
                    // Try to get the full path to catch more invalid path cases
                    string fullPath = Path.GetFullPath(customPath);

                    // Check for file migration opportunity
                    string currentCacheLocation = GetFoundryCacheLocation();
                    if (!string.IsNullOrEmpty(currentCacheLocation) &&
                        System.IO.Directory.Exists(currentCacheLocation) &&
                        !string.Equals(currentCacheLocation, fullPath, StringComparison.OrdinalIgnoreCase))
                    {
                        // Check if current cache has files to migrate
                        long currentCacheSize = GetDirectorySize(currentCacheLocation);
                        if (currentCacheSize > 0)
                        {
                            // Check if new location has enough space
                            long availableSpace = GetAvailableDiskSpace(fullPath);
                            if (availableSpace > 0 && availableSpace >= currentCacheSize)
                            {
                                string migrateChoice = PromptForYesNoChoice("Would you like to copy all cache files from the old location to the new location?");

                                if (migrateChoice == "Y")
                                {
                                    // Migrate files before configuring cache location
                                    if (MigrateCacheFiles(currentCacheLocation, fullPath))
                                    {
                                        WriteHost("Cache files migrated successfully.");
                                    }
                                    else
                                    {
                                        WriteWarning("Cache file migration failed, but will continue with cache location change.");
                                    }
                                }
                            }
                            else if (availableSpace > 0)
                            {
                                WriteHost($"New location does not have enough storage to move files from previous directory.");
                                WriteHost("Cache location will be changed without migrating existing files. You may have to redownload existing models.");
                            }
                            else
                            {
                                WriteHost("Could not determine available space in new location.");
                                WriteHost("Cache location will be changed without migrating existing files. You may have to redownload existing models.");
                            }
                        }
                    }

                    // If we get here, the path format is valid, so configure it
                    ConfigureCacheLocation(customPath);
                    return; // Exit the loop once a valid path is processed
                }
                catch (Exception ex) when (ex is ArgumentException || ex is NotSupportedException || ex is PathTooLongException)
                {
                    WriteWarning($"Invalid path format: {ex.Message}. Please try again.");
                    WriteHost("");
                    continue; // Loop back to ask for input again
                }
            }
        }

        /// <summary>
        /// Migrate all cache files from old location to new location
        /// </summary>
        /// <param name="sourcePath">Source directory path</param>
        /// <param name="destinationPath">Destination directory path</param>
        /// <returns>True if migration succeeded, false otherwise</returns>
        private bool MigrateCacheFiles(string sourcePath, string destinationPath)
        {
            try
            {
                WriteHost("");
                WriteHost("Starting cache file migration...");

                // Ensure destination directory exists
                if (!System.IO.Directory.Exists(destinationPath))
                {
                    System.IO.Directory.CreateDirectory(destinationPath);
                }

                // Get all items from source directory
                System.IO.DirectoryInfo sourceDir = new System.IO.DirectoryInfo(sourcePath);
                var files = sourceDir.GetFiles("*", System.IO.SearchOption.TopDirectoryOnly);
                var directories = sourceDir.GetDirectories("*", System.IO.SearchOption.TopDirectoryOnly);

                WriteHost($"Moving {files.Length} files and {directories.Length} directories...");

                // Move all files
                foreach (System.IO.FileInfo file in files)
                {
                    string destFilePath = System.IO.Path.Combine(destinationPath, file.Name);
                    file.MoveTo(destFilePath);
                }

                // Move all directories
                foreach (System.IO.DirectoryInfo dir in directories)
                {
                    string destDirPath = System.IO.Path.Combine(destinationPath, dir.Name);
                    dir.MoveTo(destDirPath);
                }

                WriteHost($"Successfully migrated cache files from '{sourcePath}' to '{destinationPath}'");
                return true;
            }
            catch (Exception ex)
            {
                WriteError(new ErrorRecord(
                    ex,
                    "CacheMigrationException",
                    ErrorCategory.NotSpecified,
                    null));

                WriteHost($"Cache migration failed: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// Get the current Foundry cache location
        /// </summary>
        /// <returns>The cache directory path or null if unable to retrieve</returns>
        private string GetFoundryCacheLocation()
        {
            try
            {
                var processStartInfo = new ProcessStartInfo
                {
                    FileName = FOUNDRY_EXECUTABLE,
                    Arguments = "cache location",
                    UseShellExecute = false,
                    RedirectStandardOutput = true,
                    RedirectStandardError = true,
                    CreateNoWindow = true
                };

                using (var process = Process.Start(processStartInfo))
                {
                    process.WaitForExit();
                    var outputTask = process.StandardOutput.ReadToEndAsync();
                    var errorTask = process.StandardError.ReadToEndAsync();

                    var output = outputTask.Result;
                    var error = errorTask.Result;

                    if (process.ExitCode == 0 && !string.IsNullOrWhiteSpace(output))
                    {
                        // Parse the output to extract the cache directory path
                        // Expected format: "💾 Cache directory path: C:\path\to\cache"
                        if (output.Contains("Cache directory path:"))
                        {
                            var parts = output.Split(new[] { ':' }, 2);
                            if (parts.Length >= 2)
                            {
                                var path = parts[1].Trim();
                                if (!string.IsNullOrEmpty(path))
                                {
                                    return path;
                                }
                            }
                        }
                    }

                    if (!string.IsNullOrWhiteSpace(error))
                    {
                        WriteVerbose($"Error getting cache location: {error.Trim()}");
                    }
                }
            }
            catch (Exception ex)
            {
                WriteVerbose($"Exception getting cache location: {ex.Message}");
            }

            return null;
        }

        /// <summary>
        /// Configure cache location using foundry cache cd command
        /// </summary>
        /// <param name="cacheLocation">The new cache directory path</param>
        private void ConfigureCacheLocation(string cacheLocation)
        {
            WriteHost("");
            WriteHost($"Configuring cache location to: {cacheLocation}");
            WriteHost("");

            try
            {
                var processStartInfo = new ProcessStartInfo
                {
                    FileName = "foundry",
                    Arguments = $"cache cd \"{cacheLocation}\"",
                    UseShellExecute = false,
                    RedirectStandardOutput = true,
                    RedirectStandardError = true,
                    CreateNoWindow = true
                };

                using (var process = Process.Start(processStartInfo))
                {
                    WriteProgress(new ProgressRecord(3, "Cache Configuration", "Configuring cache location..."));

                    string line;

                    // Read output line by line
                    while ((line = process.StandardOutput.ReadLine()) != null)
                    {
                        if (!string.IsNullOrWhiteSpace(line))
                        {
                            WriteProgress(new ProgressRecord(3, "Cache Configuration", line.Trim()));

                            // Check for service start (indicates successful completion)
                            if (line.Contains("Service is Started"))
                            {
                                WriteProgress(new ProgressRecord(3, "Cache Configuration", "Cache location configured successfully")
                                { RecordType = ProgressRecordType.Completed });
                                var newCacheLocation = GetFoundryCacheLocation();

                                WriteHost($"Cache location configured successfully to: {newCacheLocation}");
                                WriteHost("Foundry service restarted with new cache location.");
                                return;
                            }
                        }
                    }

                    process.WaitForExit();

                    var remainingOutput = process.StandardOutput.ReadToEnd();
                    var error = process.StandardError.ReadToEnd();

                    // Check for errors
                    if (!string.IsNullOrWhiteSpace(error))
                    {
                        WriteWarning("Cache location configuration encountered an error:");
                        WriteHost(error.Trim());
                        WriteHost($"You can manually configure the cache location using: foundry cache cd \"{cacheLocation}\"");
                        return;
                    }
                }
            }
            catch (Exception ex)
            {
                WriteError(new ErrorRecord(
                    ex,
                    "CacheLocationConfigurationException",
                    ErrorCategory.NotSpecified,
                    null));

                WriteHost("Cache location configuration encountered an error.");
                WriteHost($"You can manually configure the cache location using: foundry cache cd \"{cacheLocation}\"");
            }
        }

        /// <summary>
        /// Check if Foundry port is configured in the registry
        /// </summary>
        /// <returns> True if port is configured, false otherwise </returns>
        private bool IsFoundryPortConfigured()
        {
            try
            {
                using (var key = Registry.LocalMachine.OpenSubKey(AIMX_REGISTRY_PARAMETERS))
                {
                    if (key != null)
                    {
                        var portValue = key.GetValue(AIMX_REGISTRY_KEYNAME_FOUNDRYLOCALPORT);
                        if (portValue != null)
                        {
                            // Check if the value is a valid port number
                            string portString = portValue.ToString();
                            if (!string.IsNullOrWhiteSpace(portString) &&
                                int.TryParse(portString, out int port) &&
                                port >= AIMX_MIN_PORT && port <= AIMX_MAX_PORT)
                            {
                                return true;
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                WriteVerbose($"Error checking Foundry port configuration: {ex.Message}");
            }

            WriteVerbose("Foundry port not configured in registry");
            return false;
        }

        /// <summary>
        /// Prompt user for model choice
        /// </summary>
        private string PromptForModelChoice()
        {
            while (true)
            {
                WriteHost($"Please choose a model (1-{AIMX_AVAILABLE_MODELS.Length}), or type 'exit' to quit: ", true);

                var input = Host.UI.ReadLine();

                // Handle exit command
                if (IsExitCommand(input))
                {
                    HandleUserCancellation();
                }


                // Handle default case (empty input)
                if (string.IsNullOrWhiteSpace(input))
                {
                    return "phi-4";
                }

                // Validate numeric input
                if (int.TryParse(input.Trim(), out int choice) && choice >= 1 && choice <= AIMX_AVAILABLE_MODELS.Length)
                {
                    string selectedModel = AIMX_AVAILABLE_MODELS[choice - 1];
                    return selectedModel;
                }
            }
        }

        /// <summary>
        /// Download the selected Foundry model
        /// </summary>
        private void DownloadFoundryModel(string modelName)
        {
            WriteHost("");
            WriteHost($"Downloading model: {modelName}...");
            WriteHost("");

            try
            {
                var processStartInfo = new ProcessStartInfo
                {
                    FileName = FOUNDRY_EXECUTABLE,
                    Arguments = $"model download {modelName}",
                    UseShellExecute = false,
                    RedirectStandardOutput = true,
                    RedirectStandardError = true,
                    CreateNoWindow = true
                };

                using (var process = Process.Start(processStartInfo))
                {
                    // Use WriteProgress instead of custom line overwriting
                    WriteProgress(new ProgressRecord(1, "Model Download", $"Downloading {modelName}..."));

                    string line;
                    bool downloadSuccess = false;
                    string modelTemp = null;
                    string modelId = null;

                    while ((line = process.StandardOutput.ReadLine()) != null)
                    {
                        // Only update progress if line is not null or empty
                        if (!string.IsNullOrWhiteSpace(line))
                        {
                            WriteProgress(new ProgressRecord(1, "Model Download", line.Trim()));
                        }
                        else
                        {
                            // Use a default status for empty lines
                            WriteProgress(new ProgressRecord(1, "Model Download", "Processing..."));
                        }

                        // Extract model ID only if we haven't found one yet
                        if (modelTemp == null)
                        {
                            modelTemp = ExtractModelIdFromOutput(line);
                            if (modelTemp != null)
                            {
                                modelId = modelTemp;
                                // Store the model ID in the registry immediately when found
                            }
                        }

                        if (line.Contains("found in the local cache") || line.Contains("To find models already downloaded"))
                        {
                            WriteHost(line.Trim());
                            downloadSuccess = true;
                            break;
                        }
                    }

                    process.WaitForExit();

                    // Read any remaining output and errors
                    var remainingOutputTask = process.StandardOutput.ReadToEndAsync();
                    var errorTask = process.StandardError.ReadToEndAsync();

                    var remainingOutput = remainingOutputTask.Result;
                    var error = errorTask.Result;

                    WriteProgress(new ProgressRecord(1, "Model Download", "Download completed")
                    { RecordType = ProgressRecordType.Completed });

                    // Check for errors first
                    if (!string.IsNullOrWhiteSpace(error))
                    {
                        WriteWarning($"Model {modelName} download encountered an error:");
                        WriteHost(error.Trim());
                        WriteHost("You can manually download a model later using: foundry model download <model-name>");
                        return;
                    }

                    if (process.ExitCode == 0 || downloadSuccess)
                    {
                        if (modelId != null)
                        {
                            AddModelToRegistryList(modelId);
                            // Set the current model ID (replaces any existing value)
                            SetRegistryValue(AIMX_REGISTRY_KEYNAME_FOUNDRYLOCALMODELID, modelId);
                        }
                    }
                    else
                    {
                        WriteWarning($"Model {modelName} download may not have completed successfully (Exit code: {process.ExitCode})");
                        if (!string.IsNullOrWhiteSpace(remainingOutput))
                        {
                            WriteHost($"Output: {remainingOutput.Trim()}");
                        }
                        WriteHost("You can manually download a model later using: foundry model download <model-name>");
                    }
                }
            }
            catch (Exception ex)
            {
                WriteError(new ErrorRecord(
                    ex,
                    "ModelDownloadException",
                    ErrorCategory.NotSpecified,
                    null));

                WriteHost("Model download encountered an error, but Foundry Local is still installed.");
                WriteHost("You can manually download a model later using: foundry model download <model-name>");
            }
        }

        /// <summary>
        /// Extract model ID from foundry output (handles both "found in cache" and "Downloading" cases)
        /// Examples:
        /// - "Model Phi-3.5-mini-instruct-cuda-gpu was found in the local cache."
        /// - "Downloading Phi-3.5-mini-instruct-cuda-gpu..."
        /// </summary>
        private string ExtractModelIdFromOutput(string output)
        {
            if (string.IsNullOrWhiteSpace(output))
                return null;

            try
            {
                // Pattern 1: "Model {model-id} was found in the local cache."
                var cacheMatch = System.Text.RegularExpressions.Regex.Match(
                    output,
                    @"Model\s+(.+?)\s+was\s+found\s+in\s+the\s+local\s+cache",
                    System.Text.RegularExpressions.RegexOptions.IgnoreCase);

                if (cacheMatch.Success && cacheMatch.Groups.Count > 1)
                {
                    string modelId = cacheMatch.Groups[1].Value.Trim();
                    return modelId;
                }

                // Pattern 2: "Downloading {model-id}..."
                var downloadMatch = System.Text.RegularExpressions.Regex.Match(
                    output,
                    @"Downloading\s+(.+?)\.{3}",
                    System.Text.RegularExpressions.RegexOptions.IgnoreCase);

                if (downloadMatch.Success && downloadMatch.Groups.Count > 1)
                {
                    string modelId = downloadMatch.Groups[1].Value.Trim();
                    return modelId;
                }
            }
            catch (Exception ex)
            {
                WriteVerbose($"Error extracting model ID from output: {ex.Message}");
            }

            return null;
        }

        /// <summary>
        /// Configure Foundry Local service port
        /// </summary>
        private void ConfigureFoundryPort()
        {
            WriteHost("");
            WriteHost("=== Port Configuration ===");
            WriteHost("");
            WriteHost("Please choose a port number for Foundry Local service:");
            WriteHost("");

            string portChoice = PromptForPortChoice();

            if (!string.IsNullOrEmpty(portChoice))
            {
                SetFoundryPort(portChoice);
            }
        }

        /// <summary>
        /// Prompt user for port choice
        /// </summary>
        private string PromptForPortChoice()
        {
            while (true)
            {
                WriteHost("Enter port number (1024-65535) or press Enter for default (5273), or type 'exit' to quit: ", true);
                var input = Host.UI.ReadLine();

                // Handle exit command
                if (IsExitCommand(input))
                {
                    HandleUserCancellation();
                }

                // Handle default case (empty input)
                if (string.IsNullOrWhiteSpace(input))
                {
                    WriteHost("Selected: 5273 (default)");
                    return "5273";
                }

                // Validate port number
                if (int.TryParse(input.Trim(), out int port) && port >= AIMX_MIN_PORT && port <= AIMX_MAX_PORT)
                {
                    WriteHost($"Selected: {port}");
                    return port.ToString();
                }
            }
        }

        /// <summary>
        /// Set Foundry Local service port
        /// </summary>
        private void SetFoundryPort(string portNumber)
        {
            WriteHost("");
            WriteHost($"Configuring Foundry Local to use port {portNumber}...");

            try
            {
                var processStartInfo = new ProcessStartInfo
                {
                    FileName = FOUNDRY_EXECUTABLE,
                    Arguments = $"service set --port {portNumber}",
                    UseShellExecute = false,
                    RedirectStandardOutput = true,
                    RedirectStandardError = true,
                    CreateNoWindow = true
                };

                using (var process = Process.Start(processStartInfo))
                {
                    string line;

                    // Read output line by line as it comes in
                    while ((line = process.StandardOutput.ReadLine()) != null)
                    {
                        if (!string.IsNullOrWhiteSpace(line))
                        {
                            // Show progress to user
                            WriteProgress(new ProgressRecord(2, "Port Configuration", line.Trim()));

                            // Check for "No settings changed" case
                            if (line.Contains("No settings changed"))
                            {
                                WriteProgress(new ProgressRecord(2, "Port Configuration", "Port already configured")
                                { RecordType = ProgressRecordType.Completed });

                                WriteHost($"Foundry Local port was already configured to {portNumber}.");
                                SetRegistryValue(AIMX_REGISTRY_KEYNAME_FOUNDRYLOCALPORT, portNumber);
                                return;
                            }

                            // Check if service has started (this indicates successful completion)
                            if (line.Contains("Service is Started"))
                            {
                                WriteProgress(new ProgressRecord(2, "Port Configuration", "Configuration completed")
                                { RecordType = ProgressRecordType.Completed });

                                WriteHost($"Foundry Local port configured successfully to {portNumber}!");

                                // Store the port in registry
                                SetRegistryValue(AIMX_REGISTRY_KEYNAME_FOUNDRYLOCALPORT, portNumber);
                                return;
                            }
                        }
                    }

                    // Read any remaining error output
                    var errorTask = process.StandardError.ReadToEndAsync();
                    var error = errorTask.Result;

                    // Check for errors
                    if (!string.IsNullOrWhiteSpace(error))
                    {
                        WriteWarning("Port configuration encountered an error:");
                        WriteHost(error.Trim());
                        WriteHost($"You can manually set the port later using: foundry service set --port {portNumber}");
                        return;
                    }
                }
            }
            catch (Exception ex)
            {
                WriteError(new ErrorRecord(
                    ex,
                    "PortConfigurationException",
                    ErrorCategory.NotSpecified,
                    null));

                WriteHost("Port configuration encountered an error, but Foundry Local is still installed.");
                WriteHost($"You can manually set the port later using: foundry service set --port {portNumber}");
            }
        }

        /// <summary>
        /// Start foundry service
        /// </summary>
        private void StartFoundry()
        {
            try
            {
                var processStartInfo = new ProcessStartInfo
                {
                    FileName = FOUNDRY_EXECUTABLE,
                    Arguments = $"service start",
                    UseShellExecute = false,
                    RedirectStandardOutput = true,
                    RedirectStandardError = true,
                    CreateNoWindow = true
                }; using (var process = Process.Start(processStartInfo))
                {

                    string line;
                    while ((line = process.StandardOutput.ReadLine()) != null)
                    {
                        if (!string.IsNullOrWhiteSpace(line))
                        {

                            if (line.Contains("Service is Started"))
                            {
                                WriteHost("Foundry Local service started successfully!");
                                break;
                            }

                            if (line.Contains("Service is already running"))
                            {
                                break; // Service is already running, no need to do anything
                            }
                        }
                    }

                }
            }
            catch (Exception ex)
            {
                WriteError(new ErrorRecord(
                    ex,
                    "ServiceStartException",
                    ErrorCategory.NotSpecified,
                    null));

                WriteHost($"You can manually start the service later using: foundry service start");
            }
        }

        /// <summary>
        /// Setup cloud provider selection
        /// </summary>
        private void SetupCloudProvider()
        {
            WriteHost("");
            WriteHost("=== Cloud Provider Selection ===");
            WriteHost("");
            WriteHost("Please choose your cloud AI provider:");
            WriteHost("");
            WriteHost("1. Use OpenAI with your own API key");
            WriteHost("2. Use Gemini with your own API key");
            WriteHost("3. Use Azure AI Foundry with your API and endpoint");
            WriteHost("");

            string cloudChoice = PromptForChoice(3, "Enter your choice (1, 2, or 3)");

            switch (cloudChoice.ToLower())
            {
                case "1":
                    SetupCloudModel("OpenAI");
                    break;
                case "2":
                    SetupCloudModel("Google Gemini");
                    break;
                case "3":
                    SetupCloudModel("Azure AI Foundry");
                    break;
                default:
                    WriteWarning("Invalid selection. Please run the wizard again and choose 1, 2, or 3.");
                    return;
            }
        }

        /// <summary>
        /// Setup cloud model with API key
        /// </summary>
        private void SetupCloudModel(string provider)
        {
            WriteHost("");
            WriteHost($"Setting up {provider} configuration...");
            WriteHost("");

            string apiKey = "";
            string endpoint = "";

            if (provider == "Azure AI Foundry")
            {
                WriteHost("Please enter your Azure AI Foundry endpoint:");
                WriteHost("Endpoint: ", true);
                endpoint = Host.UI.ReadLine();

                if (string.IsNullOrWhiteSpace(endpoint))
                {
                    WriteWarning("No endpoint provided. Setup cancelled.");
                    return;
                }

                WriteHost("Please enter your Azure AI Foundry API key:");
            }
            else
            {
                WriteHost($"Please enter your {provider} API key:");
            }

            apiKey = PromptForSecureInput();

            if (string.IsNullOrWhiteSpace(apiKey))
            {
                WriteWarning("No API key provided. Setup cancelled.");
                return;
            }
            try
            {
                // Store API key in appropriate registry key based on provider
                switch (provider)
                {
                    case "OpenAI":
                        SetRegistryValue(API_KEY_VALUE, apiKey);
                        break;
                    case "Google Gemini":
                        SetRegistryValue(GEMINI_API_KEY, apiKey);
                        break;
                    case "Azure AI Foundry":
                        SetRegistryValue(AZURE_FOUNDRY_API_KEY, apiKey);
                        // Store endpoint for Azure
                        if (!string.IsNullOrWhiteSpace(endpoint))
                        {
                            SetRegistryValue(AZURE_FOUNDRY_ENDPOINT, endpoint);
                        }
                        break;
                }


                WriteHost($"{provider} configuration saved successfully!");
            }
            catch (Exception ex)
            {
                WriteError(new ErrorRecord(
                    ex,
                    "CloudModelSetupFailed",
                    ErrorCategory.NotSpecified,
                    null));
            }
        }

        /// <summary>
        /// Prompt for secure input (API key)
        /// </summary>
        private string PromptForSecureInput()
        {
            WriteHost("API Key: ", true);
            var secureString = Host.UI.ReadLineAsSecureString();

            // Convert SecureString to regular string for storage
            IntPtr ptr = IntPtr.Zero;
            try
            {
                ptr = System.Runtime.InteropServices.Marshal.SecureStringToBSTR(secureString);
                return System.Runtime.InteropServices.Marshal.PtrToStringBSTR(ptr);
            }
            finally
            {
                if (ptr != IntPtr.Zero)
                {
                    System.Runtime.InteropServices.Marshal.ZeroFreeBSTR(ptr);
                }
            }
        }

        /// <summary>
        /// Check if Foundry Local is already installed
        /// </summary>
        private bool IsFoundryLocalInstalled()
        {
            try
            {
                var processStartInfo = new ProcessStartInfo
                {
                    FileName = FOUNDRY_EXECUTABLE,
                    Arguments = "--version",
                    UseShellExecute = false,
                    RedirectStandardOutput = true,
                    RedirectStandardError = true,
                    CreateNoWindow = true
                };

                using (var process = Process.Start(processStartInfo))
                {
                    process.WaitForExit();
                    var outputTask = process.StandardOutput.ReadToEndAsync();
                    var errorTask = process.StandardError.ReadToEndAsync();

                    var output = outputTask.Result;
                    var error = errorTask.Result;

                    // If there's an error, log it but don't fail the check
                    if (!string.IsNullOrWhiteSpace(error))
                    {
                        WriteVerbose($"Warning during Foundry Local installation check: {error.Trim()}");
                    }

                    return !output.Contains("The term 'foundry' is not recognized as the name of a cmdlet") && process.ExitCode == 0;
                }
            }
            catch (Exception ex)
            {
                WriteVerbose($"Error checking Foundry Local installation: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// Get the list of downloaded model IDs by checking the cache location's Microsoft directory
        /// </summary>
        private List<string> GetDownloadedModels()
        {
            try
            {
                string cacheLocation = GetFoundryCacheLocation();
                if (string.IsNullOrEmpty(cacheLocation))
                {
                    WriteVerbose("Could not determine cache location");
                    return new List<string>();
                }

                string microsoftDir = System.IO.Path.Combine(cacheLocation, "Microsoft");
                if (!System.IO.Directory.Exists(microsoftDir))
                {
                    WriteVerbose($"Microsoft directory not found at: {microsoftDir}");
                    return new List<string>();
                }

                var modelIds = new List<string>();
                var directories = System.IO.Directory.GetDirectories(microsoftDir);

                foreach (string dir in directories)
                {
                    string modelId = System.IO.Path.GetFileName(dir);
                    if (!string.IsNullOrEmpty(modelId))
                    {
                        modelIds.Add(modelId);
                    }
                }

                WriteVerbose($"Found {modelIds.Count} downloaded models in cache location");
                return modelIds;
            }
            catch (Exception ex)
            {
                WriteVerbose($"Could not read downloaded model IDs from cache location: {ex.Message}");
            }

            return new List<string>();
        }

        /// <summary>
        /// Sync the filesystem model list to the registry
        /// </summary>
        private void AddModelToRegistryList(string modelName)
        {
            try
            {
                var existingModels = GetDownloadedModels();
                // Convert the list to a comma-separated string for registry storage
                string modelListString = string.Join(",", existingModels);
                SetRegistryValue(AIMX_REGISTRY_KEYNAME_FOUNDRYLOCALMODELIDLIST, modelListString);
            }
            catch (Exception ex)
            {
                WriteError(new ErrorRecord(
                    new InvalidOperationException($"Failed to sync models to registry: {ex.Message}"),
                    "ModelRegistryUpdateFailed",
                    ErrorCategory.WriteError,
                    modelName));
            }
        }

        /// <summary>
        /// Set a registry value in the AIMX registry key
        /// </summary>
        private void SetRegistryValue(string valueName, object value)
        {
            try
            {
                using (var key = Registry.LocalMachine.CreateSubKey(AIMX_REGISTRY_PARAMETERS))
                {
                    if (key != null)
                    {
                        key.SetValue(valueName, value);
                        WriteVerbose($"Registry value set: {AIMX_REGISTRY_PARAMETERS}\\{valueName} = {value}");
                    }
                    else
                    {
                        throw new InvalidOperationException($"Could not create or open registry key: {AIMX_REGISTRY_PARAMETERS}");
                    }
                }
            }
            catch (UnauthorizedAccessException)
            {
                WriteError(new ErrorRecord(
                    new UnauthorizedAccessException("Access denied writing to registry. Please run PowerShell as Administrator."),
                    "RegistryAccessDenied",
                    ErrorCategory.PermissionDenied,
                    null));
            }
        }

        private void RemoveFoundryRegistryValues()
        {
            try
            {
                using (var key = Registry.LocalMachine.OpenSubKey(AIMX_REGISTRY_PARAMETERS, true))
                {
                    if (key != null)
                    {
                        key.DeleteValue(AIMX_REGISTRY_KEYNAME_FOUNDRYLOCALINSTALLED, false);
                        key.DeleteValue(AIMX_REGISTRY_KEYNAME_FOUNDRYLOCALPORT, false);
                        key.DeleteValue(AIMX_REGISTRY_KEYNAME_FOUNDRYLOCALMODELID, false);
                        key.DeleteValue(AIMX_REGISTRY_KEYNAME_FOUNDRYLOCALMODELIDLIST, false);
                    }
                }
            }
            catch (Exception ex)
            {
                WriteError(new ErrorRecord(
                    new InvalidOperationException($"Failed to remove registry values: {ex.Message}"),
                    "RegistryCleanupFailed",
                    ErrorCategory.WriteError,
                    null));
            }
        }

        /// <summary>
        /// Configure AIMXsrv Windows service - two-step process
        /// </summary>
        private void ConfigureAimxService()
        {
            // Step 1: Ask to start the service now
            if (!IsAimxServiceRunning() || !IsAimxServiceDelayedAutoStart())
            {
                WriteHost("");
                WriteHost("=== AIMXsrv Service Configuration ===");
                WriteHost("");
            }

            if (!IsAimxServiceRunning())
            {

                string startChoice = PromptForYesNoChoice("Start the AIMXsrv service now?");

                if (startChoice == "Y")
                {
                    StartAimxService();
                }
                else
                {
                    WriteHost("AIMXsrv service will not be started now, features may not function properly.");
                    WriteHost("You can manually start it later using: sc.exe start AIMXsrv");
                    WriteHost("");
                }
            }

            // Step 2: Ask about automatic startup
            if (!IsAimxServiceDelayedAutoStart())
            {
                string autoStartChoice = PromptForYesNoChoice("Would you like the AIMXsrv service to start automatically on boot? This will configure the service to start when Windows starts.");
                WriteHost("");


                if (autoStartChoice == "Y")
                {
                    ConfigureAutoStart();
                }
                else
                {
                    WriteHost("AIMXsrv service will remain with manual startup.");
                    WriteHost("You can change this later using: sc.exe config AIMXsrv start= delayed-auto");
                }
            }
        }

        /// <summary>
        /// Start the AIMXsrv Windows service
        /// </summary>
        private void StartAimxService()
        {
            WriteHost("");
            WriteHost("Starting AIMXsrv service...");

            try
            {
                var startProcessInfo = new ProcessStartInfo
                {
                    FileName = "sc.exe",
                    Arguments = "start AIMXsrv",
                    UseShellExecute = false,
                    RedirectStandardOutput = true,
                    RedirectStandardError = true,
                    CreateNoWindow = true
                };

                using (var startProcess = Process.Start(startProcessInfo))
                {
                    // Read output and error asynchronously to avoid deadlocks
                    var outputTask = startProcess.StandardOutput.ReadToEndAsync();
                    var errorTask = startProcess.StandardError.ReadToEndAsync();

                    // Wait for process to complete
                    startProcess.WaitForExit();

                    var startOutput = outputTask.Result;
                    var startError = errorTask.Result;

                    // Check for already running first
                    if (startOutput.Contains("An instance of the service is already running."))
                    {
                        WriteHost("AIMXsrv service was already running.");
                        return;
                    }

                    // Check for success - START_PENDING means the start command was accepted
                    if (startProcess.ExitCode == 0 && startOutput.Contains("START_PENDING"))
                    {
                        WriteHost("AIMXsrv service start initiated, waiting for service to become ready...");

                        int maxRetries = 6;
                        int currentRetry = 0;

                        while (currentRetry < maxRetries)
                        {
                            System.Threading.Thread.Sleep(5000); // Wait 5 seconds between checks

                            if (IsAimxServiceRunning())
                            {
                                WriteHost("AIMXsrv service started successfully!");
                                return;
                            }

                            currentRetry++;
                        }

                        // If we get here, service didn't start within timeout
                        WriteWarning("Service start command was accepted but service did not start within 30 seconds.");
                        WriteHost("The service may still be starting in the background.");
                        WriteHost("You can check status using: sc.exe query AIMXsrv");
                        return;
                    }

                    // Check for specific errors in stderr
                    if (!string.IsNullOrWhiteSpace(startError))
                    {
                        WriteWarning($"AIMXsrv service start error: {startError.Trim()}");
                        WriteHost("You can try to start it manually using: sc.exe start AIMXsrv");
                        return;
                    }

                    // Generic failure case
                    if (startProcess.ExitCode != 0)
                    {
                        WriteWarning($"Failed to start AIMXsrv service (Exit code: {startProcess.ExitCode})");
                        if (!string.IsNullOrWhiteSpace(startOutput))
                        {
                            WriteHost($"Output: {startOutput.Trim()}");
                        }
                        WriteHost("You can try to start it manually later using: sc.exe start AIMXsrv");
                    }
                    else
                    {
                        // Exit code 0 but no START_PENDING - unexpected case
                        WriteWarning("Service start completed with unexpected output:");
                        if (!string.IsNullOrWhiteSpace(startOutput))
                        {
                            WriteHost($"Output: {startOutput.Trim()}");
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                WriteError(new ErrorRecord(
                    ex,
                    "AimxServiceStartException",
                    ErrorCategory.NotSpecified,
                    null));

                WriteHost("Service start encountered an error.");
                WriteHost("You can manually start the service using: sc.exe start AIMXsrv");
            }
        }


        /// <summary>
        /// Configure AIMXsrv service for automatic startup
        /// </summary>
        private void ConfigureAutoStart()
        {
            WriteHost("Configuring AIMXsrv service for automatic startup...");

            try
            {
                var configProcessInfo = new ProcessStartInfo
                {
                    FileName = "sc.exe",
                    Arguments = "config AIMXsrv start= delayed-auto",
                    UseShellExecute = false,
                    RedirectStandardOutput = true,
                    RedirectStandardError = true,
                    CreateNoWindow = true
                };

                using (var configProcess = Process.Start(configProcessInfo))
                {
                    configProcess.WaitForExit();
                    var outputTask = configProcess.StandardOutput.ReadToEndAsync();
                    var errorTask = configProcess.StandardError.ReadToEndAsync();

                    var configOutput = outputTask.Result;
                    var configError = errorTask.Result;

                    // Check for errors first
                    if (!string.IsNullOrWhiteSpace(configError) || configProcess.ExitCode != 0)
                    {
                        // Log any other errors
                        {
                            WriteWarning("AIMXsrv service auto-start configuration encountered an error:");
                            WriteHost(configError.Trim());
                            WriteHost("You can manually configure automatic startup using: sc.exe config AIMXsrv start= delayed-auto");
                            return;
                        }
                    }

                    if (configOutput.Contains("SUCCESS"))
                    {
                        WriteHost("AIMXsrv service configured to start automatically (delayed) on boot.");
                    }
                }
            }
            catch (Exception ex)
            {
                WriteError(new ErrorRecord(
                    ex,
                    "AimxServiceAutoStartException",
                    ErrorCategory.NotSpecified,
                    null));

                WriteHost("Service configuration encountered an error.");
                WriteHost("You can manually configure automatic startup using: sc.exe config AIMXsrv start= delayed-auto");
            }
        }

        /// <summary>
        /// Prompt user for Yes/No choice with customizable message
        /// </summary>
        /// <param name="promptMessage">The message to display before (Y/N)</param>
        /// <returns>Returns "Y" for Yes or "N" for No</returns>
        private string PromptForYesNoChoice(string promptMessage)
        {
            while (true)
            {
                WriteHost($"{promptMessage} (Y/N), or 'exit' to quit: ", true);

                var choice = Host.UI.ReadLine();

                // Handle exit command
                if (IsExitCommand(choice))
                {
                    HandleUserCancellation();
                }

                // Handle default case (empty input) - default to Yes
                if (string.IsNullOrWhiteSpace(choice))
                {
                    return "Y";
                }

                var input = choice.Trim().ToLower();
                if (input == "y" || input == "yes" || input == "n" || input == "no")
                {
                    return input.StartsWith("y") ? "Y" : "N";
                }
            }
        }

        /// <summary>
        /// Check if AIMXsrv service is currently running
        /// </summary>
        /// <returns>True if service is running, false otherwise</returns>
        private bool IsAimxServiceRunning()
        {
            try
            {
                var processStartInfo = new ProcessStartInfo
                {
                    FileName = "sc.exe",
                    Arguments = "query AIMXsrv",
                    UseShellExecute = false,
                    RedirectStandardOutput = true,
                    RedirectStandardError = true,
                    CreateNoWindow = true
                };

                using (var process = Process.Start(processStartInfo))
                {
                    process.WaitForExit();
                    var outputTask = process.StandardOutput.ReadToEndAsync();
                    var errorTask = process.StandardError.ReadToEndAsync();

                    var output = outputTask.Result;
                    var error = errorTask.Result;

                    // If there's an error, the service likely doesn't exist
                    if (!string.IsNullOrWhiteSpace(error) || process.ExitCode != 0)
                    {
                        WriteVerbose($"AIMXsrv service query failed: {error?.Trim()}");
                        return false;
                    }

                    // Check if the service state is RUNNING
                    return output.Contains("STATE") && output.Contains("RUNNING");
                }
            }
            catch (Exception ex)
            {
                WriteVerbose($"Error checking AIMXsrv service status: {ex.Message}");
                return false;
            }
        }


        /// <summary>
        /// Check if AIMXsrv service startup type is set to delayed auto start
        /// </summary>
        /// <returns>True if startup type is delayed auto, false otherwise</returns>
        private bool IsAimxServiceDelayedAutoStart()
        {
            try
            {
                var processStartInfo = new ProcessStartInfo
                {
                    FileName = "sc.exe",
                    Arguments = "qc AIMXsrv",
                    UseShellExecute = false,
                    RedirectStandardOutput = true,
                    RedirectStandardError = true,
                    CreateNoWindow = true
                };

                using (var process = Process.Start(processStartInfo))
                {
                    process.WaitForExit();
                    var outputTask = process.StandardOutput.ReadToEndAsync();
                    var errorTask = process.StandardError.ReadToEndAsync();

                    var output = outputTask.Result;
                    var error = errorTask.Result;

                    // If there's an error, the service likely doesn't exist
                    if (!string.IsNullOrWhiteSpace(error) || process.ExitCode != 0)
                    {
                        WriteVerbose($"AIMXsrv service config query failed: {error?.Trim()}");
                        return false;
                    }

                    // Check for delayed auto start
                    return output.Contains("START_TYPE") && output.Contains("AUTO_START") && output.Contains("DELAYED");

                }
            }
            catch (Exception ex)
            {
                WriteVerbose($"Error checking AIMXsrv service startup type: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// Install RSAT Active Directory PowerShell module
        /// </summary>
        private void InstallActiveDirectoryModule()
        {
            string installChoice = PromptForYesNoChoice("Install the Active Directory PowerShell module (RSAT-AD-PowerShell)?");

            if (installChoice != "Y")
            {
                WriteHost("Active Directory PowerShell module will not be installed.");
                WriteHost("Some features may not be available without this module.");
                return;
            }

            WriteHost("");
            WriteHost("Installing Active Directory PowerShell module...");

            try
            {
                // Use PowerShell to install the RSAT-AD-PowerShell feature
                var installScript = "Install-WindowsFeature RSAT-AD-PowerShell";
                var results = InvokeCommand.InvokeScript(installScript);

                WriteHost("Active Directory PowerShell module installation completed.");

                // Display results if any
                foreach (var result in results)
                {
                    if (result != null)
                    {
                        WriteVerbose($"Install result: {result}");
                    }
                }

                // Try to import the module after successful installation
                WriteHost("");
                WriteHost("Attempting to import the Active Directory module...");
                if (ImportActiveDirectoryModule())
                {
                    if (IsActiveDirectoryModuleLoaded())
                    {
                        WriteHost("Active Directory PowerShell module is now ready for use.");
                        // Set registry value to indicate AD module is loaded
                        SetRegistryValue(AIMX_REGISTRY_KEYNAME_ADMODULELOADED, true);
                        WriteVerbose("Active Directory module status saved to registry.");
                    }
                }
                else
                {
                    WriteWarning("Module installation completed but import failed. You may need to restart PowerShell.");
                }
            }
            catch (Exception ex)
            {
                WriteError(new ErrorRecord(
                    ex,
                    "ActiveDirectoryModuleInstallFailed",
                    ErrorCategory.NotSpecified,
                    null));

                WriteHost("Failed to install Active Directory PowerShell module.");
                WriteHost("You can manually install it using: Install-WindowsFeature RSAT-AD-PowerShell");
                WriteHost("Then import it using: Import-Module ActiveDirectory");
            }
        }

        /// <summary>
        /// Check if ActiveDirectory PowerShell module is currently loaded
        /// </summary>
        /// <returns>True if module is loaded, false otherwise</returns>
        private bool IsActiveDirectoryModuleLoaded()
        {
            try
            {
                var checkScript = "Get-Module -ListAvailable -Name ActiveDirectory";
                var results = InvokeCommand.InvokeScript(checkScript);

                bool isLoaded = results.Count > 0;
                WriteVerbose($"ActiveDirectory module loaded: {isLoaded}");

                return isLoaded;
            }
            catch (Exception ex)
            {
                WriteVerbose($"Error checking if ActiveDirectory module is loaded: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// Import the ActiveDirectory PowerShell module
        /// </summary>
        /// <returns>True if import was successful, false otherwise</returns>
        private bool ImportActiveDirectoryModule()
        {
            WriteHost("Importing Active Directory PowerShell module...");

            try
            {
                var importScript = "Import-Module ActiveDirectory -WarningAction SilentlyContinue";
                var results = InvokeCommand.InvokeScript(importScript);

                // Verify the module was imported successfully
                if (IsActiveDirectoryModuleLoaded())
                {
                    WriteHost("Active Directory PowerShell module imported successfully.");
                    return true;
                }
                else
                {
                    WriteWarning("Active Directory PowerShell module import may have failed.");
                    return false;
                }
            }
            catch (Exception ex)
            {
                WriteError(new ErrorRecord(
                    ex,
                    "ActiveDirectoryModuleImportFailed",
                    ErrorCategory.NotSpecified,
                    null));

                WriteHost("Failed to import Active Directory PowerShell module.");
                WriteHost("You can manually import it using: Import-Module ActiveDirectory");
                return false;
            }
        }

        /// <summary>
        /// Write colored text to host
        /// </summary>
        private void WriteHost(string message, bool noNewline = false)
        {
            if (noNewline)
                Host.UI.Write(Host.UI.RawUI.ForegroundColor, Host.UI.RawUI.BackgroundColor, message);
            else
                Host.UI.WriteLine(Host.UI.RawUI.ForegroundColor, Host.UI.RawUI.BackgroundColor, message);
        }

        /// <summary>
        /// Get the total size of all files in a directory (recursive)
        /// </summary>
        /// <param name="directoryPath">Path to the directory</param>
        /// <returns>Total size in bytes, or 0 if error</returns>
        private long GetDirectorySize(string directoryPath)
        {
            try
            {
                if (!System.IO.Directory.Exists(directoryPath))
                {
                    return 0;
                }

                System.IO.DirectoryInfo dirInfo = new System.IO.DirectoryInfo(directoryPath);
                long totalSize = 0;

                // Get size of all files in this directory
                foreach (System.IO.FileInfo file in dirInfo.GetFiles("*", System.IO.SearchOption.AllDirectories))
                {
                    totalSize += file.Length;
                }

                return totalSize;
            }
            catch (Exception ex)
            {
                WriteVerbose($"Error calculating directory size for '{directoryPath}': {ex.Message}");
                return 0;
            }
        }

        /// <summary>
        /// Get available disk space for a given path
        /// </summary>
        /// <param name="path">Path to check (can be directory or file path)</param>
        /// <returns>Available space in bytes, or 0 if error</returns>
        private long GetAvailableDiskSpace(string path)
        {
            try
            {
                string rootPath = System.IO.Path.GetPathRoot(System.IO.Path.GetFullPath(path));
                System.IO.DriveInfo drive = new System.IO.DriveInfo(rootPath);
                return drive.AvailableFreeSpace;
            }
            catch (Exception ex)
            {
                WriteVerbose($"Error getting available disk space for '{path}': {ex.Message}");
                return 0;
            }
        }

        /// <summary>
        /// Check if the AimxSrvStartFoundry scheduled task already exists
        /// </summary>
        /// <returns>True if the scheduled task exists, false otherwise</returns>
        private bool IsFoundryScheduledTaskExists()
        {
            try
            {
                WriteVerbose("Checking if AimxSrvStartFoundry scheduled task exists...");

                var checkScript = @"
                    try {
                        $task = Get-ScheduledTask -TaskName 'AimxSrvStartFoundry' -TaskPath '\Microsoft\Windows\AimxSrv\' -ErrorAction SilentlyContinue
                        if ($task) {
                            Write-Output 'TaskExists'
                        } else {
                            Write-Output 'TaskNotFound'
                        }
                    } catch {
                        Write-Output 'TaskNotFound'
                    }
                ";

                var results = InvokeCommand.InvokeScript(checkScript);

                foreach (var result in results)
                {
                    if (result != null && result.ToString().Trim() == "TaskExists")
                    {
                        WriteVerbose("AimxSrvStartFoundry scheduled task found.");
                        return true;
                    }
                }

                WriteVerbose("AimxSrvStartFoundry scheduled task not found.");
                return false;
            }
            catch (Exception ex)
            {
                WriteVerbose($"Error checking for scheduled task: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// Ask the user if they want to create a scheduled task to start Foundry on reboot
        /// </summary>
        private void ConfigureFoundryScheduledTask()
        {
            WriteHost("");
            WriteHost("=== Foundry Scheduled Task ===");
            WriteHost("");
            WriteHost("Foundry Local can be configured to automatically start when your computer reboots.");
            WriteHost("");
            WriteHost("This will ensure that Foundry is always available after system restarts.");
            WriteHost("If not, manual startup of the Foundry service will be required each time you reboot your computer.");
            WriteHost("");

            string response = PromptForYesNoChoice("Create scheduled task?");

            if (response == "Y")
            {
                try
                {
                    WriteHost("");
                    WriteHost("Creating scheduled task...");

                    // Execute the PowerShell script to create the scheduled task
                    var results = InvokeCommand.InvokeScript(FOUNDRY_SCHEDULED_TASK_SCRIPT);
                    bool taskExists = IsFoundryScheduledTaskExists();
                    if (!taskExists)
                    {
                        WriteWarning("Failed to create scheduled task 'AimxSrvStartFoundry'. Please ensure you are using a local user account in the Local Administrators group.");
                        return;
                    }

                    WriteVerbose($"Scheduled task 'AimxSrvStartFoundry' created in Task Scheduler folder '{FOUNDRY_TASK_FOLDER}' to run foundry service at startup.");

                    WriteHost("Successfully created scheduled task 'AimxSrvStartFoundry'.");

                    // Display results if any
                    foreach (var result in results)
                    {
                        if (result != null)
                        {
                            WriteVerbose($"Script output: {result}");
                        }
                    }
                }
                catch (Exception ex)
                {
                    WriteVerbose($"Full exception: {ex}");
                }
            }
            else
            {
                WriteHost("Skipping scheduled task creation.");
                WriteHost("You can manually start foundry with the command: 'foundry service start' after rebooting your computer.");
            }
        }


        /// <summary>
        /// Check if the currently logged in user is a domain administrator
        /// </summary>
        /// <returns>True if the user is a domain admin, false otherwise</returns>
        private bool IsCurrentUserDomainAdmin()
        {
            try
            {
                WriteVerbose("Checking if current user is a domain administrator...");

                var processStartInfo = new ProcessStartInfo
                {
                    FileName = WHOAMI_EXECUTABLE,
                    Arguments = "/groups",
                    UseShellExecute = false,
                    RedirectStandardOutput = true,
                    RedirectStandardError = true,
                    CreateNoWindow = true
                };

                using (var process = Process.Start(processStartInfo))
                {
                    process.WaitForExit();
                    var outputTask = process.StandardOutput.ReadToEndAsync();
                    var errorTask = process.StandardError.ReadToEndAsync();

                    var output = outputTask.Result;
                    var error = errorTask.Result;

                    if (process.ExitCode == 0 && !string.IsNullOrWhiteSpace(output))
                    {
                        // Check if output contains "Domain Admins" (case insensitive)
                        bool isDomainAdmin = output.IndexOf(DOMAIN_ADMINS_GROUP, StringComparison.OrdinalIgnoreCase) >= 0;

                        WriteVerbose($"Domain admin check result: {isDomainAdmin}");
                        return isDomainAdmin;
                    }

                    if (!string.IsNullOrWhiteSpace(error))
                    {
                        WriteVerbose($"Error checking domain admin status: {error.Trim()}");
                    }
                }

                WriteVerbose("Current user is not a domain administrator.");
                return false;
            }
            catch (Exception ex)
            {
                WriteVerbose($"Error checking domain admin status: {ex.Message}");
                return false;
            }
        }
    }
}