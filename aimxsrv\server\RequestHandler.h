/*++

Copyright (c) Microsoft Corporation. All rights reserved.

Module Name:
    RequestHandler.h

Abstract:
    Header file for AIMX Request Handler that processes different types of requests
    including chatbot queries, direct queries, plan status, execution, and cancellation.

Author:

    <PERSON> (SNAKE FIGHTER) (lindakup) 06/03/2025

--*/

#pragma once
#include "AimxCommon.h"
#include <nlohmann/json.hpp>
#include "McpJsonRpc.h"
#include <unordered_map>
#include <mutex>

// Global map to track OperationId -> Context GUID
extern std::unordered_map<GUID, GUID, GuidHash, GuidEqual> g_OperationToContextMap;
extern std::mutex g_OperationToContextMapMutex;

class RequestHandler
{
public:

    // Unified entry point for all request types
    static HRESULT ProcessRequest(
        _In_ AIMXR_HANDLE contextHandle,
        _In_ const nlohmann::json& requestJson,
        _Out_ nlohmann::json& responseJson
        );

private:

    static HRESULT ProcessChatbotQuery(
        _In_ AIMXR_HANDLE contextHandle,
        _In_ const nlohmann::json& requestJson,
        _Out_ nlohmann::json& responseJson
        );

    static HRESULT ProcessDirectQuery(
        _In_ AIMXR_HANDLE contextHandle,
        _In_ const nlohmann::json& requestJson,
        _Out_ nlohmann::json& responseJson
        );

    static HRESULT ProcessPlanStatusQuery(
        _In_ AIMXR_HANDLE contextHandle,
        _In_ const nlohmann::json& requestJson,
        _Out_ nlohmann::json& responseJson
        );

    static HRESULT ProcessExecutePlan(
        _In_ AIMXR_HANDLE contextHandle,
        _In_ const nlohmann::json& requestJson,
        _Out_ nlohmann::json& responseJson
        );

    static HRESULT ProcessCancelOperation(
        _In_ AIMXR_HANDLE contextHandle,
        _In_ const nlohmann::json& requestJson,
        _Out_ nlohmann::json& responseJson
        );

    static HRESULT ValidateCommonFields(
        _In_ const nlohmann::json& requestJson
        );

    static void CreateErrorResponse(
        _Out_ nlohmann::json& responseJson,
        _In_ HRESULT errorCode,
        _In_ const std::string& errorMessage
        );

    // Helper to validate OperationId belongs to context GUID
    static bool IsOperationOwnedByContext(const GUID& operationId, const GUID& contextGuid);
};
