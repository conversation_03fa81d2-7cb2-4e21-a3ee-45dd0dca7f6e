BUILD COMMAND: e:\os\tools\CoreBuild\amd64\build.exe  -parent

Reading historical build information...
Reading historical build information completed in [0:00:00.000]
BuildExe GUID: {D455CE5E-E87C-4189-B638-60FF830D9E7E}
Launching process: e:\os\tools\CoreBuild\amd64\tracer.exe  /skip:1 -f:.\buildfre.trc -guid:e:\os\src\registered_data.ini   /logPrefix:buildfre /c:"e:\os\tools\CoreBuild\amd64\buildc.exe" /s:localhost:29026  
1>  *************
1>'e:\os\tools\CoreBuild\amd64\tracer.exe  /skip:1 -f:.\buildfre.trc -guid:e:\os\src\registered_data.ini   /logPrefix:buildfre /c:"e:\os\tools\CoreBuild\amd64\buildc.exe" /s:localhost:29026  '
1>info: Microsoft.Internal.Trace.Tracer.EtwTraceAdapter[0]
1>      Processing Build Trace File Logger *************-4aa0-a34e-55f9c8ec57e3
Merging config files using BUILD_CONFIG_FILE=e:\os\obj\amd64fre\objfre\amd64\build-exe-merged.config
BUILD: (ActiveWorkLoad)*, ElapsedTime(s),Counter,Available Memory (%),Disk I/O (ms),Disk Usage (%),CPU (%),Previous,New,Last Max,Permitted Threads,ThreadPool Memory Footprint (bytes), Max Memory Allowed (bytes), Submitted Thread,Running Threads,WorkItems (Available), WorkItems (Waiting),Pass,Priority,Peak Memory Project (bytes),Directory,MachineName
1>  1>[0:00:00.672] [Pass0 ] (none) {1}
3001>Merging config files  *************
3001>'mergeconfigfilesforbuildexe.cmd '
3001>MergeConfigFilesForBuildExe.cmd: Merged config file current: [e:\os\obj\amd64fre\objfre\amd64\build-exe-merged.config].
Executing PreGraph commands  *************
1>Performing pregraph steps...  *************
1>'build_pre_graph '
1>(build_pre_graph.cmd) e:\os\tools\NodeJS.x64\node.exe e:\os\src\tools\nmakejs\Transpile.js
1>(build_pre_graph.cmd) e:\os\tools\NodeJS.x64\node.exe e:\os\obj\amd64fre\objfre\amd64\NMakeJS\PreGraph.js
1>warn: Microsoft.Internal.Trace.Tracer.EtwTraceAdapter[0]
1>      Task table not implemented
1>(build_pre_graph.cmd) Determining best branch for vpack with prefix "cdg" and suffix "amd64fre"...
1>(build_pre_graph.cmd) Latest vPack "cdg.rs_wsd_cfe_adai.amd64fre" version from label file e:\os\src\sdpublic\misc\Labels\rs_wsd_cfe_adai_label.xml is 27900.1000.2507111452
1>(build_pre_graph.cmd) Preferring this branch's vpack information
1>(build_pre_graph.cmd) Latest vPack "cdg.rs_wsd_cfe_adai.amd64fre" version from label file e:\os\src\sdpublic\misc\Labels\rs_wsd_cfe_adai_label.xml is 27900.1000.2507111452
1>(build_pre_graph.cmd) Not pulling vpack cdg.rs_wsd_cfe_adai.amd64fre because it is up-to-date (matches the marker file at e:\os\cdg\Target.cdg-amd64fre.man).
1>(build_pre_graph.cmd) Completed successfully.
Pre-Graph completed in [0:00:01.282]
Executing preprocess commands  *************
1>(build_pre_process.cmd) Determining best branch for vpack with prefix "publics" and suffix "amd64"...
1>(build_pre_process.cmd) Latest vPack "publics.rs_wsd_cfe_adai.amd64" version from label file e:\os\src\sdpublic\misc\Labels\rs_wsd_cfe_adai_label.xml is 27900.1000.2507111406
1>(build_pre_process.cmd) Preferring this branch's vpack information
1>(build_pre_process.cmd) Latest vPack "publics.rs_wsd_cfe_adai.amd64" version from label file e:\os\src\sdpublic\misc\Labels\rs_wsd_cfe_adai_label.xml is 27900.1000.2507111406
1>(build_pre_process.cmd) First run of UPDATE_PUBLIC since the overlay was resumed by razzle.cmd
1>BUILDMSG: build_pre_process: Updating publics @ e:\os\public\amd64fre
1>(build_pre_process.cmd) Running 'urtrun64 4.Latest e:\os\tools\OSPublics\OSPublics.exe UpdateOverlay /ManifestId:amd64fre-9fcb-95d09ff26d8a7535484d193c21effb73 /BaseManifestId:publics.rs_wsd_cfe_adai.amd64.27900.1000.2507111406 /Arch:amd64 /PublicRoot:e:\os\public\amd64fre /PublicChangesExternalPath:e:\os\src\tools\publicchanges.external.txt /PublishRulesPath:e:\os\src\.config\onecoreuap\build\PublishRules.json'
1>OSPublics.UpdateOverlay: Information: Version=0.0.0.0.
1>OSPublics.UpdateOverlay: Information: Parsing rules ..
1>OSPublics.UpdateOverlay: Information: [00.2s] DONE: Parsing rules ..
1>OSPublics.UpdateOverlay: Information: Starting overlay daemon...
1>OSPublics.UpdateOverlay: Information: [00.1s] DONE: Starting overlay daemon...
1>OSPublics.UpdateOverlay: Information: Starting overlay of 'publics.rs_wsd_cfe_adai.amd64.27900.1000.2507111406' to 'e:\os\public\amd64fre'...
1>OSPublics.UpdateOverlay: LogAlways: {"contentManifestId":"amd64fre-9fcb-95d09ff26d8a7535484d193c21effb73","forceRefresh":false}
1>OSPublics.UpdateOverlay: LogAlways: {"contentSize":115458,"filesOnly":true}
1>OSPublics.UpdateOverlay: LogAlways: {"succeeded":true,"callEndData":"","elapsedCallTimeInMilliseconds":655}
1>OSPublics.UpdateOverlay: LogAlways: {"succeeded":true,"callEndData":"","elapsedCallTimeInMilliseconds":1317}
1>OSPublics.UpdateOverlay: Information: [04.0s] DONE: Starting overlay of 'publics.rs_wsd_cfe_adai.amd64.27900.1000.2507111406' to 'e:\os\public\amd64fre'...
1>OSPublics.UpdateOverlay: Information: Completed in 04.4s.
1>BUILDMSG: build_pre_process: Publics update complete [elapsed=5s].
1>(build_pre_process.cmd) e:\os\tools\NodeJS.x64\node.exe e:\os\obj\amd64fre\objfre\amd64\NMakeJS\PreProcess.js
1>Possible precedence issue with control flow operator (exit) at e:\os\src\tools\xbox\ReplicaTool.cmd line 247.
1>(ReplicaTool.cmd) [07/14/25 12:23:16] Starting execution
1>(ReplicaTool.cmd)   Full LNM replica verification is active -- 'disable.lnm.replicas' build setting is not set / set to false.
1>(ReplicaTool.cmd)   Tool will verify that files are in sync
1>(ReplicaTool.cmd)   Using replica map "e:\os\src\xbox\data\replica_maps\default.txt" from the default value
1>(ReplicaTool.cmd)   Using IDK (onecore) repository root "e:\os\src" from the BASEDIR environment variable
1>(ReplicaTool.cmd)   Using Edition (xbox) repository root "e:\os\src" from the BASEDIR environment variable
1>(ReplicaTool.cmd) Reading replica map...
1>(ReplicaTool.cmd) Expanding replica map...
1>(ReplicaTool.cmd) Scanning onecore\drivers\wdm\usb\platformdetection\... => xbox\replicas\usb\platformdetection\...
1>(ReplicaTool.cmd) Scanning onecore\net\flowsteering\... => xbox\replicas\net\flowsteering\...
1>(ReplicaTool.cmd) Excluding onecore\drivers\wdm\usb\platformdetection\...\.vs\... => xbox\replicas\usb\platformdetection\...\.vs\...
1>(ReplicaTool.cmd) Excluding onecore\drivers\wdm\usb\platformdetection\...\dirs => xbox\replicas\usb\platformdetection\...\dirs
1>(ReplicaTool.cmd) Excluding onecore\drivers\wdm\usb\platformdetection\.vs\... => xbox\replicas\usb\platformdetection\.vs\...
1>(ReplicaTool.cmd) Excluding onecore\drivers\wdm\usb\platformdetection\dirs => xbox\replicas\usb\platformdetection\dirs
1>(ReplicaTool.cmd) Excluding onecore\net\flowsteering\...\.vs\... => xbox\replicas\net\flowsteering\...\.vs\...
1>(ReplicaTool.cmd) Excluding onecore\net\flowsteering\...\.vscode\... => xbox\replicas\net\flowsteering\...\.vscode\...
1>(ReplicaTool.cmd) Excluding onecore\net\flowsteering\...\dirs => xbox\replicas\net\flowsteering\...\dirs
1>(ReplicaTool.cmd) Excluding onecore\net\flowsteering\...\sources => xbox\replicas\net\flowsteering\...\sources
1>(ReplicaTool.cmd) Excluding onecore\net\flowsteering\...\sources.dep => xbox\replicas\net\flowsteering\...\sources.dep
1>(ReplicaTool.cmd) Excluding onecore\net\flowsteering\...\sources.inc => xbox\replicas\net\flowsteering\...\sources.inc
1>(ReplicaTool.cmd) Excluding onecore\net\flowsteering\.vs\... => xbox\replicas\net\flowsteering\.vs\...
1>(ReplicaTool.cmd) Excluding onecore\net\flowsteering\.vscode\... => xbox\replicas\net\flowsteering\.vscode\...
1>(ReplicaTool.cmd) Excluding onecore\net\flowsteering\FlowsteeringReplicaMap.txt => xbox\replicas\net\flowsteering\FlowsteeringReplicaMap.txt
1>(ReplicaTool.cmd) Excluding onecore\net\flowsteering\base\um_win\... => xbox\replicas\net\flowsteering\base\um_win\...
1>(ReplicaTool.cmd) Excluding onecore\net\flowsteering\core\src\usrlib\... => xbox\replicas\net\flowsteering\core\src\usrlib\...
1>(ReplicaTool.cmd) Excluding onecore\net\flowsteering\core\ut\... => xbox\replicas\net\flowsteering\core\ut\...
1>(ReplicaTool.cmd) Excluding onecore\net\flowsteering\dirs => xbox\replicas\net\flowsteering\dirs
1>(ReplicaTool.cmd) Excluding onecore\net\flowsteering\engine\... => xbox\replicas\net\flowsteering\engine\...
1>(ReplicaTool.cmd) Excluding onecore\net\flowsteering\kd\... => xbox\replicas\net\flowsteering\kd\...
1>(ReplicaTool.cmd) Excluding onecore\net\flowsteering\porttracker\allocator\perf\... => xbox\replicas\net\flowsteering\porttracker\allocator\perf\...
1>(ReplicaTool.cmd) Excluding onecore\net\flowsteering\porttracker\allocator\src\km\... => xbox\replicas\net\flowsteering\porttracker\allocator\src\km\...
1>(ReplicaTool.cmd) Excluding onecore\net\flowsteering\porttracker\allocator\src\um\... => xbox\replicas\net\flowsteering\porttracker\allocator\src\um\...
1>(ReplicaTool.cmd) Excluding onecore\net\flowsteering\porttracker\allocator\test\... => xbox\replicas\net\flowsteering\porttracker\allocator\test\...
1>(ReplicaTool.cmd) Excluding onecore\net\flowsteering\porttracker\allocator\um\... => xbox\replicas\net\flowsteering\porttracker\allocator\um\...
1>(ReplicaTool.cmd) Excluding onecore\net\flowsteering\porttracker\client\test\... => xbox\replicas\net\flowsteering\porttracker\client\test\...
1>(ReplicaTool.cmd) Excluding onecore\net\flowsteering\porttracker\server\test\... => xbox\replicas\net\flowsteering\porttracker\server\test\...
1>(ReplicaTool.cmd) Excluding onecore\net\flowsteering\porttracker\test\... => xbox\replicas\net\flowsteering\porttracker\test\...
1>(ReplicaTool.cmd) Excluding onecore\net\flowsteering\replica-command.txt => xbox\replicas\net\flowsteering\replica-command.txt
1>(ReplicaTool.cmd) Excluding onecore\net\flowsteering\test\... => xbox\replicas\net\flowsteering\test\...
1>(ReplicaTool.cmd) Excluding onecore\net\flowsteering\testlists\... => xbox\replicas\net\flowsteering\testlists\...
1>Skipping pull of Xbox BBT manifest
1> set BUILDMSG=Checking publicchanges.external.txt
1> e:\os\tools\perl\bin\perl.exe e:\os\src\tools\configure_publicchanges.pl -error
1>The system cannot find the path specified.
1>NMAKE : fatal error U1077: 'e:\os\tools\perl\bin\perl.exe e:\os\src\tools\configure_publicchanges.pl -error' : return code '0x1'
1>NMAKE : fatal error U1077: 'e:\os\tools\perl\bin\perl.exe e:\os\src\tools\configure_publicchanges.pl -error' : return code '0x1'
1>Stop.
1>e:\os\src\tools\onecoreuap\internal\CoreBuild\PreProcess.nmake(101,3): location
1>>>     $(PERL) $(RAZZLETOOLPATH)\configure_publicchanges.pl -error
1>>> __^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
1>e:\os\src\tools\onecoreuap\internal\CoreBuild\PreProcess.nmake(99,1): location
1>>> $(OBJECT_ROOT)\$(_BUILDALT)\check_publicchanges: $(RAZZLETOOLPATH)\publicchanges.external.txt $(RAZZLETOOLPATH)\configure_publicchanges.pl
1>>> ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
1>e:\os\src\tools\onecoreuap\internal\CoreBuild\PreProcess.nmake(6,1): location
1>>> all: \
1>>> ^^^
1>(build_pre_process.cmd) : ERROR: "e:\os\tools\NodeJS.x64\node.exe e:\os\obj\amd64fre\objfre\amd64\NMakeJS\PreProcess.js" => e:\os\tools\NodeJS.x64\node.exe failed with ExitCode=1. See details @ buildfre.log.
1>(build_pre_process.cmd)error  "e:\os\tools\NodeJS.x64\node.exe e:\os\obj\amd64fre\objfre\amd64\NMakeJS\PreProcess.js" => e:\os\tools\NodeJS.x64\node.exe failed with ExitCode=1. See details @ buildfre.log.
1>build_pre_process  failed - rc = 0x00000001

DBB MODE: Retrieving dependency information. Please wait...
Examining e:\os\src\onecore\ds\ds\src\aimx\prod\aimxsrv\server 
ScanSourceDirectories: Total dirs files scanned = 0
Scanning focus directories completed in [0:00:00.016]
Processing time to write metadata log file .\buildfre.metadata = 0.031 Secs

BUILD: Scanning directories in defined scope...
Examining e:\os\src (directory tree)
ScanSourceDirectories: Total dirs files scanned = 8
BUILD: Processing dependencies...
PERF: Waiting for performance monitor thread to terminate.
PERF: Terminating perf data collector thread.
1>info: Microsoft.Internal.Trace.Database.Core.TraceManager[0]
1>      Finalizing the trace reading...
1>info: Microsoft.Internal.Trace.Database.Core.AccessManager[0]
1>      Total Accesses = 722
1>info: Microsoft.Internal.Trace.Database.Core.FileManager[0]
1>      Total Files = 689
1>info: Microsoft.Internal.Trace.Database.Core.ProcessManager[0]
1>      Total Processes = 4
1>info: Microsoft.Internal.Trace.Tracer.EtwTraceAdapter[0]
1>      Total Process Time = 0.40625
1>info: Microsoft.Internal.Trace.Tracer.EtwTraceAdapter[0]
1>      (Logging time = 0.0142197, Detour time = 0.033233200000000004
1>info: Microsoft.Internal.Trace.Database.Core.TraceManager[0]
1>      Finalizing the trace reading...
1>info: Microsoft.Internal.Trace.Database.Core.DirectoryManager[0]
1>      Analyzing trace to infer dependencies...
1>info: Microsoft.Internal.Trace.Database.IO.BinaryTraceWriter[0]
1>      Writing e:\os\src\onecore\ds\ds\src\aimx\prod\aimxsrv\server\buildfre.trc
1>info: Microsoft.Internal.Trace.Database.IO.Table6[0]
1>      File table total write time = 00:00:00.0456174
1>info: Microsoft.Internal.Trace.Database.IO.Table6[0]
1>      File total bytes written = 19496
1>info: Microsoft.Internal.Trace.Database.IO.Table6[0]
1>      Process table total write time = 00:00:00.0091199
1>info: Microsoft.Internal.Trace.Database.IO.Table6[0]
1>      Process total bytes written = 2330
1>info: Microsoft.Internal.Trace.Database.IO.Table6[0]
1>      Access table total write time = 00:00:00.0023663
1>info: Microsoft.Internal.Trace.Database.IO.Table6[0]
1>      Access total bytes written = 3344
1>info: Microsoft.Internal.Trace.Database.IO.Table6[0]
1>      Task table total write time = 00:00:00.0004099
1>info: Microsoft.Internal.Trace.Database.IO.Table6[0]
1>      Task total bytes written = 21
1>info: Microsoft.Internal.Trace.Database.IO.Table6[0]
1>      EnvironmentAccess table total write time = 00:00:00.0003580
1>info: Microsoft.Internal.Trace.Database.IO.Table6[0]
1>      EnvironmentAccess total bytes written = 21
1>info: Microsoft.Internal.Trace.Database.IO.BinaryTraceWriter[0]
1>      Trace file written to e:\os\src\onecore\ds\ds\src\aimx\prod\aimxsrv\server\buildfre.trc
1>TRACER : Microsoft (R) Build Tracer
1>TRACER : Copyright (C) Microsoft Corporation. All rights reserved.
1>TRACER : Starting Realtime Build Trace File Logger *************-4aa0-a34e-55f9c8ec57e3...
1>TRACER : Enabling trace provider...
1>TRACER (BuildSocket): RegisterClient rupo-dell:0 (Tracer)
1>TRACER : Tracer Satellite: Satellite command disabled. (No value in environment variable TRACER_SATELLITE_COMMAND)
1>TRACER : Launching: "e:\os\tools\corebuild\amd64\buildc.exeTRACER :  /hostname localhost /hostport 29026TRACER : "
1>TRACER : Tracer Satellite: Stop Process: Nothing to do.
1>TRACER : Disabling trace provider...
1>TRACER (event): ETW Trace Session
1>TRACER (event): =================
1>TRACER (event): Buffers Allocated: 32
1>TRACER (event): Buffers Written: 16
1>TRACER (event): Buffer Size: 512KB
1>TRACER (event): Buffers Lost: 0
1>TRACER (event): Real Time Buffers Lost: 0
1>TRACER (event): Events Lost: 0
1>TRACER : Stopping Build Trace File Logger *************-4aa0-a34e-55f9c8ec57e3...
1>Running analyzer on build trace...
  *************
1>'toolredirector.exe analyzernative -merge:e:\os\obj\amd64fre\objfre\amd64\build.ldg -reportconfig:e:\os\src\build\config\core\dbb_report_config.xml -in:bin e:\os\src\onecore\ds\ds\src\aimx\prod\aimxsrv\server\buildfre.trc'
1>ANALYZER : Microsoft (R) Build Trace Analyzer [Build 8.0.250519001+6b8e35f5c0ee29c4c18a354e33cc8b695d5695ad]
1>ANALYZER : Copyright (C) Microsoft Corporation. All rights reserved.
1>[12:23:25.971] Parsing error policy from 'e:\os\src\build\config\Core\AnalyzerEnforcedErrors.json'...
1>TRACEREPORT : Processing e:\os\src\build\config\core\dbb_report_config.xml
1>ANALYZER : Processing e:\os\src\build\config\Core\dbb_exclusions.xml
1>ANALYZER : ---------------------------
1>Reading input file
1>---------------------------
1>ANALYZER : Processing e:\os\src\onecore\ds\ds\src\aimx\prod\aimxsrv\server\buildfre.trc
1>ANALYZER : 
1>Read access table time = 0.000s
1>ANALYZER : 
1>Trace start time: 07/14/2025 12:23:07 PM
1>ANALYZER : 
1>Finalizing the trace reading...
1>ANALYZER : Total Files = 689ANALYZER : 
1>ANALYZER : Total Processes = 4ANALYZER : 
1>ANALYZER : Total Accesses = 722ANALYZER : 
1>ANALYZER : Total parse time = 0.008s
1>ANALYZER : Loading additional trace file e:\os\obj\amd64fre\objfre\amd64\build.ldg
1>ANALYZER : Processing e:\os\obj\amd64fre\objfre\amd64\build.ldg
1>ANALYZER : Parse time (e:\os\obj\amd64fre\objfre\amd64\build.ldg) = 0.002s
1>ANALYZER : Merge time = 0.001s
1>ANALYZER : 
1>Finalizing the trace reading...
1>ANALYZER : Total Files = 689ANALYZER : 
1>ANALYZER : Total Accesses = 722ANALYZER : 
1>ANALYZER : Total analysis time = 0.001s
1>ANALYZER : Write time (e:\os\obj\amd64fre\objfre\amd64\build.ldg) = 0.001s
1>DirList is empty (was cdg/ldg loaded, and no trc?).
1>TRACEREPORT : Parsing Baseline Files...
1>TRACEREPORT : Processing e:\os\src\.config\Desktop\build\dbb_report_baselines.xml
1>TRACEREPORT (configparser): The baseline file 'e:\os\src\.config\GameCore\build\dbb_report_baselines.xml' was not found. Ignoring it.
1>TRACEREPORT (configparser): The baseline file 'e:\os\src\.config\IOT\build\dbb_report_baselines.xml' was not found. Ignoring it.
1>TRACEREPORT : Processing e:\os\src\.config\NanoServer\build\dbb_report_baselines.xml
1>TRACEREPORT : Processing e:\os\src\.config\OSClient\build\dbb_report_baselines.xml
1>TRACEREPORT : Processing e:\os\src\.config\OneCore\build\dbb_report_baselines.xml
1>TRACEREPORT : Processing e:\os\src\.config\PCShell\build\dbb_report_baselines.xml
1>TRACEREPORT : Processing e:\os\src\.config\ServerCommon\build\dbb_report_baselines.xml
1>TRACEREPORT : Processing e:\os\src\.config\Server\build\dbb_report_baselines.xml
1>TRACEREPORT : Processing e:\os\src\.config\ShellCommonDesktopBase\build\dbb_report_baselines.xml
1>TRACEREPORT : Processing e:\os\src\.config\Xbox\build\dbb_report_baselines.xml
1>TRACEREPORT : Processing e:\os\src\.config\clientcore\build\dbb_report_baselines.xml
1>TRACEREPORT : Processing e:\os\src\.config\editions\build\dbb_report_baselines.xml
1>TRACEREPORT : Processing e:\os\src\.config\onecoreuap\build\dbb_report_baselines.xml
1>TRACEREPORT : Processing e:\os\src\.config\shellcommon\build\dbb_report_baselines.xml
1>TRACEREPORT : Processing e:\os\src\.config\xbox\build\dbb_report_baselines.xml
1>TRACEREPORT : Generating reports...
1>TRACEREPORT : Processing e:\os\cdg\amd64fre\build.cdg
1>TRACEREPORT : 
1>Finalizing the trace reading...
1>TRACEREPORT : Total Files = 115446TRACEREPORT : 
1>TRACEREPORT : Running all reports in parallel...
1>TRACEREPORT : Reports may call libraries that use the global logger, and hence the parallelism may cause output to commingle.
1>TRACEREPORT : All report-specific output will be buffered and printed later.
1>TRACEREPORT : Started running the 'badpublish-cs' report in parallel.
1>TRACEREPORT : Started running the 'buildrules-cs' report in parallel.
1>TRACEREPORT : Started running the 'chunkanalyzer-cs' report in parallel.
1>TRACEREPORT : Started running the 'crosslayer-cs' report in parallel.
1>TRACEREPORT : Started running the 'externalaccess-cs' report in parallel.
1>TRACEREPORT : Started running the 'illegalprocess' report in parallel.
1>TRACEREPORT : Started running the 'includes-cs' report in parallel.
1>TRACEREPORT : Started running the 'multiwritestrict-cs' report in parallel.
1>TRACEREPORT : Started running the 'nttree-cs' report in parallel.
1>TRACEREPORT : Started running the 'objroot-cs' report in parallel.
1>TRACEREPORT : Started running the 'ostools-cs' report in parallel.
1>TRACEREPORT : Started running the 'restricteddependency-cs' report in parallel.
1>TRACEREPORT : Started running the 'sdxwrite-cs' report in parallel.
1>TRACEREPORT : Started running the 'sourcesdep-cs' report in parallel.
1>TRACEREPORT : Started running the 'tempfiles-cs' report in parallel.
1>TRACEREPORT : Started running the 'tools-cs' report in parallel.
1>TRACEREPORT : Started running the 'unc-cs' report in parallel.
1>TRACEREPORT : Started running the 'vpacknttree-cs' report in parallel.
1>TRACEREPORT : Started running the 'vpackstrict-cs' report in parallel.
1>TRACEREPORT : Finished running the 'illegalprocess' report in parallel:
1>(illegalprocess) Return Code = 0 (success).
1>(illegalprocess) Runtime = 0.020s.
1>(illegalprocess) Errors = 0.
1>(illegalprocess) Baselined Errors = 0.
1>(illegalprocess) Suppressed Errors = 0.
1>(illegalprocess) Messages = 0.
1>TRACEREPORT : Finished running the 'nttree-cs' report in parallel:
1>(nttree-cs) Return Code = 0 (success).
1>(nttree-cs) Runtime = 0.020s.
1>(nttree-cs) Errors = 0.
1>(nttree-cs) Baselined Errors = 0.
1>(nttree-cs) Suppressed Errors = 0.
1>(nttree-cs) Messages = 0.
1>TRACEREPORT : Finished running the 'unc-cs' report in parallel:
1>(unc-cs) Return Code = 0 (success).
1>(unc-cs) Runtime = 0.019s.
1>(unc-cs) Errors = 0.
1>(unc-cs) Baselined Errors = 0.
1>(unc-cs) Suppressed Errors = 0.
1>(unc-cs) Messages = 0.
1>TRACEREPORT : Finished running the 'tools-cs' report in parallel:
1>(tools-cs) Return Code = 0 (success).
1>(tools-cs) Runtime = 0.019s.
1>(tools-cs) Errors = 0.
1>(tools-cs) Baselined Errors = 0.
1>(tools-cs) Suppressed Errors = 0.
1>(tools-cs) Messages = 0.
1>TRACEREPORT : Finished running the 'crosslayer-cs' report in parallel:
1>(crosslayer-cs) Return Code = 0 (success).
1>(crosslayer-cs) Runtime = 0.020s.
1>(crosslayer-cs) Errors = 0.
1>(crosslayer-cs) Baselined Errors = 0.
1>(crosslayer-cs) Suppressed Errors = 0.
1>(crosslayer-cs) Messages = 0.
1>TRACEREPORT : Finished running the 'tempfiles-cs' report in parallel:
1>(tempfiles-cs) Return Code = 0 (success).
1>(tempfiles-cs) Runtime = 0.019s.
1>(tempfiles-cs) Errors = 0.
1>(tempfiles-cs) Baselined Errors = 0.
1>(tempfiles-cs) Suppressed Errors = 0.
1>(tempfiles-cs) Messages = 0.
1>TRACEREPORT : Finished running the 'objroot-cs' report in parallel:
1>(objroot-cs) Return Code = 0 (success).
1>(objroot-cs) Runtime = 0.020s.
1>(objroot-cs) Errors = 0.
1>(objroot-cs) Baselined Errors = 0.
1>(objroot-cs) Suppressed Errors = 0.
1>(objroot-cs) Messages = 0.
1>TRACEREPORT : Finished running the 'vpackstrict-cs' report in parallel:
1>(vpackstrict-cs) Return Code = 0 (success).
1>(vpackstrict-cs) Runtime = 0.019s.
1>(vpackstrict-cs) Errors = 0.
1>(vpackstrict-cs) Baselined Errors = 0.
1>(vpackstrict-cs) Suppressed Errors = 0.
1>(vpackstrict-cs) Messages = 0.
1>TRACEREPORT : Finished running the 'restricteddependency-cs' report in parallel:
1>(restricteddependency-cs) Return Code = 0 (success).
1>(restricteddependency-cs) Runtime = 0.020s.
1>(restricteddependency-cs) Errors = 0.
1>(restricteddependency-cs) Baselined Errors = 0.
1>(restricteddependency-cs) Suppressed Errors = 0.
1>(restricteddependency-cs) Messages = 0.
1>TRACEREPORT : Finished running the 'buildrules-cs' report in parallel:
1>(buildrules-cs) Return Code = 0 (success).
1>(buildrules-cs) Runtime = 0.020s.
1>(buildrules-cs) Errors = 0.
1>(buildrules-cs) Baselined Errors = 0.
1>(buildrules-cs) Suppressed Errors = 0.
1>(buildrules-cs) Messages = 0.
1>TRACEREPORT : Finished running the 'ostools-cs' report in parallel:
1>(ostools-cs) Return Code = 0 (success).
1>(ostools-cs) Runtime = 0.020s.
1>(ostools-cs) Errors = 0.
1>(ostools-cs) Baselined Errors = 0.
1>(ostools-cs) Suppressed Errors = 0.
1>(ostools-cs) Messages = 0.
1>TRACEREPORT : Finished running the 'chunkanalyzer-cs' report in parallel:
1>(chunkanalyzer-cs) Return Code = 0 (success).
1>(chunkanalyzer-cs) Runtime = 0.020s.
1>(chunkanalyzer-cs) Errors = 0.
1>(chunkanalyzer-cs) Baselined Errors = 0.
1>(chunkanalyzer-cs) Suppressed Errors = 0.
1>(chunkanalyzer-cs) Messages = 0.
1>TRACEREPORT : Finished running the 'badpublish-cs' report in parallel:
1>(badpublish-cs) Return Code = 0 (success).
1>(badpublish-cs) Runtime = 0.021s.
1>(badpublish-cs) Errors = 0.
1>(badpublish-cs) Baselined Errors = 0.
1>(badpublish-cs) Suppressed Errors = 0.
1>(badpublish-cs) Messages = 0.
1>TRACEREPORT : Finished running the 'includes-cs' report in parallel:
1>(includes-cs) Return Code = 0 (success).
1>(includes-cs) Runtime = 0.020s.
1>(includes-cs) Errors = 0.
1>(includes-cs) Baselined Errors = 0.
1>(includes-cs) Suppressed Errors = 0.
1>(includes-cs) Messages = 0.
1>TRACEREPORT : Finished running the 'externalaccess-cs' report in parallel:
1>(externalaccess-cs) Return Code = 0 (success).
1>(externalaccess-cs) Runtime = 0.020s.
1>(externalaccess-cs) Errors = 0.
1>(externalaccess-cs) Baselined Errors = 0.
1>(externalaccess-cs) Suppressed Errors = 0.
1>(externalaccess-cs) Messages = 0.
1>TRACEREPORT : Finished running the 'multiwritestrict-cs' report in parallel:
1>(multiwritestrict-cs) Return Code = 0 (success).
1>(multiwritestrict-cs) Runtime = 0.020s.
1>(multiwritestrict-cs) Errors = 0.
1>(multiwritestrict-cs) Baselined Errors = 0.
1>(multiwritestrict-cs) Suppressed Errors = 0.
1>(multiwritestrict-cs) Messages = 0.
1>TRACEREPORT : Finished running the 'sourcesdep-cs' report in parallel:
1>(sourcesdep-cs) Return Code = 0 (success).
1>(sourcesdep-cs) Runtime = 0.020s.
1>(sourcesdep-cs) Errors = 0.
1>(sourcesdep-cs) Baselined Errors = 0.
1>(sourcesdep-cs) Suppressed Errors = 0.
1>(sourcesdep-cs) Messages = 0.
1>TRACEREPORT : Finished running the 'sdxwrite-cs' report in parallel:
1>(sdxwrite-cs) Return Code = 0 (success).
1>(sdxwrite-cs) Runtime = 0.020s.
1>(sdxwrite-cs) Errors = 0.
1>(sdxwrite-cs) Baselined Errors = 0.
1>(sdxwrite-cs) Suppressed Errors = 0.
1>(sdxwrite-cs) Messages = 0.
1>TRACEREPORT : Finished running the 'vpacknttree-cs' report in parallel:
1>(vpacknttree-cs) Return Code = 0 (success).
1>(vpacknttree-cs) Runtime = 0.020s.
1>(vpacknttree-cs) Errors = 0.
1>(vpacknttree-cs) Baselined Errors = 0.
1>(vpacknttree-cs) Suppressed Errors = 0.
1>(vpacknttree-cs) Messages = 0.
1>TRACEREPORT : All reports have finished running in parallel. Printing buffered output in-order...
1>TRACEREPORT : There is no buffered output to print.
1>TRACEREPORT : Time taken to run all reports in parallel = 0.021s
1>ANALYZER : TraceReport time = 3.659s
1>ANALYZER (_tmain): Analyzer has completed and is exiting with return code '0' indicating success.
1>ANALYZER : PageFaultCount:     176413
1>ANALYZER : PeakWorkingSetSize: 623284224
1>ANALYZER : PeakPagefileUsage:  596930560
1>ANALYZER : ProcessCycleTime:   10865863389
1>ANALYZER : KernelTime:         0.391
1>ANALYZER : UserTime:           3.719
Build layers enabled: [OSClient,GameCore,DesktopEditions,ShellCommon,OnecoreUAP,ClientCore]
Number of excluded directories, not in layer set: 0


    3 directories scanned
    0 files compiled - 3 Errors
