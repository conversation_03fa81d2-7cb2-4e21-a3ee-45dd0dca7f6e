ts=422 Merging config files using BUILD_CONFIG_FILE=e:\os\obj\amd64fre\objfre\amd64\build-exe-merged.config
ts=1531 queue pregraph command
ts=1531 run PreGraph commands build_pre_graph
ts=2813 queue prebuild command
ts=2813 run preprocess commands build_pre_process
ts=9469 initializing DBB query
ts=17750 reading parent chain of e:\os\src\onecore\ds\ds\src\aimx\prod\aimxsrv\server
ts=17750 reading parent chain of e:\os\src\onecore\ds\ds\src\aimx\prod\aimxsrv
ts=17766 reading parent chain of e:\os\src\onecore\ds\ds\src\aimx\prod
ts=17766 reading parent chain of e:\os\src\onecore\ds\ds\src\aimx
ts=17766 reading parent chain of e:\os\src\onecore\ds\ds\src
ts=17766 reading parent chain of e:\os\src\onecore\ds\ds
ts=17766 reading parent chain of e:\os\src\onecore\ds
ts=17766 reading parent chain of e:\os\src\onecore
ts=17766 reading parent chain of e:\os\src
ts=17766 scanning focus directory e:\os\src\onecore\ds\ds\src\aimx\prod\aimxsrv\server
ts=17766 reading parent chain of e:\os\src\onecore\ds\ds\src\aimx\prod\aimxsrv\idl
ts=17766 reading parent chain of e:\os\src\onecore\ds\ds\src\aimx\prod\cpprestsdk
ts=17766 scanning focus directory e:\os\src\onecore\ds\ds\src\aimx\prod\aimxsrv\server
ts=17797 BUILD: Processing dependencies...
ts=17797 (PASS1) onecore\ds\ds\src\aimx\prod\aimxsrv\server(9): pre-build pending
ts=17797 BUILD: Scanning for circular dependencies...
ts=17797 BUILD: Processing dependencies complete
ts=17797 (onecore\ds\ds\src\aimx\prod\aimxsrv\server) e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\aimxsrv\server\objfre\amd64\_objects.mac is current (133969944328666885), sources (133969941762565131), sources recorded (133969941762565131)
ts=17797 (onecore\ds\ds\src\aimx\prod\aimxsrv\idl) e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\aimxsrv\idl\objfre\amd64\_objects.mac is current (133969944328811651), sources (133969941762565131), sources recorded (133969941762565131)

DIR EVENTS: onecore\ds\ds\src\aimx\prod\aimxsrv\server (IN FOCUS)
  CREATED=1
  FOUND_IN_DIRS=1
  INITIAL_DIRECTORY=1
  FOUND_IN_DATABASE=1
  CLEAN_ALL=0
  CLEAN_ALL_FORCED=0
  CLEAN_PASS0=0
  CLEAN_PASS1=0
  CLEAN_PASS2=0
  CLEAN_PASS_INDEPENDENT=0
  ALLOC_WORKITEM_PASS0=1
  ALLOC_WORKITEM_PASS1=1
  ALLOC_WORKITEM_PASS2=0
  ALLOC_WORKITEM_PASS_INDEPENDENT=0
  SOURCES_READ=1
  NODE_DIR=1
  DIRS_PROJ_SKIPPED=0
  MSBUILD_PROJECT_FILE_READ=0
  MSBUILD_POST_BUILD_PROJECT_FILE_READ=0
  TEST_CODE=0
  SAMPLE_CODE=0
  TESTCODE_SKIPPED=0
  PASS0_NEEDED=1
  PASS1_NEEDED=1
  PASS2_NEEDED=0
  PASS3_NEEDED=0
  SOURCES_DEP_READ=0
  QUEUE_TO_PASS_LIST_1=1
  QUEUE_TO_PASS_LIST_2=1
  QUEUE_TO_PASS_LIST_3=0
  ADD_DEPENDENCIES_TO_GRAPH=1
  SET_NON_PRODUCT=0
  SET_NON_CRITICAL=0
  SET_CS_WINDOWS=0
  SET_CS_XBOX=0
  SET_CS_PHONE=0
  SET_CS_AZURE=0
  SCAN_COMPLETE=1

  BUILD PASS0 EVENTS: onecore\ds\ds\src\aimx\prod\aimxsrv\server
    BUILD_CREATED=1
    TASK_CREATED=0
    ADDED_TO_DEPENDENCY_GRAPH=1
    ADDED_TO_PASSQ=1
    ADDED_TO_TASKQ=0
    TRANSITION_STANDBYQ_TO_PASSQ=0
    ADDED_TO_PASS0_Q=1
    ADDED_TO_PASS1_Q=0
    ADDED_TO_PASS2_Q=0
    ADDED_TO_PASS3_Q=0
    ADDED_TO_PASS_INDEPENDENT_Q=0
    ADDED_TO_TASK_Q=0
    REMOVED_FROM_PASSQ=0
    DEPENDENT_MOVED_FROM_READYQ_TO_PASSQ=0
    NMAKE_COMMAND_LINE=0
    MSBUILD_COMMAND_LINE=0
    SUBMIT_TO_DISTRIBUTED_SCHEDULER=0
    DISTRIBUTED_SCHEDULER_WORK_STARTED=0
    DISTRIBUTED_SCHEDULER_WORK_COMPLETED=0
    ADDED_TO_READYQ=0
    BYPASSED=0
    SENT_TO_CLIENT=0
    INCREMENTAL=0
    BUILD_PASS_TERMINATED_FOR_ERRORS=0
    BUILD_OPERATION_COMPLETE=0

  BUILD PASS1 EVENTS: onecore\ds\ds\src\aimx\prod\aimxsrv\server
    BUILD_CREATED=1
    TASK_CREATED=0
    ADDED_TO_DEPENDENCY_GRAPH=1
    ADDED_TO_PASSQ=1
    ADDED_TO_TASKQ=0
    TRANSITION_STANDBYQ_TO_PASSQ=0
    ADDED_TO_PASS0_Q=0
    ADDED_TO_PASS1_Q=1
    ADDED_TO_PASS2_Q=0
    ADDED_TO_PASS3_Q=0
    ADDED_TO_PASS_INDEPENDENT_Q=0
    ADDED_TO_TASK_Q=0
    REMOVED_FROM_PASSQ=0
    DEPENDENT_MOVED_FROM_READYQ_TO_PASSQ=0
    NMAKE_COMMAND_LINE=0
    MSBUILD_COMMAND_LINE=0
    SUBMIT_TO_DISTRIBUTED_SCHEDULER=0
    DISTRIBUTED_SCHEDULER_WORK_STARTED=0
    DISTRIBUTED_SCHEDULER_WORK_COMPLETED=0
    ADDED_TO_READYQ=0
    BYPASSED=0
    SENT_TO_CLIENT=0
    INCREMENTAL=0
    BUILD_PASS_TERMINATED_FOR_ERRORS=0
    BUILD_OPERATION_COMPLETE=0

DIR EVENTS: onecore\ds\ds\src\aimx\prod\cpprestsdk 
  CREATED=1
  FOUND_IN_DIRS=1
  INITIAL_DIRECTORY=0
  FOUND_IN_DATABASE=1
  CLEAN_ALL=0
  CLEAN_ALL_FORCED=0
  CLEAN_PASS0=0
  CLEAN_PASS1=0
  CLEAN_PASS2=0
  CLEAN_PASS_INDEPENDENT=0
  ALLOC_WORKITEM_PASS0=0
  ALLOC_WORKITEM_PASS1=1
  ALLOC_WORKITEM_PASS2=0
  ALLOC_WORKITEM_PASS_INDEPENDENT=0
  SOURCES_READ=0
  NODE_DIR=1
  DIRS_PROJ_SKIPPED=0
  MSBUILD_PROJECT_FILE_READ=1
  MSBUILD_POST_BUILD_PROJECT_FILE_READ=0
  TEST_CODE=0
  SAMPLE_CODE=0
  TESTCODE_SKIPPED=0
  PASS0_NEEDED=1
  PASS1_NEEDED=1
  PASS2_NEEDED=1
  PASS3_NEEDED=0
  SOURCES_DEP_READ=0
  QUEUE_TO_PASS_LIST_1=0
  QUEUE_TO_PASS_LIST_2=0
  QUEUE_TO_PASS_LIST_3=1
  ADD_DEPENDENCIES_TO_GRAPH=0
  SET_NON_PRODUCT=0
  SET_NON_CRITICAL=0
  SET_CS_WINDOWS=0
  SET_CS_XBOX=0
  SET_CS_PHONE=0
  SET_CS_AZURE=0
  SCAN_COMPLETE=1

  BUILD PASS1 EVENTS: onecore\ds\ds\src\aimx\prod\cpprestsdk
    BUILD_CREATED=1
    TASK_CREATED=0
    ADDED_TO_DEPENDENCY_GRAPH=1
    ADDED_TO_PASSQ=1
    ADDED_TO_TASKQ=0
    TRANSITION_STANDBYQ_TO_PASSQ=0
    ADDED_TO_PASS0_Q=0
    ADDED_TO_PASS1_Q=1
    ADDED_TO_PASS2_Q=0
    ADDED_TO_PASS3_Q=0
    ADDED_TO_PASS_INDEPENDENT_Q=0
    ADDED_TO_TASK_Q=0
    REMOVED_FROM_PASSQ=0
    DEPENDENT_MOVED_FROM_READYQ_TO_PASSQ=0
    NMAKE_COMMAND_LINE=0
    MSBUILD_COMMAND_LINE=0
    SUBMIT_TO_DISTRIBUTED_SCHEDULER=0
    DISTRIBUTED_SCHEDULER_WORK_STARTED=0
    DISTRIBUTED_SCHEDULER_WORK_COMPLETED=0
    ADDED_TO_READYQ=0
    BYPASSED=0
    SENT_TO_CLIENT=0
    INCREMENTAL=0
    BUILD_PASS_TERMINATED_FOR_ERRORS=0
    BUILD_OPERATION_COMPLETE=0

DIR EVENTS: onecore\ds\ds\src\aimx\prod\aimxsrv\idl 
  CREATED=1
  FOUND_IN_DIRS=1
  INITIAL_DIRECTORY=0
  FOUND_IN_DATABASE=1
  CLEAN_ALL=0
  CLEAN_ALL_FORCED=0
  CLEAN_PASS0=0
  CLEAN_PASS1=0
  CLEAN_PASS2=0
  CLEAN_PASS_INDEPENDENT=0
  ALLOC_WORKITEM_PASS0=1
  ALLOC_WORKITEM_PASS1=0
  ALLOC_WORKITEM_PASS2=0
  ALLOC_WORKITEM_PASS_INDEPENDENT=0
  SOURCES_READ=1
  NODE_DIR=1
  DIRS_PROJ_SKIPPED=0
  MSBUILD_PROJECT_FILE_READ=0
  MSBUILD_POST_BUILD_PROJECT_FILE_READ=0
  TEST_CODE=0
  SAMPLE_CODE=0
  TESTCODE_SKIPPED=0
  PASS0_NEEDED=1
  PASS1_NEEDED=0
  PASS2_NEEDED=0
  PASS3_NEEDED=0
  SOURCES_DEP_READ=0
  QUEUE_TO_PASS_LIST_1=0
  QUEUE_TO_PASS_LIST_2=0
  QUEUE_TO_PASS_LIST_3=1
  ADD_DEPENDENCIES_TO_GRAPH=0
  SET_NON_PRODUCT=0
  SET_NON_CRITICAL=0
  SET_CS_WINDOWS=0
  SET_CS_XBOX=0
  SET_CS_PHONE=0
  SET_CS_AZURE=0
  SCAN_COMPLETE=1

  BUILD PASS0 EVENTS: onecore\ds\ds\src\aimx\prod\aimxsrv\idl
    BUILD_CREATED=1
    TASK_CREATED=0
    ADDED_TO_DEPENDENCY_GRAPH=1
    ADDED_TO_PASSQ=1
    ADDED_TO_TASKQ=0
    TRANSITION_STANDBYQ_TO_PASSQ=0
    ADDED_TO_PASS0_Q=1
    ADDED_TO_PASS1_Q=0
    ADDED_TO_PASS2_Q=0
    ADDED_TO_PASS3_Q=0
    ADDED_TO_PASS_INDEPENDENT_Q=0
    ADDED_TO_TASK_Q=0
    REMOVED_FROM_PASSQ=0
    DEPENDENT_MOVED_FROM_READYQ_TO_PASSQ=0
    NMAKE_COMMAND_LINE=0
    MSBUILD_COMMAND_LINE=0
    SUBMIT_TO_DISTRIBUTED_SCHEDULER=0
    DISTRIBUTED_SCHEDULER_WORK_STARTED=0
    DISTRIBUTED_SCHEDULER_WORK_COMPLETED=0
    ADDED_TO_READYQ=0
    BYPASSED=0
    SENT_TO_CLIENT=0
    INCREMENTAL=0
    BUILD_PASS_TERMINATED_FOR_ERRORS=0
    BUILD_OPERATION_COMPLETE=0
