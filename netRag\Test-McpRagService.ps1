<#
.SYNOPSIS
    Test script for MCP Tools RAG Service
    
.DESCRIPTION
    This script tests all the functionalities of the MCP Tools RAG Service including:
    - Server registration
    - Tool registration
    - Tool search
    - Health checks
    - Statistics
    
.PARAMETER BaseUrl
    Base URL of the MCP RAG Service (default: http://localhost:5000)
    
.PARAMETER TestData
    Whether to create test data (default: $true)
    
.EXAMPLE
    .\Test-McpRagService.ps1
    
.EXAMPLE
    .\Test-McpRagService.ps1 -BaseUrl "http://localhost:8080" -TestData $false
#>

[CmdletBinding()]
param(
    [string]$BaseUrl = "http://localhost:5000",
    [bool]$TestData = $true
)

# Test configuration
$ErrorActionPreference = "Stop"
$ProgressPreference = "SilentlyContinue"

# Test data
$TestServerGuid = "12345678-1234-1234-1234-123456789abc"
$TestTools = @(
    @{
        Id = "$TestServerGuid" + "_" + "*************-4321-4321-cba987654321"
        Name = "Get-User"
        Description = "Retrieves user information from the directory service"
        Category = "User Management"
        InputSchema = '{"type":"object","properties":{"username":{"type":"string","description":"Username to lookup"}}}'
        Examples = @("Get user details for a specific username", "Lookup user by username")
        Tags = @("user", "directory", "lookup", "identity")
        Version = "1.0.0"
        Metadata = @{
            sourceSystem = "ActiveDirectory"
            permissions = "Read"
        }
    },
    @{
        Id = "$TestServerGuid" + "_" + "11111111-**************-************"
        Name = "Create-Group"
        Description = "Creates a new security group in the directory"
        Category = "Group Management"
        InputSchema = '{"type":"object","properties":{"groupName":{"type":"string"},"description":{"type":"string"}}}'
        Examples = @("Create a new security group", "Add new group to directory")
        Tags = @("group", "create", "security", "directory")
        Version = "1.0.0"
        Metadata = @{
            sourceSystem = "ActiveDirectory"
            permissions = "Write"
        }
    },
    @{
        Id = "$TestServerGuid" + "_" + "aaaaaaaa-bbbb-cccc-dddd-eeeeeeeeeeee"
        Name = "Reset-Password"
        Description = "Resets a user's password and optionally forces change on next login"
        Category = "User Management"
        InputSchema = '{"type":"object","properties":{"username":{"type":"string"},"forceChange":{"type":"boolean"}}}'
        Examples = @("Reset user password", "Force password change on next login")
        Tags = @("password", "reset", "security", "user")
        Version = "1.0.0"
        Metadata = @{
            sourceSystem = "ActiveDirectory"
            permissions = "Write"
        }
    }
)

function Write-TestHeader {
    param([string]$Title)
    Write-Host "`n" -NoNewline
    Write-Host "=" * 60 -ForegroundColor Cyan
    Write-Host " $Title" -ForegroundColor Yellow
    Write-Host "=" * 60 -ForegroundColor Cyan
}

function Write-TestStep {
    param([string]$Step)
    Write-Host "`n> $Step" -ForegroundColor Green
}

function Write-TestResult {
    param([string]$Result, [string]$Status = "INFO")
    $color = switch ($Status) {
        "SUCCESS" { "Green" }
        "ERROR" { "Red" }
        "WARNING" { "Yellow" }
        default { "White" }
    }
    Write-Host "  $Result" -ForegroundColor $color
}

function Invoke-ApiCall {
    param(
        [string]$Method,
        [string]$Endpoint,
        [object]$Body = $null,
        [hashtable]$Headers = @{"Content-Type" = "application/json"}
    )
    
    $uri = "$BaseUrl$Endpoint"
    
    try {
        $params = @{
            Uri = $uri
            Method = $Method
            Headers = $Headers
        }
        
        if ($Body) {
            $params.Body = ($Body | ConvertTo-Json -Depth 10)
        }
        
        $response = Invoke-RestMethod @params
        return @{
            Success = $true
            Data = $response
            StatusCode = 200
        }
    }
    catch {
        $statusCode = if ($_.Exception.Response) { $_.Exception.Response.StatusCode.value__ } else { 0 }
        return @{
            Success = $false
            Error = $_.Exception.Message
            StatusCode = $statusCode
        }
    }
}

function Test-ServiceHealth {
    Write-TestStep "Testing service health"
    
    $result = Invoke-ApiCall -Method "GET" -Endpoint "/api/mcptools/health"
    
    if ($result.Success) {
        Write-TestResult "[PASS] Service is healthy" "SUCCESS"
        Write-TestResult "  Service: $($result.Data.service)" "INFO"
        Write-TestResult "  Status: $($result.Data.status)" "INFO"
        return $true
    } else {
        Write-TestResult "[FAIL] Service health check failed: $($result.Error)" "ERROR"
        return $false
    }
}

function Test-ServerRegistration {
    Write-TestStep "Testing server registration"
    
    $serverData = @{
        Id = $TestServerGuid
        Name = "Test MCP Server"
        Description = "A test MCP server for validation"
        Version = "1.0.0"
        Metadata = @{
            testServer = $true
            createdBy = "Test-McpRagService.ps1"
        }
    }
    
    $result = Invoke-ApiCall -Method "POST" -Endpoint "/api/mcpservers/register" -Body $serverData
    
    if ($result.Success) {
        Write-TestResult "[PASS] Server registered successfully" "SUCCESS"
        Write-TestResult "  Server ID: $($result.Data.serverId)" "INFO"
        return $true
    } else {
        Write-TestResult "[FAIL] Server registration failed: $($result.Error)" "ERROR"
        return $false
    }
}

function Test-ToolRegistration {
    Write-TestStep "Testing tool registration"
    
    $successCount = 0
    $totalCount = $TestTools.Count
    
    foreach ($tool in $TestTools) {
        $result = Invoke-ApiCall -Method "POST" -Endpoint "/api/mcptools/register" -Body $tool
        
        if ($result.Success) {
            Write-TestResult "[PASS] Tool registered: $($tool.Name)" "SUCCESS"
            $successCount++
        } else {
            Write-TestResult "[FAIL] Tool registration failed: $($tool.Name) - $($result.Error)" "ERROR"
        }
    }
    
    Write-TestResult "Registered $successCount/$totalCount tools" "INFO"
    return $successCount -eq $totalCount
}

function Test-ToolSearch {
    Write-TestStep "Testing tool search functionality"
    
    $searchQueries = @(
        @{
            Query = "find user information"
            ExpectedTools = @("Get-User")
            Description = "User lookup query"
        },
        @{
            Query = "create new group"
            ExpectedTools = @("Create-Group")
            Description = "Group creation query"
        },
        @{
            Query = "reset password"
            ExpectedTools = @("Reset-Password")
            Description = "Password reset query"
        },
        @{
            Query = "manage users and groups"
            ExpectedTools = @("Get-User", "Create-Group", "Reset-Password")
            Description = "General management query"
        }
    )
    
    $allTestsPassed = $true
    
    foreach ($searchTest in $searchQueries) {
        Write-TestResult "Testing: $($searchTest.Description)" "INFO"
        
        $searchRequest = @{
            Query = $searchTest.Query
            TopK = 5
            MinScore = 0.0
        }
        
        $result = Invoke-ApiCall -Method "POST" -Endpoint "/api/mcptools/search" -Body $searchRequest
        
        if ($result.Success) {
            $foundTools = $result.Data.tools | ForEach-Object { $_.tool.name }
            $foundCount = $result.Data.tools.Count
            
            Write-TestResult "  [PASS] Search completed in $($result.Data.searchTimeMs)ms" "SUCCESS"
            Write-TestResult "  Found $foundCount tools: $($foundTools -join ', ')" "INFO"
            
            # Check if expected tools are found
            $expectedFound = 0
            foreach ($expectedTool in $searchTest.ExpectedTools) {
                if ($foundTools -contains $expectedTool) {
                    $expectedFound++
                }
            }
            
            if ($expectedFound -gt 0) {
                Write-TestResult "  [PASS] Found $expectedFound expected tools" "SUCCESS"
            } else {
                Write-TestResult "  [WARN] No expected tools found" "WARNING"
                $allTestsPassed = $false
            }
        } else {
            Write-TestResult "  [FAIL] Search failed: $($result.Error)" "ERROR"
            $allTestsPassed = $false
        }
    }
    
    return $allTestsPassed
}

function Test-ToolRetrieval {
    Write-TestStep "Testing individual tool retrieval"
    
    $testToolId = $TestTools[0].Id
    $result = Invoke-ApiCall -Method "GET" -Endpoint "/api/mcptools/$testToolId"
    
    if ($result.Success) {
        Write-TestResult "[PASS] Tool retrieved successfully" "SUCCESS"
        Write-TestResult "  Tool Name: $($result.Data.name)" "INFO"
        Write-TestResult "  Tool Description: $($result.Data.description)" "INFO"
        return $true
    } else {
        Write-TestResult "[FAIL] Tool retrieval failed: $($result.Error)" "ERROR"
        return $false
    }
}

function Test-Statistics {
    Write-TestStep "Testing statistics endpoints"
    
    # Test tool statistics
    $toolStatsResult = Invoke-ApiCall -Method "GET" -Endpoint "/api/mcptools/statistics"
    
    if ($toolStatsResult.Success) {
        Write-TestResult "[PASS] Tool statistics retrieved" "SUCCESS"
        Write-TestResult "  Total tools: $($toolStatsResult.Data.totalTools)" "INFO"
        Write-TestResult "  Cached tools: $($toolStatsResult.Data.cachedTools)" "INFO"
    } else {
        Write-TestResult "[FAIL] Tool statistics failed: $($toolStatsResult.Error)" "ERROR"
        return $false
    }
    
    # Test server statistics
    $serverStatsResult = Invoke-ApiCall -Method "GET" -Endpoint "/api/mcpservers/statistics"
    
    if ($serverStatsResult.Success) {
        Write-TestResult "[PASS] Server statistics retrieved" "SUCCESS"
        Write-TestResult "  Total servers: $($serverStatsResult.Data.totalServers)" "INFO"
        Write-TestResult "  Total tools: $($serverStatsResult.Data.totalTools)" "INFO"
    } else {
        Write-TestResult "[FAIL] Server statistics failed: $($serverStatsResult.Error)" "ERROR"
        return $false
    }
    
    return $true
}

function Test-BatchOperations {
    Write-TestStep "Testing batch operations"
    
    # Test batch tool registration
    $batchTools = $TestTools | Select-Object -First 2 | ForEach-Object {
        $newTool = $_.Clone()
        $newTool.Id = $newTool.Id -replace "87654321", "99999999"
        $newTool.Name = "Batch-" + $newTool.Name
        return $newTool
    }
    
    $result = Invoke-ApiCall -Method "POST" -Endpoint "/api/mcptools/batch/register" -Body $batchTools
    
    if ($result.Success) {
        Write-TestResult "[PASS] Batch registration completed" "SUCCESS"
        Write-TestResult "  Success: $($result.Data.successCount)" "INFO"
        Write-TestResult "  Failed: $($result.Data.failureCount)" "INFO"
        return $true
    } else {
        Write-TestResult "[FAIL] Batch registration failed: $($result.Error)" "ERROR"
        return $false
    }
}

# Main test execution
function Main {
    Write-TestHeader "MCP Tools RAG Service Test Suite"
    Write-Host "Testing service at: $BaseUrl" -ForegroundColor Cyan
    
    $testResults = @{}
    
    # Run tests
    $testResults["Health"] = Test-ServiceHealth
    
    if ($TestData) {
        $testResults["ServerRegistration"] = Test-ServerRegistration
        $testResults["ToolRegistration"] = Test-ToolRegistration
        $testResults["ToolSearch"] = Test-ToolSearch
        $testResults["ToolRetrieval"] = Test-ToolRetrieval
        $testResults["BatchOperations"] = Test-BatchOperations
    }
    
    $testResults["Statistics"] = Test-Statistics
    
    # Summary
    Write-TestHeader "Test Results Summary"
    
    $passedTests = 0
    $totalTests = $testResults.Count
    
    foreach ($test in $testResults.GetEnumerator()) {
        $status = if ($test.Value) { "PASSED" } else { "FAILED" }
        $color = if ($test.Value) { "Green" } else { "Red" }
        
        Write-Host "  $($test.Key): " -NoNewline
        Write-Host $status -ForegroundColor $color
        
        if ($test.Value) { $passedTests++ }
    }
    
    Write-Host "`nOverall Result: $passedTests/$totalTests tests passed" -ForegroundColor $(if ($passedTests -eq $totalTests) { "Green" } else { "Yellow" })
    
    if ($passedTests -eq $totalTests) {
        Write-Host "All tests passed! The MCP RAG Service is working correctly." -ForegroundColor Green
    } else {
        Write-Host "Some tests failed. Please check the service configuration and logs." -ForegroundColor Yellow
    }
}

# Run the tests
Main

<#
.NOTES
Usage Examples:

1. Basic test (creates test data and runs all tests):
   .\Test-McpRagService.ps1

2. Test against different URL:
   .\Test-McpRagService.ps1 -BaseUrl "http://localhost:8080"

3. Test without creating test data (assumes data already exists):
   .\Test-McpRagService.ps1 -TestData $false

4. Quick test only:
   .\Quick-Test.ps1

Expected Output:
- All tests should pass if the service is running correctly
- Search tests should find relevant tools based on queries
- Statistics should show registered servers and tools
- Health check should return "healthy" status

Troubleshooting:
- If health check fails: Ensure the service is running
- If registration fails: Check GUID format and service logs
- If search returns no results: Verify tools are registered and embeddings are working
- If statistics show 0 tools: Registration may have failed
#>
