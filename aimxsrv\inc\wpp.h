/*++

Copyright (c) Microsoft Corporation. All rights reserved.

Module Name:
    wpp.h

Abstract:
    WPP control GUID and bit definitions for AIMXSRV.

Author:
    <PERSON> (SNAKE FIGHTER) (lindakup) 06/12/2025

--*/
#pragma once

// WPP control GUID and bit definitions for AIMXSRV
//
// GUID {96b0a34f-a061-4d5b-be2f-e4d802b61733}
//
// AimxService         = bit 0: so pass 0x00000001
// AimxServer          = bit 1: so pass 0x00000002
// AimxRequest         = bit 2: so pass 0x00000004
// AimxPlanner         = bit 3: so pass 0x00000008
// AimxOrchestrator    = bit 4: so pass 0x00000010
// ADToolAgents        = bit 5: so pass 0x00000020
// AimxMcpSrv          = bit 6: so pass 0x00000040
// LlmClientLib        = bit 7: so pass 0x00000080
// AimxLlmInfer        = bit 8: so pass 0x00000100
// AimxMcpSvrMgr       = bit 9: so pass 0x00000200
// AimxMcpToolManager  = bit 10: so pass 0x00000400
// AimxConversationMgr = bit 11: so pass 0x00000800
// AdMcpSvr            = bit 12: so pass 0x00001000
// AimxRagServiceMgr   = bit 13: so pass 0x00002000

// begin_wpp config
#define WPP_CONTROL_GUIDS \
    WPP_DEFINE_CONTROL_GUID(AIMXSRVTraceGuid, (96b0a34f,a061,4d5b,be2f,e4d802b61733), \
        WPP_DEFINE_BIT(AimxService)         /* bit 0 */ \
        WPP_DEFINE_BIT(AimxServer)          /* bit 1 */ \
        WPP_DEFINE_BIT(AimxRequest)         /* bit 2 */ \
        WPP_DEFINE_BIT(AimxPlanner)         /* bit 3 */ \
        WPP_DEFINE_BIT(AimxOrchestrator)    /* bit 4 */ \
        WPP_DEFINE_BIT(ADToolAgent)         /* bit 5 */ \
        WPP_DEFINE_BIT(AimxMcpSrv)          /* bit 6 */ \
        WPP_DEFINE_BIT(LlmClientLib)        /* bit 7 */ \
        WPP_DEFINE_BIT(AimxLlmInfer)        /* bit 8 */ \
        WPP_DEFINE_BIT(AimxMcpSvrMgr)       /* bit 9 */ \
        WPP_DEFINE_BIT(AimxMcpToolManager)  /* bit 10 */ \
        WPP_DEFINE_BIT(AimxConversationMgr) /* bit 11 */ \
        WPP_DEFINE_BIT(AdMcpSvr)            /* bit 12 */ \
        WPP_DEFINE_BIT(AimxRagServiceMgr)   /* bit 13 */ \
    )

#define WPP_LEVEL_FLAGS_LOGGER(lvl, flags) WPP_LEVEL_LOGGER(flags)
#define WPP_LEVEL_FLAGS_ENABLED(lvl, flags) (WPP_LEVEL_ENABLED(flags) && WPP_CONTROL(WPP_BIT_##flags).Level >= lvl)

#define WPP_LEVEL_FLAGS_LOGGER_LOGGER(lvl, flags, LOGGER) LOGGER,
#define WPP_LEVEL_FLAGS_LOGGER_ENABLED(lvl, flags, LOGGER) (TRUE)
// end_wpp
