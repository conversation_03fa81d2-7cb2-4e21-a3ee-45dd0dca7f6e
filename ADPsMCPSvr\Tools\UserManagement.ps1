<#
.SYNOPSIS
    Active Directory User Management Tools for MCP Server
    
.DESCRIPTION
    This module registers MCP tools for Active Directory user management operations.
    Each tool is a direct wrapper around the corresponding AD PowerShell cmdlet with
    exact parameter passthrough and no output formatting.

.AUTHOR
    Rupo Zhang (rizhang)
#>

# Import required modules
Import-Module ActiveDirectory -ErrorAction SilentlyContinue

function Register-UserManagementTools {
    [CmdletBinding()]
    param()

    # Get-ADUser - Gets one or more Active Directory users
    Register-McpTool -Name "Get-ADUser" -Description "Gets one or more Active Directory users. Supports filtering by various criteria and retrieving specific properties." -ScriptBlock {
        param($Arguments)
        
        $params = @{}
        if ($Arguments.Filter) { $params.Filter = $Arguments.Filter }
        if ($Arguments.Identity) { $params.Identity = $Arguments.Identity }
        if ($Arguments.LDAPFilter) { $params.LDAPFilter = $Arguments.LDAPFilter }
        if ($Arguments.AuthType) { $params.AuthType = $Arguments.AuthType }
        if ($Arguments.Credential) { $params.Credential = $Arguments.Credential }
        if ($Arguments.Properties) { $params.Properties = $Arguments.Properties }
        if ($Arguments.ResultPageSize) { $params.ResultPageSize = $Arguments.ResultPageSize }
        if ($Arguments.ResultSetSize) { $params.ResultSetSize = $Arguments.ResultSetSize }
        if ($Arguments.SearchBase) { $params.SearchBase = $Arguments.SearchBase }
        if ($Arguments.SearchScope) { $params.SearchScope = $Arguments.SearchScope }
        if ($Arguments.Server) { $params.Server = $Arguments.Server }
        if ($Arguments.Partition) { $params.Partition = $Arguments.Partition }
        
        Get-ADUser @params
    } -InputSchema @{
        type = "object"
        properties = @{
            Filter = @{ type = "string"; description = "PowerShell Expression Language filter string" }
            Identity = @{ type = "string"; description = "User identity (DN, GUID, SID, or SAM account name)" }
            LDAPFilter = @{ type = "string"; description = "LDAP query string for filtering" }
            AuthType = @{ type = "string"; enum = @("Negotiate", "Basic"); description = "Authentication method" }
            Credential = @{ type = "string"; description = "User credentials for authentication" }
            Properties = @{ type = "array"; items = @{ type = "string" }; description = "Additional properties to retrieve" }
            ResultPageSize = @{ type = "integer"; description = "Number of objects per page" }
            ResultSetSize = @{ type = "integer"; description = "Maximum number of objects to return" }
            SearchBase = @{ type = "string"; description = "Active Directory path to search under" }
            SearchScope = @{ type = "string"; enum = @("Base", "OneLevel", "Subtree"); description = "Scope of the search" }
            Server = @{ type = "string"; description = "Domain controller to connect to" }
            Partition = @{ type = "string"; description = "Distinguished name of AD partition" }
        }
    }

    # New-ADUser - Creates a new Active Directory user
    Register-McpTool -Name "New-ADUser" -Description "Creates a new Active Directory user account with specified properties and attributes." -ScriptBlock {
        param($Arguments)
        
        $params = @{}
        if ($Arguments.Name) { $params.Name = $Arguments.Name }
        if ($Arguments.SamAccountName) { $params.SamAccountName = $Arguments.SamAccountName }
        if ($Arguments.UserPrincipalName) { $params.UserPrincipalName = $Arguments.UserPrincipalName }
        if ($Arguments.GivenName) { $params.GivenName = $Arguments.GivenName }
        if ($Arguments.Surname) { $params.Surname = $Arguments.Surname }
        if ($Arguments.DisplayName) { $params.DisplayName = $Arguments.DisplayName }
        if ($Arguments.Description) { $params.Description = $Arguments.Description }
        if ($Arguments.Path) { $params.Path = $Arguments.Path }
        if ($Arguments.Enabled) { $params.Enabled = $Arguments.Enabled }
        if ($Arguments.AccountPassword) { $params.AccountPassword = $Arguments.AccountPassword }
        if ($Arguments.ChangePasswordAtLogon) { $params.ChangePasswordAtLogon = $Arguments.ChangePasswordAtLogon }
        if ($Arguments.PasswordNeverExpires) { $params.PasswordNeverExpires = $Arguments.PasswordNeverExpires }
        if ($Arguments.CannotChangePassword) { $params.CannotChangePassword = $Arguments.CannotChangePassword }
        if ($Arguments.EmailAddress) { $params.EmailAddress = $Arguments.EmailAddress }
        if ($Arguments.HomeDirectory) { $params.HomeDirectory = $Arguments.HomeDirectory }
        if ($Arguments.HomeDrive) { $params.HomeDrive = $Arguments.HomeDrive }
        if ($Arguments.ProfilePath) { $params.ProfilePath = $Arguments.ProfilePath }
        if ($Arguments.ScriptPath) { $params.ScriptPath = $Arguments.ScriptPath }
        if ($Arguments.Office) { $params.Office = $Arguments.Office }
        if ($Arguments.OfficePhone) { $params.OfficePhone = $Arguments.OfficePhone }
        if ($Arguments.MobilePhone) { $params.MobilePhone = $Arguments.MobilePhone }
        if ($Arguments.Department) { $params.Department = $Arguments.Department }
        if ($Arguments.Company) { $params.Company = $Arguments.Company }
        if ($Arguments.Title) { $params.Title = $Arguments.Title }
        if ($Arguments.Manager) { $params.Manager = $Arguments.Manager }
        if ($Arguments.City) { $params.City = $Arguments.City }
        if ($Arguments.State) { $params.State = $Arguments.State }
        if ($Arguments.PostalCode) { $params.PostalCode = $Arguments.PostalCode }
        if ($Arguments.Country) { $params.Country = $Arguments.Country }
        if ($Arguments.StreetAddress) { $params.StreetAddress = $Arguments.StreetAddress }
        if ($Arguments.OtherAttributes) { $params.OtherAttributes = $Arguments.OtherAttributes }
        if ($Arguments.Server) { $params.Server = $Arguments.Server }
        if ($Arguments.AuthType) { $params.AuthType = $Arguments.AuthType }
        if ($Arguments.Credential) { $params.Credential = $Arguments.Credential }
        if ($Arguments.PassThru) { $params.PassThru = $Arguments.PassThru }
        
        New-ADUser @params
    } -InputSchema @{
        type = "object"
        properties = @{
            Name = @{ type = "string"; description = "Name of the user account (required)" }
            SamAccountName = @{ type = "string"; description = "SAM account name for the user" }
            UserPrincipalName = @{ type = "string"; description = "User principal name (UPN)" }
            GivenName = @{ type = "string"; description = "First name of the user" }
            Surname = @{ type = "string"; description = "Last name of the user" }
            DisplayName = @{ type = "string"; description = "Display name for the user" }
            Description = @{ type = "string"; description = "Description of the user account" }
            Path = @{ type = "string"; description = "Distinguished name of the container for the user" }
            Enabled = @{ type = "boolean"; description = "Whether the account is enabled" }
            AccountPassword = @{ type = "string"; description = "Password for the user account" }
            ChangePasswordAtLogon = @{ type = "boolean"; description = "User must change password at next logon" }
            PasswordNeverExpires = @{ type = "boolean"; description = "Password never expires" }
            CannotChangePassword = @{ type = "boolean"; description = "User cannot change password" }
            EmailAddress = @{ type = "string"; description = "Email address of the user" }
            HomeDirectory = @{ type = "string"; description = "Home directory path" }
            HomeDrive = @{ type = "string"; description = "Home drive letter" }
            ProfilePath = @{ type = "string"; description = "Profile path for the user" }
            ScriptPath = @{ type = "string"; description = "Logon script path" }
            Office = @{ type = "string"; description = "Office location" }
            OfficePhone = @{ type = "string"; description = "Office phone number" }
            MobilePhone = @{ type = "string"; description = "Mobile phone number" }
            Department = @{ type = "string"; description = "Department name" }
            Company = @{ type = "string"; description = "Company name" }
            Title = @{ type = "string"; description = "Job title" }
            Manager = @{ type = "string"; description = "Manager's distinguished name" }
            City = @{ type = "string"; description = "City" }
            State = @{ type = "string"; description = "State or province" }
            PostalCode = @{ type = "string"; description = "Postal code" }
            Country = @{ type = "string"; description = "Country" }
            StreetAddress = @{ type = "string"; description = "Street address" }
            OtherAttributes = @{ type = "object"; description = "Additional attributes as hashtable" }
            Server = @{ type = "string"; description = "Domain controller to connect to" }
            AuthType = @{ type = "string"; enum = @("Negotiate", "Basic"); description = "Authentication method" }
            Credential = @{ type = "string"; description = "User credentials for authentication" }
            PassThru = @{ type = "boolean"; description = "Return the created user object" }
        }
        required = @("Name")
    }

    # Set-ADUser - Modifies an Active Directory user
    Register-McpTool -Name "Set-ADUser" -Description "Modifies properties of an existing Active Directory user account." -ScriptBlock {
        param($Arguments)
        
        $params = @{}
        if ($Arguments.Identity) { $params.Identity = $Arguments.Identity }
        if ($Arguments.Add) { $params.Add = $Arguments.Add }
        if ($Arguments.Clear) { $params.Clear = $Arguments.Clear }
        if ($Arguments.Remove) { $params.Remove = $Arguments.Remove }
        if ($Arguments.Replace) { $params.Replace = $Arguments.Replace }
        if ($Arguments.GivenName) { $params.GivenName = $Arguments.GivenName }
        if ($Arguments.Surname) { $params.Surname = $Arguments.Surname }
        if ($Arguments.DisplayName) { $params.DisplayName = $Arguments.DisplayName }
        if ($Arguments.Description) { $params.Description = $Arguments.Description }
        if ($Arguments.UserPrincipalName) { $params.UserPrincipalName = $Arguments.UserPrincipalName }
        if ($Arguments.SamAccountName) { $params.SamAccountName = $Arguments.SamAccountName }
        if ($Arguments.EmailAddress) { $params.EmailAddress = $Arguments.EmailAddress }
        if ($Arguments.HomeDirectory) { $params.HomeDirectory = $Arguments.HomeDirectory }
        if ($Arguments.HomeDrive) { $params.HomeDrive = $Arguments.HomeDrive }
        if ($Arguments.ProfilePath) { $params.ProfilePath = $Arguments.ProfilePath }
        if ($Arguments.ScriptPath) { $params.ScriptPath = $Arguments.ScriptPath }
        if ($Arguments.Office) { $params.Office = $Arguments.Office }
        if ($Arguments.OfficePhone) { $params.OfficePhone = $Arguments.OfficePhone }
        if ($Arguments.MobilePhone) { $params.MobilePhone = $Arguments.MobilePhone }
        if ($Arguments.Department) { $params.Department = $Arguments.Department }
        if ($Arguments.Company) { $params.Company = $Arguments.Company }
        if ($Arguments.Title) { $params.Title = $Arguments.Title }
        if ($Arguments.Manager) { $params.Manager = $Arguments.Manager }
        if ($Arguments.City) { $params.City = $Arguments.City }
        if ($Arguments.State) { $params.State = $Arguments.State }
        if ($Arguments.PostalCode) { $params.PostalCode = $Arguments.PostalCode }
        if ($Arguments.Country) { $params.Country = $Arguments.Country }
        if ($Arguments.StreetAddress) { $params.StreetAddress = $Arguments.StreetAddress }
        if ($Arguments.Server) { $params.Server = $Arguments.Server }
        if ($Arguments.AuthType) { $params.AuthType = $Arguments.AuthType }
        if ($Arguments.Credential) { $params.Credential = $Arguments.Credential }
        if ($Arguments.PassThru) { $params.PassThru = $Arguments.PassThru }
        if ($Arguments.Partition) { $params.Partition = $Arguments.Partition }
        
        Set-ADUser @params
    } -InputSchema @{
        type = "object"
        properties = @{
            Identity = @{ type = "string"; description = "User identity (DN, GUID, SID, or SAM account name)" }
            Add = @{ type = "object"; description = "Attributes to add as hashtable" }
            Clear = @{ type = "array"; items = @{ type = "string" }; description = "Attributes to clear" }
            Remove = @{ type = "object"; description = "Attributes to remove as hashtable" }
            Replace = @{ type = "object"; description = "Attributes to replace as hashtable" }
            GivenName = @{ type = "string"; description = "First name of the user" }
            Surname = @{ type = "string"; description = "Last name of the user" }
            DisplayName = @{ type = "string"; description = "Display name for the user" }
            Description = @{ type = "string"; description = "Description of the user account" }
            UserPrincipalName = @{ type = "string"; description = "User principal name (UPN)" }
            SamAccountName = @{ type = "string"; description = "SAM account name for the user" }
            EmailAddress = @{ type = "string"; description = "Email address of the user" }
            HomeDirectory = @{ type = "string"; description = "Home directory path" }
            HomeDrive = @{ type = "string"; description = "Home drive letter" }
            ProfilePath = @{ type = "string"; description = "Profile path for the user" }
            ScriptPath = @{ type = "string"; description = "Logon script path" }
            Office = @{ type = "string"; description = "Office location" }
            OfficePhone = @{ type = "string"; description = "Office phone number" }
            MobilePhone = @{ type = "string"; description = "Mobile phone number" }
            Department = @{ type = "string"; description = "Department name" }
            Company = @{ type = "string"; description = "Company name" }
            Title = @{ type = "string"; description = "Job title" }
            Manager = @{ type = "string"; description = "Manager's distinguished name" }
            City = @{ type = "string"; description = "City" }
            State = @{ type = "string"; description = "State or province" }
            PostalCode = @{ type = "string"; description = "Postal code" }
            Country = @{ type = "string"; description = "Country" }
            StreetAddress = @{ type = "string"; description = "Street address" }
            Server = @{ type = "string"; description = "Domain controller to connect to" }
            AuthType = @{ type = "string"; enum = @("Negotiate", "Basic"); description = "Authentication method" }
            Credential = @{ type = "string"; description = "User credentials for authentication" }
            PassThru = @{ type = "boolean"; description = "Return the modified user object" }
            Partition = @{ type = "string"; description = "Distinguished name of AD partition" }
        }
        required = @("Identity")
    }

    # Remove-ADUser - Removes an Active Directory user
    Register-McpTool -Name "Remove-ADUser" -Description "Removes an Active Directory user account from the directory." -ScriptBlock {
        param($Arguments)
        
        $params = @{}
        if ($Arguments.Identity) { $params.Identity = $Arguments.Identity }
        if ($Arguments.Server) { $params.Server = $Arguments.Server }
        if ($Arguments.AuthType) { $params.AuthType = $Arguments.AuthType }
        if ($Arguments.Credential) { $params.Credential = $Arguments.Credential }
        if ($Arguments.Partition) { $params.Partition = $Arguments.Partition }
        if ($Arguments.Confirm) { $params.Confirm = $Arguments.Confirm }
        if ($Arguments.WhatIf) { $params.WhatIf = $Arguments.WhatIf }
        
        Remove-ADUser @params
    } -InputSchema @{
        type = "object"
        properties = @{
            Identity = @{ type = "string"; description = "User identity (DN, GUID, SID, or SAM account name)" }
            Server = @{ type = "string"; description = "Domain controller to connect to" }
            AuthType = @{ type = "string"; enum = @("Negotiate", "Basic"); description = "Authentication method" }
            Credential = @{ type = "string"; description = "User credentials for authentication" }
            Partition = @{ type = "string"; description = "Distinguished name of AD partition" }
            Confirm = @{ type = "boolean"; description = "Prompt for confirmation before removing" }
            WhatIf = @{ type = "boolean"; description = "Show what would happen without executing" }
        }
        required = @("Identity")
    }

    # Enable-ADAccount - Enables an Active Directory account
    Register-McpTool -Name "Enable-ADAccount" -Description "Enables an Active Directory user, computer, or service account." -ScriptBlock {
        param($Arguments)

        $params = @{}
        if ($Arguments.Identity) { $params.Identity = $Arguments.Identity }
        if ($Arguments.Server) { $params.Server = $Arguments.Server }
        if ($Arguments.AuthType) { $params.AuthType = $Arguments.AuthType }
        if ($Arguments.Credential) { $params.Credential = $Arguments.Credential }
        if ($Arguments.Partition) { $params.Partition = $Arguments.Partition }
        if ($Arguments.PassThru) { $params.PassThru = $Arguments.PassThru }

        Enable-ADAccount @params
    } -InputSchema @{
        type = "object"
        properties = @{
            Identity = @{ type = "string"; description = "Account identity (DN, GUID, SID, or SAM account name)" }
            Server = @{ type = "string"; description = "Domain controller to connect to" }
            AuthType = @{ type = "string"; enum = @("Negotiate", "Basic"); description = "Authentication method" }
            Credential = @{ type = "string"; description = "User credentials for authentication" }
            Partition = @{ type = "string"; description = "Distinguished name of AD partition" }
            PassThru = @{ type = "boolean"; description = "Return the enabled account object" }
        }
        required = @("Identity")
    }

    # Disable-ADAccount - Disables an Active Directory account
    Register-McpTool -Name "Disable-ADAccount" -Description "Disables an Active Directory user, computer, or service account." -ScriptBlock {
        param($Arguments)

        $params = @{}
        if ($Arguments.Identity) { $params.Identity = $Arguments.Identity }
        if ($Arguments.Server) { $params.Server = $Arguments.Server }
        if ($Arguments.AuthType) { $params.AuthType = $Arguments.AuthType }
        if ($Arguments.Credential) { $params.Credential = $Arguments.Credential }
        if ($Arguments.Partition) { $params.Partition = $Arguments.Partition }
        if ($Arguments.PassThru) { $params.PassThru = $Arguments.PassThru }

        Disable-ADAccount @params
    } -InputSchema @{
        type = "object"
        properties = @{
            Identity = @{ type = "string"; description = "Account identity (DN, GUID, SID, or SAM account name)" }
            Server = @{ type = "string"; description = "Domain controller to connect to" }
            AuthType = @{ type = "string"; enum = @("Negotiate", "Basic"); description = "Authentication method" }
            Credential = @{ type = "string"; description = "User credentials for authentication" }
            Partition = @{ type = "string"; description = "Distinguished name of AD partition" }
            PassThru = @{ type = "boolean"; description = "Return the disabled account object" }
        }
        required = @("Identity")
    }

    # Unlock-ADAccount - Unlocks an Active Directory account
    Register-McpTool -Name "Unlock-ADAccount" -Description "Unlocks an Active Directory user account that has been locked out." -ScriptBlock {
        param($Arguments)

        $params = @{}
        if ($Arguments.Identity) { $params.Identity = $Arguments.Identity }
        if ($Arguments.Server) { $params.Server = $Arguments.Server }
        if ($Arguments.AuthType) { $params.AuthType = $Arguments.AuthType }
        if ($Arguments.Credential) { $params.Credential = $Arguments.Credential }
        if ($Arguments.Partition) { $params.Partition = $Arguments.Partition }
        if ($Arguments.PassThru) { $params.PassThru = $Arguments.PassThru }

        Unlock-ADAccount @params
    } -InputSchema @{
        type = "object"
        properties = @{
            Identity = @{ type = "string"; description = "Account identity (DN, GUID, SID, or SAM account name)" }
            Server = @{ type = "string"; description = "Domain controller to connect to" }
            AuthType = @{ type = "string"; enum = @("Negotiate", "Basic"); description = "Authentication method" }
            Credential = @{ type = "string"; description = "User credentials for authentication" }
            Partition = @{ type = "string"; description = "Distinguished name of AD partition" }
            PassThru = @{ type = "boolean"; description = "Return the unlocked account object" }
        }
        required = @("Identity")
    }

    # Set-ADAccountPassword - Modifies the password of an Active Directory account
    Register-McpTool -Name "Set-ADAccountPassword" -Description "Modifies the password of an Active Directory user, computer, or service account." -ScriptBlock {
        param($Arguments)

        $params = @{}
        if ($Arguments.Identity) { $params.Identity = $Arguments.Identity }
        if ($Arguments.NewPassword) { $params.NewPassword = $Arguments.NewPassword }
        if ($Arguments.OldPassword) { $params.OldPassword = $Arguments.OldPassword }
        if ($Arguments.Reset) { $params.Reset = $Arguments.Reset }
        if ($Arguments.Server) { $params.Server = $Arguments.Server }
        if ($Arguments.AuthType) { $params.AuthType = $Arguments.AuthType }
        if ($Arguments.Credential) { $params.Credential = $Arguments.Credential }
        if ($Arguments.Partition) { $params.Partition = $Arguments.Partition }
        if ($Arguments.PassThru) { $params.PassThru = $Arguments.PassThru }

        Set-ADAccountPassword @params
    } -InputSchema @{
        type = "object"
        properties = @{
            Identity = @{ type = "string"; description = "Account identity (DN, GUID, SID, or SAM account name)" }
            NewPassword = @{ type = "string"; description = "New password for the account" }
            OldPassword = @{ type = "string"; description = "Current password (required for user-initiated changes)" }
            Reset = @{ type = "boolean"; description = "Reset password (admin operation, no old password required)" }
            Server = @{ type = "string"; description = "Domain controller to connect to" }
            AuthType = @{ type = "string"; enum = @("Negotiate", "Basic"); description = "Authentication method" }
            Credential = @{ type = "string"; description = "User credentials for authentication" }
            Partition = @{ type = "string"; description = "Distinguished name of AD partition" }
            PassThru = @{ type = "boolean"; description = "Return the account object" }
        }
        required = @("Identity", "NewPassword")
    }

    # Set-ADAccountExpiration - Sets the expiration date for an Active Directory account
    Register-McpTool -Name "Set-ADAccountExpiration" -Description "Sets the expiration date for an Active Directory user, computer, or service account." -ScriptBlock {
        param($Arguments)

        $params = @{}
        if ($Arguments.Identity) { $params.Identity = $Arguments.Identity }
        if ($Arguments.DateTime) { $params.DateTime = $Arguments.DateTime }
        if ($Arguments.TimeSpan) { $params.TimeSpan = $Arguments.TimeSpan }
        if ($Arguments.Server) { $params.Server = $Arguments.Server }
        if ($Arguments.AuthType) { $params.AuthType = $Arguments.AuthType }
        if ($Arguments.Credential) { $params.Credential = $Arguments.Credential }
        if ($Arguments.Partition) { $params.Partition = $Arguments.Partition }
        if ($Arguments.PassThru) { $params.PassThru = $Arguments.PassThru }

        Set-ADAccountExpiration @params
    } -InputSchema @{
        type = "object"
        properties = @{
            Identity = @{ type = "string"; description = "Account identity (DN, GUID, SID, or SAM account name)" }
            DateTime = @{ type = "string"; description = "Expiration date and time" }
            TimeSpan = @{ type = "string"; description = "Time span from now for expiration" }
            Server = @{ type = "string"; description = "Domain controller to connect to" }
            AuthType = @{ type = "string"; enum = @("Negotiate", "Basic"); description = "Authentication method" }
            Credential = @{ type = "string"; description = "User credentials for authentication" }
            Partition = @{ type = "string"; description = "Distinguished name of AD partition" }
            PassThru = @{ type = "boolean"; description = "Return the account object" }
        }
        required = @("Identity")
    }

    # Clear-ADAccountExpiration - Clears the expiration date for an Active Directory account
    Register-McpTool -Name "Clear-ADAccountExpiration" -Description "Clears the expiration date for an Active Directory user, computer, or service account." -ScriptBlock {
        param($Arguments)

        $params = @{}
        if ($Arguments.Identity) { $params.Identity = $Arguments.Identity }
        if ($Arguments.Server) { $params.Server = $Arguments.Server }
        if ($Arguments.AuthType) { $params.AuthType = $Arguments.AuthType }
        if ($Arguments.Credential) { $params.Credential = $Arguments.Credential }
        if ($Arguments.Partition) { $params.Partition = $Arguments.Partition }
        if ($Arguments.PassThru) { $params.PassThru = $Arguments.PassThru }

        Clear-ADAccountExpiration @params
    } -InputSchema @{
        type = "object"
        properties = @{
            Identity = @{ type = "string"; description = "Account identity (DN, GUID, SID, or SAM account name)" }
            Server = @{ type = "string"; description = "Domain controller to connect to" }
            AuthType = @{ type = "string"; enum = @("Negotiate", "Basic"); description = "Authentication method" }
            Credential = @{ type = "string"; description = "User credentials for authentication" }
            Partition = @{ type = "string"; description = "Distinguished name of AD partition" }
            PassThru = @{ type = "boolean"; description = "Return the account object" }
        }
        required = @("Identity")
    }

    # Get-ADServiceAccount - Gets one or more Active Directory managed service accounts or group managed service accounts
    Register-McpTool -Name "Get-ADServiceAccount" -Description "Gets one or more Active Directory managed service accounts or group managed service accounts." -ScriptBlock {
        param($Arguments)

        $params = @{}
        if ($Arguments.Filter) { $params.Filter = $Arguments.Filter }
        if ($Arguments.Identity) { $params.Identity = $Arguments.Identity }
        if ($Arguments.LDAPFilter) { $params.LDAPFilter = $Arguments.LDAPFilter }
        if ($Arguments.AuthType) { $params.AuthType = $Arguments.AuthType }
        if ($Arguments.Credential) { $params.Credential = $Arguments.Credential }
        if ($Arguments.Properties) { $params.Properties = $Arguments.Properties }
        if ($Arguments.ResultPageSize) { $params.ResultPageSize = $Arguments.ResultPageSize }
        if ($Arguments.ResultSetSize) { $params.ResultSetSize = $Arguments.ResultSetSize }
        if ($Arguments.SearchBase) { $params.SearchBase = $Arguments.SearchBase }
        if ($Arguments.SearchScope) { $params.SearchScope = $Arguments.SearchScope }
        if ($Arguments.Server) { $params.Server = $Arguments.Server }
        if ($Arguments.Partition) { $params.Partition = $Arguments.Partition }

        Get-ADServiceAccount @params
    } -InputSchema @{
        type = "object"
        properties = @{
            Filter = @{ type = "string"; description = "PowerShell Expression Language filter string" }
            Identity = @{ type = "string"; description = "Service account identity (DN, GUID, SID, or SAM account name)" }
            LDAPFilter = @{ type = "string"; description = "LDAP query string for filtering" }
            AuthType = @{ type = "string"; enum = @("Negotiate", "Basic"); description = "Authentication method" }
            Credential = @{ type = "string"; description = "User credentials for authentication" }
            Properties = @{ type = "array"; items = @{ type = "string" }; description = "Additional properties to retrieve" }
            ResultPageSize = @{ type = "integer"; description = "Number of objects per page" }
            ResultSetSize = @{ type = "integer"; description = "Maximum number of objects to return" }
            SearchBase = @{ type = "string"; description = "Active Directory path to search under" }
            SearchScope = @{ type = "string"; enum = @("Base", "OneLevel", "Subtree"); description = "Scope of the search" }
            Server = @{ type = "string"; description = "Domain controller to connect to" }
            Partition = @{ type = "string"; description = "Distinguished name of AD partition" }
        }
    }

    # New-ADServiceAccount - Creates a new Active Directory managed service account or group managed service account object
    Register-McpTool -Name "New-ADServiceAccount" -Description "Creates a new Active Directory managed service account or group managed service account object." -ScriptBlock {
        param($Arguments)

        $params = @{}
        if ($Arguments.Name) { $params.Name = $Arguments.Name }
        if ($Arguments.SamAccountName) { $params.SamAccountName = $Arguments.SamAccountName }
        if ($Arguments.DisplayName) { $params.DisplayName = $Arguments.DisplayName }
        if ($Arguments.Description) { $params.Description = $Arguments.Description }
        if ($Arguments.Path) { $params.Path = $Arguments.Path }
        if ($Arguments.Enabled) { $params.Enabled = $Arguments.Enabled }
        if ($Arguments.DNSHostName) { $params.DNSHostName = $Arguments.DNSHostName }
        if ($Arguments.KerberosEncryptionType) { $params.KerberosEncryptionType = $Arguments.KerberosEncryptionType }
        if ($Arguments.ManagedPasswordIntervalInDays) { $params.ManagedPasswordIntervalInDays = $Arguments.ManagedPasswordIntervalInDays }
        if ($Arguments.PrincipalsAllowedToRetrieveManagedPassword) { $params.PrincipalsAllowedToRetrieveManagedPassword = $Arguments.PrincipalsAllowedToRetrieveManagedPassword }
        if ($Arguments.PrincipalsAllowedToDelegateToAccount) { $params.PrincipalsAllowedToDelegateToAccount = $Arguments.PrincipalsAllowedToDelegateToAccount }
        if ($Arguments.ServicePrincipalNames) { $params.ServicePrincipalNames = $Arguments.ServicePrincipalNames }
        if ($Arguments.TrustedForDelegation) { $params.TrustedForDelegation = $Arguments.TrustedForDelegation }
        if ($Arguments.AccountNotDelegated) { $params.AccountNotDelegated = $Arguments.AccountNotDelegated }
        if ($Arguments.OtherAttributes) { $params.OtherAttributes = $Arguments.OtherAttributes }
        if ($Arguments.Server) { $params.Server = $Arguments.Server }
        if ($Arguments.AuthType) { $params.AuthType = $Arguments.AuthType }
        if ($Arguments.Credential) { $params.Credential = $Arguments.Credential }
        if ($Arguments.PassThru) { $params.PassThru = $Arguments.PassThru }

        New-ADServiceAccount @params
    } -InputSchema @{
        type = "object"
        properties = @{
            Name = @{ type = "string"; description = "Name of the service account (required)" }
            SamAccountName = @{ type = "string"; description = "SAM account name for the service account" }
            DisplayName = @{ type = "string"; description = "Display name for the service account" }
            Description = @{ type = "string"; description = "Description of the service account" }
            Path = @{ type = "string"; description = "Distinguished name of the container for the service account" }
            Enabled = @{ type = "boolean"; description = "Whether the account is enabled" }
            DNSHostName = @{ type = "string"; description = "DNS host name for the service account" }
            KerberosEncryptionType = @{ type = "string"; enum = @("None", "DES", "RC4", "AES128", "AES256"); description = "Kerberos encryption type" }
            ManagedPasswordIntervalInDays = @{ type = "integer"; description = "Managed password interval in days" }
            PrincipalsAllowedToRetrieveManagedPassword = @{ type = "array"; items = @{ type = "string" }; description = "Principals allowed to retrieve managed password" }
            PrincipalsAllowedToDelegateToAccount = @{ type = "array"; items = @{ type = "string" }; description = "Principals allowed to delegate to account" }
            ServicePrincipalNames = @{ type = "array"; items = @{ type = "string" }; description = "Service principal names" }
            TrustedForDelegation = @{ type = "boolean"; description = "Whether the account is trusted for delegation" }
            AccountNotDelegated = @{ type = "boolean"; description = "Account is sensitive and cannot be delegated" }
            OtherAttributes = @{ type = "object"; description = "Additional attributes as hashtable" }
            Server = @{ type = "string"; description = "Domain controller to connect to" }
            AuthType = @{ type = "string"; enum = @("Negotiate", "Basic"); description = "Authentication method" }
            Credential = @{ type = "string"; description = "User credentials for authentication" }
            PassThru = @{ type = "boolean"; description = "Return the created service account object" }
        }
        required = @("Name")
    }

    # Set-ADServiceAccount - Modifies an Active Directory managed service account or group managed service account object
    Register-McpTool -Name "Set-ADServiceAccount" -Description "Modifies an Active Directory managed service account or group managed service account object." -ScriptBlock {
        param($Arguments)

        $params = @{}
        if ($Arguments.Identity) { $params.Identity = $Arguments.Identity }
        if ($Arguments.Add) { $params.Add = $Arguments.Add }
        if ($Arguments.Clear) { $params.Clear = $Arguments.Clear }
        if ($Arguments.Remove) { $params.Remove = $Arguments.Remove }
        if ($Arguments.Replace) { $params.Replace = $Arguments.Replace }
        if ($Arguments.DisplayName) { $params.DisplayName = $Arguments.DisplayName }
        if ($Arguments.Description) { $params.Description = $Arguments.Description }
        if ($Arguments.DNSHostName) { $params.DNSHostName = $Arguments.DNSHostName }
        if ($Arguments.KerberosEncryptionType) { $params.KerberosEncryptionType = $Arguments.KerberosEncryptionType }
        if ($Arguments.ManagedPasswordIntervalInDays) { $params.ManagedPasswordIntervalInDays = $Arguments.ManagedPasswordIntervalInDays }
        if ($Arguments.PrincipalsAllowedToRetrieveManagedPassword) { $params.PrincipalsAllowedToRetrieveManagedPassword = $Arguments.PrincipalsAllowedToRetrieveManagedPassword }
        if ($Arguments.PrincipalsAllowedToDelegateToAccount) { $params.PrincipalsAllowedToDelegateToAccount = $Arguments.PrincipalsAllowedToDelegateToAccount }
        if ($Arguments.ServicePrincipalNames) { $params.ServicePrincipalNames = $Arguments.ServicePrincipalNames }
        if ($Arguments.TrustedForDelegation) { $params.TrustedForDelegation = $Arguments.TrustedForDelegation }
        if ($Arguments.AccountNotDelegated) { $params.AccountNotDelegated = $Arguments.AccountNotDelegated }
        if ($Arguments.Server) { $params.Server = $Arguments.Server }
        if ($Arguments.AuthType) { $params.AuthType = $Arguments.AuthType }
        if ($Arguments.Credential) { $params.Credential = $Arguments.Credential }
        if ($Arguments.PassThru) { $params.PassThru = $Arguments.PassThru }
        if ($Arguments.Partition) { $params.Partition = $Arguments.Partition }

        Set-ADServiceAccount @params
    } -InputSchema @{
        type = "object"
        properties = @{
            Identity = @{ type = "string"; description = "Service account identity (DN, GUID, SID, or SAM account name)" }
            Add = @{ type = "object"; description = "Attributes to add as hashtable" }
            Clear = @{ type = "array"; items = @{ type = "string" }; description = "Attributes to clear" }
            Remove = @{ type = "object"; description = "Attributes to remove as hashtable" }
            Replace = @{ type = "object"; description = "Attributes to replace as hashtable" }
            DisplayName = @{ type = "string"; description = "Display name for the service account" }
            Description = @{ type = "string"; description = "Description of the service account" }
            DNSHostName = @{ type = "string"; description = "DNS host name for the service account" }
            KerberosEncryptionType = @{ type = "string"; enum = @("None", "DES", "RC4", "AES128", "AES256"); description = "Kerberos encryption type" }
            ManagedPasswordIntervalInDays = @{ type = "integer"; description = "Managed password interval in days" }
            PrincipalsAllowedToRetrieveManagedPassword = @{ type = "array"; items = @{ type = "string" }; description = "Principals allowed to retrieve managed password" }
            PrincipalsAllowedToDelegateToAccount = @{ type = "array"; items = @{ type = "string" }; description = "Principals allowed to delegate to account" }
            ServicePrincipalNames = @{ type = "array"; items = @{ type = "string" }; description = "Service principal names" }
            TrustedForDelegation = @{ type = "boolean"; description = "Whether the account is trusted for delegation" }
            AccountNotDelegated = @{ type = "boolean"; description = "Account is sensitive and cannot be delegated" }
            Server = @{ type = "string"; description = "Domain controller to connect to" }
            AuthType = @{ type = "string"; enum = @("Negotiate", "Basic"); description = "Authentication method" }
            Credential = @{ type = "string"; description = "User credentials for authentication" }
            PassThru = @{ type = "boolean"; description = "Return the modified service account object" }
            Partition = @{ type = "string"; description = "Distinguished name of AD partition" }
        }
        required = @("Identity")
    }

    # Remove-ADServiceAccount - Removes an Active Directory managed service account or group managed service account
    Register-McpTool -Name "Remove-ADServiceAccount" -Description "Removes an Active Directory managed service account or group managed service account." -ScriptBlock {
        param($Arguments)

        $params = @{}
        if ($Arguments.Identity) { $params.Identity = $Arguments.Identity }
        if ($Arguments.Server) { $params.Server = $Arguments.Server }
        if ($Arguments.AuthType) { $params.AuthType = $Arguments.AuthType }
        if ($Arguments.Credential) { $params.Credential = $Arguments.Credential }
        if ($Arguments.Partition) { $params.Partition = $Arguments.Partition }
        if ($Arguments.Confirm) { $params.Confirm = $Arguments.Confirm }
        if ($Arguments.WhatIf) { $params.WhatIf = $Arguments.WhatIf }

        Remove-ADServiceAccount @params
    } -InputSchema @{
        type = "object"
        properties = @{
            Identity = @{ type = "string"; description = "Service account identity (DN, GUID, SID, or SAM account name)" }
            Server = @{ type = "string"; description = "Domain controller to connect to" }
            AuthType = @{ type = "string"; enum = @("Negotiate", "Basic"); description = "Authentication method" }
            Credential = @{ type = "string"; description = "User credentials for authentication" }
            Partition = @{ type = "string"; description = "Distinguished name of AD partition" }
            Confirm = @{ type = "boolean"; description = "Prompt for confirmation before removing" }
            WhatIf = @{ type = "boolean"; description = "Show what would happen without executing" }
        }
        required = @("Identity")
    }

    # Install-ADServiceAccount - Installs an Active Directory managed service account on a computer
    Register-McpTool -Name "Install-ADServiceAccount" -Description "Installs an Active Directory managed service account on a computer." -ScriptBlock {
        param($Arguments)

        $params = @{}
        if ($Arguments.Identity) { $params.Identity = $Arguments.Identity }
        if ($Arguments.AccountPassword) { $params.AccountPassword = $Arguments.AccountPassword }
        if ($Arguments.Force) { $params.Force = $Arguments.Force }
        if ($Arguments.Server) { $params.Server = $Arguments.Server }
        if ($Arguments.AuthType) { $params.AuthType = $Arguments.AuthType }
        if ($Arguments.Credential) { $params.Credential = $Arguments.Credential }

        Install-ADServiceAccount @params
    } -InputSchema @{
        type = "object"
        properties = @{
            Identity = @{ type = "string"; description = "Service account identity (DN, GUID, SID, or SAM account name)" }
            AccountPassword = @{ type = "string"; description = "Password for the service account" }
            Force = @{ type = "boolean"; description = "Force the installation without confirmation" }
            Server = @{ type = "string"; description = "Domain controller to connect to" }
            AuthType = @{ type = "string"; enum = @("Negotiate", "Basic"); description = "Authentication method" }
            Credential = @{ type = "string"; description = "User credentials for authentication" }
        }
        required = @("Identity")
    }

    # Uninstall-ADServiceAccount - Uninstalls an Active Directory managed service account from a computer
    Register-McpTool -Name "Uninstall-ADServiceAccount" -Description "Uninstalls an Active Directory managed service account from a computer." -ScriptBlock {
        param($Arguments)

        $params = @{}
        if ($Arguments.Identity) { $params.Identity = $Arguments.Identity }
        if ($Arguments.Force) { $params.Force = $Arguments.Force }
        if ($Arguments.Server) { $params.Server = $Arguments.Server }
        if ($Arguments.AuthType) { $params.AuthType = $Arguments.AuthType }
        if ($Arguments.Credential) { $params.Credential = $Arguments.Credential }

        Uninstall-ADServiceAccount @params
    } -InputSchema @{
        type = "object"
        properties = @{
            Identity = @{ type = "string"; description = "Service account identity (DN, GUID, SID, or SAM account name)" }
            Force = @{ type = "boolean"; description = "Force the uninstallation without confirmation" }
            Server = @{ type = "string"; description = "Domain controller to connect to" }
            AuthType = @{ type = "string"; enum = @("Negotiate", "Basic"); description = "Authentication method" }
            Credential = @{ type = "string"; description = "User credentials for authentication" }
        }
        required = @("Identity")
    }

    # Test-ADServiceAccount - Tests a managed service account from a computer
    Register-McpTool -Name "Test-ADServiceAccount" -Description "Tests a managed service account from a computer." -ScriptBlock {
        param($Arguments)

        $params = @{}
        if ($Arguments.Identity) { $params.Identity = $Arguments.Identity }
        if ($Arguments.Server) { $params.Server = $Arguments.Server }
        if ($Arguments.AuthType) { $params.AuthType = $Arguments.AuthType }
        if ($Arguments.Credential) { $params.Credential = $Arguments.Credential }

        Test-ADServiceAccount @params
    } -InputSchema @{
        type = "object"
        properties = @{
            Identity = @{ type = "string"; description = "Service account identity (DN, GUID, SID, or SAM account name)" }
            Server = @{ type = "string"; description = "Domain controller to connect to" }
            AuthType = @{ type = "string"; enum = @("Negotiate", "Basic"); description = "Authentication method" }
            Credential = @{ type = "string"; description = "User credentials for authentication" }
        }
        required = @("Identity")
    }
}

# Function is available after dot-sourcing
