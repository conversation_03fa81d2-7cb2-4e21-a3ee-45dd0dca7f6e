/*++

Copyright (c) Microsoft Corporation. All rights reserved.

Module Name:

    McpJsonRpc.cpp

Abstract:

    Implementation of shared JSON-RPC protocol utilities for Model Context Protocol (MCP).
    Provides common functionality for both MCP clients (AimxSrv) and MCP server executables.

Author:

    <PERSON><PERSON><PERSON> (rizhang) 07/19/2025

--*/

#include "McpJsonRpc.h"
#include <random>
#include <chrono>

using namespace McpProtocol;

// JSON-RPC 2.0 protocol utilities implementation
namespace McpProtocol::JsonRpc
{
    nlohmann::json CreateRequest(
        _In_ const std::string& method,
        _In_ const nlohmann::json& params,
        _In_opt_ const nlohmann::json& id
    )
    {
        nlohmann::json request = {
            {"jsonrpc", "2.0"},
            {"method", method}
        };

        if (!params.is_null())
        {
            request["params"] = params;
        }

        if (!id.is_null())
        {
            request["id"] = id;
        }

        return request;
    }

    nlohmann::json CreateResponse(
        _In_ const nlohmann::json& id,
        _In_ const nlohmann::json& result
    )
    {
        return {
            {"jsonrpc", "2.0"},
            {"id", id},
            {"result", result}
        };
    }

    nlohmann::json CreateErrorResponse(
        _In_ const nlohmann::json& id,
        _In_ int errorCode,
        _In_ const std::string& errorMessage,
        _In_opt_ const nlohmann::json& errorData
    )
    {
        nlohmann::json error = {
            {"code", errorCode},
            {"message", errorMessage}
        };

        if (!errorData.is_null())
        {
            error["data"] = errorData;
        }

        return {
            {"jsonrpc", "2.0"},
            {"id", id},
            {"error", error}
        };
    }

    bool IsValidRequest(
        _In_ const nlohmann::json& request
    )
    {
        return request.is_object() &&
               request.contains("jsonrpc") &&
               request["jsonrpc"] == "2.0" &&
               request.contains("method") &&
               request["method"].is_string();
    }

    bool IsValidResponse(
        _In_ const nlohmann::json& response
    )
    {
        return response.is_object() &&
               response.contains("jsonrpc") &&
               response["jsonrpc"] == "2.0" &&
               response.contains("id") &&
               (response.contains("result") || response.contains("error"));
    }

    std::string GetMethod(
        _In_ const nlohmann::json& request
    )
    {
        if (request.contains("method") && request["method"].is_string())
        {
            return request["method"].get<std::string>();
        }
        return "";
    }

    nlohmann::json GetParams(
        _In_ const nlohmann::json& request
    )
    {
        if (request.contains("params"))
        {
            return request["params"];
        }
        return nlohmann::json::object();
    }

    nlohmann::json GetId(
        _In_ const nlohmann::json& message
    )
    {
        if (message.contains("id"))
        {
            return message["id"];
        }
        return nlohmann::json();
    }

    bool IsErrorResponse(
        _In_ const nlohmann::json& response
    )
    {
        return response.is_object() && response.contains("error");
    }

    ErrorInfo GetError(
        _In_ const nlohmann::json& errorResponse
    )
    {
        ErrorInfo errorInfo = { 0, "", nlohmann::json() };

        if (errorResponse.contains("error") && errorResponse["error"].is_object())
        {
            const auto& error = errorResponse["error"];
            
            if (error.contains("code") && error["code"].is_number_integer())
            {
                errorInfo.code = error["code"].get<int>();
            }

            if (error.contains("message") && error["message"].is_string())
            {
                errorInfo.message = error["message"].get<std::string>();
            }

            if (error.contains("data"))
            {
                errorInfo.data = error["data"];
            }
        }

        return errorInfo;
    }
}

// MCP-specific protocol utilities implementation
namespace McpProtocol::Mcp
{
    nlohmann::json CreateInitializeRequest(
        _In_ const std::string& clientName,
        _In_ const std::string& clientVersion,
        _In_opt_ const nlohmann::json& capabilities,
        _In_opt_ const nlohmann::json& id
    )
    {
        nlohmann::json params = {
            {"protocolVersion", PROTOCOL_VERSION},
            {"capabilities", capabilities},
            {"clientInfo", {
                {"name", clientName},
                {"version", clientVersion}
            }}
        };

        return JsonRpc::CreateRequest(Methods::INITIALIZE, params, id);
    }

    nlohmann::json CreateInitializeResponse(
        _In_ const nlohmann::json& id,
        _In_ const std::string& serverName,
        _In_ const std::string& serverVersion,
        _In_opt_ const nlohmann::json& capabilities
    )
    {
        nlohmann::json result = {
            {"protocolVersion", PROTOCOL_VERSION},
            {"capabilities", capabilities},
            {"serverInfo", {
                {"name", serverName},
                {"version", serverVersion}
            }}
        };

        return JsonRpc::CreateResponse(id, result);
    }

    nlohmann::json CreateListToolsRequest(
        _In_opt_ const nlohmann::json& id
    )
    {
        return JsonRpc::CreateRequest(Methods::TOOLS_LIST, nlohmann::json(), id);
    }

    nlohmann::json CreateListToolsResponse(
        _In_ const nlohmann::json& id,
        _In_ const nlohmann::json& toolsArray
    )
    {
        nlohmann::json result = {
            {"tools", toolsArray}
        };

        return JsonRpc::CreateResponse(id, result);
    }

    nlohmann::json CreateCallToolRequest(
        _In_ const std::string& toolName,
        _In_ const nlohmann::json& arguments,
        _In_opt_ const nlohmann::json& id
    )
    {
        nlohmann::json params = {
            {"name", toolName},
            {"arguments", arguments}
        };

        return JsonRpc::CreateRequest(Methods::TOOLS_CALL, params, id);
    }

    nlohmann::json CreateCallToolResponse(
        _In_ const nlohmann::json& id,
        _In_ const nlohmann::json& content
    )
    {
        return JsonRpc::CreateResponse(id, content);
    }

    nlohmann::json CreateShutdownRequest(
        _In_opt_ const nlohmann::json& id
    )
    {
        return JsonRpc::CreateRequest(Methods::SHUTDOWN, nlohmann::json(), id);
    }

    nlohmann::json CreateShutdownResponse(
        _In_ const nlohmann::json& id
    )
    {
        return JsonRpc::CreateResponse(id, nlohmann::json::object());
    }

    bool IsValidToolDefinition(
        _In_ const nlohmann::json& toolDef
    )
    {
        return toolDef.is_object() &&
               toolDef.contains("name") &&
               toolDef["name"].is_string() &&
               toolDef.contains("description") &&
               toolDef["description"].is_string();
    }

    nlohmann::json CreateToolContentResponse(
        _In_ const std::string& text,
        _In_opt_ const std::string& mimeType
    )
    {
        return {
            {"type", "text"},
            {"text", text},
            {"mimeType", mimeType}
        };
    }

    nlohmann::json WrapToolContent(
        _In_ const nlohmann::json& content
    )
    {
        if (content.is_array())
        {
            return {
                {"content", content}
            };
        }
        else
        {
            return {
                {"content", nlohmann::json::array({content})}
            };
        }
    }
}

// Utility functions implementation
namespace McpProtocol::Utils
{
    bool ParseJson(
        _In_ const std::string& jsonString,
        _Out_ nlohmann::json& result,
        _Out_opt_ std::string* errorMessage
    )
    {
        try
        {
            result = nlohmann::json::parse(jsonString);
            return true;
        }
        catch (const nlohmann::json::parse_error& e)
        {
            if (errorMessage)
            {
                *errorMessage = e.what();
            }
            return false;
        }
    }

    std::string SerializeJson(
        _In_ const nlohmann::json& json,
        _In_ bool pretty
    )
    {
        try
        {
            if (pretty)
            {
                return json.dump(2);
            }
            else
            {
                return json.dump();
            }
        }
        catch (const std::exception&)
        {
            return "{}";
        }
    }

    nlohmann::json GenerateRequestId()
    {
        static std::random_device rd;
        static std::mt19937 gen(rd());
        static std::uniform_int_distribution<> dis(1, 1000000);

        return dis(gen);
    }

    int HResultToMcpErrorCode(
        _In_ HRESULT hr
    )
    {
        if (SUCCEEDED(hr))
        {
            return 0; // No error
        }

        // Map common HRESULTs to MCP error codes
        switch (hr)
        {
        case E_INVALIDARG:
            return ErrorCodes::INVALID_PARAMS;
        case E_NOTIMPL:
            return ErrorCodes::METHOD_NOT_FOUND;
        case E_OUTOFMEMORY:
            return ErrorCodes::INTERNAL_ERROR;
        case E_FAIL:
            return ErrorCodes::INTERNAL_ERROR;
        case HRESULT_FROM_WIN32(ERROR_TIMEOUT):
            return ErrorCodes::TIMEOUT;
        case HRESULT_FROM_WIN32(ERROR_CONNECTION_REFUSED):
            return ErrorCodes::CONNECTION_FAILED;
        default:
            return ErrorCodes::INTERNAL_ERROR;
        }
    }

    HRESULT McpErrorCodeToHResult(
        _In_ int errorCode
    )
    {
        switch (errorCode)
        {
        case 0:
            return S_OK;
        case ErrorCodes::PARSE_ERROR:
            return E_INVALIDARG;
        case ErrorCodes::INVALID_REQUEST:
            return E_INVALIDARG;
        case ErrorCodes::METHOD_NOT_FOUND:
            return E_NOTIMPL;
        case ErrorCodes::INVALID_PARAMS:
            return E_INVALIDARG;
        case ErrorCodes::INTERNAL_ERROR:
            return E_FAIL;
        case ErrorCodes::TOOL_NOT_FOUND:
            return HRESULT_FROM_WIN32(ERROR_NOT_FOUND);
        case ErrorCodes::TOOL_EXECUTION_FAILED:
            return E_FAIL;
        case ErrorCodes::SERVER_INITIALIZATION_FAILED:
            return E_FAIL;
        case ErrorCodes::SERVER_NOT_INITIALIZED:
            return E_FAIL;
        case ErrorCodes::TIMEOUT:
            return HRESULT_FROM_WIN32(ERROR_TIMEOUT);
        case ErrorCodes::CONNECTION_FAILED:
            return HRESULT_FROM_WIN32(ERROR_CONNECTION_REFUSED);
        default:
            return E_FAIL;
        }
    }

    nlohmann::json CreateErrorFromHResult(
        _In_ const nlohmann::json& id,
        _In_ HRESULT hr,
        _In_opt_ const std::string& context
    )
    {
        int errorCode = HResultToMcpErrorCode(hr);
        std::string message = "Operation failed";

        if (!context.empty())
        {
            message = context + ": " + message;
        }

        // Add HRESULT information to error data
        nlohmann::json errorData = {
            {"hresult", static_cast<int32_t>(hr)},
            {"hresult_hex", std::to_string(hr)}
        };

        return JsonRpc::CreateErrorResponse(id, errorCode, message, errorData);
    }
}
