/*++

Copyright (c) Microsoft Corporation. All rights reserved.

Module Name:

    PowerShellFilterParser.cpp

Abstract:

    Implementation of PowerShell filter expression parser for Active Directory MCP server.
    Converts PowerShell filter syntax to LDAP filter syntax for AD queries.

    Supports PowerShell operators like:
    - Comparison: -eq, -ne, -lt, -le, -gt, -ge, -like, -notlike
    - Logical: -and, -or, -not
    - Wildcards: * and ?
    - Grouping: ( )

Author:

    <PERSON><PERSON><PERSON> (rizhang) 2025-7-16

--*/

#include "AdMcpSvr.h"
#include "StringUtils.h"
#include "../aimxsrv/inc/wpp.h"
#include <regex>
#include <algorithm>
#include <sstream>

// WPP tracing
#include "PowerShellFilterParser.cpp.tmh"

HRESULT AdMcpSvr::ParseFilterParameter(
    _In_ const nlohmann::json& filterParam,
    _Out_ std::wstring& ldapFilter
    )
/*++

Routine Description:
    Parse PowerShell Filter parameter to LDAP filter.
    Enhanced implementation with full PowerShell expression parsing.

Arguments:
    filterParam - PowerShell filter parameter
    ldapFilter - Receives LDAP filter string

Return Value:
    S_OK on success, error HRESULT on failure

--*/
{
    if (!filterParam.is_string())
    {
        return E_INVALIDARG;
    }

    std::string filterStr = filterParam.get<std::string>();
    std::wstring psExpression = Utf8ToWide(filterStr);

    // Check if it's already an LDAP filter (starts with '(' and contains LDAP syntax)
    if (!psExpression.empty() && psExpression[0] == L'(' && 
        (psExpression.find(L"objectClass=") != std::wstring::npos ||
         psExpression.find(L"&(") != std::wstring::npos ||
         psExpression.find(L"|(") != std::wstring::npos))
    {
        // Already an LDAP filter, use as-is
        ldapFilter = psExpression;
        return S_OK;
    }

    // Parse as PowerShell expression
    return ParsePowerShellExpression(psExpression, ldapFilter);
}

HRESULT AdMcpSvr::ParsePowerShellExpression(
    _In_ const std::wstring& psExpression,
    _Out_ std::wstring& ldapFilter
    )
/*++

Routine Description:
    Parse a complete PowerShell filter expression.

Arguments:
    psExpression - PowerShell filter expression
    ldapFilter - Receives LDAP filter string

Return Value:
    S_OK on success, error HRESULT on failure

--*/
{
    TraceInfo(AdMcpSvr, L"Parsing PowerShell expression: %ws", psExpression.c_str());

    try
    {
        std::wstring trimmedExpression = psExpression;
        
        // Remove leading/trailing whitespace
        trimmedExpression.erase(0, trimmedExpression.find_first_not_of(L" \t\r\n"));
        trimmedExpression.erase(trimmedExpression.find_last_not_of(L" \t\r\n") + 1);

        if (trimmedExpression.empty())
        {
            ldapFilter = L"(objectClass=*)";
            return S_OK;
        }

        // Handle logical expressions first (AND, OR)
        if (trimmedExpression.find(L" -and ") != std::wstring::npos ||
            trimmedExpression.find(L" -or ") != std::wstring::npos)
        {
            return ParseLogicalExpression(trimmedExpression, ldapFilter);
        }

        // Handle parentheses
        if (trimmedExpression[0] == L'(' && trimmedExpression.back() == L')')
        {
            std::wstring innerExpression = trimmedExpression.substr(1, trimmedExpression.length() - 2);
            return ParsePowerShellExpression(innerExpression, ldapFilter);
        }

        // Handle comparison expressions
        return ParseComparisonExpression(trimmedExpression, ldapFilter);
    }
    catch (const std::exception& ex)
    {
        TraceErr(AdMcpSvr, L"Exception parsing PowerShell expression: %s", ex.what());
        return E_FAIL;
    }
}

HRESULT AdMcpSvr::ParseLogicalExpression(
    _In_ const std::wstring& expression,
    _Out_ std::wstring& ldapFilter
    )
/*++

Routine Description:
    Parse logical expressions with -and, -or operators.

Arguments:
    expression - PowerShell logical expression
    ldapFilter - Receives LDAP filter string

Return Value:
    S_OK on success, error HRESULT on failure

--*/
{
    // Find the main logical operator (not inside parentheses)
    int parenLevel = 0;
    size_t operatorPos = std::wstring::npos;
    std::wstring logicalOp;

    for (size_t i = 0; i < expression.length(); i++)
    {
        if (expression[i] == L'(')
        {
            parenLevel++;
        }
        else if (expression[i] == L')')
        {
            parenLevel--;
        }
        else if (parenLevel == 0)
        {
            // Check for -and
            if (i + 5 <= expression.length() && 
                expression.substr(i, 5) == L" -and")
            {
                operatorPos = i;
                logicalOp = L"-and";
                break;
            }
            // Check for -or
            else if (i + 4 <= expression.length() && 
                     expression.substr(i, 4) == L" -or")
            {
                operatorPos = i;
                logicalOp = L"-or";
                break;
            }
        }
    }

    if (operatorPos == std::wstring::npos)
    {
        // No logical operator found, treat as comparison
        return ParseComparisonExpression(expression, ldapFilter);
    }

    // Split into left and right parts
    std::wstring leftPart = expression.substr(0, operatorPos);
    std::wstring rightPart = expression.substr(operatorPos + logicalOp.length() + 1);

    std::wstring leftFilter, rightFilter;
    HRESULT hr = ParsePowerShellExpression(leftPart, leftFilter);
    if (FAILED(hr)) return hr;

    hr = ParsePowerShellExpression(rightPart, rightFilter);
    if (FAILED(hr)) return hr;

    // Combine with LDAP logical operator
    if (logicalOp == L"-and")
    {
        ldapFilter = L"(&" + leftFilter + rightFilter + L")";
    }
    else if (logicalOp == L"-or")
    {
        ldapFilter = L"(|" + leftFilter + rightFilter + L")";
    }

    return S_OK;
}

HRESULT AdMcpSvr::ParseComparisonExpression(
    _In_ const std::wstring& expression,
    _Out_ std::wstring& ldapFilter
    )
/*++

Routine Description:
    Parse comparison expressions like "Name -eq 'John'" or "Department -like 'IT*'".

Arguments:
    expression - PowerShell comparison expression
    ldapFilter - Receives LDAP filter string

Return Value:
    S_OK on success, error HRESULT on failure

--*/
{
    // Regular expression to match: attribute operator value
    std::wregex comparisonRegex(L"^\\s*(\\w+)\\s+(-eq|-ne|-lt|-le|-gt|-ge|-like|-notlike)\\s+['\"]?([^'\"]*)['\"]?\\s*$");
    std::wsmatch matches;

    if (std::regex_match(expression, matches, comparisonRegex))
    {
        std::wstring attribute = matches[1].str();
        std::wstring psOperator = matches[2].str();
        std::wstring value = matches[3].str();

        // Escape LDAP special characters in value
        std::wstring escapedValue = EscapeLdapFilterValue(value);

        // Convert PowerShell operator to LDAP syntax
        if (psOperator == L"-eq")
        {
            ldapFilter = L"(" + attribute + L"=" + escapedValue + L")";
        }
        else if (psOperator == L"-ne")
        {
            ldapFilter = L"(!(" + attribute + L"=" + escapedValue + L"))";
        }
        else if (psOperator == L"-like")
        {
            // Convert PowerShell wildcards (* and ?) to LDAP wildcards
            std::wstring ldapValue = escapedValue;
            // PowerShell * becomes LDAP *
            // PowerShell ? becomes LDAP * (LDAP doesn't have single char wildcard)
            std::replace(ldapValue.begin(), ldapValue.end(), L'?', L'*');
            ldapFilter = L"(" + attribute + L"=" + ldapValue + L")";
        }
        else if (psOperator == L"-notlike")
        {
            std::wstring ldapValue = escapedValue;
            std::replace(ldapValue.begin(), ldapValue.end(), L'?', L'*');
            ldapFilter = L"(!(" + attribute + L"=" + ldapValue + L"))";
        }
        else if (psOperator == L"-lt")
        {
            ldapFilter = L"(" + attribute + L"<" + escapedValue + L")";
        }
        else if (psOperator == L"-le")
        {
            ldapFilter = L"(" + attribute + L"<=" + escapedValue + L")";
        }
        else if (psOperator == L"-gt")
        {
            ldapFilter = L"(" + attribute + L">" + escapedValue + L")";
        }
        else if (psOperator == L"-ge")
        {
            ldapFilter = L"(" + attribute + L">=" + escapedValue + L")";
        }
        else
        {
            TraceErr(AdMcpSvr, L"Unsupported PowerShell operator: %ws", psOperator.c_str());
            return E_INVALIDARG;
        }

        return S_OK;
    }

    // If regex doesn't match, try simpler patterns
    // Handle "attribute" (existence check)
    std::wregex existenceRegex(L"^\\s*(\\w+)\\s*$");
    if (std::regex_match(expression, matches, existenceRegex))
    {
        std::wstring attribute = matches[1].str();
        ldapFilter = L"(" + attribute + L"=*)";
        return S_OK;
    }

    TraceErr(AdMcpSvr, L"Unable to parse comparison expression: %ws", expression.c_str());
    return E_INVALIDARG;
}

std::wstring AdMcpSvr::EscapeLdapFilterValue(const std::wstring& value)
/*++

Routine Description:
    Escape special characters in LDAP filter values.

Arguments:
    value - Value to escape

Return Value:
    Escaped value string

--*/
{
    std::wstring escaped = value;

    // LDAP filter special characters that need escaping
    // \ -> \\5c
    // * -> \\2a (only if not used as wildcard)
    // ( -> \\28
    // ) -> \\29
    // NUL -> \\00

    // Replace backslash first
    size_t pos = 0;
    while ((pos = escaped.find(L'\\', pos)) != std::wstring::npos)
    {
        escaped.replace(pos, 1, L"\\5c");
        pos += 3;
    }

    // Replace parentheses
    pos = 0;
    while ((pos = escaped.find(L'(', pos)) != std::wstring::npos)
    {
        escaped.replace(pos, 1, L"\\28");
        pos += 3;
    }

    pos = 0;
    while ((pos = escaped.find(L')', pos)) != std::wstring::npos)
    {
        escaped.replace(pos, 1, L"\\29");
        pos += 3;
    }

    return escaped;
}

std::wstring AdMcpSvr::ConvertPowerShellOperatorToLdap(const std::wstring& psOperator)
/*++

Routine Description:
    Convert PowerShell comparison operators to LDAP equivalents.

Arguments:
    psOperator - PowerShell operator

Return Value:
    LDAP operator string

--*/
{
    if (psOperator == L"-eq") return L"=";
    if (psOperator == L"-ne") return L"!=";
    if (psOperator == L"-lt") return L"<";
    if (psOperator == L"-le") return L"<=";
    if (psOperator == L"-gt") return L">";
    if (psOperator == L"-ge") return L">=";
    if (psOperator == L"-like") return L"=";
    if (psOperator == L"-notlike") return L"!=";

    return L"="; // Default to equality
}

std::vector<std::wstring> AdMcpSvr::TokenizePowerShellExpression(const std::wstring& expression)
/*++

Routine Description:
    Tokenize PowerShell expression for advanced parsing.

Arguments:
    expression - PowerShell expression to tokenize

Return Value:
    Vector of tokens

--*/
{
    std::vector<std::wstring> tokens;
    std::wstringstream ss(expression);
    std::wstring token;

    bool inQuotes = false;
    wchar_t quoteChar = L'\0';
    std::wstring currentToken;

    for (size_t i = 0; i < expression.length(); i++)
    {
        wchar_t ch = expression[i];

        if (!inQuotes && (ch == L'\'' || ch == L'"'))
        {
            inQuotes = true;
            quoteChar = ch;
            currentToken += ch;
        }
        else if (inQuotes && ch == quoteChar)
        {
            inQuotes = false;
            currentToken += ch;
            tokens.push_back(currentToken);
            currentToken.clear();
        }
        else if (!inQuotes && (ch == L' ' || ch == L'\t'))
        {
            if (!currentToken.empty())
            {
                tokens.push_back(currentToken);
                currentToken.clear();
            }
        }
        else if (!inQuotes && (ch == L'(' || ch == L')'))
        {
            if (!currentToken.empty())
            {
                tokens.push_back(currentToken);
                currentToken.clear();
            }
            tokens.push_back(std::wstring(1, ch));
        }
        else
        {
            currentToken += ch;
        }
    }

    if (!currentToken.empty())
    {
        tokens.push_back(currentToken);
    }

    return tokens;
}
