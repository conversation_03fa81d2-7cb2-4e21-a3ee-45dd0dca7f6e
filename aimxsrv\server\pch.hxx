/*++

Copyright (c) Microsoft Corporation. All rights reserved.

Module Name:
    pch.hxx

Abstract:
    Precompiled header for AIMX service, includes all necessary system and project headers.

Author:

    <PERSON> (SNAKE FIGHTER) (linda<PERSON>p) 06/03/2025

--*/

#pragma once

// Base OS headers
extern "C" {
#include <nt.h>
#include <ntrtl.h>
#include <nturtl.h>
#include <ntseapi_x.h>
#include <ntdef.h>
#include <ntintsafe.h>
#include <ntstrsafe.h>
#include <windows.h>
#include <winldap.h>
#include <winber.h>
#include <ntldap.h>
#include <wincrypt.h>
#define SECURITY_WIN32
#include <security.h>
#include <dsgetdc.h>
#include <winldap.h>
#include <wchar.h>
#include <stdio.h>
#include <stdlib.h>
#include <rpc.h>
#include <ntdsapi.h> // order matters!
#include <lm.h>
#include <sddl.h>

}

// C++ headers and project includes
#include <vector>
#include <string>
#include <mutex>
#include <memory>
#include <sstream>
#include <thread>
#include <chrono>
#include <set>
#include <unordered_map>
#include <wil/resource.h>
#include <combaseapi.h>
#include <nlohmann/json.hpp>
#include <shared_mutex>
#undef WPP_CONTROL_GUIDS
#include "../inc/wpp.h"