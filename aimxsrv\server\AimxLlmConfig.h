/*++

Copyright (c) Microsoft Corporation. All rights reserved.

Module Name:

    AimxLlmConfig.h

Abstract:

    class for AimxLLMConfig

--*/

#pragma once

#include "AimxConstants.h"
#include <shared_mutex>
#include <string>
#include <windows.h>
#include <mutex>

// LLM configuration singleton class
class AimxLlmConfig
{
private:
    // Private constructor for singleton pattern
    AimxLlmConfig() = default;

    // Delete copy constructor and assignment operator to prevent copying
    AimxLlmConfig(const AimxLlmConfig&) = delete;
    AimxLlmConfig& operator=(const AimxLlmConfig&) = delete;

    // Delete move constructor and assignment operator
    AimxLlmConfig(AimxLlmConfig&&) = delete;
    AimxLlmConfig& operator=(AimxLlmConfig&&) = delete;

    // Mutex for thread-safe access
    mutable std::shared_mutex mutex_;

    // Configuration parameters
    std::wstring endpointUrl_;
    std::wstring model_;
    float temperature_ = AimxConstants::LlmInferenceDefaults::LLM_DEFAULT_TEMPERATURE_VALUE;
    int topK_ = AimxConstants::LlmInferenceDefaults::LLM_DEFAULT_TOP_K_VALUE;
    float topP_ = AimxConstants::LlmInferenceDefaults::LLM_DEFAULT_TOP_P_VALUE;
    HRESULT LoadLlmConfigFromRegistry();

    std::wstring
    ReadAndConstructLlmEndpointUrlFromRegistry(
        HKEY hKey
        );

public:
    // Singleton instance getter
    static AimxLlmConfig& Instance()
    {
        static AimxLlmConfig instance;
        static bool initialized = false;

        if (!initialized)
        {
            instance.LoadLlmConfigFromRegistry();
            initialized = true;
        }

        return instance;
    }

    // Virtual destructor for proper cleanup
    virtual ~AimxLlmConfig() = default;

    // Getter methods with thread-safe access
    std::wstring GetEndpointUrl() const
    {
        std::shared_lock<std::shared_mutex> lock(mutex_);
        return endpointUrl_;
    }

    std::wstring GetModel() const
    {
        std::shared_lock<std::shared_mutex> lock(mutex_);
        return model_;
    }

    float GetTemperature() const
    {
        std::shared_lock<std::shared_mutex> lock(mutex_);
        return temperature_;
    }

    int GetTopK() const
    {
        std::shared_lock<std::shared_mutex> lock(mutex_);
        return topK_;
    }

    float GetTopP() const
    {
        std::shared_lock<std::shared_mutex> lock(mutex_);
        return topP_;
    }

    // Setter methods with thread-safe access
    void SetEndpointUrl(const std::wstring& url)
    {
        std::lock_guard<std::shared_mutex> lock(mutex_);
        endpointUrl_ = url;
    }

    void SetModel(const std::wstring& model)
    {
        std::lock_guard<std::shared_mutex> lock(mutex_);
        model_ = model;
    }

    void SetTemperature(float temperature)
    {
        std::lock_guard<std::shared_mutex> lock(mutex_);
        temperature_ = temperature;
    }

    void SetTopK(int topK)
    {
        std::lock_guard<std::shared_mutex> lock(mutex_);
        topK_ = topK;
    }

    void SetTopP(float topP)
    {
        std::lock_guard<std::shared_mutex> lock(mutex_);
        topP_ = topP;
    }
};
