/*++

Copyright (c) Microsoft Corporation. All rights reserved.

Module Name:

    StringUtils.h

Abstract:

    This module defines string utility functions for the AIMX application.
    Provides functions for string conversion and manipulation.

Author:

    <PERSON><PERSON><PERSON> (r<PERSON><PERSON>) 03/21/2025

--*/

#pragma once

#include <string>
#include <sstream>
#include <iomanip>
#include <windows.h>

/*++

Routine Description:

    Converts a wide (UTF-16) string to a UTF-8 encoded string.

Arguments:

    wide - The wide string to convert to UTF-8.

Return Value:

    std::string containing the UTF-8 encoded representation of the input string.

--*/
inline
std::string
WideToUtf8(
    _In_ const std::wstring& wide
    )
{
    if (wide.empty()) return std::string();
    
    int size_needed = WideCharToMultiByte(CP_UTF8, 0, wide.data(), (int)wide.size(), 
                                          NULL, 0, NULL, NULL);
    std::string utf8(size_needed, 0);
    WideCharToMultiByte(CP_UTF8, 0, wide.data(), (int)wide.size(), 
                        &utf8[0], size_needed, NULL, NULL);
    return utf8;
}

/*++

Routine Description:

    Converts a UTF-8 encoded string to a wide (UTF-16) string.

Arguments:

    utf8 - The UTF-8 string to convert to wide string.

Return Value:

    std::wstring containing the wide (UTF-16) representation of the input string.

--*/
inline
std::wstring
Utf8ToWide(
    _In_ const std::string& utf8
    )
{
    if (utf8.empty()) return std::wstring();
    
    int size_needed = MultiByteToWideChar(CP_UTF8, 0, utf8.data(), (int)utf8.size(), 
                                          NULL, 0);
    std::wstring wide(size_needed, 0);
    MultiByteToWideChar(CP_UTF8, 0, utf8.data(), (int)utf8.size(), 
                        &wide[0], size_needed);
    return wide;
}

/*++

Routine Description:

    Escapes a string for safe inclusion in JSON, handling special characters
    like quotes, backslashes, and control characters according to JSON spec.

Arguments:

    input - The string to escape for JSON inclusion.

Return Value:

    std::string containing the properly escaped JSON string.

--*/
inline
std::string
EscapeJsonString(
    _In_ const std::string& input
    )
{
    std::string output;
    output.reserve(input.length() * 2);
    
    for (char ch : input) {
        switch (ch) {
            case '\"': output += "\\\""; break;
            case '\\': output += "\\\\"; break;
            case '\b': output += "\\b"; break;
            case '\f': output += "\\f"; break;
            case '\n': output += "\\n"; break;
            case '\r': output += "\\r"; break;
            case '\t': output += "\\t"; break;
            default:
                if (static_cast<unsigned char>(ch) < 32) {
                    char buffer[8];
                    sprintf_s(buffer, "\\u%04x", ch);
                    output += buffer;
                } else {
                    output += ch;
                }
                break;
        }
    }
    
    return output;
}

/*++

Routine Description:

    Escapes a wide string for safe inclusion in JSON, handling special characters
    like quotes, backslashes, and control characters according to JSON spec.

Arguments:

    input - The wide string to escape for JSON inclusion.

Return Value:

    std::wstring containing the properly escaped JSON string.

--*/
inline
std::wstring
EscapeJsonStringW(
    _In_ const std::wstring& input
    )
{
    if (input.empty()) {
        return L"";
    }
    
    std::wstring output;
    output.reserve(input.length() * 2);
    
    for (wchar_t ch : input) {
        switch (ch) {
            case L'\"': output += L"\\\""; break;
            case L'\\': output += L"\\\\"; break;
            case L'\b': output += L"\\b"; break;
            case L'\f': output += L"\\f"; break;
            case L'\n': output += L"\\n"; break;
            case L'\r': output += L"\\r"; break;
            case L'\t': output += L"\\t"; break;
            default:
                if (ch < 32) {
                    wchar_t buffer[8];
                    swprintf_s(buffer, L"\\u%04x", static_cast<unsigned int>(ch));
                    output += buffer;
                } else {
                    output += ch;
                }
                break;
        }
    }
    
    return output;
}

inline std::wstring 
GuidToString(
    _In_ GUID const& guid
    )
{
    std::wstring value(40, L'\0');

    swprintf_s(&value[0], value.capacity(), L"{%08X-%04X-%04X-%02X%02X-%02X%02X%02X%02X%02X%02X}",
        guid.Data1, 
        guid.Data2,
        guid.Data3,
        guid.Data4[0],
        guid.Data4[1],
        guid.Data4[2],
        guid.Data4[3],
        guid.Data4[4],
        guid.Data4[5],
        guid.Data4[6],
        guid.Data4[7]);

    return std::wstring(value.c_str());
}

/*++

Routine Description:

    Calculates the time difference in milliseconds between two FILETIME structures.

Arguments:

    startTime - The start time as a FILETIME structure.
    endTime - The end time as a FILETIME structure.

Return Value:

    DWORD containing the time difference in milliseconds.

--*/
inline
DWORD
CalculateTimeDifferenceMs(
    _In_ const FILETIME& startTime,
    _In_ const FILETIME& endTime
    )
{
    // Convert FILETIME to ULARGE_INTEGER for easier arithmetic
    ULARGE_INTEGER start, end;
    start.LowPart = startTime.dwLowDateTime;
    start.HighPart = startTime.dwHighDateTime;
    end.LowPart = endTime.dwLowDateTime;
    end.HighPart = endTime.dwHighDateTime;
    
    // FILETIME is in 100-nanosecond intervals since January 1, 1601
    // Convert to milliseconds by dividing by 10,000
    DWORD differenceInMs = static_cast<DWORD>((end.QuadPart - start.QuadPart) / 10000);
    
    return differenceInMs;
}

/*++

Routine Description:
    Format an error message with HRESULT, context, and additional information.
    Creates a comprehensive error message for logging and user display.

Arguments:
    hr - HRESULT error code
    context - Context description of where the error occurred

Return Value:
    Formatted error message string.

--*/
std::wstring 
inline FormatErrorMessage(
    _In_ HRESULT hr,
    _In_ const std::wstring& context
    )
{
    std::wostringstream errorStream;
    
    // Add context
    if (!context.empty())
    {
        errorStream << context << L": ";
    }
    
    // Format HRESULT
    errorStream << L"Error 0x" << std::hex << std::uppercase << std::setfill(L'0') << std::setw(8) << hr;
    
    // Try to get system error message
    LPWSTR systemMessage = nullptr;
    DWORD result = FormatMessageW(
        FORMAT_MESSAGE_ALLOCATE_BUFFER | FORMAT_MESSAGE_FROM_SYSTEM | FORMAT_MESSAGE_IGNORE_INSERTS,
        nullptr,
        hr,
        MAKELANGID(LANG_NEUTRAL, SUBLANG_DEFAULT),
        reinterpret_cast<LPWSTR>(&systemMessage),
        0,
        nullptr
    );
    
    if (result != 0 && systemMessage != nullptr)
    {
        // Remove trailing newlines from system message
        std::wstring sysMsg(systemMessage);
        while (!sysMsg.empty() && (sysMsg.back() == L'\r' || sysMsg.back() == L'\n'))
        {
            sysMsg.pop_back();
        }
        
        if (!sysMsg.empty())
        {
            errorStream << L" (" << sysMsg << L")";
        }
        
        LocalFree(systemMessage);
    }
    
    return errorStream.str();
}

inline std::string
FileTimeToString(
    const FILETIME& ft
)
/*++
Routine Description:

    Converts a FILETIME to a human-readable string in local time format.

Arguments:

    ft - The FILETIME to convert.

Return Value:
    A string representation of the FILETIME in the format "YYYY-MM-DD HH:MM:SS".
--*/
{
    SYSTEMTIME stUTC, stLocal;

    if (ft.dwLowDateTime == 0 && ft.dwHighDateTime == 0)
    {
        return std::string("0000-00-00 00:00:00");
    }

    if (!FileTimeToSystemTime(&ft, &stUTC))
    {
        return std::string("0000-00-00 00:00:00");
    }

    if (!SystemTimeToTzSpecificLocalTime(NULL, &stUTC, &stLocal))
    {
        return std::string("0000-00-00 00:00:00");
    }

    // Use sprintf instead of std::ostringstream to avoid potential issues
    char buffer[20]; // YYYY-MM-DD HH:MM:SS + null terminator
    sprintf_s(buffer, sizeof(buffer), "%04d-%02d-%02d %02d:%02d:%02d",
        stLocal.wYear, stLocal.wMonth, stLocal.wDay,
        stLocal.wHour, stLocal.wMinute, stLocal.wSecond);
    
    return std::string(buffer);
}