/*++

Copyright (c) Microsoft Corporation. All rights reserved.

Module Name:
    aimxsrv.h

Abstract:
    AIMXSRV server-side declarations.

Author:
    <PERSON> (SNAKE FIGHTER) (lindakup) 06/09/2025

--*/

#pragma once
#include <wil/resource.h>
#include "../server/RequestHandler.h"

class AimxService
{
public:
    AimxService();
    HRESULT ServiceHandler(DWORD dwControlCode);
    HRESULT ServiceMain();

private:
    HRESULT _StartService();
    HRESULT _StopService();
    HRESULT _SetStatus(DWORD dwCurrentState, DWORD dwCheckPoint, DWORD dwWaitHint);

    // private state
    SERVICE_STATUS_HANDLE m_serviceStatusHandle = nullptr;
    SERVICE_STATUS m_serviceStatus = {};
    wil::critical_section m_serviceLock;
    wil::unique_event_nothrow m_hStopEvent;

    // instance of RequestHandler
    RequestHandler* m_requestHandler = nullptr;
};


