ts=532 Merging config files using BUILD_CONFIG_FILE=e:\os\obj\amd64fre\objfre\amd64\build-exe-merged.config
ts=1157 queue pregraph command
ts=1157 run PreGraph commands build_pre_graph
ts=1813 queue prebuild command
ts=1813 run preprocess commands build_pre_process
ts=15547 initializing DBB query
ts=27375 reading parent chain of e:\os\src\onecore\ds\ds\src\aimx\prod\aimxsrv
ts=27375 reading parent chain of e:\os\src\onecore\ds\ds\src\aimx\prod
ts=27375 reading parent chain of e:\os\src\onecore\ds\ds\src\aimx
ts=27375 reading parent chain of e:\os\src\onecore\ds\ds\src
ts=27375 reading parent chain of e:\os\src\onecore\ds\ds
ts=27375 reading parent chain of e:\os\src\onecore\ds
ts=27375 reading parent chain of e:\os\src\onecore
ts=27375 reading parent chain of e:\os\src
ts=27375 scanning focus directory e:\os\src\onecore\ds\ds\src\aimx\prod\aimxsrv
ts=27391 DELETE OUTPUT LIST: Invalid output entry e:\os\obj\amd64fre\temp\a24b64ce3927082925d802fbdc64ed61\_asmid.inc in e:\os\src\onecore\ds\ds\src\aimx\prod\aimxsrv\powershell (PASS0) 
ts=27407 DELETE OUTPUT LIST: Invalid output entry e:\os\obj\amd64fre\temp\a24b64ce3927082925d802fbdc64ed61\_asmid.xml in e:\os\src\onecore\ds\ds\src\aimx\prod\aimxsrv\powershell (PASS0) 
ts=27407 (PASS0) onecore\ds\ds\src\aimx\prod\aimxsrv\idl(11): sent to build client (4).
ts=27407 (PASS0) onecore\ds\ds\src\aimx\prod\aimxsrv\idl(11): distributed work started.
ts=27422 (PASS0) onecore\ds\ds\src\aimx\prod\aimxsrv\idl(11): distributed work completed.
ts=27438 DELETE OUTPUT LIST: Invalid output entry e:\os\obj\amd64fre\temp\ec655f713cf9f139062afc85ccbbbe15\_generated.cs in e:\os\src\onecore\ds\ds\src\aimx\prod\aimxsrv\powershell (PASS1) 
ts=27438 DELETE OUTPUT LIST: Invalid output entry e:\os\obj\amd64fre\temp\ec655f713cf9f139062afc85ccbbbe15\aimxpsh.asmmeta_temp in e:\os\src\onecore\ds\ds\src\aimx\prod\aimxsrv\powershell (PASS1) 
ts=27438 DELETE OUTPUT LIST: Invalid output entry e:\os\obj\amd64fre\temp\ec655f713cf9f139062afc85ccbbbe15\aimxpsh.metadata_dll in e:\os\src\onecore\ds\ds\src\aimx\prod\aimxsrv\powershell (PASS1) 
ts=27453 DELETE OUTPUT LIST: Invalid output entry e:\os\obj\amd64fre\temp\dbe0ec0aecd5bf6091fbcec20ffe2bfa\post_link_concurrent.log in e:\os\src\onecore\ds\ds\src\aimx\prod\aimxsrv\dll (PASS2) 
ts=27485 DELETE OUTPUT LIST: Invalid output entry e:\os\obj\amd64fre\temp\5e11aeb55d8a81258cf9bfb64783005e\post_link_concurrent.log in e:\os\src\onecore\ds\ds\src\aimx\prod\aimxsrv\client\dll (PASS2) 
ts=27516 DELETE OUTPUT LIST: Invalid output entry e:\os\obj\amd64fre\temp\9e4184719c22c8095a8ad49fe22db5e0\binp_2.rsp in e:\os\src\onecore\ds\ds\src\aimx\prod\aimxsrv\powershell (PASS2) 
ts=27516 DELETE OUTPUT LIST: Invalid output entry e:\os\obj\amd64fre\temp\9e4184719c22c8095a8ad49fe22db5e0\post_link_concurrent.log in e:\os\src\onecore\ds\ds\src\aimx\prod\aimxsrv\powershell (PASS2) 
ts=27516 (PASS0) onecore\ds\ds\src\aimx\prod\aimxsrv\client\lib(16): sent to build client (1).
ts=27516 (PASS0) onecore\ds\ds\src\aimx\prod\aimxsrv\client\lib(16): distributed work started.
ts=27516 (PASS0) onecore\ds\ds\src\aimx\prod\aimxsrv\client\lib(16): distributed work completed.
ts=27532 (PASS0) onecore\ds\ds\src\aimx\prod\aimxsrv\client\dll(14): sent to build client (4).
ts=27532 (PASS0) onecore\ds\ds\src\aimx\prod\aimxsrv\server(13): sent to build client (1).
ts=27547 (PASS0) onecore\ds\ds\src\aimx\prod\aimxsrv\client\dll(14): distributed work started.
ts=27547 (PASS0) onecore\ds\ds\src\aimx\prod\aimxsrv\client\dll(14): distributed work completed.
ts=27547 (PASS0) onecore\ds\ds\src\aimx\prod\aimxsrv\powershell(12): sent to build client (5).
ts=27563 DELETE OUTPUT LIST: Invalid output entry e:\os\obj\amd64fre\temp\8d92e911ed63185917e5941c19c471a9\post_link_concurrent.log in e:\os\src\onecore\ds\ds\src\aimx\prod\aimxsrv\serviceinstaller (PASS2) 
ts=27563 (PASS0) onecore\ds\ds\src\aimx\prod\aimxsrv\powershell(12): distributed work started.
ts=27563 (PASS0) onecore\ds\ds\src\aimx\prod\aimxsrv\powershell(12): distributed work completed.
ts=27578 (PASS0) onecore\ds\ds\src\aimx\prod\aimxsrv\dll(10): sent to build client (4).
ts=27594 (PASS0) onecore\ds\ds\src\aimx\prod\aimxsrv\serviceinstaller(15): sent to build client (5).
ts=27594 (PASS0) onecore\ds\ds\src\aimx\prod\aimxsrv\serviceinstaller(15): distributed work started.
ts=27594 (PASS0) onecore\ds\ds\src\aimx\prod\aimxsrv\serviceinstaller(15): distributed work completed.
ts=27610 (PASS0) onecore\ds\ds\src\aimx\prod\aimxsrv\server(13): distributed work started.
ts=27610 (PASS0) onecore\ds\ds\src\aimx\prod\aimxsrv\server(13): distributed work completed.
ts=27610 reading parent chain of e:\os\src\onecore\ds\ds\src\aimx\prod\cpprestsdk
ts=27625 (PASS0) onecore\ds\ds\src\aimx\prod\aimxsrv\dll(10): distributed work started.
ts=27625 (PASS0) onecore\ds\ds\src\aimx\prod\aimxsrv\dll(10): distributed work completed.
ts=27625 reading parent chain of e:\os\src\onecore\ds\ds\src\aimx\prod\admcpsrv\lib
ts=27625 reading parent chain of e:\os\src\onecore\ds\ds\src\aimx\prod\admcpsrv
ts=27625 reading parent chain of e:\os\src\onecore\ds\ds\src\aimx\prod\adpsmcpsvr
ts=27625 reading parent chain of e:\os\src\onecore\ds\ds\src\aimx\prod\agents\adtoolagent
ts=27625 reading parent chain of e:\os\src\onecore\ds\ds\src\aimx\prod\agents
ts=27625 reading parent chain of e:\os\src\onecore\ds\ds\src\aimx\prod\llmclientlib
ts=27625 reading parent chain of e:\os\src\onecore\ds\ds\src\aimx\prod\mcpserversample
ts=27625 scanning focus directory e:\os\src\onecore\ds\ds\src\aimx\prod\aimxsrv
ts=27703 BUILD: Processing dependencies...
ts=27703 (PASS0) onecore\ds\ds\src\aimx\prod\mcpserversample(24): sent to build client (6).
ts=27719 (PASS0) onecore\ds\ds\src\aimx\prod\mcpserversample(24): distributed work started.
ts=27719 (PASS0) onecore\ds\ds\src\aimx\prod\mcpserversample(24): distributed work completed.
ts=27719 (PASS0) onecore\ds\ds\src\aimx\prod\mcpserversample(24): sent to build client (4).
ts=27735 (PASS0) onecore\ds\ds\src\aimx\prod\mcpserversample(24): distributed work started.
ts=27735 (PASS0) onecore\ds\ds\src\aimx\prod\mcpserversample(24): distributed work completed.
ts=27985 (PASS0) onecore\ds\ds\src\aimx\prod\cpprestsdk(17): sent to build client (6).
ts=29719 (PASS0) onecore\ds\ds\src\aimx\prod\cpprestsdk(17): distributed work started.
ts=29719 (PASS0) onecore\ds\ds\src\aimx\prod\cpprestsdk(17): distributed work completed.
ts=29735 (PASS0) onecore\ds\ds\src\aimx\prod\adpsmcpsvr(20): sent to build client (4).
ts=29735 (PASS0) onecore\ds\ds\src\aimx\prod\adpsmcpsvr(20): distributed work started.
ts=29735 (PASS0) onecore\ds\ds\src\aimx\prod\adpsmcpsvr(20): distributed work completed.
ts=29735 (PASS0) onecore\ds\ds\src\aimx\prod\adpsmcpsvr(20): sent to build client (6).
ts=29766 (PASS0) onecore\ds\ds\src\aimx\prod\adpsmcpsvr(20): distributed work started.
ts=29766 (PASS0) onecore\ds\ds\src\aimx\prod\adpsmcpsvr(20): distributed work completed.
ts=29766 (PASS0) onecore\ds\ds\src\aimx\prod\llmclientlib(23): sent to build client (4).
ts=29766 (PASS0) onecore\ds\ds\src\aimx\prod\llmclientlib(23): distributed work started.
ts=29766 (PASS0) onecore\ds\ds\src\aimx\prod\llmclientlib(23): distributed work completed.
ts=29782 (PASS0) onecore\ds\ds\src\aimx\prod\llmclientlib(23): sent to build client (6).
ts=29782 (PASS0) onecore\ds\ds\src\aimx\prod\llmclientlib(23): distributed work started.
ts=29782 (PASS0) onecore\ds\ds\src\aimx\prod\llmclientlib(23): distributed work completed.
ts=29782 (PASS1) onecore\ds\ds\src\aimx\prod\aimxsrv\server(13): pre-build pending
ts=29782 BUILD: Scanning for circular dependencies...
ts=29782 BUILD: Processing dependencies complete
ts=29782 (onecore\ds\ds\src\aimx\prod\aimxsrv\server) e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\aimxsrv\server\objfre\amd64\_objects.mac created (133970869699797413), sources (133970660830630313), sources recorded (133970663047677591)
ts=29797 (onecore\ds\ds\src\aimx\prod\mcpserversample) e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\mcpserversample\objfre\amd64\_objects.mac is current (133970823994350391), sources (133970663047677591), sources recorded (133970663047677591)
ts=29797 (onecore\ds\ds\src\aimx\prod\llmclientlib) e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\llmclientlib\objfre\amd64\_objects.mac is current (133970823994370660), sources (133969941762565131), sources recorded (133969941762565131)
ts=29797 (onecore\ds\ds\src\aimx\prod\adpsmcpsvr) e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\adpsmcpsvr\objfre\amd64\_objects.mac is current (133970869189319032), sources (133970861593917088), sources recorded (133970861593917088)
ts=29797 (onecore\ds\ds\src\aimx\prod\aimxsrv\serviceinstaller) e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\aimxsrv\serviceinstaller\objfre\amd64\_objects.mac created (133970869699876474), sources (133969941762565131), sources recorded (133969941762565131)
ts=29797 (onecore\ds\ds\src\aimx\prod\aimxsrv\dll) e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\aimxsrv\dll\objfre\amd64\_objects.mac created (133970869699876474), sources (133970823180197868), sources recorded (133970823180197868)
ts=29797 (onecore\ds\ds\src\aimx\prod\aimxsrv\idl) e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\aimxsrv\idl\objfre\amd64\_objects.mac created (133970869699876474), sources (133969941762565131), sources recorded (133969941762565131)
ts=29797 (onecore\ds\ds\src\aimx\prod\aimxsrv\client\dll) e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\aimxsrv\client\dll\objfre\amd64\_objects.mac created (133970869699876474), sources (133969941762565131), sources recorded (133969941762565131)
ts=29797 (onecore\ds\ds\src\aimx\prod\aimxsrv\client\lib) e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\aimxsrv\client\lib\objfre\amd64\_objects.mac created (133970869699876474), sources (133969941762565131), sources recorded (133969941762565131)
ts=29797 (onecore\ds\ds\src\aimx\prod\aimxsrv\powershell) e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\aimxsrv\powershell\objfre\amd64\_objects.mac created (133970869699876474), sources (133970660830610246), sources recorded (133970660830610246)
ts=30157 (PASS0) onecore\ds\ds\src\aimx\prod\aimxsrv\idl(11): in transit: passQ->distributedQ.
ts=30157 (PASS0) onecore\ds\ds\src\aimx\prod\aimxsrv\idl(11): submitted to distributedQ.
ts=30157 (PASS0) onecore\ds\ds\src\aimx\prod\aimxsrv\client\lib(16): in transit: passQ->distributedQ.
ts=30157 (PASS0) onecore\ds\ds\src\aimx\prod\aimxsrv\client\lib(16): submitted to distributedQ.
ts=30157 (PASS0) onecore\ds\ds\src\aimx\prod\aimxsrv\idl(11): sent to build client (4).
ts=30157 (PASS0) onecore\ds\ds\src\aimx\prod\aimxsrv\client\dll(14): in transit: passQ->distributedQ.
ts=30157 (PASS0) onecore\ds\ds\src\aimx\prod\aimxsrv\client\dll(14): submitted to distributedQ.
ts=30157 (PASS0) onecore\ds\ds\src\aimx\prod\aimxsrv\idl(11): distributed work started.
ts=30157 (PASS0) onecore\ds\ds\src\aimx\prod\aimxsrv\powershell(12): in transit: passQ->distributedQ.
ts=30157 (PASS0) onecore\ds\ds\src\aimx\prod\aimxsrv\client\lib(16): sent to build client (6).
ts=30157 (PASS0) onecore\ds\ds\src\aimx\prod\aimxsrv\client\dll(14): sent to build client (1).
ts=30157 (PASS0) onecore\ds\ds\src\aimx\prod\aimxsrv\powershell(12): submitted to distributedQ.
ts=30157 (PASS0) onecore\ds\ds\src\aimx\prod\aimxsrv\server(13): in transit: passQ->distributedQ.
ts=30157 (PASS0) onecore\ds\ds\src\aimx\prod\aimxsrv\powershell(12): sent to build client (5).
ts=30157 (PASS0) onecore\ds\ds\src\aimx\prod\aimxsrv\server(13): submitted to distributedQ.
ts=30157 (PASS0) onecore\ds\ds\src\aimx\prod\aimxsrv\dll(10): in transit: passQ->distributedQ.
ts=30157 (PASS0) onecore\ds\ds\src\aimx\prod\aimxsrv\dll(10): submitted to distributedQ.
ts=30157 (PASS0) onecore\ds\ds\src\aimx\prod\aimxsrv\server(13): sent to build client (7).
ts=30157 (PASS0) onecore\ds\ds\src\aimx\prod\mcpserversample(24): in transit: passQ->distributedQ.
ts=30157 (PASS0) onecore\ds\ds\src\aimx\prod\aimxsrv\client\lib(16): distributed work started.
ts=30157 (PASS0) onecore\ds\ds\src\aimx\prod\aimxsrv\client\dll(14): distributed work started.
ts=30157 (PASS0) onecore\ds\ds\src\aimx\prod\aimxsrv\server(13): distributed work started.
ts=30157 (PASS0) onecore\ds\ds\src\aimx\prod\aimxsrv\powershell(12): distributed work started.
ts=30157 (PASS0) onecore\ds\ds\src\aimx\prod\mcpserversample(24): submitted to distributedQ.
ts=30172 (PASS0) onecore\ds\ds\src\aimx\prod\aimxsrv\dll(10): sent to build client (8).
ts=30172 (PASS0) onecore\ds\ds\src\aimx\prod\adpsmcpsvr(20): in transit: passQ->distributedQ.
ts=30172 (PASS0) onecore\ds\ds\src\aimx\prod\adpsmcpsvr(20): submitted to distributedQ.
ts=30172 (PASS0) onecore\ds\ds\src\aimx\prod\aimxsrv\dll(10): distributed work started.
ts=30172 (PASS0) onecore\ds\ds\src\aimx\prod\llmclientlib(23): in transit: passQ->distributedQ.
ts=30172 (PASS0) onecore\ds\ds\src\aimx\prod\mcpserversample(24): sent to build client (9).
ts=30172 (PASS0) onecore\ds\ds\src\aimx\prod\llmclientlib(23): submitted to distributedQ.
ts=30172 (PASS0) onecore\ds\ds\src\aimx\prod\mcpserversample(24): distributed work started.
ts=30172 (PASS0) onecore\ds\ds\src\aimx\prod\adpsmcpsvr(20): sent to build client (10).
ts=30172 (PASS0) onecore\ds\ds\src\aimx\prod\adpsmcpsvr(20): distributed work started.
ts=30172 (PASS0) onecore\ds\ds\src\aimx\prod\llmclientlib(23): sent to build client (11).
ts=30172 (PASS0) onecore\ds\ds\src\aimx\prod\llmclientlib(23): distributed work started.
ts=32000 (PASS0) onecore\ds\ds\src\aimx\prod\aimxsrv\client\lib(16): distributed work completed.
ts=32000 (PASS0) onecore\ds\ds\src\aimx\prod\aimxsrv\client\lib(16): operation completed.
ts=32016 (PASS0) onecore\ds\ds\src\aimx\prod\aimxsrv\client\dll(14): distributed work completed.
ts=32016 (PASS0) onecore\ds\ds\src\aimx\prod\aimxsrv\client\dll(14): operation completed.
ts=32063 (PASS0) onecore\ds\ds\src\aimx\prod\llmclientlib(23): distributed work completed.
ts=32063 (PASS0) onecore\ds\ds\src\aimx\prod\llmclientlib(23): operation completed.
ts=32188 (PASS0) onecore\ds\ds\src\aimx\prod\mcpserversample(24): distributed work completed.
ts=32188 (PASS0) onecore\ds\ds\src\aimx\prod\mcpserversample(24): operation completed.
ts=32203 (PASS0) onecore\ds\ds\src\aimx\prod\aimxsrv\server(13): distributed work completed.
ts=32203 (PASS0) onecore\ds\ds\src\aimx\prod\aimxsrv\server(13): operation completed.
ts=32219 (PASS0) onecore\ds\ds\src\aimx\prod\aimxsrv\dll(10): distributed work completed.
ts=32219 (PASS0) onecore\ds\ds\src\aimx\prod\aimxsrv\dll(10): operation completed.
ts=32250 (PASS0) onecore\ds\ds\src\aimx\prod\adpsmcpsvr(20): distributed work completed.
ts=32250 (PASS0) onecore\ds\ds\src\aimx\prod\adpsmcpsvr(20): operation completed.
ts=32563 (PASS0) onecore\ds\ds\src\aimx\prod\aimxsrv\idl(11): distributed work completed.
ts=32563 (PASS0) onecore\ds\ds\src\aimx\prod\aimxsrv\idl(11): operation completed.
ts=33297 (PASS0) onecore\ds\ds\src\aimx\prod\aimxsrv\powershell(12): distributed work completed.
ts=33297 (PASS0) onecore\ds\ds\src\aimx\prod\aimxsrv\powershell(12): operation completed.
ts=34219 (PASS0) processing complete.
ts=34219 (PASS1) onecore\ds\ds\src\aimx\prod\aimxsrv\client\lib(16): in transit: passQ->distributedQ.
ts=34219 (PASS1) onecore\ds\ds\src\aimx\prod\aimxsrv\client\lib(16): submitted to distributedQ.
ts=34219 (PASS1) onecore\ds\ds\src\aimx\prod\aimxsrv\client\dll(14): in transit: passQ->distributedQ.
ts=34219 (PASS1) onecore\ds\ds\src\aimx\prod\aimxsrv\client\dll(14): submitted to distributedQ.
ts=34219 (PASS1) onecore\ds\ds\src\aimx\prod\aimxsrv\powershell(12): in transit: passQ->distributedQ.
ts=34219 (PASS1) onecore\ds\ds\src\aimx\prod\aimxsrv\client\lib(16): sent to build client (12).
ts=34219 (PASS1) onecore\ds\ds\src\aimx\prod\aimxsrv\powershell(12): submitted to distributedQ.
ts=34219 (PASS1) onecore\ds\ds\src\aimx\prod\aimxsrv\serviceinstaller(15): in transit: passQ->distributedQ.
ts=34219 (PASS1) onecore\ds\ds\src\aimx\prod\aimxsrv\client\dll(14): sent to build client (5).
ts=34219 (PASS1) onecore\ds\ds\src\aimx\prod\aimxsrv\client\lib(16): distributed work started.
ts=34219 (PASS1) onecore\ds\ds\src\aimx\prod\aimxsrv\serviceinstaller(15): submitted to distributedQ.
ts=34219 (PASS1) onecore\ds\ds\src\aimx\prod\aimxsrv\powershell(12): sent to build client (4).
ts=34219 (PASS1) onecore\ds\ds\src\aimx\prod\aimxsrv\serviceinstaller(15): sent to build client (10).
ts=34219 (PASS1) onecore\ds\ds\src\aimx\prod\aimxsrv\dll(10): in transit: passQ->distributedQ.
ts=34219 (PASS1) onecore\ds\ds\src\aimx\prod\aimxsrv\dll(10): submitted to distributedQ.
ts=34219 (PASS1) onecore\ds\ds\src\aimx\prod\mcpserversample(24): in transit: passQ->distributedQ.
ts=34219 (PASS1) onecore\ds\ds\src\aimx\prod\mcpserversample(24): submitted to distributedQ.
ts=34219 (PASS1) onecore\ds\ds\src\aimx\prod\aimxsrv\dll(10): sent to build client (8).
ts=34219 (PASS1) onecore\ds\ds\src\aimx\prod\cpprestsdk(17): in transit: passQ->distributedQ.
ts=34219 (PASS1) onecore\ds\ds\src\aimx\prod\mcpserversample(24): sent to build client (7).
ts=34219 (PASS1) onecore\ds\ds\src\aimx\prod\cpprestsdk(17): submitted to distributedQ.
ts=34219 (PASS1) onecore\ds\ds\src\aimx\prod\adpsmcpsvr(20): in transit: passQ->distributedQ.
ts=34219 (PASS1) onecore\ds\ds\src\aimx\prod\cpprestsdk(17): sent to build client (9).
ts=34219 (PASS1) onecore\ds\ds\src\aimx\prod\adpsmcpsvr(20): submitted to distributedQ.
ts=34219 (PASS1) onecore\ds\ds\src\aimx\prod\aimxsrv\client\dll(14): distributed work started.
ts=34235 (PASS1) onecore\ds\ds\src\aimx\prod\aimxsrv\dll(10): distributed work started.
ts=34235 (PASS1) onecore\ds\ds\src\aimx\prod\aimxsrv\serviceinstaller(15): distributed work started.
ts=34235 (PASS1) onecore\ds\ds\src\aimx\prod\llmclientlib(23): in transit: passQ->distributedQ.
ts=34235 (PASS1) onecore\ds\ds\src\aimx\prod\aimxsrv\powershell(12): distributed work started.
ts=34235 (PASS1) onecore\ds\ds\src\aimx\prod\adpsmcpsvr(20): sent to build client (11).
ts=34235 (PASS1) onecore\ds\ds\src\aimx\prod\mcpserversample(24): distributed work started.
ts=34235 (PASS1) onecore\ds\ds\src\aimx\prod\cpprestsdk(17): distributed work started.
ts=34235 (PASS1) onecore\ds\ds\src\aimx\prod\llmclientlib(23): submitted to distributedQ.
ts=34235 (PASS1) onecore\ds\ds\src\aimx\prod\adpsmcpsvr(20): distributed work started.
ts=34235 (PASS1) onecore\ds\ds\src\aimx\prod\llmclientlib(23): sent to build client (1).
ts=34235 (PASS1) onecore\ds\ds\src\aimx\prod\llmclientlib(23): distributed work started.
ts=38782 (PASS1) onecore\ds\ds\src\aimx\prod\aimxsrv\serviceinstaller(15): distributed work completed.
ts=38782 (PASS1) onecore\ds\ds\src\aimx\prod\aimxsrv\serviceinstaller(15): operation completed.
ts=40422 (PASS1) onecore\ds\ds\src\aimx\prod\aimxsrv\powershell(12): distributed work completed.
ts=40422 (PASS1) onecore\ds\ds\src\aimx\prod\aimxsrv\powershell(12): operation completed.
ts=40563 (PASS1) onecore\ds\ds\src\aimx\prod\llmclientlib(23): distributed work completed.
ts=40563 (PASS1) onecore\ds\ds\src\aimx\prod\llmclientlib(23): operation completed.
ts=47344 (PASS1) onecore\ds\ds\src\aimx\prod\aimxsrv\client\lib(16): distributed work completed.
ts=47344 (PASS1) onecore\ds\ds\src\aimx\prod\aimxsrv\client\lib(16): operation completed.
ts=47563 (PASS1) onecore\ds\ds\src\aimx\prod\aimxsrv\client\dll(14): distributed work completed.
ts=47563 (PASS1) onecore\ds\ds\src\aimx\prod\aimxsrv\client\dll(14): operation completed.
ts=48032 (PASS1) onecore\ds\ds\src\aimx\prod\mcpserversample(24): distributed work completed.
ts=48032 (PASS1) onecore\ds\ds\src\aimx\prod\mcpserversample(24): operation completed.
ts=50016 (PASS1) onecore\ds\ds\src\aimx\prod\aimxsrv\dll(10): distributed work completed.
ts=50016 (PASS1) onecore\ds\ds\src\aimx\prod\aimxsrv\dll(10): operation completed.
ts=52297 (PASS1) onecore\ds\ds\src\aimx\prod\adpsmcpsvr(20): distributed work completed.
ts=52297 (PASS1) onecore\ds\ds\src\aimx\prod\adpsmcpsvr(20): operation completed.
ts=75828 (PASS1) onecore\ds\ds\src\aimx\prod\cpprestsdk(17): distributed work completed.
ts=75828 
READYQ INSERTED: e:\os\src\onecore\ds\ds\src\aimx\prod\aimxsrv\server (PASS1 RC:0, pending:0, queued:1)
ts=75828 (PASS1) onecore\ds\ds\src\aimx\prod\cpprestsdk(17): operation completed.
ts=75828 (PASS1) onecore\ds\ds\src\aimx\prod\aimxsrv\server(13): transition: readyQ->passQ.
ts=75828 (PASS1) flush readyQ: 1 work items transitioned to pass queue.
ts=75828 (PASS1) onecore\ds\ds\src\aimx\prod\aimxsrv\server(13): in transit: passQ->distributedQ.
ts=75828 (PASS1) onecore\ds\ds\src\aimx\prod\aimxsrv\server(13): submitted to distributedQ.
ts=75844 (PASS1) onecore\ds\ds\src\aimx\prod\aimxsrv\server(13): sent to build client (6).
ts=75844 (PASS1) onecore\ds\ds\src\aimx\prod\aimxsrv\server(13): distributed work started.
ts=98969 (PASS1) onecore\ds\ds\src\aimx\prod\aimxsrv\server(13): distributed work completed.
ts=98985 (PASS1) onecore\ds\ds\src\aimx\prod\aimxsrv\server(13): operation completed.
ts=99047 (PASS1) processing complete.
ts=99047 (PASS2) onecore\ds\ds\src\aimx\prod\aimxsrv\client\dll(14): in transit: passQ->distributedQ.
ts=99047 (PASS2) onecore\ds\ds\src\aimx\prod\aimxsrv\client\dll(14): submitted to distributedQ.
ts=99047 (PASS2) onecore\ds\ds\src\aimx\prod\aimxsrv\powershell(12): in transit: passQ->distributedQ.
ts=99047 (PASS2) onecore\ds\ds\src\aimx\prod\aimxsrv\powershell(12): submitted to distributedQ.
ts=99047 (PASS2) onecore\ds\ds\src\aimx\prod\aimxsrv\serviceinstaller(15): in transit: passQ->distributedQ.
ts=99047 (PASS2) onecore\ds\ds\src\aimx\prod\aimxsrv\client\dll(14): sent to build client (9).
ts=99047 (PASS2) onecore\ds\ds\src\aimx\prod\aimxsrv\serviceinstaller(15): submitted to distributedQ.
ts=99047 (PASS2) onecore\ds\ds\src\aimx\prod\aimxsrv\dll(10): in transit: passQ->distributedQ.
ts=99047 (PASS2) onecore\ds\ds\src\aimx\prod\aimxsrv\powershell(12): sent to build client (6).
ts=99047 (PASS2) onecore\ds\ds\src\aimx\prod\aimxsrv\client\dll(14): distributed work started.
ts=99047 (PASS2) onecore\ds\ds\src\aimx\prod\aimxsrv\serviceinstaller(15): sent to build client (11).
ts=99047 (PASS2) onecore\ds\ds\src\aimx\prod\aimxsrv\dll(10): submitted to distributedQ.
ts=99047 (PASS2) onecore\ds\ds\src\aimx\prod\aimxsrv\dll(10): sent to build client (8).
ts=99078 (PASS2) onecore\ds\ds\src\aimx\prod\aimxsrv\powershell(12): distributed work started.
ts=99078 (PASS2) onecore\ds\ds\src\aimx\prod\aimxsrv\serviceinstaller(15): distributed work started.
ts=99078 (PASS2) onecore\ds\ds\src\aimx\prod\aimxsrv\dll(10): distributed work started.
ts=102735 (PASS2) onecore\ds\ds\src\aimx\prod\aimxsrv\powershell(12): distributed work completed.
ts=102735 (PASS2) onecore\ds\ds\src\aimx\prod\aimxsrv\powershell(12): operation completed.
ts=105344 (PASS2) onecore\ds\ds\src\aimx\prod\aimxsrv\client\dll(14): distributed work completed.
ts=105344 (PASS2) onecore\ds\ds\src\aimx\prod\aimxsrv\client\dll(14): operation completed.
ts=106235 (PASS2) onecore\ds\ds\src\aimx\prod\aimxsrv\serviceinstaller(15): distributed work completed.
ts=106235 (PASS2) onecore\ds\ds\src\aimx\prod\aimxsrv\serviceinstaller(15): operation completed.
ts=110813 (PASS2) onecore\ds\ds\src\aimx\prod\aimxsrv\dll(10): distributed work completed.
ts=110813 (PASS2) onecore\ds\ds\src\aimx\prod\aimxsrv\dll(10): operation completed.
ts=111203 (PASS2) processing complete.
ts=111203 PASS_INDEPENDENT processing complete.
ts=111203 PASS_INDEPENDENT processing complete.

DIR EVENTS: onecore\ds\ds\src\aimx\prod\aimxsrv\server (IN FOCUS)
  CREATED=1
  FOUND_IN_DIRS=1
  INITIAL_DIRECTORY=0
  FOUND_IN_DATABASE=1
  CLEAN_ALL=1
  CLEAN_ALL_FORCED=0
  CLEAN_PASS0=1
  CLEAN_PASS1=1
  CLEAN_PASS2=1
  CLEAN_PASS_INDEPENDENT=1
  ALLOC_WORKITEM_PASS0=1
  ALLOC_WORKITEM_PASS1=1
  ALLOC_WORKITEM_PASS2=0
  ALLOC_WORKITEM_PASS_INDEPENDENT=0
  SOURCES_READ=1
  NODE_DIR=1
  DIRS_PROJ_SKIPPED=0
  MSBUILD_PROJECT_FILE_READ=0
  MSBUILD_POST_BUILD_PROJECT_FILE_READ=0
  TEST_CODE=0
  SAMPLE_CODE=0
  TESTCODE_SKIPPED=0
  PASS0_NEEDED=1
  PASS1_NEEDED=1
  PASS2_NEEDED=0
  PASS3_NEEDED=0
  SOURCES_DEP_READ=0
  QUEUE_TO_PASS_LIST_1=1
  QUEUE_TO_PASS_LIST_2=1
  QUEUE_TO_PASS_LIST_3=0
  ADD_DEPENDENCIES_TO_GRAPH=1
  SET_NON_PRODUCT=0
  SET_NON_CRITICAL=0
  SET_CS_WINDOWS=0
  SET_CS_XBOX=0
  SET_CS_PHONE=0
  SET_CS_AZURE=0
  SCAN_COMPLETE=1

  BUILD PASS0 EVENTS: onecore\ds\ds\src\aimx\prod\aimxsrv\server
    BUILD_CREATED=1
    TASK_CREATED=0
    ADDED_TO_DEPENDENCY_GRAPH=1
    ADDED_TO_PASSQ=1
    ADDED_TO_TASKQ=0
    TRANSITION_STANDBYQ_TO_PASSQ=0
    ADDED_TO_PASS0_Q=1
    ADDED_TO_PASS1_Q=0
    ADDED_TO_PASS2_Q=0
    ADDED_TO_PASS3_Q=0
    ADDED_TO_PASS_INDEPENDENT_Q=0
    ADDED_TO_TASK_Q=0
    REMOVED_FROM_PASSQ=1
    DEPENDENT_MOVED_FROM_READYQ_TO_PASSQ=0
    NMAKE_COMMAND_LINE=1
    MSBUILD_COMMAND_LINE=0
    SUBMIT_TO_DISTRIBUTED_SCHEDULER=1
    DISTRIBUTED_SCHEDULER_WORK_STARTED=1
    DISTRIBUTED_SCHEDULER_WORK_COMPLETED=1
    ADDED_TO_READYQ=0
    BYPASSED=0
    SENT_TO_CLIENT=1
    INCREMENTAL=0
    BUILD_PASS_TERMINATED_FOR_ERRORS=0
    BUILD_OPERATION_COMPLETE=1

  BUILD PASS1 EVENTS: onecore\ds\ds\src\aimx\prod\aimxsrv\server
    BUILD_CREATED=1
    TASK_CREATED=0
    ADDED_TO_DEPENDENCY_GRAPH=1
    ADDED_TO_PASSQ=1
    ADDED_TO_TASKQ=0
    TRANSITION_STANDBYQ_TO_PASSQ=0
    ADDED_TO_PASS0_Q=0
    ADDED_TO_PASS1_Q=1
    ADDED_TO_PASS2_Q=0
    ADDED_TO_PASS3_Q=0
    ADDED_TO_PASS_INDEPENDENT_Q=0
    ADDED_TO_TASK_Q=0
    REMOVED_FROM_PASSQ=1
    DEPENDENT_MOVED_FROM_READYQ_TO_PASSQ=1
    NMAKE_COMMAND_LINE=1
    MSBUILD_COMMAND_LINE=0
    SUBMIT_TO_DISTRIBUTED_SCHEDULER=1
    DISTRIBUTED_SCHEDULER_WORK_STARTED=1
    DISTRIBUTED_SCHEDULER_WORK_COMPLETED=1
    ADDED_TO_READYQ=1
    BYPASSED=0
    SENT_TO_CLIENT=1
    INCREMENTAL=0
    BUILD_PASS_TERMINATED_FOR_ERRORS=0
    BUILD_OPERATION_COMPLETE=1

DIR EVENTS: onecore\ds\ds\src\aimx\prod\mcpserversample 
  CREATED=1
  FOUND_IN_DIRS=1
  INITIAL_DIRECTORY=0
  FOUND_IN_DATABASE=1
  CLEAN_ALL=0
  CLEAN_ALL_FORCED=0
  CLEAN_PASS0=1
  CLEAN_PASS1=1
  CLEAN_PASS2=0
  CLEAN_PASS_INDEPENDENT=0
  ALLOC_WORKITEM_PASS0=1
  ALLOC_WORKITEM_PASS1=1
  ALLOC_WORKITEM_PASS2=0
  ALLOC_WORKITEM_PASS_INDEPENDENT=0
  SOURCES_READ=1
  NODE_DIR=1
  DIRS_PROJ_SKIPPED=0
  MSBUILD_PROJECT_FILE_READ=0
  MSBUILD_POST_BUILD_PROJECT_FILE_READ=0
  TEST_CODE=0
  SAMPLE_CODE=0
  TESTCODE_SKIPPED=0
  PASS0_NEEDED=1
  PASS1_NEEDED=1
  PASS2_NEEDED=0
  PASS3_NEEDED=0
  SOURCES_DEP_READ=0
  QUEUE_TO_PASS_LIST_1=0
  QUEUE_TO_PASS_LIST_2=0
  QUEUE_TO_PASS_LIST_3=1
  ADD_DEPENDENCIES_TO_GRAPH=0
  SET_NON_PRODUCT=0
  SET_NON_CRITICAL=0
  SET_CS_WINDOWS=0
  SET_CS_XBOX=0
  SET_CS_PHONE=0
  SET_CS_AZURE=0
  SCAN_COMPLETE=1

  BUILD PASS0 EVENTS: onecore\ds\ds\src\aimx\prod\mcpserversample
    BUILD_CREATED=1
    TASK_CREATED=0
    ADDED_TO_DEPENDENCY_GRAPH=1
    ADDED_TO_PASSQ=1
    ADDED_TO_TASKQ=0
    TRANSITION_STANDBYQ_TO_PASSQ=0
    ADDED_TO_PASS0_Q=1
    ADDED_TO_PASS1_Q=0
    ADDED_TO_PASS2_Q=0
    ADDED_TO_PASS3_Q=0
    ADDED_TO_PASS_INDEPENDENT_Q=0
    ADDED_TO_TASK_Q=0
    REMOVED_FROM_PASSQ=1
    DEPENDENT_MOVED_FROM_READYQ_TO_PASSQ=0
    NMAKE_COMMAND_LINE=1
    MSBUILD_COMMAND_LINE=0
    SUBMIT_TO_DISTRIBUTED_SCHEDULER=1
    DISTRIBUTED_SCHEDULER_WORK_STARTED=1
    DISTRIBUTED_SCHEDULER_WORK_COMPLETED=1
    ADDED_TO_READYQ=0
    BYPASSED=0
    SENT_TO_CLIENT=1
    INCREMENTAL=0
    BUILD_PASS_TERMINATED_FOR_ERRORS=0
    BUILD_OPERATION_COMPLETE=1

  BUILD PASS1 EVENTS: onecore\ds\ds\src\aimx\prod\mcpserversample
    BUILD_CREATED=1
    TASK_CREATED=0
    ADDED_TO_DEPENDENCY_GRAPH=1
    ADDED_TO_PASSQ=1
    ADDED_TO_TASKQ=0
    TRANSITION_STANDBYQ_TO_PASSQ=0
    ADDED_TO_PASS0_Q=0
    ADDED_TO_PASS1_Q=1
    ADDED_TO_PASS2_Q=0
    ADDED_TO_PASS3_Q=0
    ADDED_TO_PASS_INDEPENDENT_Q=0
    ADDED_TO_TASK_Q=0
    REMOVED_FROM_PASSQ=1
    DEPENDENT_MOVED_FROM_READYQ_TO_PASSQ=0
    NMAKE_COMMAND_LINE=1
    MSBUILD_COMMAND_LINE=0
    SUBMIT_TO_DISTRIBUTED_SCHEDULER=1
    DISTRIBUTED_SCHEDULER_WORK_STARTED=1
    DISTRIBUTED_SCHEDULER_WORK_COMPLETED=1
    ADDED_TO_READYQ=0
    BYPASSED=0
    SENT_TO_CLIENT=1
    INCREMENTAL=0
    BUILD_PASS_TERMINATED_FOR_ERRORS=0
    BUILD_OPERATION_COMPLETE=1

DIR EVENTS: onecore\ds\ds\src\aimx\prod\admcpsrv\lib 
  CREATED=1
  FOUND_IN_DIRS=0
  INITIAL_DIRECTORY=0
  FOUND_IN_DATABASE=0
  CLEAN_ALL=0
  CLEAN_ALL_FORCED=0
  CLEAN_PASS0=0
  CLEAN_PASS1=0
  CLEAN_PASS2=0
  CLEAN_PASS_INDEPENDENT=0
  ALLOC_WORKITEM_PASS0=0
  ALLOC_WORKITEM_PASS1=0
  ALLOC_WORKITEM_PASS2=0
  ALLOC_WORKITEM_PASS_INDEPENDENT=0
  SOURCES_READ=0
  NODE_DIR=0
  DIRS_PROJ_SKIPPED=0
  MSBUILD_PROJECT_FILE_READ=0
  MSBUILD_POST_BUILD_PROJECT_FILE_READ=0
  TEST_CODE=0
  SAMPLE_CODE=0
  TESTCODE_SKIPPED=0
  PASS0_NEEDED=0
  PASS1_NEEDED=0
  PASS2_NEEDED=0
  PASS3_NEEDED=0
  SOURCES_DEP_READ=0
  QUEUE_TO_PASS_LIST_1=0
  QUEUE_TO_PASS_LIST_2=0
  QUEUE_TO_PASS_LIST_3=0
  ADD_DEPENDENCIES_TO_GRAPH=0
  SET_NON_PRODUCT=0
  SET_NON_CRITICAL=0
  SET_CS_WINDOWS=0
  SET_CS_XBOX=0
  SET_CS_PHONE=0
  SET_CS_AZURE=0
  SCAN_COMPLETE=0

DIR EVENTS: onecore\ds\ds\src\aimx\prod\llmclientlib 
  CREATED=1
  FOUND_IN_DIRS=1
  INITIAL_DIRECTORY=0
  FOUND_IN_DATABASE=1
  CLEAN_ALL=0
  CLEAN_ALL_FORCED=0
  CLEAN_PASS0=1
  CLEAN_PASS1=1
  CLEAN_PASS2=0
  CLEAN_PASS_INDEPENDENT=0
  ALLOC_WORKITEM_PASS0=1
  ALLOC_WORKITEM_PASS1=1
  ALLOC_WORKITEM_PASS2=0
  ALLOC_WORKITEM_PASS_INDEPENDENT=0
  SOURCES_READ=1
  NODE_DIR=1
  DIRS_PROJ_SKIPPED=0
  MSBUILD_PROJECT_FILE_READ=0
  MSBUILD_POST_BUILD_PROJECT_FILE_READ=0
  TEST_CODE=0
  SAMPLE_CODE=0
  TESTCODE_SKIPPED=0
  PASS0_NEEDED=1
  PASS1_NEEDED=1
  PASS2_NEEDED=0
  PASS3_NEEDED=0
  SOURCES_DEP_READ=0
  QUEUE_TO_PASS_LIST_1=0
  QUEUE_TO_PASS_LIST_2=0
  QUEUE_TO_PASS_LIST_3=1
  ADD_DEPENDENCIES_TO_GRAPH=0
  SET_NON_PRODUCT=0
  SET_NON_CRITICAL=0
  SET_CS_WINDOWS=0
  SET_CS_XBOX=0
  SET_CS_PHONE=0
  SET_CS_AZURE=0
  SCAN_COMPLETE=1

  BUILD PASS0 EVENTS: onecore\ds\ds\src\aimx\prod\llmclientlib
    BUILD_CREATED=1
    TASK_CREATED=0
    ADDED_TO_DEPENDENCY_GRAPH=1
    ADDED_TO_PASSQ=1
    ADDED_TO_TASKQ=0
    TRANSITION_STANDBYQ_TO_PASSQ=0
    ADDED_TO_PASS0_Q=1
    ADDED_TO_PASS1_Q=0
    ADDED_TO_PASS2_Q=0
    ADDED_TO_PASS3_Q=0
    ADDED_TO_PASS_INDEPENDENT_Q=0
    ADDED_TO_TASK_Q=0
    REMOVED_FROM_PASSQ=1
    DEPENDENT_MOVED_FROM_READYQ_TO_PASSQ=0
    NMAKE_COMMAND_LINE=1
    MSBUILD_COMMAND_LINE=0
    SUBMIT_TO_DISTRIBUTED_SCHEDULER=1
    DISTRIBUTED_SCHEDULER_WORK_STARTED=1
    DISTRIBUTED_SCHEDULER_WORK_COMPLETED=1
    ADDED_TO_READYQ=0
    BYPASSED=0
    SENT_TO_CLIENT=1
    INCREMENTAL=0
    BUILD_PASS_TERMINATED_FOR_ERRORS=0
    BUILD_OPERATION_COMPLETE=1

  BUILD PASS1 EVENTS: onecore\ds\ds\src\aimx\prod\llmclientlib
    BUILD_CREATED=1
    TASK_CREATED=0
    ADDED_TO_DEPENDENCY_GRAPH=1
    ADDED_TO_PASSQ=1
    ADDED_TO_TASKQ=0
    TRANSITION_STANDBYQ_TO_PASSQ=0
    ADDED_TO_PASS0_Q=0
    ADDED_TO_PASS1_Q=1
    ADDED_TO_PASS2_Q=0
    ADDED_TO_PASS3_Q=0
    ADDED_TO_PASS_INDEPENDENT_Q=0
    ADDED_TO_TASK_Q=0
    REMOVED_FROM_PASSQ=1
    DEPENDENT_MOVED_FROM_READYQ_TO_PASSQ=0
    NMAKE_COMMAND_LINE=1
    MSBUILD_COMMAND_LINE=0
    SUBMIT_TO_DISTRIBUTED_SCHEDULER=1
    DISTRIBUTED_SCHEDULER_WORK_STARTED=1
    DISTRIBUTED_SCHEDULER_WORK_COMPLETED=1
    ADDED_TO_READYQ=0
    BYPASSED=0
    SENT_TO_CLIENT=1
    INCREMENTAL=0
    BUILD_PASS_TERMINATED_FOR_ERRORS=0
    BUILD_OPERATION_COMPLETE=1

DIR EVENTS: onecore\ds\ds\src\aimx\prod\adpsmcpsvr 
  CREATED=1
  FOUND_IN_DIRS=1
  INITIAL_DIRECTORY=0
  FOUND_IN_DATABASE=1
  CLEAN_ALL=0
  CLEAN_ALL_FORCED=0
  CLEAN_PASS0=1
  CLEAN_PASS1=1
  CLEAN_PASS2=0
  CLEAN_PASS_INDEPENDENT=0
  ALLOC_WORKITEM_PASS0=1
  ALLOC_WORKITEM_PASS1=1
  ALLOC_WORKITEM_PASS2=0
  ALLOC_WORKITEM_PASS_INDEPENDENT=0
  SOURCES_READ=1
  NODE_DIR=1
  DIRS_PROJ_SKIPPED=0
  MSBUILD_PROJECT_FILE_READ=0
  MSBUILD_POST_BUILD_PROJECT_FILE_READ=0
  TEST_CODE=0
  SAMPLE_CODE=0
  TESTCODE_SKIPPED=0
  PASS0_NEEDED=1
  PASS1_NEEDED=1
  PASS2_NEEDED=0
  PASS3_NEEDED=0
  SOURCES_DEP_READ=0
  QUEUE_TO_PASS_LIST_1=0
  QUEUE_TO_PASS_LIST_2=0
  QUEUE_TO_PASS_LIST_3=1
  ADD_DEPENDENCIES_TO_GRAPH=0
  SET_NON_PRODUCT=0
  SET_NON_CRITICAL=0
  SET_CS_WINDOWS=0
  SET_CS_XBOX=0
  SET_CS_PHONE=0
  SET_CS_AZURE=0
  SCAN_COMPLETE=1

  BUILD PASS0 EVENTS: onecore\ds\ds\src\aimx\prod\adpsmcpsvr
    BUILD_CREATED=1
    TASK_CREATED=0
    ADDED_TO_DEPENDENCY_GRAPH=1
    ADDED_TO_PASSQ=1
    ADDED_TO_TASKQ=0
    TRANSITION_STANDBYQ_TO_PASSQ=0
    ADDED_TO_PASS0_Q=1
    ADDED_TO_PASS1_Q=0
    ADDED_TO_PASS2_Q=0
    ADDED_TO_PASS3_Q=0
    ADDED_TO_PASS_INDEPENDENT_Q=0
    ADDED_TO_TASK_Q=0
    REMOVED_FROM_PASSQ=1
    DEPENDENT_MOVED_FROM_READYQ_TO_PASSQ=0
    NMAKE_COMMAND_LINE=1
    MSBUILD_COMMAND_LINE=0
    SUBMIT_TO_DISTRIBUTED_SCHEDULER=1
    DISTRIBUTED_SCHEDULER_WORK_STARTED=1
    DISTRIBUTED_SCHEDULER_WORK_COMPLETED=1
    ADDED_TO_READYQ=0
    BYPASSED=0
    SENT_TO_CLIENT=1
    INCREMENTAL=0
    BUILD_PASS_TERMINATED_FOR_ERRORS=0
    BUILD_OPERATION_COMPLETE=1

  BUILD PASS1 EVENTS: onecore\ds\ds\src\aimx\prod\adpsmcpsvr
    BUILD_CREATED=1
    TASK_CREATED=0
    ADDED_TO_DEPENDENCY_GRAPH=1
    ADDED_TO_PASSQ=1
    ADDED_TO_TASKQ=0
    TRANSITION_STANDBYQ_TO_PASSQ=0
    ADDED_TO_PASS0_Q=0
    ADDED_TO_PASS1_Q=1
    ADDED_TO_PASS2_Q=0
    ADDED_TO_PASS3_Q=0
    ADDED_TO_PASS_INDEPENDENT_Q=0
    ADDED_TO_TASK_Q=0
    REMOVED_FROM_PASSQ=1
    DEPENDENT_MOVED_FROM_READYQ_TO_PASSQ=0
    NMAKE_COMMAND_LINE=1
    MSBUILD_COMMAND_LINE=0
    SUBMIT_TO_DISTRIBUTED_SCHEDULER=1
    DISTRIBUTED_SCHEDULER_WORK_STARTED=1
    DISTRIBUTED_SCHEDULER_WORK_COMPLETED=1
    ADDED_TO_READYQ=0
    BYPASSED=0
    SENT_TO_CLIENT=1
    INCREMENTAL=0
    BUILD_PASS_TERMINATED_FOR_ERRORS=0
    BUILD_OPERATION_COMPLETE=1

DIR EVENTS: onecore\ds\ds\src\aimx\prod\cpprestsdk 
  CREATED=1
  FOUND_IN_DIRS=1
  INITIAL_DIRECTORY=0
  FOUND_IN_DATABASE=1
  CLEAN_ALL=0
  CLEAN_ALL_FORCED=0
  CLEAN_PASS0=0
  CLEAN_PASS1=1
  CLEAN_PASS2=0
  CLEAN_PASS_INDEPENDENT=0
  ALLOC_WORKITEM_PASS0=0
  ALLOC_WORKITEM_PASS1=1
  ALLOC_WORKITEM_PASS2=0
  ALLOC_WORKITEM_PASS_INDEPENDENT=0
  SOURCES_READ=0
  NODE_DIR=1
  DIRS_PROJ_SKIPPED=0
  MSBUILD_PROJECT_FILE_READ=1
  MSBUILD_POST_BUILD_PROJECT_FILE_READ=0
  TEST_CODE=0
  SAMPLE_CODE=0
  TESTCODE_SKIPPED=0
  PASS0_NEEDED=1
  PASS1_NEEDED=1
  PASS2_NEEDED=1
  PASS3_NEEDED=0
  SOURCES_DEP_READ=0
  QUEUE_TO_PASS_LIST_1=0
  QUEUE_TO_PASS_LIST_2=0
  QUEUE_TO_PASS_LIST_3=1
  ADD_DEPENDENCIES_TO_GRAPH=0
  SET_NON_PRODUCT=0
  SET_NON_CRITICAL=0
  SET_CS_WINDOWS=0
  SET_CS_XBOX=0
  SET_CS_PHONE=0
  SET_CS_AZURE=0
  SCAN_COMPLETE=1

  BUILD PASS1 EVENTS: onecore\ds\ds\src\aimx\prod\cpprestsdk
    BUILD_CREATED=1
    TASK_CREATED=0
    ADDED_TO_DEPENDENCY_GRAPH=1
    ADDED_TO_PASSQ=1
    ADDED_TO_TASKQ=0
    TRANSITION_STANDBYQ_TO_PASSQ=0
    ADDED_TO_PASS0_Q=0
    ADDED_TO_PASS1_Q=1
    ADDED_TO_PASS2_Q=0
    ADDED_TO_PASS3_Q=0
    ADDED_TO_PASS_INDEPENDENT_Q=0
    ADDED_TO_TASK_Q=0
    REMOVED_FROM_PASSQ=1
    DEPENDENT_MOVED_FROM_READYQ_TO_PASSQ=0
    NMAKE_COMMAND_LINE=0
    MSBUILD_COMMAND_LINE=1
    SUBMIT_TO_DISTRIBUTED_SCHEDULER=1
    DISTRIBUTED_SCHEDULER_WORK_STARTED=1
    DISTRIBUTED_SCHEDULER_WORK_COMPLETED=1
    ADDED_TO_READYQ=0
    BYPASSED=0
    SENT_TO_CLIENT=1
    INCREMENTAL=0
    BUILD_PASS_TERMINATED_FOR_ERRORS=0
    BUILD_OPERATION_COMPLETE=1

DIR EVENTS: onecore\ds\ds\src\aimx\prod\aimxsrv\serviceinstaller (IN FOCUS)
  CREATED=1
  FOUND_IN_DIRS=1
  INITIAL_DIRECTORY=0
  FOUND_IN_DATABASE=1
  CLEAN_ALL=1
  CLEAN_ALL_FORCED=0
  CLEAN_PASS0=1
  CLEAN_PASS1=1
  CLEAN_PASS2=1
  CLEAN_PASS_INDEPENDENT=1
  ALLOC_WORKITEM_PASS0=0
  ALLOC_WORKITEM_PASS1=1
  ALLOC_WORKITEM_PASS2=1
  ALLOC_WORKITEM_PASS_INDEPENDENT=0
  SOURCES_READ=1
  NODE_DIR=1
  DIRS_PROJ_SKIPPED=0
  MSBUILD_PROJECT_FILE_READ=0
  MSBUILD_POST_BUILD_PROJECT_FILE_READ=0
  TEST_CODE=0
  SAMPLE_CODE=0
  TESTCODE_SKIPPED=0
  PASS0_NEEDED=0
  PASS1_NEEDED=1
  PASS2_NEEDED=1
  PASS3_NEEDED=0
  SOURCES_DEP_READ=0
  QUEUE_TO_PASS_LIST_1=1
  QUEUE_TO_PASS_LIST_2=1
  QUEUE_TO_PASS_LIST_3=0
  ADD_DEPENDENCIES_TO_GRAPH=1
  SET_NON_PRODUCT=0
  SET_NON_CRITICAL=0
  SET_CS_WINDOWS=0
  SET_CS_XBOX=0
  SET_CS_PHONE=0
  SET_CS_AZURE=0
  SCAN_COMPLETE=1

  BUILD PASS1 EVENTS: onecore\ds\ds\src\aimx\prod\aimxsrv\serviceinstaller
    BUILD_CREATED=1
    TASK_CREATED=0
    ADDED_TO_DEPENDENCY_GRAPH=1
    ADDED_TO_PASSQ=1
    ADDED_TO_TASKQ=0
    TRANSITION_STANDBYQ_TO_PASSQ=0
    ADDED_TO_PASS0_Q=0
    ADDED_TO_PASS1_Q=1
    ADDED_TO_PASS2_Q=0
    ADDED_TO_PASS3_Q=0
    ADDED_TO_PASS_INDEPENDENT_Q=0
    ADDED_TO_TASK_Q=0
    REMOVED_FROM_PASSQ=1
    DEPENDENT_MOVED_FROM_READYQ_TO_PASSQ=0
    NMAKE_COMMAND_LINE=1
    MSBUILD_COMMAND_LINE=0
    SUBMIT_TO_DISTRIBUTED_SCHEDULER=1
    DISTRIBUTED_SCHEDULER_WORK_STARTED=1
    DISTRIBUTED_SCHEDULER_WORK_COMPLETED=1
    ADDED_TO_READYQ=0
    BYPASSED=0
    SENT_TO_CLIENT=1
    INCREMENTAL=0
    BUILD_PASS_TERMINATED_FOR_ERRORS=0
    BUILD_OPERATION_COMPLETE=1

  BUILD PASS2 EVENTS: onecore\ds\ds\src\aimx\prod\aimxsrv\serviceinstaller
    BUILD_CREATED=1
    TASK_CREATED=0
    ADDED_TO_DEPENDENCY_GRAPH=1
    ADDED_TO_PASSQ=1
    ADDED_TO_TASKQ=0
    TRANSITION_STANDBYQ_TO_PASSQ=0
    ADDED_TO_PASS0_Q=0
    ADDED_TO_PASS1_Q=0
    ADDED_TO_PASS2_Q=1
    ADDED_TO_PASS3_Q=0
    ADDED_TO_PASS_INDEPENDENT_Q=0
    ADDED_TO_TASK_Q=0
    REMOVED_FROM_PASSQ=1
    DEPENDENT_MOVED_FROM_READYQ_TO_PASSQ=0
    NMAKE_COMMAND_LINE=1
    MSBUILD_COMMAND_LINE=0
    SUBMIT_TO_DISTRIBUTED_SCHEDULER=1
    DISTRIBUTED_SCHEDULER_WORK_STARTED=1
    DISTRIBUTED_SCHEDULER_WORK_COMPLETED=1
    ADDED_TO_READYQ=0
    BYPASSED=0
    SENT_TO_CLIENT=1
    INCREMENTAL=0
    BUILD_PASS_TERMINATED_FOR_ERRORS=0
    BUILD_OPERATION_COMPLETE=1

DIR EVENTS: onecore\ds\ds\src\aimx\prod\aimxsrv\dll (IN FOCUS)
  CREATED=1
  FOUND_IN_DIRS=1
  INITIAL_DIRECTORY=0
  FOUND_IN_DATABASE=1
  CLEAN_ALL=1
  CLEAN_ALL_FORCED=0
  CLEAN_PASS0=1
  CLEAN_PASS1=1
  CLEAN_PASS2=1
  CLEAN_PASS_INDEPENDENT=1
  ALLOC_WORKITEM_PASS0=1
  ALLOC_WORKITEM_PASS1=1
  ALLOC_WORKITEM_PASS2=1
  ALLOC_WORKITEM_PASS_INDEPENDENT=0
  SOURCES_READ=1
  NODE_DIR=1
  DIRS_PROJ_SKIPPED=0
  MSBUILD_PROJECT_FILE_READ=0
  MSBUILD_POST_BUILD_PROJECT_FILE_READ=0
  TEST_CODE=0
  SAMPLE_CODE=0
  TESTCODE_SKIPPED=0
  PASS0_NEEDED=1
  PASS1_NEEDED=1
  PASS2_NEEDED=1
  PASS3_NEEDED=0
  SOURCES_DEP_READ=0
  QUEUE_TO_PASS_LIST_1=1
  QUEUE_TO_PASS_LIST_2=1
  QUEUE_TO_PASS_LIST_3=0
  ADD_DEPENDENCIES_TO_GRAPH=1
  SET_NON_PRODUCT=0
  SET_NON_CRITICAL=0
  SET_CS_WINDOWS=0
  SET_CS_XBOX=0
  SET_CS_PHONE=0
  SET_CS_AZURE=0
  SCAN_COMPLETE=1

  BUILD PASS0 EVENTS: onecore\ds\ds\src\aimx\prod\aimxsrv\dll
    BUILD_CREATED=1
    TASK_CREATED=0
    ADDED_TO_DEPENDENCY_GRAPH=1
    ADDED_TO_PASSQ=1
    ADDED_TO_TASKQ=0
    TRANSITION_STANDBYQ_TO_PASSQ=0
    ADDED_TO_PASS0_Q=1
    ADDED_TO_PASS1_Q=0
    ADDED_TO_PASS2_Q=0
    ADDED_TO_PASS3_Q=0
    ADDED_TO_PASS_INDEPENDENT_Q=0
    ADDED_TO_TASK_Q=0
    REMOVED_FROM_PASSQ=1
    DEPENDENT_MOVED_FROM_READYQ_TO_PASSQ=0
    NMAKE_COMMAND_LINE=1
    MSBUILD_COMMAND_LINE=0
    SUBMIT_TO_DISTRIBUTED_SCHEDULER=1
    DISTRIBUTED_SCHEDULER_WORK_STARTED=1
    DISTRIBUTED_SCHEDULER_WORK_COMPLETED=1
    ADDED_TO_READYQ=0
    BYPASSED=0
    SENT_TO_CLIENT=1
    INCREMENTAL=0
    BUILD_PASS_TERMINATED_FOR_ERRORS=0
    BUILD_OPERATION_COMPLETE=1

  BUILD PASS1 EVENTS: onecore\ds\ds\src\aimx\prod\aimxsrv\dll
    BUILD_CREATED=1
    TASK_CREATED=0
    ADDED_TO_DEPENDENCY_GRAPH=1
    ADDED_TO_PASSQ=1
    ADDED_TO_TASKQ=0
    TRANSITION_STANDBYQ_TO_PASSQ=0
    ADDED_TO_PASS0_Q=0
    ADDED_TO_PASS1_Q=1
    ADDED_TO_PASS2_Q=0
    ADDED_TO_PASS3_Q=0
    ADDED_TO_PASS_INDEPENDENT_Q=0
    ADDED_TO_TASK_Q=0
    REMOVED_FROM_PASSQ=1
    DEPENDENT_MOVED_FROM_READYQ_TO_PASSQ=0
    NMAKE_COMMAND_LINE=1
    MSBUILD_COMMAND_LINE=0
    SUBMIT_TO_DISTRIBUTED_SCHEDULER=1
    DISTRIBUTED_SCHEDULER_WORK_STARTED=1
    DISTRIBUTED_SCHEDULER_WORK_COMPLETED=1
    ADDED_TO_READYQ=0
    BYPASSED=0
    SENT_TO_CLIENT=1
    INCREMENTAL=0
    BUILD_PASS_TERMINATED_FOR_ERRORS=0
    BUILD_OPERATION_COMPLETE=1

  BUILD PASS2 EVENTS: onecore\ds\ds\src\aimx\prod\aimxsrv\dll
    BUILD_CREATED=1
    TASK_CREATED=0
    ADDED_TO_DEPENDENCY_GRAPH=1
    ADDED_TO_PASSQ=1
    ADDED_TO_TASKQ=0
    TRANSITION_STANDBYQ_TO_PASSQ=0
    ADDED_TO_PASS0_Q=0
    ADDED_TO_PASS1_Q=0
    ADDED_TO_PASS2_Q=1
    ADDED_TO_PASS3_Q=0
    ADDED_TO_PASS_INDEPENDENT_Q=0
    ADDED_TO_TASK_Q=0
    REMOVED_FROM_PASSQ=1
    DEPENDENT_MOVED_FROM_READYQ_TO_PASSQ=0
    NMAKE_COMMAND_LINE=1
    MSBUILD_COMMAND_LINE=0
    SUBMIT_TO_DISTRIBUTED_SCHEDULER=1
    DISTRIBUTED_SCHEDULER_WORK_STARTED=1
    DISTRIBUTED_SCHEDULER_WORK_COMPLETED=1
    ADDED_TO_READYQ=0
    BYPASSED=0
    SENT_TO_CLIENT=1
    INCREMENTAL=0
    BUILD_PASS_TERMINATED_FOR_ERRORS=0
    BUILD_OPERATION_COMPLETE=1

DIR EVENTS: onecore\ds\ds\src\aimx\prod\aimxsrv\idl (IN FOCUS)
  CREATED=1
  FOUND_IN_DIRS=1
  INITIAL_DIRECTORY=0
  FOUND_IN_DATABASE=1
  CLEAN_ALL=1
  CLEAN_ALL_FORCED=0
  CLEAN_PASS0=1
  CLEAN_PASS1=1
  CLEAN_PASS2=1
  CLEAN_PASS_INDEPENDENT=1
  ALLOC_WORKITEM_PASS0=1
  ALLOC_WORKITEM_PASS1=0
  ALLOC_WORKITEM_PASS2=0
  ALLOC_WORKITEM_PASS_INDEPENDENT=0
  SOURCES_READ=1
  NODE_DIR=1
  DIRS_PROJ_SKIPPED=0
  MSBUILD_PROJECT_FILE_READ=0
  MSBUILD_POST_BUILD_PROJECT_FILE_READ=0
  TEST_CODE=0
  SAMPLE_CODE=0
  TESTCODE_SKIPPED=0
  PASS0_NEEDED=1
  PASS1_NEEDED=0
  PASS2_NEEDED=0
  PASS3_NEEDED=0
  SOURCES_DEP_READ=0
  QUEUE_TO_PASS_LIST_1=1
  QUEUE_TO_PASS_LIST_2=1
  QUEUE_TO_PASS_LIST_3=0
  ADD_DEPENDENCIES_TO_GRAPH=1
  SET_NON_PRODUCT=0
  SET_NON_CRITICAL=0
  SET_CS_WINDOWS=0
  SET_CS_XBOX=0
  SET_CS_PHONE=0
  SET_CS_AZURE=0
  SCAN_COMPLETE=1

  BUILD PASS0 EVENTS: onecore\ds\ds\src\aimx\prod\aimxsrv\idl
    BUILD_CREATED=1
    TASK_CREATED=0
    ADDED_TO_DEPENDENCY_GRAPH=1
    ADDED_TO_PASSQ=1
    ADDED_TO_TASKQ=0
    TRANSITION_STANDBYQ_TO_PASSQ=0
    ADDED_TO_PASS0_Q=1
    ADDED_TO_PASS1_Q=0
    ADDED_TO_PASS2_Q=0
    ADDED_TO_PASS3_Q=0
    ADDED_TO_PASS_INDEPENDENT_Q=0
    ADDED_TO_TASK_Q=0
    REMOVED_FROM_PASSQ=1
    DEPENDENT_MOVED_FROM_READYQ_TO_PASSQ=0
    NMAKE_COMMAND_LINE=1
    MSBUILD_COMMAND_LINE=0
    SUBMIT_TO_DISTRIBUTED_SCHEDULER=1
    DISTRIBUTED_SCHEDULER_WORK_STARTED=1
    DISTRIBUTED_SCHEDULER_WORK_COMPLETED=1
    ADDED_TO_READYQ=0
    BYPASSED=0
    SENT_TO_CLIENT=1
    INCREMENTAL=0
    BUILD_PASS_TERMINATED_FOR_ERRORS=0
    BUILD_OPERATION_COMPLETE=1

DIR EVENTS: onecore\ds\ds\src\aimx\prod\agents\adtoolagent 
  CREATED=1
  FOUND_IN_DIRS=0
  INITIAL_DIRECTORY=0
  FOUND_IN_DATABASE=0
  CLEAN_ALL=0
  CLEAN_ALL_FORCED=0
  CLEAN_PASS0=0
  CLEAN_PASS1=0
  CLEAN_PASS2=0
  CLEAN_PASS_INDEPENDENT=0
  ALLOC_WORKITEM_PASS0=0
  ALLOC_WORKITEM_PASS1=0
  ALLOC_WORKITEM_PASS2=0
  ALLOC_WORKITEM_PASS_INDEPENDENT=0
  SOURCES_READ=0
  NODE_DIR=0
  DIRS_PROJ_SKIPPED=0
  MSBUILD_PROJECT_FILE_READ=0
  MSBUILD_POST_BUILD_PROJECT_FILE_READ=0
  TEST_CODE=0
  SAMPLE_CODE=0
  TESTCODE_SKIPPED=0
  PASS0_NEEDED=0
  PASS1_NEEDED=0
  PASS2_NEEDED=0
  PASS3_NEEDED=0
  SOURCES_DEP_READ=0
  QUEUE_TO_PASS_LIST_1=0
  QUEUE_TO_PASS_LIST_2=0
  QUEUE_TO_PASS_LIST_3=0
  ADD_DEPENDENCIES_TO_GRAPH=0
  SET_NON_PRODUCT=0
  SET_NON_CRITICAL=0
  SET_CS_WINDOWS=0
  SET_CS_XBOX=0
  SET_CS_PHONE=0
  SET_CS_AZURE=0
  SCAN_COMPLETE=0

DIR EVENTS: onecore\ds\ds\src\aimx\prod\aimxsrv\client\dll (IN FOCUS)
  CREATED=1
  FOUND_IN_DIRS=1
  INITIAL_DIRECTORY=0
  FOUND_IN_DATABASE=1
  CLEAN_ALL=1
  CLEAN_ALL_FORCED=0
  CLEAN_PASS0=1
  CLEAN_PASS1=1
  CLEAN_PASS2=1
  CLEAN_PASS_INDEPENDENT=1
  ALLOC_WORKITEM_PASS0=1
  ALLOC_WORKITEM_PASS1=1
  ALLOC_WORKITEM_PASS2=1
  ALLOC_WORKITEM_PASS_INDEPENDENT=0
  SOURCES_READ=1
  NODE_DIR=1
  DIRS_PROJ_SKIPPED=0
  MSBUILD_PROJECT_FILE_READ=0
  MSBUILD_POST_BUILD_PROJECT_FILE_READ=0
  TEST_CODE=0
  SAMPLE_CODE=0
  TESTCODE_SKIPPED=0
  PASS0_NEEDED=1
  PASS1_NEEDED=1
  PASS2_NEEDED=1
  PASS3_NEEDED=0
  SOURCES_DEP_READ=0
  QUEUE_TO_PASS_LIST_1=1
  QUEUE_TO_PASS_LIST_2=1
  QUEUE_TO_PASS_LIST_3=0
  ADD_DEPENDENCIES_TO_GRAPH=1
  SET_NON_PRODUCT=0
  SET_NON_CRITICAL=0
  SET_CS_WINDOWS=0
  SET_CS_XBOX=0
  SET_CS_PHONE=0
  SET_CS_AZURE=0
  SCAN_COMPLETE=1

  BUILD PASS0 EVENTS: onecore\ds\ds\src\aimx\prod\aimxsrv\client\dll
    BUILD_CREATED=1
    TASK_CREATED=0
    ADDED_TO_DEPENDENCY_GRAPH=1
    ADDED_TO_PASSQ=1
    ADDED_TO_TASKQ=0
    TRANSITION_STANDBYQ_TO_PASSQ=0
    ADDED_TO_PASS0_Q=1
    ADDED_TO_PASS1_Q=0
    ADDED_TO_PASS2_Q=0
    ADDED_TO_PASS3_Q=0
    ADDED_TO_PASS_INDEPENDENT_Q=0
    ADDED_TO_TASK_Q=0
    REMOVED_FROM_PASSQ=1
    DEPENDENT_MOVED_FROM_READYQ_TO_PASSQ=0
    NMAKE_COMMAND_LINE=1
    MSBUILD_COMMAND_LINE=0
    SUBMIT_TO_DISTRIBUTED_SCHEDULER=1
    DISTRIBUTED_SCHEDULER_WORK_STARTED=1
    DISTRIBUTED_SCHEDULER_WORK_COMPLETED=1
    ADDED_TO_READYQ=0
    BYPASSED=0
    SENT_TO_CLIENT=1
    INCREMENTAL=0
    BUILD_PASS_TERMINATED_FOR_ERRORS=0
    BUILD_OPERATION_COMPLETE=1

  BUILD PASS1 EVENTS: onecore\ds\ds\src\aimx\prod\aimxsrv\client\dll
    BUILD_CREATED=1
    TASK_CREATED=0
    ADDED_TO_DEPENDENCY_GRAPH=1
    ADDED_TO_PASSQ=1
    ADDED_TO_TASKQ=0
    TRANSITION_STANDBYQ_TO_PASSQ=0
    ADDED_TO_PASS0_Q=0
    ADDED_TO_PASS1_Q=1
    ADDED_TO_PASS2_Q=0
    ADDED_TO_PASS3_Q=0
    ADDED_TO_PASS_INDEPENDENT_Q=0
    ADDED_TO_TASK_Q=0
    REMOVED_FROM_PASSQ=1
    DEPENDENT_MOVED_FROM_READYQ_TO_PASSQ=0
    NMAKE_COMMAND_LINE=1
    MSBUILD_COMMAND_LINE=0
    SUBMIT_TO_DISTRIBUTED_SCHEDULER=1
    DISTRIBUTED_SCHEDULER_WORK_STARTED=1
    DISTRIBUTED_SCHEDULER_WORK_COMPLETED=1
    ADDED_TO_READYQ=0
    BYPASSED=0
    SENT_TO_CLIENT=1
    INCREMENTAL=0
    BUILD_PASS_TERMINATED_FOR_ERRORS=0
    BUILD_OPERATION_COMPLETE=1

  BUILD PASS2 EVENTS: onecore\ds\ds\src\aimx\prod\aimxsrv\client\dll
    BUILD_CREATED=1
    TASK_CREATED=0
    ADDED_TO_DEPENDENCY_GRAPH=1
    ADDED_TO_PASSQ=1
    ADDED_TO_TASKQ=0
    TRANSITION_STANDBYQ_TO_PASSQ=0
    ADDED_TO_PASS0_Q=0
    ADDED_TO_PASS1_Q=0
    ADDED_TO_PASS2_Q=1
    ADDED_TO_PASS3_Q=0
    ADDED_TO_PASS_INDEPENDENT_Q=0
    ADDED_TO_TASK_Q=0
    REMOVED_FROM_PASSQ=1
    DEPENDENT_MOVED_FROM_READYQ_TO_PASSQ=0
    NMAKE_COMMAND_LINE=1
    MSBUILD_COMMAND_LINE=0
    SUBMIT_TO_DISTRIBUTED_SCHEDULER=1
    DISTRIBUTED_SCHEDULER_WORK_STARTED=1
    DISTRIBUTED_SCHEDULER_WORK_COMPLETED=1
    ADDED_TO_READYQ=0
    BYPASSED=0
    SENT_TO_CLIENT=1
    INCREMENTAL=0
    BUILD_PASS_TERMINATED_FOR_ERRORS=0
    BUILD_OPERATION_COMPLETE=1

DIR EVENTS: onecore\ds\ds\src\aimx\prod\aimxsrv\client\lib (IN FOCUS)
  CREATED=1
  FOUND_IN_DIRS=1
  INITIAL_DIRECTORY=0
  FOUND_IN_DATABASE=1
  CLEAN_ALL=1
  CLEAN_ALL_FORCED=0
  CLEAN_PASS0=1
  CLEAN_PASS1=1
  CLEAN_PASS2=1
  CLEAN_PASS_INDEPENDENT=1
  ALLOC_WORKITEM_PASS0=1
  ALLOC_WORKITEM_PASS1=1
  ALLOC_WORKITEM_PASS2=0
  ALLOC_WORKITEM_PASS_INDEPENDENT=0
  SOURCES_READ=1
  NODE_DIR=1
  DIRS_PROJ_SKIPPED=0
  MSBUILD_PROJECT_FILE_READ=0
  MSBUILD_POST_BUILD_PROJECT_FILE_READ=0
  TEST_CODE=0
  SAMPLE_CODE=0
  TESTCODE_SKIPPED=0
  PASS0_NEEDED=1
  PASS1_NEEDED=1
  PASS2_NEEDED=0
  PASS3_NEEDED=0
  SOURCES_DEP_READ=0
  QUEUE_TO_PASS_LIST_1=1
  QUEUE_TO_PASS_LIST_2=1
  QUEUE_TO_PASS_LIST_3=0
  ADD_DEPENDENCIES_TO_GRAPH=1
  SET_NON_PRODUCT=0
  SET_NON_CRITICAL=0
  SET_CS_WINDOWS=0
  SET_CS_XBOX=0
  SET_CS_PHONE=0
  SET_CS_AZURE=0
  SCAN_COMPLETE=1

  BUILD PASS0 EVENTS: onecore\ds\ds\src\aimx\prod\aimxsrv\client\lib
    BUILD_CREATED=1
    TASK_CREATED=0
    ADDED_TO_DEPENDENCY_GRAPH=1
    ADDED_TO_PASSQ=1
    ADDED_TO_TASKQ=0
    TRANSITION_STANDBYQ_TO_PASSQ=0
    ADDED_TO_PASS0_Q=1
    ADDED_TO_PASS1_Q=0
    ADDED_TO_PASS2_Q=0
    ADDED_TO_PASS3_Q=0
    ADDED_TO_PASS_INDEPENDENT_Q=0
    ADDED_TO_TASK_Q=0
    REMOVED_FROM_PASSQ=1
    DEPENDENT_MOVED_FROM_READYQ_TO_PASSQ=0
    NMAKE_COMMAND_LINE=1
    MSBUILD_COMMAND_LINE=0
    SUBMIT_TO_DISTRIBUTED_SCHEDULER=1
    DISTRIBUTED_SCHEDULER_WORK_STARTED=1
    DISTRIBUTED_SCHEDULER_WORK_COMPLETED=1
    ADDED_TO_READYQ=0
    BYPASSED=0
    SENT_TO_CLIENT=1
    INCREMENTAL=0
    BUILD_PASS_TERMINATED_FOR_ERRORS=0
    BUILD_OPERATION_COMPLETE=1

  BUILD PASS1 EVENTS: onecore\ds\ds\src\aimx\prod\aimxsrv\client\lib
    BUILD_CREATED=1
    TASK_CREATED=0
    ADDED_TO_DEPENDENCY_GRAPH=1
    ADDED_TO_PASSQ=1
    ADDED_TO_TASKQ=0
    TRANSITION_STANDBYQ_TO_PASSQ=0
    ADDED_TO_PASS0_Q=0
    ADDED_TO_PASS1_Q=1
    ADDED_TO_PASS2_Q=0
    ADDED_TO_PASS3_Q=0
    ADDED_TO_PASS_INDEPENDENT_Q=0
    ADDED_TO_TASK_Q=0
    REMOVED_FROM_PASSQ=1
    DEPENDENT_MOVED_FROM_READYQ_TO_PASSQ=0
    NMAKE_COMMAND_LINE=1
    MSBUILD_COMMAND_LINE=0
    SUBMIT_TO_DISTRIBUTED_SCHEDULER=1
    DISTRIBUTED_SCHEDULER_WORK_STARTED=1
    DISTRIBUTED_SCHEDULER_WORK_COMPLETED=1
    ADDED_TO_READYQ=0
    BYPASSED=0
    SENT_TO_CLIENT=1
    INCREMENTAL=0
    BUILD_PASS_TERMINATED_FOR_ERRORS=0
    BUILD_OPERATION_COMPLETE=1

DIR EVENTS: onecore\ds\ds\src\aimx\prod\aimxsrv\powershell (IN FOCUS)
  CREATED=1
  FOUND_IN_DIRS=1
  INITIAL_DIRECTORY=0
  FOUND_IN_DATABASE=1
  CLEAN_ALL=1
  CLEAN_ALL_FORCED=0
  CLEAN_PASS0=1
  CLEAN_PASS1=1
  CLEAN_PASS2=1
  CLEAN_PASS_INDEPENDENT=1
  ALLOC_WORKITEM_PASS0=1
  ALLOC_WORKITEM_PASS1=1
  ALLOC_WORKITEM_PASS2=1
  ALLOC_WORKITEM_PASS_INDEPENDENT=0
  SOURCES_READ=1
  NODE_DIR=1
  DIRS_PROJ_SKIPPED=0
  MSBUILD_PROJECT_FILE_READ=0
  MSBUILD_POST_BUILD_PROJECT_FILE_READ=0
  TEST_CODE=0
  SAMPLE_CODE=0
  TESTCODE_SKIPPED=0
  PASS0_NEEDED=1
  PASS1_NEEDED=1
  PASS2_NEEDED=1
  PASS3_NEEDED=0
  SOURCES_DEP_READ=0
  QUEUE_TO_PASS_LIST_1=1
  QUEUE_TO_PASS_LIST_2=1
  QUEUE_TO_PASS_LIST_3=0
  ADD_DEPENDENCIES_TO_GRAPH=1
  SET_NON_PRODUCT=0
  SET_NON_CRITICAL=0
  SET_CS_WINDOWS=0
  SET_CS_XBOX=0
  SET_CS_PHONE=0
  SET_CS_AZURE=0
  SCAN_COMPLETE=1

  BUILD PASS0 EVENTS: onecore\ds\ds\src\aimx\prod\aimxsrv\powershell
    BUILD_CREATED=1
    TASK_CREATED=0
    ADDED_TO_DEPENDENCY_GRAPH=1
    ADDED_TO_PASSQ=1
    ADDED_TO_TASKQ=0
    TRANSITION_STANDBYQ_TO_PASSQ=0
    ADDED_TO_PASS0_Q=1
    ADDED_TO_PASS1_Q=0
    ADDED_TO_PASS2_Q=0
    ADDED_TO_PASS3_Q=0
    ADDED_TO_PASS_INDEPENDENT_Q=0
    ADDED_TO_TASK_Q=0
    REMOVED_FROM_PASSQ=1
    DEPENDENT_MOVED_FROM_READYQ_TO_PASSQ=0
    NMAKE_COMMAND_LINE=1
    MSBUILD_COMMAND_LINE=0
    SUBMIT_TO_DISTRIBUTED_SCHEDULER=1
    DISTRIBUTED_SCHEDULER_WORK_STARTED=1
    DISTRIBUTED_SCHEDULER_WORK_COMPLETED=1
    ADDED_TO_READYQ=0
    BYPASSED=0
    SENT_TO_CLIENT=1
    INCREMENTAL=0
    BUILD_PASS_TERMINATED_FOR_ERRORS=0
    BUILD_OPERATION_COMPLETE=1

  BUILD PASS1 EVENTS: onecore\ds\ds\src\aimx\prod\aimxsrv\powershell
    BUILD_CREATED=1
    TASK_CREATED=0
    ADDED_TO_DEPENDENCY_GRAPH=1
    ADDED_TO_PASSQ=1
    ADDED_TO_TASKQ=0
    TRANSITION_STANDBYQ_TO_PASSQ=0
    ADDED_TO_PASS0_Q=0
    ADDED_TO_PASS1_Q=1
    ADDED_TO_PASS2_Q=0
    ADDED_TO_PASS3_Q=0
    ADDED_TO_PASS_INDEPENDENT_Q=0
    ADDED_TO_TASK_Q=0
    REMOVED_FROM_PASSQ=1
    DEPENDENT_MOVED_FROM_READYQ_TO_PASSQ=0
    NMAKE_COMMAND_LINE=1
    MSBUILD_COMMAND_LINE=0
    SUBMIT_TO_DISTRIBUTED_SCHEDULER=1
    DISTRIBUTED_SCHEDULER_WORK_STARTED=1
    DISTRIBUTED_SCHEDULER_WORK_COMPLETED=1
    ADDED_TO_READYQ=0
    BYPASSED=0
    SENT_TO_CLIENT=1
    INCREMENTAL=0
    BUILD_PASS_TERMINATED_FOR_ERRORS=0
    BUILD_OPERATION_COMPLETE=1

  BUILD PASS2 EVENTS: onecore\ds\ds\src\aimx\prod\aimxsrv\powershell
    BUILD_CREATED=1
    TASK_CREATED=0
    ADDED_TO_DEPENDENCY_GRAPH=1
    ADDED_TO_PASSQ=1
    ADDED_TO_TASKQ=0
    TRANSITION_STANDBYQ_TO_PASSQ=0
    ADDED_TO_PASS0_Q=0
    ADDED_TO_PASS1_Q=0
    ADDED_TO_PASS2_Q=1
    ADDED_TO_PASS3_Q=0
    ADDED_TO_PASS_INDEPENDENT_Q=0
    ADDED_TO_TASK_Q=0
    REMOVED_FROM_PASSQ=1
    DEPENDENT_MOVED_FROM_READYQ_TO_PASSQ=0
    NMAKE_COMMAND_LINE=1
    MSBUILD_COMMAND_LINE=0
    SUBMIT_TO_DISTRIBUTED_SCHEDULER=1
    DISTRIBUTED_SCHEDULER_WORK_STARTED=1
    DISTRIBUTED_SCHEDULER_WORK_COMPLETED=1
    ADDED_TO_READYQ=0
    BYPASSED=0
    SENT_TO_CLIENT=1
    INCREMENTAL=0
    BUILD_PASS_TERMINATED_FOR_ERRORS=0
    BUILD_OPERATION_COMPLETE=1
