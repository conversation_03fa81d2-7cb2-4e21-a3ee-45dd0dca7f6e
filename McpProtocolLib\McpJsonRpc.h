/*++

Copyright (c) Microsoft Corporation. All rights reserved.

Module Name:

    McpJsonRpc.h

Abstract:

    Shared JSON-RPC protocol utilities for Model Context Protocol (MCP).
    Provides common functionality for both MCP clients (AimxSrv) and 
    MCP server executables to ensure consistent protocol handling.

Author:

    <PERSON><PERSON><PERSON> (rizhang) 07/19/2025

--*/

#pragma once

#include <windows.h>
#include <string>
#include <nlohmann/json.hpp>

namespace McpProtocol
{
    // JSON-RPC 2.0 protocol utilities
    namespace JsonRpc
    {
        // Create a standard JSON-RPC 2.0 request
        nlohmann::json CreateRequest(
            _In_ const std::string& method,
            _In_ const nlohmann::json& params = nlohmann::json::object(),
            _In_opt_ const nlohmann::json& id = nlohmann::json()
        );

        // Create a standard JSON-RPC 2.0 response
        nlohmann::json CreateResponse(
            _In_ const nlohmann::json& id,
            _In_ const nlohmann::json& result
        );

        // Create a standard JSON-RPC 2.0 error response
        nlohmann::json CreateErrorResponse(
            _In_ const nlohmann::json& id,
            _In_ int errorCode,
            _In_ const std::string& errorMessage,
            _In_opt_ const nlohmann::json& errorData = nlohmann::json()
        );

        // Validate JSON-RPC request format
        bool IsValidRequest(
            _In_ const nlohmann::json& request
        );

        // Validate JSON-RPC response format
        bool IsValidResponse(
            _In_ const nlohmann::json& response
        );

        // Extract method name from request
        std::string GetMethod(
            _In_ const nlohmann::json& request
        );

        // Extract parameters from request
        nlohmann::json GetParams(
            _In_ const nlohmann::json& request
        );

        // Extract ID from request/response
        nlohmann::json GetId(
            _In_ const nlohmann::json& message
        );

        // Check if response is an error
        bool IsErrorResponse(
            _In_ const nlohmann::json& response
        );

        // Extract error information from error response
        struct ErrorInfo
        {
            int code;
            std::string message;
            nlohmann::json data;
        };

        ErrorInfo GetError(
            _In_ const nlohmann::json& errorResponse
        );
    }

    // MCP-specific protocol utilities
    namespace Mcp
    {
        // MCP protocol version
        constexpr const char* PROTOCOL_VERSION = "2024-11-05";

        // Standard MCP methods
        namespace Methods
        {
            constexpr const char* INITIALIZE = "initialize";
            constexpr const char* TOOLS_LIST = "tools/list";
            constexpr const char* TOOLS_CALL = "tools/call";
            constexpr const char* SHUTDOWN = "shutdown";
        }

        // Create MCP initialize request
        nlohmann::json CreateInitializeRequest(
            _In_ const std::string& clientName,
            _In_ const std::string& clientVersion,
            _In_opt_ const nlohmann::json& capabilities = nlohmann::json::object(),
            _In_opt_ const nlohmann::json& id = 1
        );

        // Create MCP initialize response
        nlohmann::json CreateInitializeResponse(
            _In_ const nlohmann::json& id,
            _In_ const std::string& serverName,
            _In_ const std::string& serverVersion,
            _In_opt_ const nlohmann::json& capabilities = nlohmann::json::object()
        );

        // Create MCP tools/list request
        nlohmann::json CreateListToolsRequest(
            _In_opt_ const nlohmann::json& id = nlohmann::json()
        );

        // Create MCP tools/list response
        nlohmann::json CreateListToolsResponse(
            _In_ const nlohmann::json& id,
            _In_ const nlohmann::json& toolsArray
        );

        // Create MCP tools/call request
        nlohmann::json CreateCallToolRequest(
            _In_ const std::string& toolName,
            _In_ const nlohmann::json& arguments,
            _In_opt_ const nlohmann::json& id = nlohmann::json()
        );

        // Create MCP tools/call response
        nlohmann::json CreateCallToolResponse(
            _In_ const nlohmann::json& id,
            _In_ const nlohmann::json& content
        );

        // Create MCP shutdown request
        nlohmann::json CreateShutdownRequest(
            _In_opt_ const nlohmann::json& id = nlohmann::json()
        );

        // Create MCP shutdown response
        nlohmann::json CreateShutdownResponse(
            _In_ const nlohmann::json& id
        );

        // Validate MCP tool definition
        bool IsValidToolDefinition(
            _In_ const nlohmann::json& toolDef
        );

        // Create standard MCP tool content response
        nlohmann::json CreateToolContentResponse(
            _In_ const std::string& text,
            _In_opt_ const std::string& mimeType = "text/plain"
        );

        // Wrap content in MCP tools/call response format
        nlohmann::json WrapToolContent(
            _In_ const nlohmann::json& content
        );
    }

    // Error codes following JSON-RPC 2.0 and MCP specifications
    namespace ErrorCodes
    {
        // JSON-RPC 2.0 standard error codes
        constexpr int PARSE_ERROR = -32700;
        constexpr int INVALID_REQUEST = -32600;
        constexpr int METHOD_NOT_FOUND = -32601;
        constexpr int INVALID_PARAMS = -32602;
        constexpr int INTERNAL_ERROR = -32603;

        // MCP-specific error codes (using implementation-defined range)
        constexpr int TOOL_NOT_FOUND = -32000;
        constexpr int TOOL_EXECUTION_FAILED = -32001;
        constexpr int SERVER_INITIALIZATION_FAILED = -32002;
        constexpr int SERVER_NOT_INITIALIZED = -32003;
        constexpr int INVALID_TOOL_DEFINITION = -32004;
        constexpr int CONNECTION_FAILED = -32005;
        constexpr int TIMEOUT = -32006;
    }

    // Utility functions for common operations
    namespace Utils
    {
        // Parse JSON string safely
        bool ParseJson(
            _In_ const std::string& jsonString,
            _Out_ nlohmann::json& result,
            _Out_opt_ std::string* errorMessage = nullptr
        );

        // Serialize JSON safely
        std::string SerializeJson(
            _In_ const nlohmann::json& json,
            _In_ bool pretty = false
        );

        // Generate unique request ID
        nlohmann::json GenerateRequestId();

        // Convert HRESULT to MCP error code
        int HResultToMcpErrorCode(
            _In_ HRESULT hr
        );

        // Convert MCP error code to HRESULT
        HRESULT McpErrorCodeToHResult(
            _In_ int errorCode
        );

        // Create error response from HRESULT
        nlohmann::json CreateErrorFromHResult(
            _In_ const nlohmann::json& id,
            _In_ HRESULT hr,
            _In_opt_ const std::string& context = ""
        );
    }
}
