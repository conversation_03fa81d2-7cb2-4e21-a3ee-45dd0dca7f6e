/*++

Copyright (c) Microsoft Corporation. All rights reserved.

Module Name:
    memory.h

Abstract:
    Memory allocation and deallocation helpers for AIMXSRV.

Author:
    <PERSON> (SNAKE FIGHTER) (lindakup) 06/10/2025

--*/
#pragma once

#include <windows.h>

#ifndef MAX_ULONG
#define MAX_ULONG ((ULONG)-1)
#endif

void* AimxAlloc(unsigned long Size);
void* AimxAlloc(size_t Size);

#ifdef AIMXCLIENT_EXPORTS
#define AIMXCLIENT_API __declspec(dllexport)
#elif defined(AIMXCLIENT_IMPORTS)
#define AIMXCLIENT_API __declspec(dllimport)
#else
#define AIMXCLIENT_API
#endif

#ifdef __cplusplus
extern "C" {
#endif

AIMXCLIENT_API void AimxFree(void* p);

void* __RPC_USER MIDL_user_allocate(size_t NumBytes);
void __RPC_USER MIDL_user_free(void* p);


#ifdef __cplusplus
}

// Template version for type-safe allocation
template <typename T>
T* AimxAlloc()
{
    return static_cast<T*>(AimxAlloc(sizeof(T)));
}
#endif
