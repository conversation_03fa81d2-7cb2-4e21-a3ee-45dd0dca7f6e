TARGETNAME=aimxpsh
TARGETTYPE=<PERSON><PERSON><PERSON><PERSON>K
TARGET_DESTINATION=retail

URT_VER=4.8

MANAGED_CODE=1

SOURCES=\
    NativeMethods.cs \
    AimxServerCmdLets.cs \
    FoundryLocalWizard.cs \

REFERENCES=\
    $(CLR_REF_PATH)\System.metadata_dll; \
    $(CLR_REF_PATH)\System.Core.metadata_dll; \
    $(CLR_REF_PATH)\System.DirectoryServices.metadata_dll; \
    $(CLR_REF_PATH)\System.DirectoryServices.Protocols.metadata_dll; \
    $(CLR_REF_PATH)\System.IO.metadata_dll; \
    $(CLR_REF_PATH)\System.Management.metadata_dll; \
    $(CLR_REF_PATH)\System.Runtime.metadata_dll; \
    $(CLR_REF_PATH)\System.Runtime.InteropServices.metadata_dll; \
    $(CLR_REF_PATH)\System.Runtime.Serialization.metadata_dll; \
    $(CLR_REF_PATH)\System.Runtime.Serialization.Json.metadata_dll; \
    $(CLR_REF_PATH)\System.Xml.metadata_dll; \
    $(ONECORE_INTERNAL_SDK_REF_PATH)\System.Management.Automation.metadata_dll; \

PASS2_BINPLACE=\
    $(PASS2_BINPLACE) \
    AIMX.psd1 \
