/*++

Copyright (c) Microsoft Corporation. All rights reserved.

Module Name:

    LlmClient.h

Abstract:

    Splitting the common LLM commucation code into a separate library.
    Provides interface for comunicating with the backend LLM service.

Author:

    <PERSON><PERSON><PERSON> (r<PERSON><PERSON>) 04/20/2025

--*/

#pragma once

#include <winhttp.h>
#include <functional>

// global constants
// TODO: Configurable through registry or GPO or some Config UI
// (Configuration can be implemented post M1 as we are short on time)
constexpr auto LLM_DEFAULT_TEMPERATURE_VALUE = 0.7f;
constexpr auto LLM_DEFAULT_TOP_K_VALUE = 40;
constexpr auto LLM_DEFAULT_TOP_P_VALUE = 0.9f;
constexpr auto LLM_DEFAULT_STREAM_VALUE = false;

// Enum for LLM Foundry role types
enum class LlmFoundryRoleType
{
    System,
    User,
    Assistant
};

// Convert LlmFoundryRoleType to string representation
inline const char* GetLlmFoundryRoleTypeString(LlmFoundryRoleType role)
{
    switch (role)
    {
    case LlmFoundryRoleType::System:
        return "system";
    case LlmFoundryRoleType::User:
        return "user";
    case LlmFoundryRoleType::Assistant:
        return "assistant";
    default:
        return "";
    }
}


// Callback type for streaming responses
using StreamingResponseCallback = std::function<void(const std::wstring&, bool)>;

class LlmClient
{
public:
  
    // Constructor with LLM configuration parameters
    LlmClient(
        _In_ const std::wstring& endpointUrl,
        _In_ const std::wstring& model,
        _In_ float temperature,
        _In_ int topK,
        _In_ float topP
    );
    
    ~LlmClient();

    // Send a prompt to the Foundry LLM service and recieve a response
    bool
    SendFoundryPrompt(
        _In_ const std::wstring& systemPrompt,
        _In_ const std::wstring& userPrompt,
        _In_ const std::wstring& model,
        _Out_ std::wstring& response
    );

    // Send a prompt to the LLM service and recieve a response
    bool
    SendPrompt(
        _In_ const std::wstring& prompt,
        _Out_ std::wstring& response
        );

    // Send a prompt with context (for RAG)
    bool
    SendPromptWithContext(
        _In_ const std::wstring& prompt,
        _In_ const std::wstring& context,
        _Out_ std::wstring& response
        );

    // Streaming methods
    bool
    StreamPrompt(
        _In_ const std::wstring& prompt,
        _In_ StreamingResponseCallback callback
        );

    bool
    StreamPromptWithContext(
        _In_ const std::wstring& prompt,
        _In_ const std::wstring& context,
        _In_ StreamingResponseCallback callback
        );


    std::wstring
    GetEndpointUrl() const;

    std::string
    GetEndpointUrlUtf8() const;

    void
    SetEndpointUrl(
        _In_ const  std::wstring& url
    );

    // Model configuration methods
    std::wstring
    GetModel() const;

    void
    SetModel(
        _In_ const std::wstring& model
    );

    // Temperature configuration methods
    float
    GetTemperature() const;

    void
    SetTemperature(
        _In_ float temperature
    );

    // Top-K configuration methods
    int
    GetTopK() const;

    void
    SetTopK(
        _In_ int topK
    );

    // Top-P configuration methods
    float
    GetTopP() const;

    void
    SetTopP(
        _In_ float topP
    );

    // Set all configuration parameters at once
    void
    SetLlmConfiguration(
        _In_ const std::wstring& endpointUrl,
        _In_ const std::wstring& model,
        _In_ float temperature,
        _In_ int topK,
        _In_ float topP
    );

    bool
    SendPromptWithRag(
        _In_ const std::wstring& prompt,
        _Out_ std::wstring& response
        );

    bool
    StreamPromptWithRag(
        _In_ const std::wstring& prompt,
        _In_ StreamingResponseCallback callback
        );

private:
    // Perform a HTTP POST request to the LLM endpoint
    bool
    HttpPost(
        _In_ const std::wstring& url,
        _In_ const std::wstring& data,
        _Out_ std::wstring& response
        );

    // Method for streaming HTTP requests
    bool
    HttpPostStreaming(
        _In_ const std::wstring& url,
        _In_ const std::wstring& data,
        _In_ StreamingResponseCallback callback
        );

    // Format prompt wit context
    std::wstring
    FormatPromptWithContext(
        _In_ const std::wstring& prompt,
        _In_ const std::wstring& context
        );

    // Format a simple prompt (no context)
    std::wstring
    FormatPrompt(
        _In_ const std::wstring& prompt
    );

    std::wstring m_endpointUrl;
    std::wstring m_model;
    float m_temperature;
    int m_topK;
    float m_topP;
};
