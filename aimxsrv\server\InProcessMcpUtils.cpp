/*++

Copyright (c) Microsoft Corporation. All rights reserved.

Module Name:

    InProcessMcpUtils.cpp

Abstract:

    Implementation of utility functions for in-process MCP servers.
    Provides JSON blob creation and parsing functions that maintain
    full MCP protocol compliance for direct API calls.

Author:

    <PERSON><PERSON><PERSON> (rizhang) 07/12/2025

--*/

#include "pch.hxx"
#include "InProcessMcpUtils.h"
#include "StringUtils.h"
#include "AimxConstants.h"

namespace InProcessMcpUtils
{

nlohmann::json CreateToolsListResponse(
    _In_ const std::vector<nlohmann::json>& tools
    )
/*++

Routine Description:
    Creates an MCP-compliant tools/list response JSON blob using shared library.

Arguments:
    tools - Vector of tool definition JSON objects

Return Value:
    JSON response matching MCP protocol format

--*/
{
    nlohmann::json toolsArray = nlohmann::json::array();
    for (const auto& tool : tools)
    {
        toolsArray.push_back(tool);
    }
    return McpProtocol::Mcp::CreateListToolsResponse(nlohmann::json(), toolsArray);
}

nlohmann::json CreateToolCallSuccessResponse(
    _In_ const nlohmann::json& content
    )
/*++

Routine Description:
    Creates an MCP-compliant tools/call success response JSON blob using shared library.

Arguments:
    content - Tool execution result content

Return Value:
    JSON response matching MCP protocol format

--*/
{
    nlohmann::json wrappedContent = McpProtocol::Mcp::WrapToolContent(content);
    return McpProtocol::Mcp::CreateCallToolResponse(nlohmann::json(), wrappedContent);
}

nlohmann::json CreateMcpErrorResponse(
    _In_ int errorCode,
    _In_ const std::string& message,
    _In_opt_ const nlohmann::json* data
    )
/*++

Routine Description:
    Creates an MCP-compliant error response JSON blob using shared library.

Arguments:
    errorCode - MCP error code (e.g., -32601 for method not found)
    message - Error message
    data - Optional additional error data

Return Value:
    JSON error response matching MCP protocol format

--*/
{
    nlohmann::json errorData = data ? *data : nlohmann::json();
    return McpProtocol::JsonRpc::CreateErrorResponse(nlohmann::json(), errorCode, message, errorData);
}

HRESULT ParseToolCallRequest(
    _In_ const nlohmann::json& request,
    _Out_ std::string& toolName,
    _Out_ nlohmann::json& parameters
    )
/*++

Routine Description:
    Parses an MCP tools/call request to extract tool name and parameters using shared library.

Arguments:
    request - MCP tools/call request JSON
    toolName - Receives the tool name
    parameters - Receives the tool parameters

Return Value:
    S_OK on success, error HRESULT on failure

--*/
{
    try
    {
        // Validate request structure using shared library
        if (!McpProtocol::JsonRpc::IsValidRequest(request))
        {
            return E_INVALIDARG;
        }

        // Verify this is a tools/call request
        std::string method = McpProtocol::JsonRpc::GetMethod(request);
        if (method != McpProtocol::Mcp::Methods::TOOLS_CALL)
        {
            return E_INVALIDARG;
        }

        // Extract parameters using shared library
        nlohmann::json params = McpProtocol::JsonRpc::GetParams(request);

        // Extract tool name
        if (!params.contains("name") || !params["name"].is_string())
        {
            return E_INVALIDARG;
        }

        toolName = params["name"].get<std::string>();

        // Extract parameters (optional)
        if (params.contains("arguments"))
        {
            parameters = params["arguments"];
        }
        else
        {
            parameters = nlohmann::json::object();
        }

        return S_OK;
    }
    catch (const nlohmann::json::exception&)
    {
        return E_INVALIDARG;
    }
    catch (...)
    {
        return E_FAIL;
    }
}

nlohmann::json ExceptionToMcpError(
    _In_ const std::exception& ex
    )
/*++

Routine Description:
    Converts a C++ exception to an MCP error response.

Arguments:
    ex - The exception to convert

Return Value:
    JSON error response matching MCP protocol format

--*/
{
    std::string message = "Internal server error";
    
    try
    {
        message = ex.what();
    }
    catch (...)
    {
        // Use default message if ex.what() throws
    }
    
    return CreateMcpErrorResponse(McpProtocol::ErrorCodes::INTERNAL_ERROR, message);
}

} // namespace InProcessMcpUtils
