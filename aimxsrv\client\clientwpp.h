/*++

Copyright (c) Microsoft Corporation. All rights reserved.

Module Name:
    clientwpp.h

Abstract:
    WPP control GUID and bit definitions for the aimxclient.

Author:
    <PERSON> (SNAKE FIGHTER) (linda<PERSON>p) 06/13/2025

--*/
#pragma once

// AimxClient WPP control GUID and bit definitions
//
// GUID {b1e2c3d4-5f67-489a-bcde-1234567890ab}
//
// AimxClient = bit 0: so pass 0x00000001
//
// begin_wpp config
#define WPP_CONTROL_GUIDS \
    WPP_DEFINE_CONTROL_GUID(AimxClientTraceGuid, (b1e2c3d4,5f67,489a,bcde,1234567890ab), \
        WPP_DEFINE_BIT(AimxClient) /* bit 0 */ \
    )

#define WPP_LEVEL_FLAGS_LOGGER(lvl, flags) WPP_LEVEL_LOGGER(flags)
#define WPP_LEVEL_FLAGS_ENABLED(lvl, flags) (WPP_LEVEL_ENABLED(flags) && WPP_CONTROL(WPP_BIT_##flags).Level >= lvl)

#define WPP_LEVEL_FLAGS_LOGGER_LOGGER(lvl, flags, LOGGER) LOGGER,
#define WPP_LEVEL_FLAGS_LOGGER_ENABLED(lvl, flags, LOGGER) (TRUE)
// end_wpp
