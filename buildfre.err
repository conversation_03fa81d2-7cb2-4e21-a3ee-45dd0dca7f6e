3202>errors in directory e:\os\src\onecore\ds\ds\src\aimx\prod\aimxsrv\server
3202>e:\os\src\onecore\ds\ds\src\aimx\prod\aimxsrv\server\ragservicemanager.cpp(644) : error C2665: 'web::http::client::http_client::request': no overloaded function could convert all the argument types
3202>error-continuation e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\cpprestsdk\objfre\amd64\vcpkg_installed\target-windows\include\cpprest\http_client.h(519): note: could be 'pplx::task<web::http::http_response> web::http::client::http_client::request(const web::http::method &,const utility::string_t &,const pplx::cancellation_token &)'
3202>error-continuation e:\os\src\onecore\ds\ds\src\aimx\prod\aimxsrv\server\ragservicemanager.cpp(644): note: 'pplx::task<web::http::http_response> web::http::client::http_client::request(const web::http::method &,const utility::string_t &,const pplx::cancellation_token &)': cannot convert argument 1 from 'web::http::http_request' to 'const web::http::method &'
3202>error-continuation e:\os\src\onecore\ds\ds\src\aimx\prod\aimxsrv\server\ragservicemanager.cpp(644): note: Reason: cannot convert from 'web::http::http_request' to 'const web::http::method'
3202>error-continuation e:\os\src\onecore\ds\ds\src\aimx\prod\aimxsrv\server\ragservicemanager.cpp(644): note: No user-defined-conversion operator available that can perform this conversion, or the operator cannot be called
3202>error-continuation e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\cpprestsdk\objfre\amd64\vcpkg_installed\target-windows\include\cpprest\http_client.h(505): note: or       'pplx::task<web::http::http_response> web::http::client::http_client::request(const web::http::method &,const pplx::cancellation_token &)'
3202>error-continuation e:\os\src\onecore\ds\ds\src\aimx\prod\aimxsrv\server\ragservicemanager.cpp(644): note: 'pplx::task<web::http::http_response> web::http::client::http_client::request(const web::http::method &,const pplx::cancellation_token &)': cannot convert argument 1 from 'web::http::http_request' to 'const web::http::method &'
3202>error-continuation e:\os\src\onecore\ds\ds\src\aimx\prod\aimxsrv\server\ragservicemanager.cpp(644): note: Reason: cannot convert from 'web::http::http_request' to 'const web::http::method'
3202>error-continuation e:\os\src\onecore\ds\ds\src\aimx\prod\aimxsrv\server\ragservicemanager.cpp(644): note: No user-defined-conversion operator available that can perform this conversion, or the operator cannot be called
3202>error-continuation e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\cpprestsdk\objfre\amd64\vcpkg_installed\target-windows\include\cpprest\http_client.h(496): note: or       'pplx::task<web::http::http_response> web::http::client::http_client::request(web::http::http_request,const pplx::cancellation_token &)'
3202>error-continuation e:\os\src\onecore\ds\ds\src\aimx\prod\aimxsrv\server\ragservicemanager.cpp(644): note: 'pplx::task<web::http::http_response> web::http::client::http_client::request(web::http::http_request,const pplx::cancellation_token &)': cannot convert argument 2 from 'const std::wstring' to 'const pplx::cancellation_token &'
3202>error-continuation e:\os\src\onecore\ds\ds\src\aimx\prod\aimxsrv\server\ragservicemanager.cpp(644): note: Reason: cannot convert from 'const std::wstring' to 'const pplx::cancellation_token'
3202>error-continuation e:\os\src\onecore\ds\ds\src\aimx\prod\aimxsrv\server\ragservicemanager.cpp(644): note: No user-defined-conversion operator available that can perform this conversion, or the operator cannot be called
3202>error-continuation e:\os\src\onecore\ds\ds\src\aimx\prod\aimxsrv\server\ragservicemanager.cpp(644): note: while trying to match the argument list '(web::http::http_request, const std::wstring)'
3202>NMAKE : fatal error U1077: 'e:\os\tools\vc\HostX86\amd64\cl.exe @e:\os\obj\amd64fre\temp\370d0993782184a8126dbf8c5a6bd11a\cl_1.rsp' : return code '0x2'
3202>e:\os\tools\NMakeRust\x64\bin\nmake.exe /nologo BUILDMSG=Stop. /nologo /f e:\os\src\tools\makefile.def BUILD_PASS=PASS1 NOLINK=1 MAKEDIR_RELATIVE_TO_BASEDIR=onecore\ds\ds\src\aimx\prod\aimxsrv\server failed - rc = 0x00000001

ERROR: errors in directory e:\os\src\onecore\ds\ds\src\aimx\prod\mcpserversample\exe
ERROR: errors found in parent: terminating pass 2

ERROR: errors in directory e:\os\src\onecore\ds\ds\src\aimx\prod\aimxsrv\dll
ERROR: errors found in parent: terminating pass 2

(sourcesdep) : error : Inverted pass dependency detected with onecore\ds\ds\src\aimx\prod\aimxsrv\dll(PASS1).
(TRACEREPORT)error :   Dependent files from onecore\ds\ds\src\aimx\prod\aimxsrv\dll:
(TRACEREPORT)error :     onecore\ds\ds\src\aimx\prod\aimxsrv\dll\objfre\amd64\dllmain.wrn
(TRACEREPORT)error :     onecore\ds\ds\src\aimx\prod\aimxsrv\dll\objfre\amd64\pch_hdr.wrn
(sourcesdep) : error : Inverted pass dependency detected with onecore\ds\ds\src\aimx\prod\aimxsrv\server(PASS1).
(TRACEREPORT)error :   Dependent files from onecore\ds\ds\src\aimx\prod\aimxsrv\server:
(TRACEREPORT)error :     onecore\ds\ds\src\aimx\prod\aimxsrv\server\objfre\amd64\aimxllmconfig.wrn
(TRACEREPORT)error :     onecore\ds\ds\src\aimx\prod\aimxsrv\server\objfre\amd64\aimxrpcserver.wrn
(TRACEREPORT)error :     onecore\ds\ds\src\aimx\prod\aimxsrv\server\objfre\amd64\conversationmanager.wrn
(TRACEREPORT)error :     onecore\ds\ds\src\aimx\prod\aimxsrv\server\objfre\amd64\llminfer.wrn
(TRACEREPORT)error :     onecore\ds\ds\src\aimx\prod\aimxsrv\server\objfre\amd64\mcpstdioclient.wrn
(TRACEREPORT)error :     onecore\ds\ds\src\aimx\prod\aimxsrv\server\objfre\amd64\mcpsvrmgr.wrn
(TRACEREPORT)error :     onecore\ds\ds\src\aimx\prod\aimxsrv\server\objfre\amd64\mcptoolmanager.wrn
(TRACEREPORT)error :     onecore\ds\ds\src\aimx\prod\aimxsrv\server\objfre\amd64\orchestrator.wrn
(TRACEREPORT)error :     onecore\ds\ds\src\aimx\prod\aimxsrv\server\objfre\amd64\pch_hdr.wrn
(TRACEREPORT)error :     onecore\ds\ds\src\aimx\prod\aimxsrv\server\objfre\amd64\planner.wrn
(TRACEREPORT)error :     onecore\ds\ds\src\aimx\prod\aimxsrv\server\objfre\amd64\ragservicemanager.wrn
(TRACEREPORT)error :     onecore\ds\ds\src\aimx\prod\aimxsrv\server\objfre\amd64\requesthandler.wrn
toolredirector.exe analyzernative -merge:e:\os\obj\amd64fre\objfre\amd64\build.ldg -reportconfig:e:\os\src\build\config\core\dbb_report_config.xml -in:bin e:\os\src\onecore\ds\ds\src\aimx\prod\buildfre.trc failed - rc = 0x00000001

