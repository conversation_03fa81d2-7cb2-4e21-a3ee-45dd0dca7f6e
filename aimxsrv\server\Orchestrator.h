#include <string>
/*++

Copyright (c) Microsoft Corporation. All rights reserved.

Module Name:
    Orchestrator.h

Abstract:
    Header file for AIMX Orchestrator that executes planned operations by coordinating
    with components like MCP Servers and Data Collection Agents.

Author:

    <PERSON> (SNAKE FIGHTER) (linda<PERSON>p) 06/03/2025

--*/

#pragma once

#include <Windows.h>
#include <string>
#include "AimxCommon.h"
#include "LLMInfer.h"
#include "McpToolManager.h"

//
// Orchestrator class handles execution of planned operations
//
class Orchestrator
{
public:
    //
    // Start asynchronous execution of an operation
    //
    static HRESULT ExecuteOperationAsync(
        _In_ const GUID& operationId
        );

    //
    // Cancel a running operation
    //
    static HRESULT CancelOperation(
        _In_ const GUID& operationId
        );

private:
    //
    // Internal worker thread function for asynchronous execution
    //
    static DWORD WINAPI ExecutionWorkerThread(
        _In_ LPVOID lpParameter
        );

    //
    // Execute a single step from the execution plan
    //
    static HRESULT ExecuteStep(
        _In_ const GUID& operationId,
        _In_ int stepId,
        _In_ const std::wstring& stepJson,
        _Out_ std::wstring& result
    );

    //
    // Aggregate results from multiple data sources
    //
    static HRESULT AggregateResults(
        _In_ const std::wstring& rawResults,
        _Out_ std::wstring& aggregatedResults
    );

    //
    // Call the LLM provider with the given query.
    //
    static HRESULT CallLLMProvider(
        _In_ const std::wstring& query,
        _Out_ std::wstring& response
    );


};

// Summarize results using the original question and combined results.
HRESULT GenerateLLMAnswerFromContext(
    _In_ const std::wstring& originalQuestion,
    _In_ const std::wstring& combinedResults,
    _Out_ std::wstring& answerSummary
);
