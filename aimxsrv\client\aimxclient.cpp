/*++

Copyright (c) Microsoft Corporation. All rights reserved.

Module Name:
    aimxclient.cpp

Abstract:
    Exported C API for AIMXSRV client access, wrapping AimxRpcClient methods.

Author:
    <PERSON> (SNAKE FIGHTER) (linda<PERSON>p) 06/13/2025

--*/

#include "pch.hxx"
#include "aimxrpcclient.h"

#include "aimxclient.cpp.tmh"

extern "C" __declspec(dllexport)
HRESULT AimxConnect(_Out_ GUID* pContextId)
{
    AimxRpcClient* pAimxClient = new AimxRpcClient();
    if (!pAimxClient)
    {
        TraceErr(AimxClient, "AimxRpcClient allocation failed");
        return E_OUTOFMEMORY;
    }
    return pAimxClient->AimxConnect(pContextId);
}

extern "C" __declspec(dllexport)
HRESULT AimxClose(_In_ GUID contextId)
{
    AimxRpcClient* pAimxClient = new AimxRpcClient();
    if (!pAimxClient)
    {
        TraceErr(AimxClient, "AimxRpcClient allocation failed");
        return E_OUTOFMEMORY;
    }
    return pAimxClient->AimxClose(contextId);
}

extern "C" __declspec(dllexport)
HRESULT AimxProcessPrompt(
    _In_ GUID contextId,
    _In_ LPCWSTR InputPrompt,
    _Out_ LPWSTR* Response)
{
    AimxRpcClient* pAimxClient = new AimxRpcClient();
    if (!pAimxClient)
    {
        TraceErr(AimxClient, "AimxRpcClient allocation failed");
        return E_OUTOFMEMORY;
    }
    return pAimxClient->AimxProcessPrompt(contextId, InputPrompt, Response);
}

extern "C" __declspec(dllexport)
HRESULT AimxPollConversationMessages(
    _In_ GUID contextId,
    _Out_ LPWSTR* messages)
{
    AimxRpcClient* pAimxClient = new AimxRpcClient();
    if (!pAimxClient)
    {
        TraceErr(AimxClient, "AimxRpcClient allocation failed");
        return E_OUTOFMEMORY;
    }
    return pAimxClient->AimxPollConversationMessages(contextId, messages);
}

extern "C" __declspec(dllexport)
HRESULT AimxGetConversationStatus(
    _In_ GUID contextId,
    _Out_ LPWSTR* statusJson)
{
    AimxRpcClient* pAimxClient = new AimxRpcClient();
    if (!pAimxClient)
    {
        TraceErr(AimxClient, "AimxRpcClient allocation failed");
        return E_OUTOFMEMORY;
    }
    return pAimxClient->AimxGetConversationStatus(contextId, statusJson);
}

extern "C" __declspec(dllexport)
HRESULT AimxStartConversation(
    _In_ GUID contextId,
    _In_ LPCWSTR query,
    _In_ LONG executionMode)
{
    AimxRpcClient* pAimxClient = new AimxRpcClient();
    if (!pAimxClient)
    {
        TraceErr(AimxClient, "AimxRpcClient allocation failed");
        return E_OUTOFMEMORY;
    }
    return pAimxClient->AimxStartConversation(contextId, query, executionMode);
}

extern "C" __declspec(dllexport)
HRESULT AimxGetLlmStatus(
    _In_ GUID contextId,
    _Out_ LPWSTR* statusJson)
{
    AimxRpcClient* pAimxClient = new AimxRpcClient();
    if (!pAimxClient)
    {
        TraceErr(AimxClient, "AimxRpcClient allocation failed");
        return E_OUTOFMEMORY;
    }
    return pAimxClient->AimxGetLlmStatus(contextId, statusJson);
}

extern "C" __declspec(dllexport)
HRESULT AimxGetMcpServerInfo(
    _In_ GUID contextId,
    _Out_ LPWSTR* serverInfoJson)
{
    AimxRpcClient* pAimxClient = new AimxRpcClient();
    if (!pAimxClient)
    {
        TraceErr(AimxClient, "AimxRpcClient allocation failed");
        return E_OUTOFMEMORY;
    }
    return pAimxClient->AimxGetMcpServerInfo(contextId, serverInfoJson);
}

// DllMain for aimxclient.dll
BOOL APIENTRY DllMain(
    _In_ HINSTANCE hinstDLL,
    _In_ DWORD fdwReason,
    _In_ LPVOID lpvReserved)
{

    UNREFERENCED_PARAMETER(lpvReserved);

    switch (fdwReason)
    {
    case DLL_PROCESS_ATTACH:

        DisableThreadLibraryCalls(hinstDLL);

        WPP_INIT_TRACING(L"aimxclient");

        TraceInfo(AimxClient, "DLL_PROCESS_ATTACH: Initialized AIMX client");

        break;
    case DLL_PROCESS_DETACH:
        WPP_CLEANUP();
        break;
    default:
        break;
    }
    return TRUE;
}
