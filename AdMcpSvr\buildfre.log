BUILD COMMAND: e:\os\tools\CoreBuild\amd64\build.exe  -parent

Reading historical build information...
Reading historical build information completed in [0:00:00.000]
BuildExe GUID: {9BDB1CDA-B78F-40A3-87E7-C3B057091AA8}
Launching process: e:\os\tools\CoreBuild\amd64\tracer.exe  /skip:1 -f:.\buildfre.trc -guid:e:\os\src\registered_data.ini   /logPrefix:buildfre /c:"e:\os\tools\CoreBuild\amd64\buildc.exe" /s:localhost:29026  
1>  *************
1>'e:\os\tools\CoreBuild\amd64\tracer.exe  /skip:1 -f:.\buildfre.trc -guid:e:\os\src\registered_data.ini   /logPrefix:buildfre /c:"e:\os\tools\CoreBuild\amd64\buildc.exe" /s:localhost:29026  '
1>info: Microsoft.Internal.Trace.Tracer.EtwTraceAdapter[0]
1>      Processing Build Trace File Logger *************-4aa0-a34e-55f9c8ec57e3
Merging config files using BUILD_CONFIG_FILE=e:\os\obj\amd64fre\objfre\amd64\build-exe-merged.config
BUILD: (ActiveWorkLoad)*, ElapsedTime(s),Counter,Available Memory (%),Disk I/O (ms),Disk Usage (%),CPU (%),Previous,New,Last Max,Permitted Threads,ThreadPool Memory Footprint (bytes), Max Memory Allowed (bytes), Submitted Thread,Running Threads,WorkItems (Available), WorkItems (Waiting),Pass,Priority,Peak Memory Project (bytes),Directory,MachineName
1>  1>[0:00:00.218] [Pass0 ] (none) {1}
3001>Merging config files  *************
3001>'mergeconfigfilesforbuildexe.cmd '
3001>MergeConfigFilesForBuildExe.cmd: Merged config file current: [e:\os\obj\amd64fre\objfre\amd64\build-exe-merged.config].
Executing PreGraph commands  *************
1>Performing pregraph steps...  *************
1>'build_pre_graph '
1>(build_pre_graph.cmd) e:\os\tools\NodeJS.x64\node.exe e:\os\src\tools\nmakejs\Transpile.js
1>(build_pre_graph.cmd) e:\os\tools\NodeJS.x64\node.exe e:\os\obj\amd64fre\objfre\amd64\NMakeJS\PreGraph.js
1>(build_pre_graph.cmd) Determining best branch for vpack with prefix "cdg" and suffix "amd64fre"...
1>(build_pre_graph.cmd) Latest vPack "cdg.rs_wsd_cfe_adai.amd64fre" version from label file e:\os\src\sdpublic\misc\Labels\rs_wsd_cfe_adai_label.xml is 27903.1000.2507161529
1>(build_pre_graph.cmd) Preferring this branch's vpack information
1>(build_pre_graph.cmd) Latest vPack "cdg.rs_wsd_cfe_adai.amd64fre" version from label file e:\os\src\sdpublic\misc\Labels\rs_wsd_cfe_adai_label.xml is 27903.1000.2507161529
1>(build_pre_graph.cmd) Not pulling vpack cdg.rs_wsd_cfe_adai.amd64fre because it is up-to-date (matches the marker file at e:\os\cdg\Target.cdg-amd64fre.man).
1>(build_pre_graph.cmd) Completed successfully.
Pre-Graph completed in [0:00:00.735]
Executing preprocess commands  *************
1>warn: Microsoft.Internal.Trace.Tracer.EtwTraceAdapter[0]
1>      Task table not implemented
1>(build_pre_process.cmd) Determining best branch for vpack with prefix "publics" and suffix "amd64"...
1>(build_pre_process.cmd) Latest vPack "publics.rs_wsd_cfe_adai.amd64" version from label file e:\os\src\sdpublic\misc\Labels\rs_wsd_cfe_adai_label.xml is 27903.1000.2507161445
1>(build_pre_process.cmd) Preferring this branch's vpack information
1>(build_pre_process.cmd) Latest vPack "publics.rs_wsd_cfe_adai.amd64" version from label file e:\os\src\sdpublic\misc\Labels\rs_wsd_cfe_adai_label.xml is 27903.1000.2507161445
1>(build_pre_process.cmd) First run of UPDATE_PUBLIC since the overlay was resumed by razzle.cmd
1>BUILDMSG: build_pre_process: Updating publics @ e:\os\public\amd64fre
1>(build_pre_process.cmd) Running 'urtrun64 4.Latest e:\os\tools\OSPublics\OSPublics.exe UpdateOverlay /ManifestId:amd64fre-6602-875f0c19b2160da5dee02afeb94c2219 /BaseManifestId:publics.rs_wsd_cfe_adai.amd64.27903.1000.2507161445 /Arch:amd64 /PublicRoot:e:\os\public\amd64fre /PublicChangesExternalPath:e:\os\src\tools\publicchanges.external.txt /PublishRulesPath:e:\os\src\.config\onecoreuap\build\PublishRules.json'
1>OSPublics.UpdateOverlay: Information: Version=0.0.0.0.
1>OSPublics.UpdateOverlay: Information: Parsing rules ..
1>OSPublics.UpdateOverlay: Information: [00.3s] DONE: Parsing rules ..
1>OSPublics.UpdateOverlay: Information: Starting overlay daemon...
1>OSPublics.UpdateOverlay: Information: [00.2s] DONE: Starting overlay daemon...
1>OSPublics.UpdateOverlay: Information: Starting overlay of 'publics.rs_wsd_cfe_adai.amd64.27903.1000.2507161445' to 'e:\os\public\amd64fre'...
1>OSPublics.UpdateOverlay: LogAlways: {"contentManifestId":"amd64fre-6602-875f0c19b2160da5dee02afeb94c2219","forceRefresh":false}
1>OSPublics.UpdateOverlay: LogAlways: {"contentSize":115482,"filesOnly":true}
1>OSPublics.UpdateOverlay: LogAlways: {"succeeded":true,"callEndData":"","elapsedCallTimeInMilliseconds":544}
1>OSPublics.UpdateOverlay: LogAlways: {"succeeded":true,"callEndData":"","elapsedCallTimeInMilliseconds":1161}
1>OSPublics.UpdateOverlay: Information: [04.1s] DONE: Starting overlay of 'publics.rs_wsd_cfe_adai.amd64.27903.1000.2507161445' to 'e:\os\public\amd64fre'...
1>OSPublics.UpdateOverlay: Information: Completed in 04.6s.
1>BUILDMSG: build_pre_process: Publics update complete [elapsed=5s].
1>(build_pre_process.cmd) e:\os\tools\NodeJS.x64\node.exe e:\os\obj\amd64fre\objfre\amd64\NMakeJS\PreProcess.js
1>(ReplicaTool.cmd) [07/17/25 20:19:47] Starting execution
1>(ReplicaTool.cmd)   Full LNM replica verification is active -- 'disable.lnm.replicas' build setting is not set / set to false.
1>(ReplicaTool.cmd)   Tool will verify that files are in sync
1>(ReplicaTool.cmd)   Using replica map "e:\os\src\xbox\data\replica_maps\default.txt" from the default value
1>(ReplicaTool.cmd)   Using IDK (onecore) repository root "e:\os\src" from the BASEDIR environment variable
1>(ReplicaTool.cmd)   Using Edition (xbox) repository root "e:\os\src" from the BASEDIR environment variable
1>(ReplicaTool.cmd) Reading replica map...
1>(ReplicaTool.cmd) Expanding replica map...
1>(ReplicaTool.cmd) Scanning onecore\drivers\wdm\usb\platformdetection\... => xbox\replicas\usb\platformdetection\...
1>(ReplicaTool.cmd) Scanning onecore\net\flowsteering\... => xbox\replicas\net\flowsteering\...
1>(ReplicaTool.cmd) Excluding onecore\drivers\wdm\usb\platformdetection\...\.vs\... => xbox\replicas\usb\platformdetection\...\.vs\...
1>(ReplicaTool.cmd) Excluding onecore\drivers\wdm\usb\platformdetection\...\dirs => xbox\replicas\usb\platformdetection\...\dirs
1>(ReplicaTool.cmd) Excluding onecore\drivers\wdm\usb\platformdetection\.vs\... => xbox\replicas\usb\platformdetection\.vs\...
1>(ReplicaTool.cmd) Excluding onecore\drivers\wdm\usb\platformdetection\dirs => xbox\replicas\usb\platformdetection\dirs
1>(ReplicaTool.cmd) Excluding onecore\net\flowsteering\...\.vs\... => xbox\replicas\net\flowsteering\...\.vs\...
1>(ReplicaTool.cmd) Excluding onecore\net\flowsteering\...\.vscode\... => xbox\replicas\net\flowsteering\...\.vscode\...
1>(ReplicaTool.cmd) Excluding onecore\net\flowsteering\...\dirs => xbox\replicas\net\flowsteering\...\dirs
1>(ReplicaTool.cmd) Excluding onecore\net\flowsteering\...\sources => xbox\replicas\net\flowsteering\...\sources
1>(ReplicaTool.cmd) Excluding onecore\net\flowsteering\...\sources.dep => xbox\replicas\net\flowsteering\...\sources.dep
1>(ReplicaTool.cmd) Excluding onecore\net\flowsteering\...\sources.inc => xbox\replicas\net\flowsteering\...\sources.inc
1>(ReplicaTool.cmd) Excluding onecore\net\flowsteering\.vs\... => xbox\replicas\net\flowsteering\.vs\...
1>(ReplicaTool.cmd) Excluding onecore\net\flowsteering\.vscode\... => xbox\replicas\net\flowsteering\.vscode\...
1>(ReplicaTool.cmd) Excluding onecore\net\flowsteering\FlowsteeringReplicaMap.txt => xbox\replicas\net\flowsteering\FlowsteeringReplicaMap.txt
1>(ReplicaTool.cmd) Excluding onecore\net\flowsteering\base\um_win\... => xbox\replicas\net\flowsteering\base\um_win\...
1>(ReplicaTool.cmd) Excluding onecore\net\flowsteering\core\src\usrlib\... => xbox\replicas\net\flowsteering\core\src\usrlib\...
1>(ReplicaTool.cmd) Excluding onecore\net\flowsteering\core\ut\... => xbox\replicas\net\flowsteering\core\ut\...
1>(ReplicaTool.cmd) Excluding onecore\net\flowsteering\dirs => xbox\replicas\net\flowsteering\dirs
1>(ReplicaTool.cmd) Excluding onecore\net\flowsteering\engine\... => xbox\replicas\net\flowsteering\engine\...
1>(ReplicaTool.cmd) Excluding onecore\net\flowsteering\kd\... => xbox\replicas\net\flowsteering\kd\...
1>(ReplicaTool.cmd) Excluding onecore\net\flowsteering\porttracker\allocator\perf\... => xbox\replicas\net\flowsteering\porttracker\allocator\perf\...
1>(ReplicaTool.cmd) Excluding onecore\net\flowsteering\porttracker\allocator\src\km\... => xbox\replicas\net\flowsteering\porttracker\allocator\src\km\...
1>(ReplicaTool.cmd) Excluding onecore\net\flowsteering\porttracker\allocator\src\um\... => xbox\replicas\net\flowsteering\porttracker\allocator\src\um\...
1>(ReplicaTool.cmd) Excluding onecore\net\flowsteering\porttracker\allocator\test\... => xbox\replicas\net\flowsteering\porttracker\allocator\test\...
1>(ReplicaTool.cmd) Excluding onecore\net\flowsteering\porttracker\allocator\um\... => xbox\replicas\net\flowsteering\porttracker\allocator\um\...
1>(ReplicaTool.cmd) Excluding onecore\net\flowsteering\porttracker\client\test\... => xbox\replicas\net\flowsteering\porttracker\client\test\...
1>(ReplicaTool.cmd) Excluding onecore\net\flowsteering\porttracker\server\test\... => xbox\replicas\net\flowsteering\porttracker\server\test\...
1>(ReplicaTool.cmd) Excluding onecore\net\flowsteering\porttracker\test\... => xbox\replicas\net\flowsteering\porttracker\test\...
1>(ReplicaTool.cmd) Excluding onecore\net\flowsteering\replica-command.txt => xbox\replicas\net\flowsteering\replica-command.txt
1>(ReplicaTool.cmd) Excluding onecore\net\flowsteering\test\... => xbox\replicas\net\flowsteering\test\...
1>(ReplicaTool.cmd) Excluding onecore\net\flowsteering\testlists\... => xbox\replicas\net\flowsteering\testlists\...
1>Skipping pull of Xbox BBT manifest
1> e:\os\tools\Windows.Desktop.Tools.amd64\tools\touch.exe /c e:\os\public\amd64fre\public.log
1>Projecting build config
1> e:\os\tools\perl64\bin\perl.exe e:\os\src\tools\project-buildconfigs.pl -config:e:\os\obj\amd64fre\objfre\amd64\build-exe-merged.config -buildOfficialBranch: -outputFolder:e:\os\obj\amd64fre\objfre\amd64\projectedconfig  -branchkeys: -keys:
1>(project-buildconfigs.pl) Done projecting build configuration keys
1>Skipping Source Link files generation
1> c:\windows\system32\cmd.exe /c del /Q e:\os\obj\amd64fre\objfre\amd64\CoreBuild\__SourceLinkConfiguration_uncached__.json 2>nul
1>Skipping PGI instrumentation.
1>Skipping OneCore Velocity import in a branch that builds OneCore
1>(build_pre_process.cmd) Completed successfully.
DBB MODE: Retrieving dependency information. Please wait...
Examining e:\os\src\onecore\ds\ds\src\aimx\prod\admcpsvr 
ScanSourceDirectories: Total dirs files scanned = 0
Scanning focus directories completed in [0:00:00.015]
Processing time to write metadata log file .\buildfre.metadata = 0.016 Secs

BUILD: Scanning directories in defined scope...
Examining e:\os\src (directory tree)
ScanSourceDirectories: Total dirs files scanned = 7
BUILD: Processing dependencies...
Pre-Build completed in [0:00:16.062]
1>  1>[0:00:17.484] [Pass0 ] e:\os\src\onecore\ds\ds\src\aimx\prod\admcpsvr {4}
3001>Building generated files in e:\os\src\onecore\ds\ds\src\aimx\prod\admcpsvr *************
3001>'e:\os\tools\NMakeRust\x64\bin\nmake.exe /nologo BUILDMSG=Stop. BUILD_PASS=PASS0 /nologo /f e:\os\src\tools\makefile.def NOLINK=1 PASS0ONLY=1 MAKEDIR_RELATIVE_TO_BASEDIR=onecore\ds\ds\src\aimx\prod\admcpsvr TARGET_PLATFORM=windows CODE_SETS_BUILDING=cs_windows,cs_xbox,cs_phone CODE_SETS_TAGGED=cs_windows,cs_xbox'
BUILD: (ActiveWorkLoad),17.25,,72,2,1,7,16,0,0,1,0,0,0,0,PASS0,0,2000000000,onecore\ds\ds\src\aimx\prod\admcpsvr,RUPO-DELL
3001>Calculated LAYERINFO_MODULE='OneCoreDS'.
3001>makefile.def: TEMP=e:\os\obj\amd64fre\temp\01217401af709b68411c833429617939
3001>makefile.def: BUILDINGINDATT=
3001>[Core OS Undocking] NOT using package ''
3001>UCRT enabled: dir 'e:\os\src\onecore\ds\ds\src\aimx\prod\admcpsvr' (target 'AdMcpSvr', type 'LIBRARY', nt_target_version '0xA000011')
3001>ObjectsMac.ts: validation succeeded
3001>STL version 120 used in "e:\os\src\onecore\ds\ds\src\aimx\prod\admcpsvr" (STL_VER_TELEMETRY)
3001>_NEED_BUILDDATE not defined setting BUILDDATE to an invalid value.
3001>A subdirectory or file e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\admcpsvr\objfre\amd64 already exists.
3001> e:\os\tools\Windows.Desktop.Tools.amd64\tools\touch.exe /c e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\admcpsvr\objfre\amd64\_PASS0_Marker.log
3001> set BUILDMSG=WPP Processing: e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\admcpsvr\objfre\amd64
3001> e:\os\tools\EtwTools\tools\tracewpp.exe  -q    -ext:.cpp.h.hxx                                                                  -preserveext:.cpp.h.hxx                                                          -scan:..\aimxsrv\inc\wpp.h                                                    -DWPP_CHECK_INIT                                                                 -p:AIMXADMCPSRV                                                                  -func:TraceCrit{LEVEL=TRACE_LEVEL_CRITICAL}(FLAGS,MSG,...)                       -func:TraceErr{LEVEL=TRACE_LEVEL_ERROR}(FLAGS,MSG,...)                           -func:TraceWarn{LEVEL=TRACE_LEVEL_WARNING}(FLAGS,MSG,...)                        -func:TraceInfo{LEVEL=TRACE_LEVEL_INFORMATION}(FLAGS,MSG,...)                    -func:TraceVerb{LEVEL=TRACE_LEVEL_VERBOSE}(FLAGS,MSG,...)                         AdMcpSvr.cpp  PowerShellFilterParser.cpp  GetADUserTool.cpp  GetADGroupTool.cpp  GetADComputerTool.cpp  GetADDomainTool.cpp  GetADForestTool.cpp  GetADDefaultDomainPasswordPolicyTool.cpp  -cfgdir:e:\os\tools\EtwTools\tools\WppConfig\rev1  -odir:e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\admcpsvr\objfre\amd64
3001> e:\os\tools\cppwinrt\cppwinrt.exe @e:\os\obj\amd64fre\temp\01217401af709b68411c833429617939\tmp_33480_1752808797692372100.tmp
3001> c:\windows\system32\cmd.exe /c del e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\admcpsvr\objfre\amd64\binplace_PASS0.rsp 2>nul
3001> e:\os\tools\powershell\pwsh.exe -NoProfile e:\os\src\tools\NMakeJS\WarningsCop\WarningsCop.ps1 -MakeSubDir "onecore\ds\ds\src\aimx\prod\admcpsvr" -Pass PASS0 -BaselineFile e:\os\src\.config\OneCore\WarningsCop.json -OutputDir "e:\os\bin\amd64fre\evidence\WarningsCop\OneCore\onecoreds"
3001>WarningsCop.ps1 : WarningsCop: Processing onecore\ds\ds\src\aimx\prod\admcpsvr in pass PASS0
BUILD: Pass complete => PASS0
1>  1>[0:00:19.515] [Pass1 ] e:\os\src\onecore\ds\ds\src\aimx\prod\admcpsvr {5}
3201>Compiling e:\os\src\onecore\ds\ds\src\aimx\prod\admcpsvr *************
3201>'e:\os\tools\NMakeRust\x64\bin\nmake.exe /nologo BUILDMSG=Stop. /nologo /f e:\os\src\tools\makefile.def BUILD_PASS=PASS1 NOLINK=1 MAKEDIR_RELATIVE_TO_BASEDIR=onecore\ds\ds\src\aimx\prod\admcpsvr TARGET_PLATFORM=windows CODE_SETS_BUILDING=cs_windows,cs_xbox,cs_phone CODE_SETS_TAGGED=cs_windows,cs_xbox'
BUILD: (ActiveWorkLoad),19.27,,72,1,2,11,16,0,0,0,0,0,0,0,PASS1,0,2000000000,onecore\ds\ds\src\aimx\prod\admcpsvr,RUPO-DELL
3201>Calculated LAYERINFO_MODULE='OneCoreDS'.
3201>makefile.def: TEMP=e:\os\obj\amd64fre\temp\751ff34733a32ec0971a685ba3825374
3201>makefile.def: BUILDINGINDATT=
3201>[Core OS Undocking] NOT using package ''
3201>UCRT enabled: dir 'e:\os\src\onecore\ds\ds\src\aimx\prod\admcpsvr' (target 'AdMcpSvr', type 'LIBRARY', nt_target_version '0xA000011')
3201>ObjectsMac.ts: validation succeeded
3201>STL version 120 used in "e:\os\src\onecore\ds\ds\src\aimx\prod\admcpsvr" (STL_VER_TELEMETRY)
3201>_NEED_BUILDDATE not defined setting BUILDDATE to an invalid value.
3201>A subdirectory or file e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\admcpsvr\objfre\amd64 already exists.
3201> e:\os\tools\Windows.Desktop.Tools.amd64\tools\touch.exe /c e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\admcpsvr\objfre\amd64\_PASS1_Marker.log
3201> e:\os\tools\vc\HostX86\amd64\cl.exe @e:\os\obj\amd64fre\temp\751ff34733a32ec0971a685ba3825374\cl_1.rsp
3201>Microsoft (R) C/C++ Optimizing Compiler Version 19.42.34444.100 for x64
3201>Copyright (C) Microsoft Corporation.  All rights reserved.
3201>cl /Fo"e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\admcpsvr\objfre\amd64/"
3201>   /FC
3201>   /Iamd64
3201>   /I.
3201>   /Ie:\os\src\data\MSRC
3201>   /Ie:\os\public\amd64fre\OneCore\Internal\MinWin\Priv_Sdk\Inc
3201>   /Ie:\os\public\amd64fre\OneCoreUap\Internal\BuildMetadata\internal\cppwinrt
3201>   /Ie:\os\public\amd64fre\onecoreuap\restricted\windows\inc
3201>   /I..\common
3201>   /I..\llmclientlib
3201>   /I..\aimxsrv\inc
3201>   /I..\aimxsrv\server
3201>   /I..\aimxsrv\client
3201>   /Ie:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\admcpsvr\..\aimxsrv\idl\objfre\amd64
3201>   /Ie:\os\public\amd64fre\OneCore\Internal\OneCore\Priv_Sdk\Inc
3201>   /Ie:\os\public\amd64fre\onecore\internal\base\inc\appmodel\runtime\winrt
3201>   /Ie:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\admcpsvr\objfre\amd64
3201>   /Ie:\os\src\onecore\ds\inc
3201>   /Ie:\os\obj\amd64fre\onecore\ds\inc\objfre\amd64
3201>   /Ie:\os\public\amd64fre\internal\onecoreds\inc
3201>   /Ie:\os\public\amd64fre\OneCore\Restricted\DS\inc
3201>   /Ie:\os\public\amd64fre\OneCoreUap\Restricted\DS\inc
3201>   /Ie:\os\public\amd64fre\OneCore\External\DS\inc
3201>   /Ie:\os\public\amd64fre\OneCoreUap\External\DS\inc
3201>   /Ie:\os\public\amd64fre\ClientCore\External\DS\inc
3201>   /Ie:\os\public\amd64fre\OneCore\Internal\DS\inc
3201>   /Ie:\os\public\amd64fre\OneCoreUap\Internal\DS\inc
3201>   /Ie:\os\public\amd64fre\ClientCore\Internal\DS\inc
3201>   /Ie:\os\public\amd64fre\OneCore\Private\DS\inc
3201>   /Ie:\os\public\amd64fre\OneCoreUap\Private\DS\inc
3201>   /Ie:\os\public\amd64fre\ClientCore\Private\DS\inc
3201>   /Ie:\os\public\amd64fre\OneCore\external\DS\inc
3201>   /Ie:\os\public\amd64fre\OneCore\restricted\DS\inc
3201>   /Ie:\os\public\amd64fre\OneCore\internal\DS\inc
3201>   /Ie:\os\public\amd64fre\OneCore\private\DS\inc
3201>   /Ie:\os\public\amd64fre\onecore\external\oak\inc
3201>   /Ie:\os\public\amd64fre\onecoreuap\external\oak\inc
3201>   /Ie:\os\public\amd64fre\shared\inc
3201>   /Ie:\os\public\amd64fre\onecore\external\shared\inc
3201>   /Ie:\os\public\amd64fre\onecoreuap\external\shared\inc
3201>   /Ie:\os\public\amd64fre\onecore\external\shared\inc\MinWin
3201>   /Ie:\os\public\amd64fre\sdk\inc
3201>   /Ie:\os\public\amd64fre\onecore\external\sdk\inc
3201>   /Ie:\os\public\amd64fre\onecoreuap\external\sdk\inc
3201>   /Ie:\os\public\amd64fre\onecore\private\sdk\inc\MinWin
3201>   /Ie:\os\public\amd64fre\onecore\internal\sdk\inc\MinWin
3201>   /Ie:\os\public\amd64fre\onecore\external\sdk\inc\MinWin
3201>   /Ie:\os\public\amd64fre\onecore\internal\sdk\inc\MinCore
3201>   /Ie:\os\public\amd64fre\onecore\external\sdk\inc\MinCore
3201>   /Ie:\os\public\amd64fre\onecore\internal\sdk\inc\ClientCore
3201>   /Ie:\os\public\amd64fre\onecore\external\sdk\inc\ClientCore
3201>   /Ie:\os\public\amd64fre\onecore\internal\sdk\inc\ModernCore
3201>   /Ie:\os\public\amd64fre\onecore\external\sdk\inc\ModernCore
3201>   /Ie:\os\public\amd64fre\shared\inc
3201>   /Ie:\os\public\amd64fre\onecore\external\shared\inc
3201>   /Ie:\os\public\amd64fre\onecoreuap\external\shared\inc
3201>   /Ie:\os\public\amd64fre\onecore\external\ddk\inc
3201>   /Ie:\os\public\amd64fre\onecoreuap\external\ddk\inc
3201>   /Ie:\os\public\amd64fre\onecore\external\ddk\inc\wdm
3201>   /Ie:\os\public\amd64fre\onecoreuap\external\ddk\inc\wdm
3201>   /Ie:\os\public\amd64fre\internal\sdk\inc
3201>   /Ie:\os\public\amd64fre\onecore\private\sdk\inc
3201>   /Ie:\os\public\amd64fre\onecoreuap\private\sdk\inc
3201>   /Ie:\os\public\amd64fre\onecore\internal\sdk\inc
3201>   /Ie:\os\public\amd64fre\onecore\restricted\sdk\inc
3201>   /Ie:\os\public\amd64fre\onecoreuap\internal\sdk\inc
3201>   /Ie:\os\public\amd64fre\onecoreuap\restricted\sdk\inc
3201>   /Ie:\os\public\amd64fre\onecore\internal\sdk\inc\MinWin
3201>   /Ie:\os\public\amd64fre\onecore\internal\sdk\inc\MinWin\fs
3201>   /Ie:\os\public\amd64fre\onecore\internal\sdk\inc\MinCore
3201>   /Ie:\os\public\amd64fre\onecore\internal\sdk\inc\ClientCore
3201>   /Ie:\os\public\amd64fre\onecore\internal\sdk\inc\ModernCore
3201>   /Ie:\os\public\amd64fre\OneCore\Internal\hv\hvsdk\just_built\inc\private
3201>   /Ie:\os\public\amd64fre\OneCore\Internal\hv\hvsdk\just_built\inc\internal
3201>   /Ie:\os\public\amd64fre\sdk\inc\ucrt
3201>   /Ie:\os\public\amd64fre\internal\sdk\inc\ucrt
3201>   /Ie:\os\public\amd64fre\onecore\external\sdk\inc\ucrt
3201>   /Ie:\os\public\amd64fre\onecore\internal\sdk\inc\ucrt
3201>   /Ie:\os\public\amd64fre\onecore\internal\sdk\inc\ucrt\stl120
3201>   /Ie:\os\public\amd64fre\onecore\internal\sdk\inc\ucrt\stl120
3201>   /D_WIN64
3201>   /D_AMD64_
3201>   /DAMD64
3201>   /DCONDITION_HANDLING=1
3201>   /DNT_INST=0
3201>   /DWIN32=100
3201>   /D_NT1X_=100
3201>   /DWINNT=1
3201>   /D_WIN32_WINNT=0x0A00
3201>   /DWINVER=0x0A00
3201>   /D_WIN32_IE=0x0A00
3201>   /DWIN32_LEAN_AND_MEAN=1
3201>   /DDEVL=1
3201>   /DNDEBUG
3201>   /D_STL120_
3201>   /D_STL140_
3201>   /D_DLL=1
3201>   /D_MT=1
3201>   -DNT_IUM
3201>   -DWIN32
3201>   -D_WIN32
3201>   -DUNICODE
3201>   -D_UNICODE
3201>   -D_ARM_WINAPI_PARTITION_DESKTOP_SDK_AVAILABLE
3201>   /D_USE_DEV11_CRT
3201>   -D_APISET_MINWIN_VERSION=0x0115
3201>   -D_APISET_MINCORE_VERSION=0x0114
3201>   /DFE_SB
3201>   /DFE_IME
3201>   /DNTDDI_VERSION=0x0A000011
3201>   /DWINBLUE_KBSPRING14
3201>   /DBUILD_WINDOWS
3201>   /DUNDOCKED_WINDOWS_UCRT
3201>   /D__WRL_CONFIGURATION_LEGACY__
3201>   /DBUILD_UMS_ENABLED=1
3201>   /DBUILD_WOW64_ENABLED=1
3201>   /DBUILD_ARM64X_ENABLED=0
3201>   /DEXECUTABLE_WRITES_SUPPORT=0
3201>   -D_USE_DECLSPECS_FOR_SAL=1
3201>   /DRUN_WPP
3201>   -D__PLACEHOLDER_SAL=1
3201>   /c
3201>   /Zc:wchar_t-
3201>   /Zl
3201>   /Zp8
3201>   /Gy
3201>   /W4
3201>   /wd4244
3201>   /EHsc
3201>   /d1import_no_registry
3201>   /EHsc
3201>   /GR-
3201>   /GF
3201>   /GS
3201>   /Z7
3201>   /Oxs
3201>   /GL
3201>   /Z7
3201>   @e:\os\src\tools\NMakeJS\WarningsCop\WarningsCop.Cxx.DefaultErrors.rsp
3201>   /we4308 /we4509 /we4510 /we4532 /we4533 /we4610 /we4700 /we4789
3201>   /w15043
3201>   /Zc:rvalueCast
3201>   -D_UCRT
3201>   -D_CONST_RETURN=
3201>   -D_CRT_SECURE_NO_WARNINGS
3201>   -D_CRT_NON_CONFORMING_SWPRINTFS
3201>   -D_CRT_NONSTDC_NO_WARNINGS
3201>   -D_CRT_STDIO_ARBITRARY_WIDE_SPECIFIERS
3201>   /D_CRT_STDIO_INLINE=extern
3201>   /D_NO_CRT_STDIO_INLINE
3201>   /D_ACRTIMP_ALT=
3201>   /D_SILENCE_STDEXT_HASH_DEPRECATION_WARNINGS
3201>   /D_STL_EXTRA_DISABLED_WARNINGS=4239
3201>   /D_SILENCE_TR1_NAMESPACE_DEPRECATION_WARNING
3201>   /D_SILENCE_ALL_CXX17_DEPRECATION_WARNINGS
3201>   /D_SILENCE_TR2_SYS_NAMESPACE_DEPRECATION_WARNING
3201>   /D_HAS_FUNCTION_ALLOCATOR_SUPPORT=1
3201>   /D_SILENCE_STDEXT_ALLOCATORS_DEPRECATION_WARNING
3201>   /D_HAS_STD_BYTE=0
3201>   /D_ENFORCE_MATCHING_ALLOCATORS=0
3201>   /D_HAS_FUNCTION_ALLOCATOR_SUPPORT=1
3201>   /D_SILENCE_STDEXT_ALLOCATORS_DEPRECATION_WARNING
3201>   /D_FULL_IOBUF
3201>   /d1initAll:Mask11
3201>   /d1initAll:FillPattern0
3201>   /d1nodatetime
3201>   /d1trimfile:e:\os\src\=BASEDIR
3201>   /d1trimfile:e:\os\public\amd64fre\=PUBLIC_ROOT
3201>   /d1trimfile:e:\os\obj\amd64fre\=OBJECT_ROOT
3201>   /d1trimfile:e:\os\bin\amd64fre\=_NTTREE
3201>   /d1trimfile:e:\os\osdep\=OSDEPENDSROOT
3201>   /d2AllowCompatibleILVersions
3201>   /d2Zi+
3201>   /ZH:SHA_256
3201>   /wd4986
3201>   /wd4987
3201>   /wd4471
3201>   /wd4369
3201>   /wd4309
3201>   /wd4754
3201>   /wd4427
3201>   /d2DeepThoughtInliner-
3201>   /d2implyavx512upperregs-
3201>   /Wv:19.23
3201>   /Fwe:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\admcpsvr\objfre\amd64\
3201>   @e:\os\obj\amd64fre\objfre\amd64\DMF\logged-warnings.rsp
3201>   /wl4002
3201>   /wl4003
3201>   /wl4005
3201>   /wl4006
3201>   /wl4007
3201>   /wl4008
3201>   /wl4010
3201>   /wl4013
3201>   /wl4015
3201>   /wl4018
3201>   /wl4020
3201>   /wl4022
3201>   /wl4024
3201>   /wl4025
3201>   /wl4026
3201>   /wl4027
3201>   /wl4028
3201>   /wl4029
3201>   /wl4030
3201>   /wl4031
3201>   /wl4033
3201>   /wl4034
3201>   /wl4036
3201>   /wl4038
3201>   /wl4041
3201>   /wl4042
3201>   /wl4045
3201>   /wl4047
3201>   /wl4048
3201>   /wl4049
3201>   /wl4056
3201>   /wl4066
3201>   /wl4067
3201>   /wl4068
3201>   /wl4073
3201>   /wl4074
3201>   /wl4075
3201>   /wl4076
3201>   /wl4077
3201>   /wl4079
3201>   /wl4080
3201>   /wl4081
3201>   /wl4083
3201>   /wl4085
3201>   /wl4086
3201>   /wl4087
3201>   /wl4088
3201>   /wl4089
3201>   /wl4090
3201>   /wl4091
3201>   /wl4094
3201>   /wl4096
3201>   /wl4097
3201>   /wl4098
3201>   /wl4099
3201>   /wl4101
3201>   /wl4102
3201>   /wl4109
3201>   /wl4112
3201>   /wl4113
3201>   /wl4114
3201>   /wl4115
3201>   /wl4116
3201>   /wl4117
3201>   /wl4119
3201>   /wl4120
3201>   /wl4122
3201>   /wl4124
3201>   /wl4129
3201>   /wl4133
3201>   /wl4138
3201>   /wl4141
3201>   /wl4142
3201>   /wl4143
3201>   /wl4144
3201>   /wl4145
3201>   /wl4150
3201>   /wl4153
3201>   /wl4154
3201>   /wl4155
3201>   /wl4156
3201>   /wl4157
3201>   /wl4158
3201>   /wl4159
3201>   /wl4160
3201>   /wl4161
3201>   /wl4162
3201>   /wl4163
3201>   /wl4164
3201>   /wl4166
3201>   /wl4167
3201>   /wl4168
3201>   /wl4172
3201>   /wl4174
3201>   /wl4175
3201>   /wl4176
3201>   /wl4177
3201>   /wl4178
3201>   /wl4180
3201>   /wl4182
3201>   /wl4183
3201>   /wl4185
3201>   /wl4186
3201>   /wl4187
3201>   /wl4190
3201>   /wl4192
3201>   /wl4197
3201>   /wl4200
3201>   /wl4213
3201>   /wl4215
3201>   /wl4216
3201>   /wl4218
3201>   /wl4223
3201>   /wl4224
3201>   /wl4226
3201>   /wl4227
3201>   /wl4228
3201>   /wl4229
3201>   /wl4230
3201>   /wl4237
3201>   /wl4240
3201>   /wl4243
3201>   /wl4244
3201>   /wl4250
3201>   /wl4251
3201>   /wl4258
3201>   /wl4267
3201>   /wl4269
3201>   /wl4272
3201>   /wl4273
3201>   /wl4274
3201>   /wl4275
3201>   /wl4276
3201>   /wl4278
3201>   /wl4280
3201>   /wl4281
3201>   /wl4282
3201>   /wl4283
3201>   /wl4285
3201>   /wl4286
3201>   /wl4288
3201>   /wl4290
3201>   /wl4291
3201>   /wl4293
3201>   /wl4297
3201>   /wl4302
3201>   /wl4305
3201>   /wl4306
3201>   /wl4307
3201>   /wl4309
3201>   /wl4310
3201>   /wl4311
3201>   /wl4312
3201>   /wl4313
3201>   /wl4316
3201>   /wl4319
3201>   /wl4325
3201>   /wl4326
3201>   /wl4329
3201>   /wl4333
3201>   /wl4334
3201>   /wl4335
3201>   /wl4340
3201>   /wl4344
3201>   /wl4346
3201>   /wl4348
3201>   /wl4353
3201>   /wl4356
3201>   /wl4357
3201>   /wl4358
3201>   /wl4359
3201>   /wl4364
3201>   /wl4368
3201>   /wl4369
3201>   /wl4373
3201>   /wl4374
3201>   /wl4375
3201>   /wl4376
3201>   /wl4377
3201>   /wl4378
3201>   /wl4379
3201>   /wl4381
3201>   /wl4382
3201>   /wl4383
3201>   /wl4384
3201>   /wl4390
3201>   /wl4391
3201>   /wl4392
3201>   /wl4393
3201>   /wl4394
3201>   /wl4395
3201>   /wl4396
3201>   /wl4397
3201>   /wl4398
3201>   /wl4399
3201>   /wl4600
3201>   /wl4401
3201>   /wl4402
3201>   /wl4403
3201>   /wl4404
3201>   /wl4405
3201>   /wl4406
3201>   /wl4407
3201>   /wl4409
3201>   /wl4410
3201>   /wl4411
3201>   /wl4414
3201>   /wl4420
3201>   /wl4430
3201>   /wl4436
3201>   /wl4439
3201>   /wl4440
3201>   /wl4441
3201>   /wl4445
3201>   /wl4461
3201>   /wl4462
3201>   /wl4470
3201>   /wl4473
3201>   /wl4477
3201>   /wl4484
3201>   /wl4485
3201>   /wl4486
3201>   /wl4488
3201>   /wl4489
3201>   /wl4490
3201>   /wl4502
3201>   /wl4503
3201>   /wl4506
3201>   /wl4508
3201>   /wl4511
3201>   /wl4518
3201>   /wl4521
3201>   /wl4522
3201>   /wl4523
3201>   /wl4526
3201>   /wl4530
3201>   /wl4534
3201>   /wl4535
3201>   /wl4537
3201>   /wl4538
3201>   /wl4540
3201>   /wl4541
3201>   /wl4543
3201>   /wl4544
3201>   /wl4550
3201>   /wl4551
3201>   /wl4552
3201>   /wl4553
3201>   /wl4554
3201>   /wl4556
3201>   /wl4558
3201>   /wl4561
3201>   /wl4566
3201>   /wl4570
3201>   /wl4572
3201>   /wl4580
3201>   /wl4581
3201>   /wl4584
3201>   /wl4596
3201>   /wl4597
3201>   /wl4602
3201>   /wl4603
3201>   /wl4606
3201>   /wl4612
3201>   /wl4613
3201>   /wl4615
3201>   /wl4616
3201>   /wl4618
3201>   /wl4620
3201>   /wl4621
3201>   /wl4622
3201>   /wl4624
3201>   /wl4627
3201>   /wl4630
3201>   /wl4632
3201>   /wl4633
3201>   /wl4635
3201>   /wl4636
3201>   /wl4637
3201>   /wl4638
3201>   /wl4641
3201>   /wl4645
3201>   /wl4646
3201>   /wl4650
3201>   /wl4651
3201>   /wl4652
3201>   /wl4653
3201>   /wl4655
3201>   /wl4656
3201>   /wl4657
3201>   /wl4659
3201>   /wl4661
3201>   /wl4662
3201>   /wl4667
3201>   /wl4669
3201>   /wl4674
3201>   /wl4677
3201>   /wl4678
3201>   /wl4679
3201>   /wl4683
3201>   /wl4684
3201>   /wl4685
3201>   /wl4687
3201>   /wl4688
3201>   /wl4691
3201>   /wl4693
3201>   /wl4694
3201>   /wl4698
3201>   /wl4711
3201>   /wl4715
3201>   /wl4716
3201>   /wl4717
3201>   /wl4722
3201>   /wl4723
3201>   /wl4724
3201>   /wl4727
3201>   /wl4730
3201>   /wl4731
3201>   /wl4733
3201>   /wl4739
3201>   /wl4742
3201>   /wl4743
3201>   /wl4744
3201>   /wl4747
3201>   /wl4750
3201>   /wl4756
3201>   /wl4768
3201>   /wl4772
3201>   /wl4788
3201>   /wl4793
3201>   /wl4794
3201>   /wl4799
3201>   /wl4803
3201>   /wl4804
3201>   /wl4805
3201>   /wl4806
3201>   /wl4807
3201>   /wl4810
3201>   /wl4811
3201>   /wl4812
3201>   /wl4813
3201>   /wl4817
3201>   /wl4819
3201>   /wl4821
3201>   /wl4823
3201>   /wl4829
3201>   /wl4834
3201>   /wl4835
3201>   /wl4838
3201>   /wl4839
3201>   /wl4867
3201>   /wl4900
3201>   /wl4910
3201>   /wl4912
3201>   /wl4920
3201>   /wl4925
3201>   /wl4926
3201>   /wl4927
3201>   /wl4929
3201>   /wl4930
3201>   /wl4935
3201>   /wl4936
3201>   /wl4939
3201>   /wl4944
3201>   /wl4945
3201>   /wl4947
3201>   /wl4948
3201>   /wl4949
3201>   /wl4950
3201>   /wl4951
3201>   /wl4952
3201>   /wl4953
3201>   /wl4956
3201>   /wl4957
3201>   /wl4958
3201>   /wl4959
3201>   /wl4961
3201>   /wl4964
3201>   /wl4965
3201>   /wl4972
3201>   /wl4984
3201>   /wl4995
3201>   /wl4996
3201>   /wl4997
3201>   /wl4999
3201>   /wl5033
3201>   /wl5037
3201>   /wl5046
3201>   /wl5050
3201>   /wl5055
3201>   /wl5056
3201>   /wl5105
3201>   /wl5208
3201>   /d2Qvec-mathlib-
3201>   /d2Qvec-sse2only
3201>   /Gw
3201>   /Zc:checkGwOdr
3201>   /d1ignorePragmaWarningError
3201>   /wd4316
3201>   /wd4973
3201>   /DDONT_DISABLE_PCH_WARNINGS_IN_WARNING_H
3201>   /d2FH4
3201>   /Brepro
3201>   -D_HAS_MAGIC_STATICS=1
3201>   /Qspectre
3201>   /wd5045
3201>   /d2guardspecanalysismode:v1_0
3201>   /d2guardspecmode2
3201>   /guard:cf
3201>   /d2guardcfgfuncptr-
3201>   /d2guardcfgdispatch
3201>   /guard:ehcont
3201>   -D__PLACEHOLDER_SAL=1
3201>   -wd4425
3201>   @e:\os\obj\amd64fre\objfre\amd64\WarningsCop\OneCore.rsp
3201>   /wl4146 /wl4308 /wl4509 /wl4510 /wl4532 /wl4533 /wl4610 /wl4700 /wl4701 /wl4703 /wl4789
3201>   /FIe:\os\public\amd64fre\onecore\internal\sdk\inc\warning.h
3201>   /std:c++17
3201>   .\admcpsvr.cpp .\powershellfilterparser.cpp .\getadusertool.cpp .\getadgrouptool.cpp .\getadcomputertool.cpp .\getaddomaintool.cpp .\getadforesttool.cpp .\getaddefaultdomainpasswordpolicytool.cpp 
3201>admcpsvr.cpp
3201>powershellfilterparser.cpp
3201>getadusertool.cpp
3201>getadgrouptool.cpp
3201>getadcomputertool.cpp
3201>getaddomaintool.cpp
3201>getadforesttool.cpp
3201>getaddefaultdomainpasswordpolicytool.cpp
3201> e:\os\tools\vc\HostX64\amd64\link.exe /lib /out:e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\admcpsvr\objfre\amd64\AdMcpSvr.lib /IGNORE:4078,4221,4281,4006,4198   /nodefaultlib /machine:amd64 /ltcg /Brepro @e:\os\obj\amd64fre\temp\751ff34733a32ec0971a685ba3825374\lib_1.rsp
3201>Microsoft (R) Library Manager Version 14.42.34444.100
3201>Copyright (C) Microsoft Corporation.  All rights reserved.
3201>e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\admcpsvr\objfre\amd64\admcpsvr.obj 
3201>e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\admcpsvr\objfre\amd64\powershellfilterparser.obj 
3201>e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\admcpsvr\objfre\amd64\getadusertool.obj 
3201>e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\admcpsvr\objfre\amd64\getadgrouptool.obj 
3201>e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\admcpsvr\objfre\amd64\getadcomputertool.obj 
3201>e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\admcpsvr\objfre\amd64\getaddomaintool.obj 
3201>e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\admcpsvr\objfre\amd64\getadforesttool.obj 
3201>e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\admcpsvr\objfre\amd64\getaddefaultdomainpasswordpolicytool.obj 
3201>Writing out macros...e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\admcpsvr\objfre\amd64\Macros-PASS1.txt
3201>binplace e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\admcpsvr\objfre\amd64\Macros-PASS1.txt
3201> e:\os\tools\powershell\pwsh.exe -NoProfile e:\os\src\tools\NMakeJS\WarningsCop\WarningsCop.ps1 -MakeSubDir "onecore\ds\ds\src\aimx\prod\admcpsvr" -Pass PASS1 -BaselineFile e:\os\src\.config\OneCore\WarningsCop.json -OutputDir "e:\os\bin\amd64fre\evidence\WarningsCop\OneCore\onecoreds"
3201>WarningsCop.ps1 : WarningsCop: Processing onecore\ds\ds\src\aimx\prod\admcpsvr in pass PASS1
BUILD: Pass complete => PASS1

PERF: Waiting for performance monitor thread to terminate.
PERF: Terminating perf data collector thread.
1>info: Microsoft.Internal.Trace.Database.Core.TraceManager[0]
1>      Finalizing the trace reading...
1>info: Microsoft.Internal.Trace.Database.Core.AccessManager[0]
1>      Total Accesses = 2810
1>info: Microsoft.Internal.Trace.Database.Core.FileManager[0]
1>      Total Files = 1593
1>info: Microsoft.Internal.Trace.Database.Core.ProcessManager[0]
1>      Total Processes = 26
1>info: Microsoft.Internal.Trace.Tracer.EtwTraceAdapter[0]
1>      Total Process Time = 29.609375
1>info: Microsoft.Internal.Trace.Tracer.EtwTraceAdapter[0]
1>      (Logging time = 0.5845714999999998, Detour time = 0.5135381
1>info: Microsoft.Internal.Trace.Database.Core.TraceManager[0]
1>      Finalizing the trace reading...
1>warn: Microsoft.Internal.Trace.Database.Core.FileManager[0]
1>      Including all files for analysis because the Exclusion Evaluation rules are not loaded.
1>warn: Microsoft.Internal.Trace.Database.Core.DirectoryManager[0]
1>      Including all directories for analysis because the Exclusion Evaluation rules are not loaded.
1>info: Microsoft.Internal.Trace.Database.Core.DirectoryManager[0]
1>      Analyzing trace to infer dependencies...
1>info: Microsoft.Internal.Trace.Database.IO.BinaryTraceWriter[0]
1>      File e:\os\src\onecore\ds\ds\src\aimx\prod\admcpsvr\buildfre.trc already exists. Implicitly overwriting as the native code would have done.
1>info: Microsoft.Internal.Trace.Database.IO.BinaryTraceWriter[0]
1>      Writing e:\os\src\onecore\ds\ds\src\aimx\prod\admcpsvr\buildfre.trc
1>info: Microsoft.Internal.Trace.Database.IO.Table6[0]
1>      File table total write time = 00:00:00.0630612
1>info: Microsoft.Internal.Trace.Database.IO.Table6[0]
1>      File total bytes written = 38504
1>info: Microsoft.Internal.Trace.Database.IO.Table6[0]
1>      Process table total write time = 00:00:00.0089789
1>info: Microsoft.Internal.Trace.Database.IO.Table6[0]
1>      Process total bytes written = 7770
1>info: Microsoft.Internal.Trace.Database.IO.Table6[0]
1>      Access table total write time = 00:00:00.0029066
1>info: Microsoft.Internal.Trace.Database.IO.Table6[0]
1>      Access total bytes written = 12938
1>info: Microsoft.Internal.Trace.Database.IO.Table6[0]
1>      Task table total write time = 00:00:00.0001797
1>info: Microsoft.Internal.Trace.Database.IO.Table6[0]
1>      Task total bytes written = 21
1>info: Microsoft.Internal.Trace.Database.IO.Table6[0]
1>      EnvironmentAccess table total write time = 00:00:00.0001112
1>info: Microsoft.Internal.Trace.Database.IO.Table6[0]
1>      EnvironmentAccess total bytes written = 21
1>info: Microsoft.Internal.Trace.Database.IO.BinaryTraceWriter[0]
1>      Trace file written to e:\os\src\onecore\ds\ds\src\aimx\prod\admcpsvr\buildfre.trc
1>TRACER : Microsoft (R) Build Tracer
1>TRACER : Copyright (C) Microsoft Corporation. All rights reserved.
1>TRACER : Starting Realtime Build Trace File Logger *************-4aa0-a34e-55f9c8ec57e3...
1>TRACER : Enabling trace provider...
1>TRACER (BuildSocket): RegisterClient rupo-dell:0 (Tracer)
1>TRACER : Tracer Satellite: Satellite command disabled. (No value in environment variable TRACER_SATELLITE_COMMAND)
1>TRACER : Launching: "e:\os\tools\corebuild\amd64\buildc.exeTRACER :  /hostname localhost /hostport 29026TRACER : "
1>TRACER : Tracer Satellite: Stop Process: Nothing to do.
1>TRACER : Disabling trace provider...
1>TRACER (event): ETW Trace Session
1>TRACER (event): =================
1>TRACER (event): Buffers Allocated: 32
1>TRACER (event): Buffers Written: 135
1>TRACER (event): Buffer Size: 512KB
1>TRACER (event): Buffers Lost: 0
1>TRACER (event): Real Time Buffers Lost: 0
1>TRACER (event): Events Lost: 0
1>TRACER : Stopping Build Trace File Logger *************-4aa0-a34e-55f9c8ec57e3...
1>Running analyzer on build trace...
  *************
1>'toolredirector.exe analyzernative -merge:e:\os\obj\amd64fre\objfre\amd64\build.ldg -reportconfig:e:\os\src\build\config\core\dbb_report_config.xml -in:bin e:\os\src\onecore\ds\ds\src\aimx\prod\admcpsvr\buildfre.trc'
1>warn: Microsoft.Internal.Trace.Reporting.Reports.ExternalAccessReport.ExternalAccessReportExclusions[0]
1>      Provided file '' does not exist or is empty. Moving to fallback file 'e:\os\tools\Analyzer\amd64\DefaultConfigFiles\ExternalAccessReportExclusions.json'
1>ANALYZER : Microsoft (R) Build Trace Analyzer [Build 8.0.250519001+6b8e35f5c0ee29c4c18a354e33cc8b695d5695ad]
1>ANALYZER : Copyright (C) Microsoft Corporation. All rights reserved.
1>[20:20:33.439] Parsing error policy from 'e:\os\src\build\config\Core\AnalyzerEnforcedErrors.json'...
1>TRACEREPORT : Processing e:\os\src\build\config\core\dbb_report_config.xml
1>ANALYZER : Processing e:\os\src\build\config\Core\dbb_exclusions.xml
1>ANALYZER : ---------------------------
1>Reading input file
1>---------------------------
1>ANALYZER : Processing e:\os\src\onecore\ds\ds\src\aimx\prod\admcpsvr\buildfre.trc
1>ANALYZER : 
1>Read access table time = 0.001s
1>ANALYZER : 
1>Trace start time: 07/17/2025 08:19:38 PM
1>ANALYZER : 
1>Finalizing the trace reading...
1>ANALYZER : Total Files = 1593ANALYZER : 
1>ANALYZER : Total Processes = 26ANALYZER : 
1>ANALYZER : Total Accesses = 2647ANALYZER : 
1>ANALYZER : Total parse time = 0.007s
1>ANALYZER : Loading additional trace file e:\os\obj\amd64fre\objfre\amd64\build.ldg
1>ANALYZER : Parse time (e:\os\obj\amd64fre\objfre\amd64\build.ldg) = 0.000s
1>ANALYZER : 
1>Finalizing the trace reading...
1>ANALYZER : Total Accesses = 2647ANALYZER : 
1>ANALYZER : Total analysis time = 0.000s
1>ANALYZER : Write time (e:\os\obj\amd64fre\objfre\amd64\build.ldg) = 0.002s
1>TRACEREPORT : Parsing Baseline Files...
1>TRACEREPORT : Processing e:\os\src\.config\Desktop\build\dbb_report_baselines.xml
1>TRACEREPORT (configparser): The baseline file 'e:\os\src\.config\GameCore\build\dbb_report_baselines.xml' was not found. Ignoring it.
1>TRACEREPORT (configparser): The baseline file 'e:\os\src\.config\IOT\build\dbb_report_baselines.xml' was not found. Ignoring it.
1>TRACEREPORT : Processing e:\os\src\.config\NanoServer\build\dbb_report_baselines.xml
1>TRACEREPORT : Processing e:\os\src\.config\OSClient\build\dbb_report_baselines.xml
1>TRACEREPORT : Processing e:\os\src\.config\OneCore\build\dbb_report_baselines.xml
1>TRACEREPORT : Processing e:\os\src\.config\PCShell\build\dbb_report_baselines.xml
1>TRACEREPORT : Processing e:\os\src\.config\ServerCommon\build\dbb_report_baselines.xml
1>TRACEREPORT : Processing e:\os\src\.config\Server\build\dbb_report_baselines.xml
1>TRACEREPORT : Processing e:\os\src\.config\ShellCommonDesktopBase\build\dbb_report_baselines.xml
1>TRACEREPORT : Processing e:\os\src\.config\Xbox\build\dbb_report_baselines.xml
1>TRACEREPORT : Processing e:\os\src\.config\clientcore\build\dbb_report_baselines.xml
1>TRACEREPORT : Processing e:\os\src\.config\editions\build\dbb_report_baselines.xml
1>TRACEREPORT : Processing e:\os\src\.config\onecoreuap\build\dbb_report_baselines.xml
1>TRACEREPORT : Processing e:\os\src\.config\shellcommon\build\dbb_report_baselines.xml
1>TRACEREPORT : Processing e:\os\src\.config\xbox\build\dbb_report_baselines.xml
1>TRACEREPORT : Generating reports...
1>TRACEREPORT : Processing e:\os\cdg\amd64fre\build.cdg
1>TRACEREPORT : 
1>Finalizing the trace reading...
1>TRACEREPORT : Total Files = 115466TRACEREPORT : 
1>TRACEREPORT : Running all reports in parallel...
1>TRACEREPORT : Reports may call libraries that use the global logger, and hence the parallelism may cause output to commingle.
1>TRACEREPORT : All report-specific output will be buffered and printed later.
1>TRACEREPORT : Started running the 'badpublish-cs' report in parallel.
1>TRACEREPORT : Started running the 'buildrules-cs' report in parallel.
1>TRACEREPORT : Started running the 'chunkanalyzer-cs' report in parallel.
1>TRACEREPORT : Started running the 'crosslayer-cs' report in parallel.
1>TRACEREPORT : Started running the 'externalaccess-cs' report in parallel.
1>TRACEREPORT : Started running the 'illegalprocess' report in parallel.
1>TRACEREPORT : Started running the 'includes-cs' report in parallel.
1>TRACEREPORT : Started running the 'multiwritestrict-cs' report in parallel.
1>TRACEREPORT : Started running the 'nttree-cs' report in parallel.
1>TRACEREPORT : Started running the 'objroot-cs' report in parallel.
1>TRACEREPORT : Started running the 'ostools-cs' report in parallel.
1>TRACEREPORT : Started running the 'restricteddependency-cs' report in parallel.
1>TRACEREPORT : Started running the 'sdxwrite-cs' report in parallel.
1>TRACEREPORT : Started running the 'sourcesdep-cs' report in parallel.
1>TRACEREPORT : Started running the 'tempfiles-cs' report in parallel.
1>TRACEREPORT : Started running the 'tools-cs' report in parallel.
1>TRACEREPORT : Started running the 'unc-cs' report in parallel.
1>TRACEREPORT : Started running the 'vpacknttree-cs' report in parallel.
1>TRACEREPORT : Started running the 'vpackstrict-cs' report in parallel.
1>TRACEREPORT : Finished running the 'chunkanalyzer-cs' report in parallel:
1>(chunkanalyzer-cs) Return Code = 0 (success).
1>(chunkanalyzer-cs) Runtime = 0.028s.
1>(chunkanalyzer-cs) Errors = 0.
1>(chunkanalyzer-cs) Baselined Errors = 0.
1>(chunkanalyzer-cs) Suppressed Errors = 0.
1>(chunkanalyzer-cs) Messages = 4.
1>TRACEREPORT : Finished running the 'illegalprocess' report in parallel:
1>(illegalprocess) Return Code = 0 (success).
1>(illegalprocess) Runtime = 0.028s.
1>(illegalprocess) Errors = 0.
1>(illegalprocess) Baselined Errors = 0.
1>(illegalprocess) Suppressed Errors = 0.
1>(illegalprocess) Messages = 4.
1>TRACEREPORT : Finished running the 'ostools-cs' report in parallel:
1>(ostools-cs) Return Code = 0 (success).
1>(ostools-cs) Runtime = 0.027s.
1>(ostools-cs) Errors = 0.
1>(ostools-cs) Baselined Errors = 0.
1>(ostools-cs) Suppressed Errors = 0.
1>(ostools-cs) Messages = 4.
1>TRACEREPORT : Finished running the 'unc-cs' report in parallel:
1>(unc-cs) Return Code = 0 (success).
1>(unc-cs) Runtime = 0.027s.
1>(unc-cs) Errors = 0.
1>(unc-cs) Baselined Errors = 0.
1>(unc-cs) Suppressed Errors = 0.
1>(unc-cs) Messages = 4.
1>TRACEREPORT : Finished running the 'multiwritestrict-cs' report in parallel:
1>(multiwritestrict-cs) Return Code = 0 (success).
1>(multiwritestrict-cs) Runtime = 0.028s.
1>(multiwritestrict-cs) Errors = 0.
1>(multiwritestrict-cs) Baselined Errors = 0.
1>(multiwritestrict-cs) Suppressed Errors = 0.
1>(multiwritestrict-cs) Messages = 4.
1>TRACEREPORT : Finished running the 'sdxwrite-cs' report in parallel:
1>(sdxwrite-cs) Return Code = 0 (success).
1>(sdxwrite-cs) Runtime = 0.027s.
1>(sdxwrite-cs) Errors = 0.
1>(sdxwrite-cs) Baselined Errors = 0.
1>(sdxwrite-cs) Suppressed Errors = 0.
1>(sdxwrite-cs) Messages = 4.
1>TRACEREPORT : Finished running the 'tools-cs' report in parallel:
1>(tools-cs) Return Code = 0 (success).
1>(tools-cs) Runtime = 0.027s.
1>(tools-cs) Errors = 0.
1>(tools-cs) Baselined Errors = 0.
1>(tools-cs) Suppressed Errors = 0.
1>(tools-cs) Messages = 4.
1>TRACEREPORT : Finished running the 'badpublish-cs' report in parallel:
1>(badpublish-cs) Return Code = 0 (success).
1>(badpublish-cs) Runtime = 0.028s.
1>(badpublish-cs) Errors = 0.
1>(badpublish-cs) Baselined Errors = 0.
1>(badpublish-cs) Suppressed Errors = 0.
1>(badpublish-cs) Messages = 4.
1>TRACEREPORT : Finished running the 'restricteddependency-cs' report in parallel:
1>(restricteddependency-cs) Return Code = 0 (success).
1>(restricteddependency-cs) Runtime = 0.027s.
1>(restricteddependency-cs) Errors = 0.
1>(restricteddependency-cs) Baselined Errors = 0.
1>(restricteddependency-cs) Suppressed Errors = 0.
1>(restricteddependency-cs) Messages = 4.
1>TRACEREPORT : Finished running the 'nttree-cs' report in parallel:
1>(nttree-cs) Return Code = 0 (success).
1>(nttree-cs) Runtime = 0.028s.
1>(nttree-cs) Errors = 0.
1>(nttree-cs) Baselined Errors = 0.
1>(nttree-cs) Suppressed Errors = 0.
1>(nttree-cs) Messages = 4.
1>TRACEREPORT : Finished running the 'crosslayer-cs' report in parallel:
1>(crosslayer-cs) Return Code = 0 (success).
1>(crosslayer-cs) Runtime = 0.032s.
1>(crosslayer-cs) Errors = 0.
1>(crosslayer-cs) Baselined Errors = 0.
1>(crosslayer-cs) Suppressed Errors = 0.
1>(crosslayer-cs) Messages = 4.
1>TRACEREPORT : Finished running the 'vpacknttree-cs' report in parallel:
1>(vpacknttree-cs) Return Code = 0 (success).
1>(vpacknttree-cs) Runtime = 0.032s.
1>(vpacknttree-cs) Errors = 0.
1>(vpacknttree-cs) Baselined Errors = 0.
1>(vpacknttree-cs) Suppressed Errors = 0.
1>(vpacknttree-cs) Messages = 2.
1>TRACEREPORT : Finished running the 'objroot-cs' report in parallel:
1>(objroot-cs) Return Code = 0 (success).
1>(objroot-cs) Runtime = 0.035s.
1>(objroot-cs) Errors = 0.
1>(objroot-cs) Baselined Errors = 0.
1>(objroot-cs) Suppressed Errors = 0.
1>(objroot-cs) Messages = 4.
1>TRACEREPORT : Finished ruinfo: Microsoft.Internal.Trace.Reporting.Reports.TempFilesReport[0]
1>      Total 8 Temp Files Write records
1>info: Microsoft.Internal.Trace.Reporting.Reports.ExternalAccessReport.ExternalAccessReportExclusions[0]
1>      Loaded exclusions from 'e:\os\tools\Analyzer\amd64\DefaultConfigFiles\ExternalAccessReportExclusions.json'.
1>nning the 'vpackstrict-cs' report in parallel:
1>(vpackstrict-cs) Return Code = 0 (success).
1>(vpackstrict-cs) Runtime = 0.035s.
1>(vpackstrict-cs) Errors = 0.
1>(vpackstrict-cs) Baselined Errors = 0.
1>(vpackstrict-cs) Suppressed Errors = 0.
1>(vpackstrict-cs) Messages = 11.
1>TRACEREPORT : Finished running the 'includes-cs' report in parallel:
1>(includes-cs) Return Code = 0 (success).
1>(includes-cs) Runtime = 0.037s.
1>(includes-cs) Errors = 0.
1>(includes-cs) Baselined Errors = 0.
1>(includes-cs) Suppressed Errors = 0.
1>(includes-cs) Messages = 4.
1>TRACEREPORT : Finished running the 'tempfiles-cs' report in parallel:
1>(tempfiles-cs) Return Code = 0 (success).
1>(tempfiles-cs) Runtime = 0.039s.
1>(tempfiles-cs) Errors = 0.
1>(tempfiles-cs) Baselined Errors = 0.
1>(tempfiles-cs) Suppressed Errors = 0.
1>(tempfiles-cs) Messages = 4.
1>TRACEREPORT : Finished running the 'sourcesdep-cs' report in parallel:
1>(sourcesdep-cs) Return Code = 0 (success).
1>(sourcesdep-cs) Runtime = 0.072s.
1>(sourcesdep-cs) Errors = 0.
1>(sourcesdep-cs) Baselined Errors = 0.
1>(sourcesdep-cs) Suppressed Errors = 0.
1>(sourcesdep-cs) Messages = 5.
1>TRACEREPORT : Finished running the 'externalaccess-cs' report in parallel:
1>(externalaccess-cs) Return Code = 0 (success).
1>(externalaccess-cs) Runtime = 0.097s.
1>(externalaccess-cs) Errors = 0.
1>(externalaccess-cs) Baselined Errors = 0.
1>(externalaccess-cs) Suppressed Errors = 0.
1>(externalaccess-cs) Messages = 4.
1>TRACEREPORT : Finished running the 'buildrules-cs' report in parallel:
1>(buildrules-cs) Return Code = 0 (success).
1>(buildrules-cs) Runtime = 0.105s.
1>(buildrules-cs) Errors = 0.
1>(buildrules-cs) Baselined Errors = 0.
1>(buildrules-cs) Suppressed Errors = 0.
1>(buildrules-cs) Messages = 3.
1>TRACEREPORT : All reports have finished running in parallel. Printing buffered output in-order...
1>illegalprocess : Report will run if all of the following constraints are met:
1>illegalprocess :  - Constraint 'Trace was generated in a fre environment' is 'True' (EVALUATED TO TRUE)
1>illegalprocess :  - Constraint 'Trace was generated in a BuildXL environment' is 'False' (EVALUATED TO FALSE)
1>illegalprocess : This report will run. ('env=IC; arch=AMD64; type=Fre; isBuildXL=False')
1>chunkanalyzer : Report will run if all of the following constraints are met:
1>chunkanalyzer :  - Constraint 'Trace was generated during Timebuild' is 'True' (EVALUATED TO FALSE)
1>chunkanalyzer :  - Constraint 'Trace was generated in a fre environment' is 'True' (EVALUATED TO TRUE)
1>chunkanalyzer : This report will be skipped. Use 'forcerun' if you want it to run anyways. ('env=IC; arch=AMD64; type=Fre; isBuildXL=False')
1>unc : Report will run if all of the following constraints are met:
1>unc :  - Constraint 'Trace was generated in a fre environment' is 'True' (EVALUATED TO TRUE)
1>unc :  - Constraint 'Trace was generated in a BuildXL environment' is 'False' (EVALUATED TO FALSE)
1>unc : This report will run. ('env=IC; arch=AMD64; type=Fre; isBuildXL=False')
1>ostools : Report will run if all of the following constraints are met:
1>ostools :  - Constraint 'Trace was generated in a fre environment' is 'True' (EVALUATED TO TRUE)
1>ostools :  - Constraint 'Trace was generated in a BuildXL environment' is 'False' (EVALUATED TO FALSE)
1>ostools : This report will run. ('env=IC; arch=AMD64; type=Fre; isBuildXL=False')
1>multiwritestrict : Report will run if all of the following constraints are met:
1>multiwritestrict :  - Constraint 'Trace was generated in a fre environment' is 'True' (EVALUATED TO TRUE)
1>multiwritestrict :  - Constraint 'Trace was generated in a BuildXL environment' is 'False' (EVALUATED TO FALSE)
1>multiwritestrict : This report will run. ('env=IC; arch=AMD64; type=Fre; isBuildXL=False')
1>sdxwrite : Report will run if all of the following constraints are met:
1>sdxwrite :  - Constraint 'Trace was generated in a fre environment' is 'True' (EVALUATED TO TRUE)
1>sdxwrite :  - Constraint 'Trace was generated in a BuildXL environment' is 'False' (EVALUATED TO FALSE)
1>sdxwrite : This report will run. ('env=IC; arch=AMD64; type=Fre; isBuildXL=False')
1>restricteddependency : Report will run if all of the following constraints are met:
1>restricteddependency :  - Constraint 'Trace was generated during Timebuild' is 'True' (EVALUATED TO FALSE)
1>restricteddependency :  - Constraint 'Trace was generated in a fre environment' is 'True' (EVALUATED TO TRUE)
1>restricteddependency : This report will be skipped. Use 'forcerun' if you want it to run anyways. ('env=IC; arch=AMD64; type=Fre; isBuildXL=False')
1>badpublish : Report will run if all of the following constraints are met:
1>badpublish :  - Constraint 'Trace was generated in a fre environment' is 'True' (EVALUATED TO TRUE)
1>badpublish :  - Constraint 'Trace was generated in a BuildXL environment' is 'False' (EVALUATED TO FALSE)
1>badpublish : This report will run. ('env=IC; arch=AMD64; type=Fre; isBuildXL=False')
1>tools : Report will run if all of the following constraints are met:
1>tools :  - Constraint 'Trace was generated in a fre environment' is 'True' (EVALUATED TO TRUE)
1>tools :  - Constraint 'Trace was generated in a BuildXL environment' is 'False' (EVALUATED TO FALSE)
1>tools : This report will run. ('env=IC; arch=AMD64; type=Fre; isBuildXL=False')
1>nttree : Report will run if all of the following constraints are met:
1>nttree :  - Constraint 'Trace was generated in a fre environment' is 'True' (EVALUATED TO TRUE)
1>nttree :  - Constraint 'Trace was generated in a BuildXL environment' is 'False' (EVALUATED TO FALSE)
1>nttree : This report will run. ('env=IC; arch=AMD64; type=Fre; isBuildXL=False')
1>crosslayer : Report will run if all of the following constraints are met:
1>crosslayer :  - Constraint 'Trace was generated in a fre environment' is 'True' (EVALUATED TO TRUE)
1>crosslayer :  - Constraint 'Trace was generated in a BuildXL environment' is 'False' (EVALUATED TO FALSE)
1>crosslayer : This report will run. ('env=IC; arch=AMD64; type=Fre; isBuildXL=False')
1>vpacknttree : No run constraints were specified for this report.
1>vpacknttree : This report will run. ('env=IC; arch=AMD64; type=Fre; isBuildXL=False')
1>objroot : Report will run if all of the following constraints are met:
1>objroot :  - Constraint 'Trace was generated in a fre environment' is 'True' (EVALUATED TO TRUE)
1>objroot :  - Constraint 'Trace was generated in a BuildXL environment' is 'False' (EVALUATED TO FALSE)
1>objroot : This report will run. ('env=IC; arch=AMD64; type=Fre; isBuildXL=False')
1>vpackstrict : No run constraints were specified for this report.
1>vpackstrict : This report will run. ('env=IC; arch=AMD64; type=Fre; isBuildXL=False')
1>vpackstrict : --------------------------------------------------------------
1>vpackstrict :         Total number of 'vpack pull' calls found: 0
1>vpackstrict : ...Calls from ValidateVpackPackage.ps1 (ignored): 0
1>vpackstrict : ..........Number of calls checked in this report: 0
1>vpackstrict : --------------------------------------------------------------
1>vpackstrict :        Number of calls without /StrictVersioning: 0
1>vpackstrict : .............................baselined (ignored): 0
1>vpackstrict : ..........................non-baselined (errors): 0
1>vpackstrict : --------------------------------------------------------------
1>includes : Report will run if all of the following constraints are met:
1>includes :  - Constraint 'Trace was generated in a fre environment' is 'True' (EVALUATED TO TRUE)
1>includes :  - Constraint 'Trace was generated in a BuildXL environment' is 'False' (EVALUATED TO FALSE)
1>includes : This report will run. ('env=IC; arch=AMD64; type=Fre; isBuildXL=False')
1>tempfiles : Report will run if all of the following constraints are met:
1>tempfiles :  - Constraint 'Trace was generated in a fre environment' is 'True' (EVALUATED TO TRUE)
1>tempfiles :  - Constraint 'Trace was generated in a BuildXL environment' is 'False' (EVALUATED TO FALSE)
1>tempfiles : This report will run. ('env=IC; arch=AMD64; type=Fre; isBuildXL=False')
1>sourcesdep : No run constraints were specified for this report.
1>sourcesdep : This report will run. ('env=IC; arch=AMD64; type=Fre; isBuildXL=False')
1>sourcesdep : First Loop: 11 ms
1>sourcesdep : Time taken for SuiteSparse circular dependency checks in sources.dep data = 6 ms
1>sourcesdep : Second Loop: 12 ms
1>externalaccess : Report will run if all of the following constraints are met:
1>externalaccess :  - Constraint 'Trace was generated in a fre environment' is 'True' (EVALUATED TO TRUE)
1>externalaccess :  - Constraint 'Trace was generated in a BuildXL environment' is 'False' (EVALUATED TO FALSE)
1>externalaccess : This report will run. ('env=IC; arch=AMD64; type=Fre; isBuildXL=False')
1>buildrules : Report will run if all of the following constraints are met:
1>buildrules :  - Constraint 'Trace was generated in a fre environment' is 'True' (EVALUATED TO TRUE)
1>buildrules : This report will run. ('env=IC; arch=AMD64; type=Fre; isBuildXL=False')
1>TRACEREPORT : All buffered output has been printed.
1>TRACEREPORT : Time taken to run all reports in parallel = 0.105s
1>ANALYZER : TraceReport time = 3.566s
1>ANALYZER (_tmain): Analyzer has completed and is exiting with return code '0' indicating success.
1>ANALYZER : PageFaultCount:     179484
1>ANALYZER : PeakWorkingSetSize: 638705664
1>ANALYZER : PeakPagefileUsage:  603860992
1>ANALYZER : ProcessCycleTime:   11280529419
1>ANALYZER : KernelTime:         0.453
1>ANALYZER : UserTime:           3.859
Build layers enabled: [ShellCommon,ClientCore,OSClient,DesktopEditions,OnecoreUAP,GameCore]
Number of excluded directories, not in layer set: 0


    1 directory scanned
    10 files compiled
    1 library built
    1 file binplaced
