#pragma once
#include "aimxrpcclient.h"

#ifdef AIMXCLIENT_EXPORTS
#define AIMXCLIENT_API __declspec(dllexport)
#elif defined(AIMXCLIENT_IMPORTS)
#define AIMXCLIENT_API __declspec(dllimport)
#else
#define AIMXCLIENT_API
#endif

#ifdef __cplusplus
extern "C" {
#endif

AIMXCLIENT_API HRESULT AimxConnect(_Out_ GUID* pContextId);
AIMXCLIENT_API HRESULT AimxClose(_In_ GUID contextId);

AIMXCLIENT_API
HRESULT AimxProcessPrompt(
    _In_ GUID contextId,
    _In_ LPCWSTR InputPrompt,
    _Out_ LPWSTR* Response);

AIMXCLIENT_API
HRESULT AimxPollConversationMessages(
    _In_ GUID contextId,
    _Out_ LPWSTR* messages);

AIMXCLIENT_API
HRESULT AimxGetConversationStatus(
    _In_ GUID contextId,
    _Out_ LPWSTR* statusJson);

AIMXCLIENT_API
HRESULT AimxStartConversation(
    _In_ GUID contextId,
    _In_ LPCWSTR query,
    _In_ LONG executionMode);

AIMXCLIENT_API
HRESULT AimxGetLlmStatus(
    _In_ GUID contextId,
    _Out_ LPWSTR* statusJson);

AIMXCLIENT_API
HRESULT AimxGetMcpServerInfo(
    _In_ GUID contextId,
    _Out_ LPWSTR* serverInfoJson);

#ifdef __cplusplus
}
#endif
