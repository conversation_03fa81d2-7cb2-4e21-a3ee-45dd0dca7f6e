/*++

Copyright (c) Microsoft Corporation. All rights reserved.

Module Name:

    AdMcpSvr.cpp

Abstract:

    Implementation of Active Directory in-process MCP server.
    Provides comprehensive AD query tools using native Win32 LDAP APIs
    while maintaining PowerShell cmdlet parameter compatibility.

Author:

    <PERSON><PERSON><PERSON> (rizhang) 2025-7-16

--*/

#include "AdMcpSvr.h"
#include "StringUtils.h"
#include "AimxConstants.h"
#include "McpSvrMgr.h"
#include "../aimxsrv/inc/wpp.h"

// WPP tracing
#include "AdMcpSvr.cpp.tmh"

AdMcpSvr::AdMcpSvr()
    : InProcessMcpServerBase(
        L"AdMcpSvr",
        L"Active Directory MCP Server providing comprehensive AD query tools using native Win32 LDAP APIs with PowerShell cmdlet compatibility",
        L"1.0.0"
    )
    , m_pLdapConnection(nullptr)
{
    TraceInfo(AdMcpSvr, L"AdMcpSvr constructor called");
}

AdMcpSvr::~AdMcpSvr()
{
    TraceInfo(AdMcpSvr, L"AdMcpSvr destructor called");
    CleanupLdapConnection();
}

HRESULT AdMcpSvr::OnInitialize()
/*++

Routine Description:
    Initialize the server by registering all AD tools.

Return Value:
    S_OK on success, error HRESULT on failure

--*/
{
    TraceInfo(AdMcpSvr, L"AdMcpSvr::OnInitialize - Registering AD tools");

    HRESULT hr = S_OK;

    // Register Get-ADUser tool
    hr = REGISTER_MCP_TOOL(Get-ADUser, 
        "Gets one or more Active Directory users. Supports Identity, Filter, LDAPFilter, Properties, SearchBase, SearchScope, Server, ResultPageSize, and ResultSetSize parameters for comprehensive user queries.",
        GetADUserTool);
    if (FAILED(hr))
    {
        TraceErr(AdMcpSvr, L"Failed to register Get-ADUser tool: 0x%08x", hr);
        return hr;
    }

    // Register Get-ADGroup tool  
    hr = REGISTER_MCP_TOOL(Get-ADGroup,
        "Gets one or more Active Directory groups. Supports Identity, Filter, LDAPFilter, Properties, SearchBase, SearchScope, Server, ResultPageSize, ResultSetSize, and ShowMemberTimeToLive parameters for comprehensive group queries.",
        GetADGroupTool);
    if (FAILED(hr))
    {
        TraceErr(AdMcpSvr, L"Failed to register Get-ADGroup tool: 0x%08x", hr);
        return hr;
    }

    // Register Get-ADComputer tool
    hr = REGISTER_MCP_TOOL(Get-ADComputer,
        "Gets one or more Active Directory computers. Supports Identity, Filter, LDAPFilter, Properties, SearchBase, SearchScope, Server, ResultPageSize, and ResultSetSize parameters for comprehensive computer queries.",
        GetADComputerTool);
    if (FAILED(hr))
    {
        TraceErr(AdMcpSvr, L"Failed to register Get-ADComputer tool: 0x%08x", hr);
        return hr;
    }

    // Register Get-ADDomain tool
    hr = REGISTER_MCP_TOOL(Get-ADDomain,
        "Gets an Active Directory domain. Supports Identity, Current, and Server parameters to retrieve comprehensive domain information including FSMO roles, containers, and domain properties.",
        GetADDomainTool);
    if (FAILED(hr))
    {
        TraceErr(AdMcpSvr, L"Failed to register Get-ADDomain tool: 0x%08x", hr);
        return hr;
    }

    // Register Get-ADForest tool
    hr = REGISTER_MCP_TOOL(Get-ADForest,
        "Gets an Active Directory forest. Supports Identity, Current, and Server parameters to retrieve comprehensive forest information including domains, sites, global catalogs, and FSMO roles.",
        GetADForestTool);
    if (FAILED(hr))
    {
        TraceErr(AdMcpSvr, L"Failed to register Get-ADForest tool: 0x%08x", hr);
        return hr;
    }

    TraceInfo(AdMcpSvr, L"AdMcpSvr::OnInitialize - Successfully registered all AD tools");
    return S_OK;
}

void AdMcpSvr::OnUninitialize()
/*++

Routine Description:
    Cleanup server resources including LDAP connections.

--*/
{
    TraceInfo(AdMcpSvr, L"AdMcpSvr::OnUninitialize - Cleaning up resources");
    CleanupLdapConnection();
}

HRESULT AdMcpSvr::InitializeLdapConnection(
    _In_opt_ const std::wstring& serverName
    )
/*++

Routine Description:
    Initialize LDAP connection to domain controller.

Arguments:
    serverName - Optional server name to connect to. If empty, discovers DC automatically.

Return Value:
    S_OK on success, error HRESULT on failure

--*/
{
    TraceInfo(AdMcpSvr, L"AdMcpSvr::InitializeLdapConnection - Initializing LDAP connection");

    // Cleanup existing connection if any
    CleanupLdapConnection();

    HRESULT hr = S_OK;
    std::wstring dcName;

    // Discover domain controller if server name not provided
    if (serverName.empty())
    {
        TraceInfo(AdMcpSvr, L"Discovering domain controller...");
        hr = DiscoverDomainController(L"", dcName);
        if (FAILED(hr))
        {
            TraceErr(AdMcpSvr, L"Failed to discover domain controller: 0x%08x", hr);
            return hr;
        }
        TraceInfo(AdMcpSvr, L"Discovered domain controller: %ws", dcName.c_str());
    }
    else
    {
        dcName = serverName;
        TraceInfo(AdMcpSvr, L"Using specified server: %ws", dcName.c_str());
    }

    // Initialize LDAP connection
    TraceInfo(AdMcpSvr, L"Initializing LDAP connection to: %ws", dcName.c_str());
    m_pLdapConnection = ldap_initW(const_cast<PWCHAR>(dcName.c_str()), LDAP_PORT);
    if (m_pLdapConnection == nullptr)
    {
        ULONG ldapError = LdapGetLastError();
        TraceErr(AdMcpSvr, L"ldap_initW failed for server %ws: 0x%08x", dcName.c_str(), ldapError);
        return HRESULT_FROM_WIN32(ldapError);
    }
    TraceInfo(AdMcpSvr, L"LDAP connection initialized successfully");

    // Set LDAP options
    ULONG version = LDAP_VERSION3;
    ULONG result = ldap_set_optionW(m_pLdapConnection, LDAP_OPT_PROTOCOL_VERSION, &version);
    if (result != LDAP_SUCCESS)
    {
        TraceErr(AdMcpSvr, L"ldap_set_option for version failed: 0x%08x", result);
        CleanupLdapConnection();
        return HRESULT_FROM_WIN32(result);
    }

    // Bind to LDAP server using current credentials
    TraceInfo(AdMcpSvr, L"Binding to LDAP server with current credentials...");
    result = ldap_bind_sW(m_pLdapConnection, nullptr, nullptr, LDAP_AUTH_NEGOTIATE);
    if (result != LDAP_SUCCESS)
    {
        TraceErr(AdMcpSvr, L"ldap_bind_s failed for server %ws: 0x%08x", dcName.c_str(), result);
        CleanupLdapConnection();
        return HRESULT_FROM_WIN32(result);
    }
    TraceInfo(AdMcpSvr, L"LDAP bind successful");

    // Get default naming context
    LDAPMessage* pRootDSE = nullptr;
    PWCHAR rootDSEAttrs[] = { L"defaultNamingContext", nullptr };
    
    result = ldap_search_sW(m_pLdapConnection, L"", LDAP_SCOPE_BASE, L"(objectClass=*)", rootDSEAttrs, 0, &pRootDSE);
    if (result == LDAP_SUCCESS && pRootDSE != nullptr)
    {
        LDAPMessage* pEntry = ldap_first_entry(m_pLdapConnection, pRootDSE);
        if (pEntry != nullptr)
        {
            PWCHAR* ppValues = ldap_get_valuesW(m_pLdapConnection, pEntry, L"defaultNamingContext");
            if (ppValues != nullptr && ppValues[0] != nullptr)
            {
                m_defaultNamingContext = ppValues[0];
                ldap_value_freeW(ppValues);
            }
        }
        ldap_msgfree(pRootDSE);
    }

    m_domainController = dcName;
    TraceInfo(AdMcpSvr, L"AdMcpSvr::InitializeLdapConnection - Successfully connected to DC: %ws", dcName.c_str());
    
    return S_OK;
}

void AdMcpSvr::CleanupLdapConnection()
/*++

Routine Description:
    Cleanup LDAP connection and reset member variables.

--*/
{
    if (m_pLdapConnection != nullptr)
    {
        ldap_unbind(m_pLdapConnection);
        m_pLdapConnection = nullptr;
    }
    
    m_domainController.clear();
    m_defaultNamingContext.clear();
}

HRESULT AdMcpSvr::DiscoverDomainController(
    _In_opt_ const std::wstring& domainName,
    _Out_ std::wstring& dcName
    )
/*++

Routine Description:
    Discover a domain controller for the specified domain.

Arguments:
    domainName - Domain name to discover DC for. If empty, uses current domain.
    dcName - Receives the discovered DC name.

Return Value:
    S_OK on success, error HRESULT on failure

--*/
{
    PDOMAIN_CONTROLLER_INFOW pDcInfo = nullptr;
    
    DWORD result = DsGetDcNameW(
        nullptr,                                    // Computer name (local)
        domainName.empty() ? nullptr : domainName.c_str(),  // Domain name
        nullptr,                                    // Domain GUID
        nullptr,                                    // Site name
        DS_DIRECTORY_SERVICE_REQUIRED,             // Flags
        &pDcInfo
    );

    if (result != ERROR_SUCCESS)
    {
        TraceErr(AdMcpSvr, L"DsGetDcNameW failed: 0x%08x", result);
        return HRESULT_FROM_WIN32(result);
    }

    if (pDcInfo != nullptr && pDcInfo->DomainControllerName != nullptr)
    {
        // Remove leading \\ if present
        dcName = pDcInfo->DomainControllerName;
        if (dcName.length() > 2 && dcName.substr(0, 2) == L"\\\\")
        {
            dcName = dcName.substr(2);
        }
        
        NetApiBufferFree(pDcInfo);
        return S_OK;
    }

    if (pDcInfo != nullptr)
    {
        NetApiBufferFree(pDcInfo);
    }
    
    return E_FAIL;
}

// Export functions for registration
extern "C" __declspec(dllexport) HRESULT RegisterAdMcpSvr()
{
    TraceInfo(AdMcpSvr, L"RegisterAdMcpSvr called");
    auto server = std::make_shared<AdMcpSvr>();
    return McpSvrMgr::RegisterInProcessServer(server);
}

extern "C" __declspec(dllexport) HRESULT UnregisterAdMcpSvr()
{
    TraceInfo(AdMcpSvr, L"UnregisterAdMcpSvr called");
    return McpSvrMgr::UnregisterInProcessServer(L"AdMcpSvr");
}











HRESULT AdMcpSvr::ExecuteLdapSearch(
    _In_ const std::wstring& searchBase,
    _In_ const std::wstring& filter,
    _In_ const std::vector<std::wstring>& attributes,
    _In_ ULONG searchScope,
    _Out_ std::vector<nlohmann::json>& results
    )
/*++

Routine Description:
    Execute LDAP search with specified parameters.

Arguments:
    searchBase - LDAP search base DN
    filter - LDAP filter string
    attributes - List of attributes to retrieve
    searchScope - LDAP search scope
    results - Receives search results as JSON objects

Return Value:
    S_OK on success, error HRESULT on failure

--*/
{
    if (m_pLdapConnection == nullptr)
    {
        return E_INVALIDARG;
    }

    results.clear();

    // Convert attributes to PWCHAR array
    std::vector<PWCHAR> attrArray;
    for (const auto& attr : attributes)
    {
        attrArray.push_back(const_cast<PWCHAR>(attr.c_str()));
    }
    attrArray.push_back(nullptr);

    LDAPMessage* pSearchResult = nullptr;
    ULONG ldapResult = ldap_search_sW(
        m_pLdapConnection,
        const_cast<PWCHAR>(searchBase.c_str()),
        searchScope,
        const_cast<PWCHAR>(filter.c_str()),
        attrArray.data(),
        0,
        &pSearchResult
    );

    if (ldapResult != LDAP_SUCCESS)
    {
        TraceErr(AdMcpSvr, L"ldap_search_s failed: 0x%08x", ldapResult);
        return HRESULT_FROM_WIN32(ldapResult);
    }

    if (pSearchResult == nullptr)
    {
        return S_OK; // No results
    }

    // Process search results
    LDAPMessage* pEntry = ldap_first_entry(m_pLdapConnection, pSearchResult);
    while (pEntry != nullptr)
    {
        nlohmann::json entryJson;
        HRESULT hr = ConvertLdapEntryToJson(m_pLdapConnection, pEntry, attributes, entryJson);
        if (SUCCEEDED(hr))
        {
            results.push_back(entryJson);
        }

        pEntry = ldap_next_entry(m_pLdapConnection, pEntry);
    }

    ldap_msgfree(pSearchResult);
    return S_OK;
}

HRESULT AdMcpSvr::ConvertLdapEntryToJson(
    _In_ LDAP* pLdap,
    _In_ LDAPMessage* pEntry,
    _In_ const std::vector<std::wstring>& requestedAttributes,
    _Out_ nlohmann::json& jsonObject
    )
/*++

Routine Description:
    Convert LDAP search result entry to JSON object.

Arguments:
    pLdap - LDAP connection handle
    pEntry - LDAP entry to convert
    requestedAttributes - List of requested attributes
    jsonObject - Receives JSON representation of the entry

Return Value:
    S_OK on success, error HRESULT on failure

--*/
{
    jsonObject = nlohmann::json::object();

    for (const auto& attrName : requestedAttributes)
    {
        // Handle binary attributes specially
        if (attrName == L"objectGUID" || attrName == L"objectSid")
        {
            struct berval** ppBerValues = ldap_get_values_lenW(pLdap, pEntry, const_cast<PWCHAR>(attrName.c_str()));
            if (ppBerValues != nullptr && ppBerValues[0] != nullptr)
            {
                std::string convertedValue;
                if (attrName == L"objectGUID")
                {
                    convertedValue = ConvertGuidToString(ppBerValues[0]);
                }
                else if (attrName == L"objectSid")
                {
                    convertedValue = ConvertSidToString(ppBerValues[0]);
                }

                if (!convertedValue.empty())
                {
                    jsonObject[WideToUtf8(attrName)] = convertedValue;
                }

                ldap_value_free_len(ppBerValues);
            }
        }
        else
        {
            // Handle regular string attributes
            PWCHAR* ppValues = ldap_get_valuesW(pLdap, pEntry, const_cast<PWCHAR>(attrName.c_str()));
            if (ppValues != nullptr)
            {
                ULONG valueCount = ldap_count_valuesW(ppValues);
                if (valueCount == 1)
                {
                    // Single value
                    jsonObject[WideToUtf8(attrName)] = WideToUtf8(ppValues[0]);
                }
                else if (valueCount > 1)
                {
                    // Multiple values - create array
                    nlohmann::json valueArray = nlohmann::json::array();
                    for (ULONG i = 0; i < valueCount; i++)
                    {
                        valueArray.push_back(WideToUtf8(ppValues[i]));
                    }
                    jsonObject[WideToUtf8(attrName)] = valueArray;
                }

                ldap_value_freeW(ppValues);
            }
        }
    }

    return S_OK;
}

// ParseFilterParameter implementation moved to PowerShellFilterParser.cpp

HRESULT AdMcpSvr::ParsePropertiesParameter(
    _In_ const nlohmann::json& propertiesParam,
    _Out_ std::vector<std::wstring>& attributes
    )
/*++

Routine Description:
    Parse Properties parameter to attribute list.

Arguments:
    propertiesParam - Properties parameter (string or array)
    attributes - Receives list of attributes to retrieve

Return Value:
    S_OK on success, error HRESULT on failure

--*/
{
    attributes.clear();

    if (propertiesParam.is_string())
    {
        std::string propStr = propertiesParam.get<std::string>();
        if (propStr == "*")
        {
            // Request all attributes - use a comprehensive list
            attributes = {
                L"*", L"distinguishedName", L"objectGUID", L"objectSid", L"sAMAccountName",
                L"name", L"displayName", L"description", L"whenCreated", L"whenChanged"
            };
        }
        else
        {
            attributes.push_back(Utf8ToWide(propStr));
        }
    }
    else if (propertiesParam.is_array())
    {
        for (const auto& prop : propertiesParam)
        {
            if (prop.is_string())
            {
                attributes.push_back(Utf8ToWide(prop.get<std::string>()));
            }
        }
    }
    else
    {
        return E_INVALIDARG;
    }

    return S_OK;
}

HRESULT AdMcpSvr::ParseSearchScopeParameter(
    _In_ const nlohmann::json& scopeParam,
    _Out_ ULONG& searchScope
    )
/*++

Routine Description:
    Parse SearchScope parameter.

Arguments:
    scopeParam - SearchScope parameter
    searchScope - Receives LDAP search scope value

Return Value:
    S_OK on success, error HRESULT on failure

--*/
{
    if (!scopeParam.is_string())
    {
        return E_INVALIDARG;
    }

    std::string scopeStr = scopeParam.get<std::string>();

    if (scopeStr == "Base" || scopeStr == "0")
    {
        searchScope = LDAP_SCOPE_BASE;
    }
    else if (scopeStr == "OneLevel" || scopeStr == "1")
    {
        searchScope = LDAP_SCOPE_ONELEVEL;
    }
    else if (scopeStr == "Subtree" || scopeStr == "2")
    {
        searchScope = LDAP_SCOPE_SUBTREE;
    }
    else
    {
        return E_INVALIDARG;
    }

    return S_OK;
}



HRESULT AdMcpSvr::CreateLdapErrorResponse(
    _In_ ULONG ldapError,
    _In_ const std::wstring& operation,
    _Out_ nlohmann::json& errorResponse
    )
/*++

Routine Description:
    Create error response for LDAP errors.

Arguments:
    ldapError - LDAP error code
    operation - Operation that failed
    errorResponse - Receives error response JSON

Return Value:
    E_FAIL

--*/
{
    std::wstring errorMsg = operation + L" failed with LDAP error: 0x" +
                           std::to_wstring(ldapError);

    errorResponse = CreateErrorResponse(errorMsg, L"ldap_error");
    errorResponse["ldap_error_code"] = ldapError;

    TraceErr(AdMcpSvr, L"LDAP Error: %ws (0x%08x)", errorMsg.c_str(), ldapError);

    return E_FAIL;
}

HRESULT AdMcpSvr::CreateHResultErrorResponse(
    _In_ HRESULT hr,
    _In_ const std::wstring& operation,
    _Out_ nlohmann::json& errorResponse
    )
/*++

Routine Description:
    Create error response for HRESULT errors.

Arguments:
    hr - HRESULT error code
    operation - Operation that failed
    errorResponse - Receives error response JSON

Return Value:
    E_FAIL

--*/
{
    std::wstring errorMsg = operation + L" failed with HRESULT: 0x" +
                           std::to_wstring(static_cast<ULONG>(hr));

    errorResponse = CreateErrorResponse(errorMsg, L"hresult_error");
    errorResponse["hresult_code"] = static_cast<int>(hr);

    TraceErr(AdMcpSvr, L"HRESULT Error: %ws (0x%08x)", errorMsg.c_str(), hr);

    return E_FAIL;
}

std::string AdMcpSvr::ConvertGuidToString(_In_ const berval* pBerVal)
/*++

Routine Description:
    Convert a GUID from LDAP berval structure to string representation.

Arguments:
    pBerVal - Pointer to berval structure containing the GUID data

Return Value:
    String representation of the GUID, empty string on failure

--*/
{
    if (pBerVal == nullptr || pBerVal->bv_val == nullptr || pBerVal->bv_len != sizeof(GUID))
    {
        TraceErr(AdMcpSvr, L"Invalid berval for GUID conversion");
        return std::string();
    }

    const GUID* pGuid = reinterpret_cast<const GUID*>(pBerVal->bv_val);
    std::wstring guidWideStr = GuidToString(*pGuid);
    return WideToUtf8(guidWideStr);
}

std::string AdMcpSvr::ConvertSidToString(_In_ const berval* pBerVal)
/*++

Routine Description:
    Convert a SID from LDAP berval structure to string representation.

Arguments:
    pBerVal - Pointer to berval structure containing the SID data

Return Value:
    String representation of the SID, empty string on failure

--*/
{
    if (pBerVal == nullptr || pBerVal->bv_val == nullptr || pBerVal->bv_len == 0)
    {
        TraceErr(AdMcpSvr, L"Invalid berval for SID conversion");
        return std::string();
    }

    PSID pSid = reinterpret_cast<PSID>(pBerVal->bv_val);
    
    // Validate SID structure
    if (!IsValidSid(pSid))
    {
        TraceErr(AdMcpSvr, L"Invalid SID structure");
        return std::string();
    }

    // Convert SID to string using Win32 API
    LPWSTR sidString = nullptr;
    if (!ConvertSidToStringSidW(pSid, &sidString))
    {
        DWORD error = GetLastError();
        TraceErr(AdMcpSvr, L"ConvertSidToStringSidW failed: 0x%08x", error);
        return std::string();
    }

    // Convert to UTF-8 and clean up
    std::string result = WideToUtf8(sidString);
    LocalFree(sidString);
    
    return result;
}

// Register the server factory
REGISTER_INPROCESS_MCP_SERVER(AdMcpSvr)
