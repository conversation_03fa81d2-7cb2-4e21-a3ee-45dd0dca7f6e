/*++

Copyright (c) Microsoft Corporation. All rights reserved.

Module Name:
    Planner.h

Abstract:
    This module defines the AIMX Planner class that creates execution plans for different types of requests.
    The Planner analyzes queries and generates detailed workflow plans that can be executed
    by the Orchestrator component.

Author:

    <PERSON> (SNAKE FIGHTER) (lindakup) 06/03/2025

--*/

#pragma once

#include <windows.h>
#include <string>
#include <memory>
#include <vector>
#include "AimxCommon.h"
#include "LLMInfer.h"
#include "McpSvrMgr.h"

class Planner
{
public:
    // Static methods for planning operations
    static HRESULT StartPlanningAsync(
        _In_ const GUID& operationId,
        _In_ const std::wstring& query,
        _In_ AIMX_EXECUTION_MODE executionMode
        );

    static HRESULT CreateSimplePlan(
        _In_ const GUID& operationId,
        _In_ const std::wstring& command
        );

    static HRESULT CancelPlanning(
        _In_ const GUID& operationId
        );


private:
    // Private helper methods
    static DWORD WINAPI PlanningWorkerThread(
        _In_ LPVOID lpParameter
        );

    static HRESULT AnalyzeQuery(
        _In_ const GUID& operationId,
        _In_ const std::wstring& query,
        _Out_ nlohmann::json& planningSteps
        );

    static HRESULT GenerateExecutionPlan(
        _In_ const std::wstring& query,
        _In_ const nlohmann::json& planningSteps,
        _Out_ nlohmann::json& executionPlan
        );

    static HRESULT ValidateExecutionPlan(
        _In_ const nlohmann::json& executionPlan
        );

     static HRESULT CreateFallbackPlan(
        _In_ const std::wstring& query,
        _Out_ nlohmann::json& planningSteps
        );

    static HRESULT ConvertAnalysisToExecutionPlan(
        _In_ const LLM_ANALYSIS_RESULT& analysisResult,
        _In_ const std::vector<MCP_SERVER_INFO>& enabledServers,
        _Out_ nlohmann::json& planningSteps,
        _In_opt_ std::shared_ptr<ConversationSession> conversationSession = nullptr
        );
};
