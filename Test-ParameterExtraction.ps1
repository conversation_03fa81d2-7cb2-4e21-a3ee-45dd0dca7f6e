<#
.SYNOPSIS
    Test script to verify parameter extraction behavior
    
.DESCRIPTION
    This script tests how parameters should be extracted from user queries
    without making up values that weren't specified.
    
.EXAMPLE
    .\Test-ParameterExtraction.ps1
#>

[CmdletBinding()]
param()

function Test-ParameterExtraction {
    param(
        [string]$UserQuery,
        [hashtable]$ExpectedParameters,
        [string]$Description
    )
    
    Write-Host "Testing: $Description" -ForegroundColor Cyan
    Write-Host "User Query: '$UserQuery'" -ForegroundColor Yellow
    Write-Host "Expected Parameters:" -ForegroundColor Green
    
    foreach ($key in $ExpectedParameters.Keys) {
        Write-Host "  $key = '$($ExpectedParameters[$key])'" -ForegroundColor White
    }
    
    # Simulate the corrected behavior
    Write-Host "✅ CORRECT: Use simple values as provided by user" -ForegroundColor Green
    Write-Host "❌ INCORRECT: Do not construct fake DNs or make up values" -ForegroundColor Red
    Write-Host ("-" * 60) -ForegroundColor Gray
    Write-Host ""
}

Write-Host "=== Parameter Extraction Test Cases ===" -ForegroundColor Magenta
Write-Host "Testing how LLM should extract parameters without making up values" -ForegroundColor Cyan
Write-Host ""

# Test cases showing correct parameter extraction
$testCases = @(
    @{
        UserQuery = "find user rizhang"
        ExpectedParameters = @{ Identity = "rizhang" }
        Description = "Simple username lookup"
    },
    @{
        UserQuery = "get user john.doe"
        ExpectedParameters = @{ Identity = "john.doe" }
        Description = "Username with dot notation"
    },
    @{
        UserQuery = "find group Administrators"
        ExpectedParameters = @{ Identity = "Administrators" }
        Description = "Simple group name"
    },
    @{
        UserQuery = "get user CN=John Doe,OU=Users,DC=contoso,DC=com"
        ExpectedParameters = @{ Identity = "CN=John Doe,OU=Users,DC=contoso,DC=com" }
        Description = "User provided full DN - use as-is"
    },
    @{
        UserQuery = "find users in Marketing department"
        ExpectedParameters = @{ Filter = "Department -eq 'Marketing'" }
        Description = "Filter-based query"
    },
    @{
        UserQuery = "get computer DESKTOP-123"
        ExpectedParameters = @{ Identity = "DESKTOP-123" }
        Description = "Computer name lookup"
    },
    @{
        UserQuery = "find all users"
        ExpectedParameters = @{ Filter = "*" }
        Description = "Get all users query"
    },
    @{
        UserQuery = "get user details for alice with email and phone"
        ExpectedParameters = @{ 
            Identity = "alice"
            Properties = @("mail", "telephoneNumber")
        }
        Description = "User with specific properties"
    }
)

foreach ($testCase in $testCases) {
    Test-ParameterExtraction -UserQuery $testCase.UserQuery -ExpectedParameters $testCase.ExpectedParameters -Description $testCase.Description
}

Write-Host "=== Key Principles ===" -ForegroundColor Magenta
Write-Host ""
Write-Host "✅ DO:" -ForegroundColor Green
Write-Host "  - Use simple names exactly as provided by user" -ForegroundColor White
Write-Host "  - Extract only explicitly mentioned parameters" -ForegroundColor White
Write-Host "  - Use full DNs only when user provides them" -ForegroundColor White
Write-Host "  - Derive obvious parameters (like Properties from 'with email')" -ForegroundColor White
Write-Host ""
Write-Host "❌ DON'T:" -ForegroundColor Red
Write-Host "  - Construct fake Distinguished Names" -ForegroundColor White
Write-Host "  - Make up domain structures like 'DC=example,DC=com'" -ForegroundColor White
Write-Host "  - Infer complex parameter values not mentioned by user" -ForegroundColor White
Write-Host "  - Add parameters the user didn't ask for" -ForegroundColor White
Write-Host ""

Write-Host "=== Impact on Direct PowerShell Execution ===" -ForegroundColor Magenta
Write-Host ""
Write-Host "With the corrected parameter extraction:" -ForegroundColor Cyan
Write-Host "1. User says: 'find user rizhang'" -ForegroundColor Yellow
Write-Host "2. LLM extracts: {\"Identity\": \"rizhang\"}" -ForegroundColor Green
Write-Host "3. Direct execution: Get-ADUser -Identity 'rizhang'" -ForegroundColor White
Write-Host "4. PowerShell handles the lookup correctly" -ForegroundColor Green
Write-Host ""
Write-Host "This will work because:" -ForegroundColor Cyan
Write-Host "- Get-ADUser accepts simple usernames for Identity parameter" -ForegroundColor White
Write-Host "- Active Directory cmdlets handle SAM account names automatically" -ForegroundColor White
Write-Host "- No need to construct complex DN structures" -ForegroundColor White
Write-Host ""

Write-Host "The modified prompt will prevent the LLM from constructing fake DNs!" -ForegroundColor Green
