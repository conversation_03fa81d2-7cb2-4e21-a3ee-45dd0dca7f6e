INCS = -I$(SDK_INC_PATH) -I$(MINCORE_EXTERNAL_SDK_INC_PATH_L) -I$(KIT_SHARED_INC_PATH) -I$(ONECORE_EXTERNAL_SHARED_INC_PATH)

LAPSRPC_DEPENDS = .\aimxrpc.idl .\aimxrpc.acf

#
# Note, -ms_ext and -c_ext options default to on even if not specified
#

$(OBJ_PATH)\$(O)\aimxrpc.h $(OBJ_PATH)\$(O)\aimxrpc_c.c $(OBJ_PATH)\$(O)\aimxrpc_s.c : $(LAPSRPC_DEPENDS)
    $(MIDL) $(MIDL_OPTIMIZATION) -oldnames -out $(OBJ_PATH)\$(O) $(INCS) /prefix client "c_" /prefix server "s_" /export aimxrpc.idl

!if $(ARM64) && "$(ARM64X_EC_ENABLED)" == "1"

$(OBJ_PATH)\$(O)\arm64ec\aimxrpc.h $(OBJ_PATH)\$(O)\arm64ec\aimxrpc_c.c $(OBJ_PATH)\$(O)\arm64ec\aimxrpc_s.c : $(LAPSRPC_DEPENDS)
    $(MIDL) $(MIDL_OPTIMIZATION_AMD64) -oldnames -out $(OBJ_PATH)\$(O)\arm64ec $(INCS) /prefix client "c_" /prefix server "s_" /export aimxrpc.idl

!endif