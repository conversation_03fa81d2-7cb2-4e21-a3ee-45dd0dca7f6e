/*++

Copyright (c) Microsoft Corporation. All rights reserved.

Module Name:

    McpServerUtils.cpp

Abstract:

    Implementation of high-level utilities for creating MCP server executables.
    Provides base classes and helper functions that can be used by any MCP server.

Author:

    <PERSON><PERSON><PERSON> (riz<PERSON>) 07/19/2025

--*/

#include "McpServerUtils.h"
#include <iostream>
#include <objbase.h>

using namespace McpProtocol;

// Server-side utilities implementation
namespace McpProtocol::Server
{
    std::string ProcessRequest(
        _In_ const std::string& requestJson,
        _In_ const std::map<std::string, RequestHandler>& methodHandlers
    )
    {
        try
        {
            // Parse the JSON request
            nlohmann::json request;
            std::string parseError;
            if (!Utils::ParseJson(requestJson, request, &parseError))
            {
                // Return parse error response
                auto errorResponse = JsonRpc::CreateErrorResponse(
                    nlohmann::json(),
                    ErrorCodes::PARSE_ERROR,
                    "Parse error: " + parseError
                );
                return Utils::SerializeJson(errorResponse);
            }

            // Validate JSON-RPC request format
            if (!JsonRpc::IsValidRequest(request))
            {
                auto errorResponse = JsonRpc::CreateErrorResponse(
                    JsonRpc::GetId(request),
                    ErrorCodes::INVALID_REQUEST,
                    "Invalid JSON-RPC request format"
                );
                return Utils::SerializeJson(errorResponse);
            }

            // Extract method and parameters
            std::string method = JsonRpc::GetMethod(request);
            nlohmann::json params = JsonRpc::GetParams(request);
            nlohmann::json id = JsonRpc::GetId(request);

            // Find method handler
            auto handlerIt = methodHandlers.find(method);
            if (handlerIt == methodHandlers.end())
            {
                auto errorResponse = JsonRpc::CreateErrorResponse(
                    id,
                    ErrorCodes::METHOD_NOT_FOUND,
                    "Method not found: " + method
                );
                return Utils::SerializeJson(errorResponse);
            }

            // Call the method handler
            try
            {
                nlohmann::json result = handlerIt->second(params);
                auto response = JsonRpc::CreateResponse(id, result);
                return Utils::SerializeJson(response);
            }
            catch (const std::exception& e)
            {
                auto errorResponse = JsonRpc::CreateErrorResponse(
                    id,
                    ErrorCodes::INTERNAL_ERROR,
                    "Method execution failed: " + std::string(e.what())
                );
                return Utils::SerializeJson(errorResponse);
            }
        }
        catch (const std::exception& e)
        {
            // Fallback error response
            auto errorResponse = JsonRpc::CreateErrorResponse(
                nlohmann::json(),
                ErrorCodes::INTERNAL_ERROR,
                "Request processing failed: " + std::string(e.what())
            );
            return Utils::SerializeJson(errorResponse);
        }
    }

    nlohmann::json HandleShutdown(
        _In_ const nlohmann::json& params
    )
    {
        UNREFERENCED_PARAMETER(params);
        
        // Return empty result for shutdown
        return nlohmann::json::object();
    }
}

// Client-side utilities implementation
namespace McpProtocol::Client
{
    void AsyncRequestManager::SendRequest(
        _In_ const nlohmann::json& request,
        _In_ ResponseHandler onResponse,
        _In_ ErrorHandler onError
    )
    {
        nlohmann::json id = JsonRpc::GetId(request);
        if (!id.is_null())
        {
            m_pendingRequests[id] = std::make_pair(onResponse, onError);
        }
    }

    void AsyncRequestManager::ProcessResponse(
        _In_ const nlohmann::json& response
    )
    {
        nlohmann::json id = JsonRpc::GetId(response);
        if (id.is_null())
        {
            return; // No ID, can't match to request
        }

        auto it = m_pendingRequests.find(id);
        if (it == m_pendingRequests.end())
        {
            return; // No matching request
        }

        auto [onResponse, onError] = it->second;
        m_pendingRequests.erase(it);

        if (JsonRpc::IsErrorResponse(response))
        {
            onError(response);
        }
        else
        {
            onResponse(response);
        }
    }

    nlohmann::json CreateInitializeRequest(
        _In_ const std::string& clientName,
        _In_ const std::string& clientVersion
    )
    {
        // Create proper capabilities structure that standard
        // MCP server expects
        nlohmann::json capabilities = {
            {"roots", {
                {"listChanged", true}
            }},
            {"sampling", nlohmann::json::object()}
        };

        return Mcp::CreateInitializeRequest(clientName, clientVersion, capabilities, Utils::GenerateRequestId());
    }

    nlohmann::json CreateListToolsRequest()
    {
        return Mcp::CreateListToolsRequest(Utils::GenerateRequestId());
    }

    nlohmann::json CreateCallToolRequest(
        _In_ const std::string& toolName,
        _In_ const nlohmann::json& arguments
    )
    {
        return Mcp::CreateCallToolRequest(toolName, arguments, Utils::GenerateRequestId());
    }

    nlohmann::json CreateShutdownRequest()
    {
        return Mcp::CreateShutdownRequest(Utils::GenerateRequestId());
    }
}
