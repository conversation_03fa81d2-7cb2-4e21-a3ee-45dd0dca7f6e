<#
.SYNOPSIS
    Active Directory Domain Controller Management Tools for MCP Server
    
.DESCRIPTION
    This module registers MCP tools for Active Directory domain controller management operations.
    Each tool is a direct wrapper around the corresponding AD PowerShell cmdlet with
    exact parameter passthrough and no output formatting.

.AUTHOR
    Rupo Zhang (rizhang)
#>

# Import required modules
Import-Module ActiveDirectory -ErrorAction SilentlyContinue

function Register-DomainControllerManagementTools {
    [CmdletBinding()]
    param()

    # Get-ADDomainController - Gets the domain controllers in a domain
    Register-McpTool -Name "Get-ADDomainController" -Description "Gets the domain controllers in a domain." -ScriptBlock {
        param($Arguments)
        
        $params = @{}
        if ($Arguments.Filter) { $params.Filter = $Arguments.Filter }
        if ($Arguments.Identity) { $params.Identity = $Arguments.Identity }
        if ($Arguments.DomainName) { $params.DomainName = $Arguments.DomainName }
        if ($Arguments.Discover) { $params.Discover = $Arguments.Discover }
        if ($Arguments.AvoidSelf) { $params.AvoidSelf = $Arguments.AvoidSelf }
        if ($Arguments.ForceDiscover) { $params.ForceDiscover = $Arguments.ForceDiscover }
        if ($Arguments.NextClosestSite) { $params.NextClosestSite = $Arguments.NextClosestSite }
        if ($Arguments.Service) { $params.Service = $Arguments.Service }
        if ($Arguments.SiteName) { $params.SiteName = $Arguments.SiteName }
        if ($Arguments.Writable) { $params.Writable = $Arguments.Writable }
        if ($Arguments.Server) { $params.Server = $Arguments.Server }
        if ($Arguments.AuthType) { $params.AuthType = $Arguments.AuthType }
        if ($Arguments.Credential) { $params.Credential = $Arguments.Credential }
        
        Get-ADDomainController @params
    } -InputSchema @{
        type = "object"
        properties = @{
            Filter = @{ type = "string"; description = "PowerShell Expression Language filter string" }
            Identity = @{ type = "string"; description = "Domain controller identity (DN, GUID, or name)" }
            DomainName = @{ type = "string"; description = "Domain name to search for domain controllers" }
            Discover = @{ type = "boolean"; description = "Discover domain controllers using DNS" }
            AvoidSelf = @{ type = "boolean"; description = "Avoid returning the current domain controller" }
            ForceDiscover = @{ type = "boolean"; description = "Force discovery even if cached results exist" }
            NextClosestSite = @{ type = "boolean"; description = "Include domain controllers from the next closest site" }
            Service = @{ type = "array"; items = @{ type = "string"; enum = @("ADWS", "GlobalCatalog", "KDC", "NetLogon", "TimeService") }; description = "Required services" }
            SiteName = @{ type = "string"; description = "Site name to search for domain controllers" }
            Writable = @{ type = "boolean"; description = "Return only writable domain controllers" }
            Server = @{ type = "string"; description = "Domain controller to connect to" }
            AuthType = @{ type = "string"; enum = @("Negotiate", "Basic"); description = "Authentication method" }
            Credential = @{ type = "string"; description = "User credentials for authentication" }
        }
    }

    # Move-ADDirectoryServer - Moves a directory server in Active Directory to a new site
    Register-McpTool -Name "Move-ADDirectoryServer" -Description "Moves a directory server in Active Directory to a new site." -ScriptBlock {
        param($Arguments)
        
        $params = @{}
        if ($Arguments.Identity) { $params.Identity = $Arguments.Identity }
        if ($Arguments.Site) { $params.Site = $Arguments.Site }
        if ($Arguments.Server) { $params.Server = $Arguments.Server }
        if ($Arguments.AuthType) { $params.AuthType = $Arguments.AuthType }
        if ($Arguments.Credential) { $params.Credential = $Arguments.Credential }
        if ($Arguments.PassThru) { $params.PassThru = $Arguments.PassThru }
        
        Move-ADDirectoryServer @params
    } -InputSchema @{
        type = "object"
        properties = @{
            Identity = @{ type = "string"; description = "Directory server identity (DN, GUID, or name)" }
            Site = @{ type = "string"; description = "Target site for the directory server" }
            Server = @{ type = "string"; description = "Domain controller to connect to" }
            AuthType = @{ type = "string"; enum = @("Negotiate", "Basic"); description = "Authentication method" }
            Credential = @{ type = "string"; description = "User credentials for authentication" }
            PassThru = @{ type = "boolean"; description = "Return the moved directory server object" }
        }
        required = @("Identity", "Site")
    }

    # Move-ADDirectoryServerOperationMasterRole - Moves operation master roles to a domain controller
    Register-McpTool -Name "Move-ADDirectoryServerOperationMasterRole" -Description "Moves operation master roles to a domain controller." -ScriptBlock {
        param($Arguments)
        
        $params = @{}
        if ($Arguments.Identity) { $params.Identity = $Arguments.Identity }
        if ($Arguments.OperationMasterRole) { $params.OperationMasterRole = $Arguments.OperationMasterRole }
        if ($Arguments.Force) { $params.Force = $Arguments.Force }
        if ($Arguments.Server) { $params.Server = $Arguments.Server }
        if ($Arguments.AuthType) { $params.AuthType = $Arguments.AuthType }
        if ($Arguments.Credential) { $params.Credential = $Arguments.Credential }
        if ($Arguments.PassThru) { $params.PassThru = $Arguments.PassThru }
        
        Move-ADDirectoryServerOperationMasterRole @params
    } -InputSchema @{
        type = "object"
        properties = @{
            Identity = @{ type = "string"; description = "Target domain controller identity (DN, GUID, or name)" }
            OperationMasterRole = @{ type = "array"; items = @{ type = "string"; enum = @("DomainNamingMaster", "InfrastructureMaster", "PDCEmulator", "RIDMaster", "SchemaMaster") }; description = "Operation master roles to move" }
            Force = @{ type = "boolean"; description = "Force the role transfer without confirmation" }
            Server = @{ type = "string"; description = "Domain controller to connect to" }
            AuthType = @{ type = "string"; enum = @("Negotiate", "Basic"); description = "Authentication method" }
            Credential = @{ type = "string"; description = "User credentials for authentication" }
            PassThru = @{ type = "boolean"; description = "Return the domain controller object" }
        }
        required = @("Identity", "OperationMasterRole")
    }

    # Add-ADDomainControllerPasswordReplicationPolicy - Adds users, computers, and groups to the allowed or denied list of a read-only domain controller password replication policy
    Register-McpTool -Name "Add-ADDomainControllerPasswordReplicationPolicy" -Description "Adds users, computers, and groups to the allowed or denied list of a read-only domain controller password replication policy." -ScriptBlock {
        param($Arguments)
        
        $params = @{}
        if ($Arguments.Identity) { $params.Identity = $Arguments.Identity }
        if ($Arguments.AllowedList) { $params.AllowedList = $Arguments.AllowedList }
        if ($Arguments.DeniedList) { $params.DeniedList = $Arguments.DeniedList }
        if ($Arguments.Server) { $params.Server = $Arguments.Server }
        if ($Arguments.AuthType) { $params.AuthType = $Arguments.AuthType }
        if ($Arguments.Credential) { $params.Credential = $Arguments.Credential }
        if ($Arguments.PassThru) { $params.PassThru = $Arguments.PassThru }
        
        Add-ADDomainControllerPasswordReplicationPolicy @params
    } -InputSchema @{
        type = "object"
        properties = @{
            Identity = @{ type = "string"; description = "Read-only domain controller identity (DN, GUID, or name)" }
            AllowedList = @{ type = "array"; items = @{ type = "string" }; description = "Accounts to add to the allowed list" }
            DeniedList = @{ type = "array"; items = @{ type = "string" }; description = "Accounts to add to the denied list" }
            Server = @{ type = "string"; description = "Domain controller to connect to" }
            AuthType = @{ type = "string"; enum = @("Negotiate", "Basic"); description = "Authentication method" }
            Credential = @{ type = "string"; description = "User credentials for authentication" }
            PassThru = @{ type = "boolean"; description = "Return the domain controller object" }
        }
        required = @("Identity")
    }

    # Remove-ADDomainControllerPasswordReplicationPolicy - Removes users, computers, and groups from the allowed or denied list of a read-only domain controller password replication policy
    Register-McpTool -Name "Remove-ADDomainControllerPasswordReplicationPolicy" -Description "Removes users, computers, and groups from the allowed or denied list of a read-only domain controller password replication policy." -ScriptBlock {
        param($Arguments)
        
        $params = @{}
        if ($Arguments.Identity) { $params.Identity = $Arguments.Identity }
        if ($Arguments.AllowedList) { $params.AllowedList = $Arguments.AllowedList }
        if ($Arguments.DeniedList) { $params.DeniedList = $Arguments.DeniedList }
        if ($Arguments.Server) { $params.Server = $Arguments.Server }
        if ($Arguments.AuthType) { $params.AuthType = $Arguments.AuthType }
        if ($Arguments.Credential) { $params.Credential = $Arguments.Credential }
        if ($Arguments.PassThru) { $params.PassThru = $Arguments.PassThru }
        
        Remove-ADDomainControllerPasswordReplicationPolicy @params
    } -InputSchema @{
        type = "object"
        properties = @{
            Identity = @{ type = "string"; description = "Read-only domain controller identity (DN, GUID, or name)" }
            AllowedList = @{ type = "array"; items = @{ type = "string" }; description = "Accounts to remove from the allowed list" }
            DeniedList = @{ type = "array"; items = @{ type = "string" }; description = "Accounts to remove from the denied list" }
            Server = @{ type = "string"; description = "Domain controller to connect to" }
            AuthType = @{ type = "string"; enum = @("Negotiate", "Basic"); description = "Authentication method" }
            Credential = @{ type = "string"; description = "User credentials for authentication" }
            PassThru = @{ type = "boolean"; description = "Return the domain controller object" }
        }
        required = @("Identity")
    }

    # Get-ADDomainControllerPasswordReplicationPolicy - Gets the members of the allowed list or denied list of a read-only domain controller password replication policy
    Register-McpTool -Name "Get-ADDomainControllerPasswordReplicationPolicy" -Description "Gets the members of the allowed list or denied list of a read-only domain controller password replication policy." -ScriptBlock {
        param($Arguments)
        
        $params = @{}
        if ($Arguments.Identity) { $params.Identity = $Arguments.Identity }
        if ($Arguments.Allowed) { $params.Allowed = $Arguments.Allowed }
        if ($Arguments.Denied) { $params.Denied = $Arguments.Denied }
        if ($Arguments.Server) { $params.Server = $Arguments.Server }
        if ($Arguments.AuthType) { $params.AuthType = $Arguments.AuthType }
        if ($Arguments.Credential) { $params.Credential = $Arguments.Credential }
        
        Get-ADDomainControllerPasswordReplicationPolicy @params
    } -InputSchema @{
        type = "object"
        properties = @{
            Identity = @{ type = "string"; description = "Read-only domain controller identity (DN, GUID, or name)" }
            Allowed = @{ type = "boolean"; description = "Get the allowed list" }
            Denied = @{ type = "boolean"; description = "Get the denied list" }
            Server = @{ type = "string"; description = "Domain controller to connect to" }
            AuthType = @{ type = "string"; enum = @("Negotiate", "Basic"); description = "Authentication method" }
            Credential = @{ type = "string"; description = "User credentials for authentication" }
        }
        required = @("Identity")
    }

    # Get-ADDomainControllerPasswordReplicationPolicyUsage - Gets the resultant password replication policy for a user or computer on a read-only domain controller
    Register-McpTool -Name "Get-ADDomainControllerPasswordReplicationPolicyUsage" -Description "Gets the resultant password replication policy for a user or computer on a read-only domain controller." -ScriptBlock {
        param($Arguments)
        
        $params = @{}
        if ($Arguments.Identity) { $params.Identity = $Arguments.Identity }
        if ($Arguments.AccountIdentity) { $params.AccountIdentity = $Arguments.AccountIdentity }
        if ($Arguments.Server) { $params.Server = $Arguments.Server }
        if ($Arguments.AuthType) { $params.AuthType = $Arguments.AuthType }
        if ($Arguments.Credential) { $params.Credential = $Arguments.Credential }
        
        Get-ADDomainControllerPasswordReplicationPolicyUsage @params
    } -InputSchema @{
        type = "object"
        properties = @{
            Identity = @{ type = "string"; description = "Read-only domain controller identity (DN, GUID, or name)" }
            AccountIdentity = @{ type = "string"; description = "Account identity to check policy for" }
            Server = @{ type = "string"; description = "Domain controller to connect to" }
            AuthType = @{ type = "string"; enum = @("Negotiate", "Basic"); description = "Authentication method" }
            Credential = @{ type = "string"; description = "User credentials for authentication" }
        }
        required = @("Identity", "AccountIdentity")
    }

    # Get-ADDCCloningExcludedApplicationList - Gets a list of installed programs and services present on this domain controller that are not in the default or user-defined inclusion list for domain controller cloning
    Register-McpTool -Name "Get-ADDCCloningExcludedApplicationList" -Description "Gets a list of installed programs and services present on this domain controller that are not in the default or user-defined inclusion list for domain controller cloning." -ScriptBlock {
        param($Arguments)
        
        $params = @{}
        if ($Arguments.GenerateXml) { $params.GenerateXml = $Arguments.GenerateXml }
        if ($Arguments.Path) { $params.Path = $Arguments.Path }
        if ($Arguments.Force) { $params.Force = $Arguments.Force }
        
        Get-ADDCCloningExcludedApplicationList @params
    } -InputSchema @{
        type = "object"
        properties = @{
            GenerateXml = @{ type = "boolean"; description = "Generate XML file with excluded applications" }
            Path = @{ type = "string"; description = "Path to save the XML file" }
            Force = @{ type = "boolean"; description = "Overwrite existing file without confirmation" }
        }
    }

    # New-ADDCCloneConfigFile - Performs prerequisite checks for cloning a domain controller and generates a clone configuration file if all checks succeed
    Register-McpTool -Name "New-ADDCCloneConfigFile" -Description "Performs prerequisite checks for cloning a domain controller and generates a clone configuration file if all checks succeed." -ScriptBlock {
        param($Arguments)
        
        $params = @{}
        if ($Arguments.CloneComputerName) { $params.CloneComputerName = $Arguments.CloneComputerName }
        if ($Arguments.IPv4Address) { $params.IPv4Address = $Arguments.IPv4Address }
        if ($Arguments.IPv4DefaultGateway) { $params.IPv4DefaultGateway = $Arguments.IPv4DefaultGateway }
        if ($Arguments.IPv4DNSResolver) { $params.IPv4DNSResolver = $Arguments.IPv4DNSResolver }
        if ($Arguments.IPv4SubnetMask) { $params.IPv4SubnetMask = $Arguments.IPv4SubnetMask }
        if ($Arguments.IPv6DNSResolver) { $params.IPv6DNSResolver = $Arguments.IPv6DNSResolver }
        if ($Arguments.Path) { $params.Path = $Arguments.Path }
        if ($Arguments.SiteName) { $params.SiteName = $Arguments.SiteName }
        if ($Arguments.Static) { $params.Static = $Arguments.Static }
        if ($Arguments.Force) { $params.Force = $Arguments.Force }
        
        New-ADDCCloneConfigFile @params
    } -InputSchema @{
        type = "object"
        properties = @{
            CloneComputerName = @{ type = "string"; description = "Name for the cloned domain controller" }
            IPv4Address = @{ type = "string"; description = "IPv4 address for the cloned domain controller" }
            IPv4DefaultGateway = @{ type = "string"; description = "IPv4 default gateway" }
            IPv4DNSResolver = @{ type = "array"; items = @{ type = "string" }; description = "IPv4 DNS resolver addresses" }
            IPv4SubnetMask = @{ type = "string"; description = "IPv4 subnet mask" }
            IPv6DNSResolver = @{ type = "array"; items = @{ type = "string" }; description = "IPv6 DNS resolver addresses" }
            Path = @{ type = "string"; description = "Path to save the clone configuration file" }
            SiteName = @{ type = "string"; description = "Site name for the cloned domain controller" }
            Static = @{ type = "boolean"; description = "Use static IP configuration" }
            Force = @{ type = "boolean"; description = "Overwrite existing file without confirmation" }
        }
    }
}

# Function is available after dot-sourcing
