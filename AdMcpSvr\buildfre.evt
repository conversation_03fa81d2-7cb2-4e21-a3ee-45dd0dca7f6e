ts=2812 Merging config files using BUILD_CONFIG_FILE=e:\os\obj\amd64fre\objfre\amd64\build-exe-merged.config
ts=3468 queue pregraph command
ts=3468 run PreGraph commands build_pre_graph
ts=4203 queue prebuild command
ts=4203 run preprocess commands build_pre_process
ts=11531 initializing DBB query
ts=19875 reading parent chain of e:\os\src\onecore\ds\ds\src\aimx\prod\admcpsvr
ts=19875 reading parent chain of e:\os\src\onecore\ds\ds\src\aimx\prod
ts=19875 reading parent chain of e:\os\src\onecore\ds\ds\src\aimx
ts=19875 reading parent chain of e:\os\src\onecore\ds\ds\src
ts=19875 reading parent chain of e:\os\src\onecore\ds\ds
ts=19875 reading parent chain of e:\os\src\onecore\ds
ts=19875 reading parent chain of e:\os\src\onecore
ts=19875 reading parent chain of e:\os\src
ts=19890 scanning focus directory e:\os\src\onecore\ds\ds\src\aimx\prod\admcpsvr
ts=19890 scanning focus directory e:\os\src\onecore\ds\ds\src\aimx\prod\admcpsvr
ts=19906 BUILD: Processing dependencies...
ts=19906 BUILD: Scanning for circular dependencies...
ts=19906 BUILD: Processing dependencies complete
ts=19906 (onecore\ds\ds\src\aimx\prod\admcpsvr) e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\admcpsvr\objfre\amd64\_objects.mac created (133972823966757063), sources (133972823192923187), sources recorded (133972823192923187)
ts=20265 (PASS0) onecore\ds\ds\src\aimx\prod\admcpsvr(8): in transit: passQ->distributedQ.
ts=20265 (PASS0) onecore\ds\ds\src\aimx\prod\admcpsvr(8): submitted to distributedQ.
ts=20281 DBB INCR: onecore\ds\ds\src\aimx\prod\admcpsvr|PASS0 changed - rebuild needed (uninitialized)
ts=20281 (PASS0) onecore\ds\ds\src\aimx\prod\admcpsvr(8): sent to build client (4).
ts=20281 (PASS0) onecore\ds\ds\src\aimx\prod\admcpsvr(8): distributed work started.
ts=21812 (PASS0) onecore\ds\ds\src\aimx\prod\admcpsvr(8): distributed work completed.
ts=21812 (PASS0) onecore\ds\ds\src\aimx\prod\admcpsvr(8): operation completed.
ts=22281 (PASS0) processing complete.
ts=22281 (PASS1) onecore\ds\ds\src\aimx\prod\admcpsvr(8): in transit: passQ->distributedQ.
ts=22297 (PASS1) onecore\ds\ds\src\aimx\prod\admcpsvr(8): submitted to distributedQ.
ts=22297 DBB INCR: onecore\ds\ds\src\aimx\prod\admcpsvr|PASS1 changed - rebuild needed (pip not found)
ts=22297 (PASS1) onecore\ds\ds\src\aimx\prod\admcpsvr(8): sent to build client (1).
ts=22312 (PASS1) onecore\ds\ds\src\aimx\prod\admcpsvr(8): distributed work started.
ts=53172 (PASS1) onecore\ds\ds\src\aimx\prod\admcpsvr(8): distributed work completed.
ts=53172 (PASS1) onecore\ds\ds\src\aimx\prod\admcpsvr(8): operation completed.
ts=53484 (PASS1) processing complete.
ts=53484 PASS_INDEPENDENT processing complete.
ts=53484 PASS_INDEPENDENT processing complete.

DIR EVENTS: onecore\ds\ds\src\aimx\prod\admcpsvr (IN FOCUS)
  CREATED=1
  FOUND_IN_DIRS=1
  INITIAL_DIRECTORY=1
  FOUND_IN_DATABASE=1
  CLEAN_ALL=0
  CLEAN_ALL_FORCED=0
  CLEAN_PASS0=1
  CLEAN_PASS1=1
  CLEAN_PASS2=0
  CLEAN_PASS_INDEPENDENT=0
  ALLOC_WORKITEM_PASS0=1
  ALLOC_WORKITEM_PASS1=1
  ALLOC_WORKITEM_PASS2=0
  ALLOC_WORKITEM_PASS_INDEPENDENT=0
  SOURCES_READ=1
  NODE_DIR=1
  DIRS_PROJ_SKIPPED=0
  MSBUILD_PROJECT_FILE_READ=0
  MSBUILD_POST_BUILD_PROJECT_FILE_READ=0
  TEST_CODE=0
  SAMPLE_CODE=0
  TESTCODE_SKIPPED=0
  PASS0_NEEDED=1
  PASS1_NEEDED=1
  PASS2_NEEDED=0
  PASS3_NEEDED=0
  SOURCES_DEP_READ=0
  QUEUE_TO_PASS_LIST_1=1
  QUEUE_TO_PASS_LIST_2=1
  QUEUE_TO_PASS_LIST_3=0
  ADD_DEPENDENCIES_TO_GRAPH=1
  SET_NON_PRODUCT=0
  SET_NON_CRITICAL=0
  SET_CS_WINDOWS=0
  SET_CS_XBOX=0
  SET_CS_PHONE=0
  SET_CS_AZURE=0
  SCAN_COMPLETE=1

  BUILD PASS0 EVENTS: onecore\ds\ds\src\aimx\prod\admcpsvr
    BUILD_CREATED=1
    TASK_CREATED=0
    ADDED_TO_DEPENDENCY_GRAPH=1
    ADDED_TO_PASSQ=1
    ADDED_TO_TASKQ=0
    TRANSITION_STANDBYQ_TO_PASSQ=0
    ADDED_TO_PASS0_Q=1
    ADDED_TO_PASS1_Q=0
    ADDED_TO_PASS2_Q=0
    ADDED_TO_PASS3_Q=0
    ADDED_TO_PASS_INDEPENDENT_Q=0
    ADDED_TO_TASK_Q=0
    REMOVED_FROM_PASSQ=1
    DEPENDENT_MOVED_FROM_READYQ_TO_PASSQ=0
    NMAKE_COMMAND_LINE=1
    MSBUILD_COMMAND_LINE=0
    SUBMIT_TO_DISTRIBUTED_SCHEDULER=1
    DISTRIBUTED_SCHEDULER_WORK_STARTED=1
    DISTRIBUTED_SCHEDULER_WORK_COMPLETED=1
    ADDED_TO_READYQ=0
    BYPASSED=0
    SENT_TO_CLIENT=1
    INCREMENTAL=1
    BUILD_PASS_TERMINATED_FOR_ERRORS=0
    BUILD_OPERATION_COMPLETE=1

  BUILD PASS1 EVENTS: onecore\ds\ds\src\aimx\prod\admcpsvr
    BUILD_CREATED=1
    TASK_CREATED=0
    ADDED_TO_DEPENDENCY_GRAPH=1
    ADDED_TO_PASSQ=1
    ADDED_TO_TASKQ=0
    TRANSITION_STANDBYQ_TO_PASSQ=0
    ADDED_TO_PASS0_Q=0
    ADDED_TO_PASS1_Q=1
    ADDED_TO_PASS2_Q=0
    ADDED_TO_PASS3_Q=0
    ADDED_TO_PASS_INDEPENDENT_Q=0
    ADDED_TO_TASK_Q=0
    REMOVED_FROM_PASSQ=1
    DEPENDENT_MOVED_FROM_READYQ_TO_PASSQ=0
    NMAKE_COMMAND_LINE=1
    MSBUILD_COMMAND_LINE=0
    SUBMIT_TO_DISTRIBUTED_SCHEDULER=1
    DISTRIBUTED_SCHEDULER_WORK_STARTED=1
    DISTRIBUTED_SCHEDULER_WORK_COMPLETED=1
    ADDED_TO_READYQ=0
    BYPASSED=0
    SENT_TO_CLIENT=1
    INCREMENTAL=1
    BUILD_PASS_TERMINATED_FOR_ERRORS=0
    BUILD_OPERATION_COMPLETE=1
