BUILD COMMAND: e:\os\tools\CoreBuild\amd64\build.exe  -parent -c

Reading historical build information...
Reading historical build information completed in [0:00:00.000]
BuildExe GUID: {76AF40FF-FEC8-4426-B1EC-624B844523C0}
Launching process: e:\os\tools\CoreBuild\amd64\tracer.exe  /skip:1 -f:.\buildfre.trc -guid:e:\os\src\registered_data.ini   /logPrefix:buildfre /c:"e:\os\tools\CoreBuild\amd64\buildc.exe" /s:localhost:29026  
1>  *************
1>'e:\os\tools\CoreBuild\amd64\tracer.exe  /skip:1 -f:.\buildfre.trc -guid:e:\os\src\registered_data.ini   /logPrefix:buildfre /c:"e:\os\tools\CoreBuild\amd64\buildc.exe" /s:localhost:29026  '
1>info: Microsoft.Internal.Trace.Tracer.EtwTraceAdapter[0]
1>      Processing Build Trace File Logger *************-4aa0-a34e-55f9c8ec57e3
Merging config files using BUILD_CONFIG_FILE=e:\os\obj\amd64fre\objfre\amd64\build-exe-merged.config
BUILD: (ActiveWorkLoad)*, ElapsedTime(s),Counter,Available Memory (%),Disk I/O (ms),Disk Usage (%),CPU (%),Previous,New,Last Max,Permitted Threads,ThreadPool Memory Footprint (bytes), Max Memory Allowed (bytes), Submitted Thread,Running Threads,WorkItems (Available), WorkItems (Waiting),Pass,Priority,Peak Memory Project (bytes),Directory,MachineName
1>  1>[0:00:00.235] [Pass0 ] (none) {1}
3001>Merging config files  *************
3001>'mergeconfigfilesforbuildexe.cmd '
3001>MergeConfigFilesForBuildExe.cmd: Merged config file current: [e:\os\obj\amd64fre\objfre\amd64\build-exe-merged.config].
Executing PreGraph commands  *************
1>Performing pregraph steps...  *************
1>'build_pre_graph -c'
1>(build_pre_graph.cmd) e:\os\tools\NodeJS.x64\node.exe e:\os\src\tools\nmakejs\Transpile.js
1>(build_pre_graph.cmd) e:\os\tools\NodeJS.x64\node.exe e:\os\obj\amd64fre\objfre\amd64\NMakeJS\PreGraph.js
1>(build_pre_graph.cmd) Determining best branch for vpack with prefix "cdg" and suffix "amd64fre"...
1>(build_pre_graph.cmd) Latest vPack "cdg.rs_wsd_cfe_adai.amd64fre" version from label file e:\os\src\sdpublic\misc\Labels\rs_wsd_cfe_adai_label.xml is 27902.1000.2507131458
1>(build_pre_graph.cmd) Preferring this branch's vpack information
1>(build_pre_graph.cmd) Latest vPack "cdg.rs_wsd_cfe_adai.amd64fre" version from label file e:\os\src\sdpublic\misc\Labels\rs_wsd_cfe_adai_label.xml is 27902.1000.2507131458
1>(build_pre_graph.cmd) Not pulling vpack cdg.rs_wsd_cfe_adai.amd64fre because it is up-to-date (matches the marker file at e:\os\cdg\Target.cdg-amd64fre.man).
1>(build_pre_graph.cmd) Completed successfully.
Pre-Graph completed in [0:00:00.656]
Executing preprocess commands  *************
1>warn: Microsoft.Internal.Trace.Tracer.EtwTraceAdapter[0]
1>      Task table not implemented
1>(build_pre_process.cmd) Determining best branch for vpack with prefix "publics" and suffix "amd64"...
1>(build_pre_process.cmd) Latest vPack "publics.rs_wsd_cfe_adai.amd64" version from label file e:\os\src\sdpublic\misc\Labels\rs_wsd_cfe_adai_label.xml is 27902.1000.2507131412
1>(build_pre_process.cmd) Preferring this branch's vpack information
1>(build_pre_process.cmd) Latest vPack "publics.rs_wsd_cfe_adai.amd64" version from label file e:\os\src\sdpublic\misc\Labels\rs_wsd_cfe_adai_label.xml is 27902.1000.2507131412
1>(build_pre_process.cmd) No overlay daemon running
1>BUILDMSG: build_pre_process: Updating publics @ e:\os\public\amd64fre
1>(build_pre_process.cmd) Running 'urtrun64 4.Latest e:\os\tools\OSPublics\OSPublics.exe UpdateOverlay /ManifestId:amd64fre-86fc-538b2f77bb20df7ed50aad8ac966ab7d /BaseManifestId:publics.rs_wsd_cfe_adai.amd64.27902.1000.2507131412 /Arch:amd64 /PublicRoot:e:\os\public\amd64fre /PublicChangesExternalPath:e:\os\src\tools\publicchanges.external.txt /PublishRulesPath:e:\os\src\.config\onecoreuap\build\PublishRules.json'
1>OSPublics.UpdateOverlay: Information: Version=0.0.0.0.
1>OSPublics.UpdateOverlay: Information: Parsing rules ..
1>OSPublics.UpdateOverlay: Information: [00.2s] DONE: Parsing rules ..
1>OSPublics.UpdateOverlay: Information: Starting overlay daemon...
1>OSPublics.UpdateOverlay: Information: Starting overlay daemon with 'e:\os\tools\OSPublics\OverlayDaemon.exe Run /Port:10250 /CacheDirectory:"e:\os\public\.cache" /VirtualizationRoot:"e:\os\public\amd64fre"'.
1>OSPublics.UpdateOverlay: Information: [07.4s] DONE: Starting overlay daemon...
1>OSPublics.UpdateOverlay: Information: Starting overlay of 'publics.rs_wsd_cfe_adai.amd64.27902.1000.2507131412' to 'e:\os\public\amd64fre'...
1>OSPublics.UpdateOverlay: LogAlways: {"contentManifestId":"amd64fre-86fc-538b2f77bb20df7ed50aad8ac966ab7d","forceRefresh":false}
1>OSPublics.UpdateOverlay: LogAlways: {"contentSize":115481,"filesOnly":true}
1>OSPublics.UpdateOverlay: LogAlways: {"succeeded":true,"callEndData":"","elapsedCallTimeInMilliseconds":495}
1>OSPublics.UpdateOverlay: LogAlways: {"succeeded":true,"callEndData":"","elapsedCallTimeInMilliseconds":1027}
1>OSPublics.UpdateOverlay: Information: [03.8s] DONE: Starting overlay of 'publics.rs_wsd_cfe_adai.amd64.27902.1000.2507131412' to 'e:\os\public\amd64fre'...
1>OSPublics.UpdateOverlay: Information: Completed in 11.5s.
1>BUILDMSG: build_pre_process: Publics update complete [elapsed=12s].
1>(build_pre_process.cmd) e:\os\tools\NodeJS.x64\node.exe e:\os\obj\amd64fre\objfre\amd64\NMakeJS\PreProcess.js
1>(ReplicaTool.cmd) [07/15/25 14:02:35] Starting execution
1>(ReplicaTool.cmd)   Full LNM replica verification is active -- 'disable.lnm.replicas' build setting is not set / set to false.
1>(ReplicaTool.cmd)   Tool will verify that files are in sync
1>(ReplicaTool.cmd)   Using replica map "e:\os\src\xbox\data\replica_maps\default.txt" from the default value
1>(ReplicaTool.cmd)   Using IDK (onecore) repository root "e:\os\src" from the BASEDIR environment variable
1>(ReplicaTool.cmd)   Using Edition (xbox) repository root "e:\os\src" from the BASEDIR environment variable
1>(ReplicaTool.cmd) Reading replica map...
1>(ReplicaTool.cmd) Expanding replica map...
1>(ReplicaTool.cmd) Scanning onecore\drivers\wdm\usb\platformdetection\... => xbox\replicas\usb\platformdetection\...
1>(ReplicaTool.cmd) Scanning onecore\net\flowsteering\... => xbox\replicas\net\flowsteering\...
1>(ReplicaTool.cmd) Excluding onecore\drivers\wdm\usb\platformdetection\...\.vs\... => xbox\replicas\usb\platformdetection\...\.vs\...
1>(ReplicaTool.cmd) Excluding onecore\drivers\wdm\usb\platformdetection\...\dirs => xbox\replicas\usb\platformdetection\...\dirs
1>(ReplicaTool.cmd) Excluding onecore\drivers\wdm\usb\platformdetection\.vs\... => xbox\replicas\usb\platformdetection\.vs\...
1>(ReplicaTool.cmd) Excluding onecore\drivers\wdm\usb\platformdetection\dirs => xbox\replicas\usb\platformdetection\dirs
1>(ReplicaTool.cmd) Excluding onecore\net\flowsteering\...\.vs\... => xbox\replicas\net\flowsteering\...\.vs\...
1>(ReplicaTool.cmd) Excluding onecore\net\flowsteering\...\.vscode\... => xbox\replicas\net\flowsteering\...\.vscode\...
1>(ReplicaTool.cmd) Excluding onecore\net\flowsteering\...\dirs => xbox\replicas\net\flowsteering\...\dirs
1>(ReplicaTool.cmd) Excluding onecore\net\flowsteering\...\sources => xbox\replicas\net\flowsteering\...\sources
1>(ReplicaTool.cmd) Excluding onecore\net\flowsteering\...\sources.dep => xbox\replicas\net\flowsteering\...\sources.dep
1>(ReplicaTool.cmd) Excluding onecore\net\flowsteering\...\sources.inc => xbox\replicas\net\flowsteering\...\sources.inc
1>(ReplicaTool.cmd) Excluding onecore\net\flowsteering\.vs\... => xbox\replicas\net\flowsteering\.vs\...
1>(ReplicaTool.cmd) Excluding onecore\net\flowsteering\.vscode\... => xbox\replicas\net\flowsteering\.vscode\...
1>(ReplicaTool.cmd) Excluding onecore\net\flowsteering\FlowsteeringReplicaMap.txt => xbox\replicas\net\flowsteering\FlowsteeringReplicaMap.txt
1>(ReplicaTool.cmd) Excluding onecore\net\flowsteering\base\um_win\... => xbox\replicas\net\flowsteering\base\um_win\...
1>(ReplicaTool.cmd) Excluding onecore\net\flowsteering\core\src\usrlib\... => xbox\replicas\net\flowsteering\core\src\usrlib\...
1>(ReplicaTool.cmd) Excluding onecore\net\flowsteering\core\ut\... => xbox\replicas\net\flowsteering\core\ut\...
1>(ReplicaTool.cmd) Excluding onecore\net\flowsteering\dirs => xbox\replicas\net\flowsteering\dirs
1>(ReplicaTool.cmd) Excluding onecore\net\flowsteering\engine\... => xbox\replicas\net\flowsteering\engine\...
1>(ReplicaTool.cmd) Excluding onecore\net\flowsteering\kd\... => xbox\replicas\net\flowsteering\kd\...
1>(ReplicaTool.cmd) Excluding onecore\net\flowsteering\porttracker\allocator\perf\... => xbox\replicas\net\flowsteering\porttracker\allocator\perf\...
1>(ReplicaTool.cmd) Excluding onecore\net\flowsteering\porttracker\allocator\src\km\... => xbox\replicas\net\flowsteering\porttracker\allocator\src\km\...
1>(ReplicaTool.cmd) Excluding onecore\net\flowsteering\porttracker\allocator\src\um\... => xbox\replicas\net\flowsteering\porttracker\allocator\src\um\...
1>(ReplicaTool.cmd) Excluding onecore\net\flowsteering\porttracker\allocator\test\... => xbox\replicas\net\flowsteering\porttracker\allocator\test\...
1>(ReplicaTool.cmd) Excluding onecore\net\flowsteering\porttracker\allocator\um\... => xbox\replicas\net\flowsteering\porttracker\allocator\um\...
1>(ReplicaTool.cmd) Excluding onecore\net\flowsteering\porttracker\client\test\... => xbox\replicas\net\flowsteering\porttracker\client\test\...
1>(ReplicaTool.cmd) Excluding onecore\net\flowsteering\porttracker\server\test\... => xbox\replicas\net\flowsteering\porttracker\server\test\...
1>(ReplicaTool.cmd) Excluding onecore\net\flowsteering\porttracker\test\... => xbox\replicas\net\flowsteering\porttracker\test\...
1>(ReplicaTool.cmd) Excluding onecore\net\flowsteering\replica-command.txt => xbox\replicas\net\flowsteering\replica-command.txt
1>(ReplicaTool.cmd) Excluding onecore\net\flowsteering\test\... => xbox\replicas\net\flowsteering\test\...
1>(ReplicaTool.cmd) Excluding onecore\net\flowsteering\testlists\... => xbox\replicas\net\flowsteering\testlists\...
1>Skipping pull of Xbox BBT manifest
1> e:\os\tools\Windows.Desktop.Tools.amd64\tools\touch.exe /c e:\os\public\amd64fre\public.log
1>Projecting build config
1> e:\os\tools\perl\bin\perl.exe e:\os\src\tools\project-buildconfigs.pl -config:e:\os\obj\amd64fre\objfre\amd64\build-exe-merged.config -buildOfficialBranch: -outputFolder:e:\os\obj\amd64fre\objfre\amd64\projectedconfig  -branchkeys: -keys:
1>(project-buildconfigs.pl) Done projecting build configuration keys
1>Skipping Source Link files generation
1> c:\windows\system32\cmd.exe /c del /Q e:\os\obj\amd64fre\objfre\amd64\CoreBuild\__SourceLinkConfiguration_uncached__.json 2>nul
1>Skipping PGI instrumentation.
1>Skipping OneCore Velocity import in a branch that builds OneCore
1>(build_pre_process.cmd) Completed successfully.
DBB MODE: Retrieving dependency information. Please wait...
Examining e:\os\src\onecore\ds\ds\src\aimx\prod\aimxsrv (directory tree)
1>  1>[0:00:26.907] [Pass M] e:\os\src\onecore\ds\ds\src\aimx\prod\aimxsrv\idl {4}
1>  1>[0:00:27.016] [Pass M] e:\os\src\onecore\ds\ds\src\aimx\prod\aimxsrv\client\lib {5}
1>  1>[0:00:27.047] [Pass M] e:\os\src\onecore\ds\ds\src\aimx\prod\aimxsrv\client\dll {6}
1>  1>[0:00:27.063] [Pass M] e:\os\src\onecore\ds\ds\src\aimx\prod\aimxsrv\powershell {8}
1>  3>[0:00:27.094] [Pass M] e:\os\src\onecore\ds\ds\src\aimx\prod\aimxsrv\serviceinstaller {10}
1>  2>[0:00:27.110] [Pass M] e:\os\src\onecore\ds\ds\src\aimx\prod\aimxsrv\server {7}
1>  1>[0:00:27.125] [Pass M] e:\os\src\onecore\ds\ds\src\aimx\prod\aimxsrv\dll {9}
ScanSourceDirectories: Total dirs files scanned = 2
Scanning focus directories completed in [0:00:00.250]
Processing time to write metadata log file .\buildfre.metadata = 0.016 Secs

BUILD: Scanning directories in defined scope...
Examining e:\os\src (directory tree)
ScanSourceDirectories: Total dirs files scanned = 9
BUILD: Processing dependencies...
1>  1>[0:00:27.219] [Pass M] e:\os\src\onecore\ds\ds\src\aimx\prod\mcpserversample {11}
1>  1>[0:00:27.235] [Pass M] e:\os\src\onecore\ds\ds\src\aimx\prod\mcpserversample {12}
1>  1>[0:00:29.219] [Pass M] e:\os\src\onecore\ds\ds\src\aimx\prod\cpprestsdk {13}
1>  1>[0:00:29.235] [Pass M] e:\os\src\onecore\ds\ds\src\aimx\prod\adpsmcpsvr {14}
1>  1>[0:00:29.266] [Pass M] e:\os\src\onecore\ds\ds\src\aimx\prod\adpsmcpsvr {15}
1>  1>[0:00:29.266] [Pass M] e:\os\src\onecore\ds\ds\src\aimx\prod\llmclientlib {16}
1>  1>[0:00:29.282] [Pass M] e:\os\src\onecore\ds\ds\src\aimx\prod\llmclientlib {17}
Pre-Build completed in [0:00:28.344]
1>  1>[0:00:29.657] [Pass0 ] e:\os\src\onecore\ds\ds\src\aimx\prod\aimxsrv\idl {18}
3001>BUILDMSG: Processing e:\os\src\onecore\ds\ds\src\aimx\prod\aimxsrv\idl
3001>Building generated files in e:\os\src\onecore\ds\ds\src\aimx\prod\aimxsrv\idl *************
3001>'e:\os\tools\NMakeRust\x64\bin\nmake.exe /nologo BUILDMSG=Stop. BUILD_PASS=PASS0 /nologo /f e:\os\src\tools\makefile.def NOLINK=1 PASS0ONLY=1 MAKEDIR_RELATIVE_TO_BASEDIR=onecore\ds\ds\src\aimx\prod\aimxsrv\idl TARGET_PLATFORM=windows CODE_SETS_BUILDING=cs_windows,cs_xbox,cs_phone CODE_SETS_TAGGED=cs_windows,cs_xbox'
1>  2>[0:00:29.657] [Pass0 ] e:\os\src\onecore\ds\ds\src\aimx\prod\aimxsrv\client\lib {19}
3002>BUILDMSG: Processing e:\os\src\onecore\ds\ds\src\aimx\prod\aimxsrv\client\lib
3002>Building generated files in e:\os\src\onecore\ds\ds\src\aimx\prod\aimxsrv\client\lib *************
3002>'e:\os\tools\NMakeRust\x64\bin\nmake.exe /nologo BUILDMSG=Stop. BUILD_PASS=PASS0 /nologo /f e:\os\src\tools\makefile.def NOLINK=1 PASS0ONLY=1 MAKEDIR_RELATIVE_TO_BASEDIR=onecore\ds\ds\src\aimx\prod\aimxsrv\client\lib TARGET_PLATFORM=windows CODE_SETS_BUILDING=cs_windows,cs_xbox,cs_phone CODE_SETS_TAGGED=cs_windows,cs_xbox'
1>  3>[0:00:29.657] [Pass0 ] e:\os\src\onecore\ds\ds\src\aimx\prod\aimxsrv\client\dll {20}
3003>BUILDMSG: Processing e:\os\src\onecore\ds\ds\src\aimx\prod\aimxsrv\client\dll
3003>Building generated files in e:\os\src\onecore\ds\ds\src\aimx\prod\aimxsrv\client\dll *************
3003>'e:\os\tools\NMakeRust\x64\bin\nmake.exe /nologo BUILDMSG=Stop. BUILD_PASS=PASS0 /nologo /f e:\os\src\tools\makefile.def NOLINK=1 PASS0ONLY=1 MAKEDIR_RELATIVE_TO_BASEDIR=onecore\ds\ds\src\aimx\prod\aimxsrv\client\dll TARGET_PLATFORM=windows CODE_SETS_BUILDING=cs_windows,cs_xbox,cs_phone CODE_SETS_TAGGED=cs_windows,cs_xbox'
3004>BUILDMSG: Processing e:\os\src\onecore\ds\ds\src\aimx\prod\aimxsrv\powershell
3004>Building generated files in e:\os\src\onecore\ds\ds\src\aimx\prod\aimxsrv\powershell *************
3004>'e:\os\tools\NMakeRust\x64\bin\nmake.exe /nologo BUILDMSG=Stop. BUILD_PASS=PASS0 /nologo /f e:\os\src\tools\makefile.def NOLINK=1 PASS0ONLY=1 MAKEDIR_RELATIVE_TO_BASEDIR=onecore\ds\ds\src\aimx\prod\aimxsrv\powershell TARGET_PLATFORM=windows CODE_SETS_BUILDING=cs_windows,cs_xbox,cs_phone CODE_SETS_TAGGED=cs_windows,cs_xbox'
1>  4>[0:00:29.657] [Pass0 ] e:\os\src\onecore\ds\ds\src\aimx\prod\aimxsrv\powershell {21}
1>  5>[0:00:29.657] [Pass0 ] e:\os\src\onecore\ds\ds\src\aimx\prod\aimxsrv\server {22}
3005>BUILDMSG: Processing e:\os\src\onecore\ds\ds\src\aimx\prod\aimxsrv\server
3005>Building generated files in e:\os\src\onecore\ds\ds\src\aimx\prod\aimxsrv\server *************
3005>'e:\os\tools\NMakeRust\x64\bin\nmake.exe /nologo BUILDMSG=Stop. BUILD_PASS=PASS0 /nologo /f e:\os\src\tools\makefile.def NOLINK=1 PASS0ONLY=1 MAKEDIR_RELATIVE_TO_BASEDIR=onecore\ds\ds\src\aimx\prod\aimxsrv\server TARGET_PLATFORM=windows CODE_SETS_BUILDING=cs_windows,cs_xbox,cs_phone CODE_SETS_TAGGED=cs_windows,cs_xbox'
1>  6>[0:00:29.672] [Pass0 ] e:\os\src\onecore\ds\ds\src\aimx\prod\aimxsrv\dll {23}
3006>BUILDMSG: Processing e:\os\src\onecore\ds\ds\src\aimx\prod\aimxsrv\dll
3006>Building generated files in e:\os\src\onecore\ds\ds\src\aimx\prod\aimxsrv\dll *************
3006>'e:\os\tools\NMakeRust\x64\bin\nmake.exe /nologo BUILDMSG=Stop. BUILD_PASS=PASS0 /nologo /f e:\os\src\tools\makefile.def NOLINK=1 PASS0ONLY=1 MAKEDIR_RELATIVE_TO_BASEDIR=onecore\ds\ds\src\aimx\prod\aimxsrv\dll TARGET_PLATFORM=windows CODE_SETS_BUILDING=cs_windows,cs_xbox,cs_phone CODE_SETS_TAGGED=cs_windows,cs_xbox'
1>  7>[0:00:29.672] [Pass0 ] e:\os\src\onecore\ds\ds\src\aimx\prod\mcpserversample {24}
3007>BUILDMSG: Processing e:\os\src\onecore\ds\ds\src\aimx\prod\mcpserversample
3007>Building generated files in e:\os\src\onecore\ds\ds\src\aimx\prod\mcpserversample *************
3007>'e:\os\tools\NMakeRust\x64\bin\nmake.exe /nologo BUILDMSG=Stop. BUILD_PASS=PASS0 /nologo /f e:\os\src\tools\makefile.def NOLINK=1 PASS0ONLY=1 MAKEDIR_RELATIVE_TO_BASEDIR=onecore\ds\ds\src\aimx\prod\mcpserversample TARGET_PLATFORM=windows CODE_SETS_BUILDING=cs_windows,cs_xbox,cs_phone CODE_SETS_TAGGED=cs_windows,cs_xbox'
1>  8>[0:00:29.672] [Pass0 ] e:\os\src\onecore\ds\ds\src\aimx\prod\adpsmcpsvr {25}
3008>BUILDMSG: Processing e:\os\src\onecore\ds\ds\src\aimx\prod\adpsmcpsvr
3008>Building generated files in e:\os\src\onecore\ds\ds\src\aimx\prod\adpsmcpsvr *************
3008>'e:\os\tools\NMakeRust\x64\bin\nmake.exe /nologo BUILDMSG=Stop. BUILD_PASS=PASS0 /nologo /f e:\os\src\tools\makefile.def NOLINK=1 PASS0ONLY=1 MAKEDIR_RELATIVE_TO_BASEDIR=onecore\ds\ds\src\aimx\prod\adpsmcpsvr TARGET_PLATFORM=windows CODE_SETS_BUILDING=cs_windows,cs_xbox,cs_phone CODE_SETS_TAGGED=cs_windows,cs_xbox'
1>BUILDC : Microsoft (R) Build Engine worker [Build 8.0.250519001+6b8e35f5c0ee29c4c18a354e33cc8b695d5695ad]
1>BUILDC : Copyright (C) Microsoft Corporation. All rights reserved.
1>BUILDC (BuildSocket): RegisterClient rupo-dell:0 (BuildC Worker)
1>BUILDC (BuildSocket): RegisterClient rupo-dell:0 (BuildC Controller)
1>BUILDC (BuildSocket): RegisterClient rupo-dell:0 (BuildC Logger)
1>BUILDC (IsHost): Buildc is running on : RUPO-DELL and host is : localhost.
1>BUILDC (BuildSocket): RegisterClient rupo-dell:0 (BuildC Worker)
1>BUILDC (PipeSpawn): c:\windows\system32\cmd.exe /c mergeconfigfilesforbuildexe.cmd  at (none).
1>BUILDC (BuildSocket): RegisterClient rupo-dell:0 (BuildC Worker)
1>BUILDC (BuildSocket): RegisterClient rupo-dell:0 (BuildC Worker)
1>BUILDC (PipeSpawn): e:\os\tools\NMakeRust\x64\bin\nmake.exe /nologo BUILDMSG=Stop. BUILD_PASS=PASS0 /nologo /f e:\os\src\tools\makefile.def NOLINK=1 PASS0ONLY=1 MAKEDIR_RELATIVE_TO_BASEDIR=onecore\ds\ds\src\aimx\prod\aimxsrv\idl TARGET_PLATFORM=windows CODE_SETS_BUILDING=cs_windows,cs_xbox,cs_phone CODE_SETS_TAGGED=cs_windows,cs_xbox THREAD_ID=1 at e:\os\src\onecore\ds\ds\src\aimx\prod\aimxsrv\idl.
1>BUILDC (PipeSpawn): e:\os\tools\NMakeRust\x64\bin\nmake.exe /nologo BUILDMSG=Stop. BUILD_PASS=PASS0 /nologo /f e:\os\src\tools\makefile.def NOLINK=1 PASS0ONLY=1 MAKEDIR_RELATIVE_TO_BASEDIR=onecore\ds\ds\src\aimx\prod\aimxsrv\client\lib TARGET_PLATFORM=windows CODE_SETS_BUILDING=cs_windows,cs_xbox,cs_phone CODE_SETS_TAGGED=cs_windows,cs_xbox THREAD_ID=2 at e:\os\src\onecore\ds\ds\src\aimx\prod\aimxsrv\client\lib.
1>BUILDC (PipeSpawn): e:\os\tools\NMakeRust\x64\bin\nmake.exe /nologo BUILDMSG=Stop. BUILD_PASS=PASS0 /nologo /f e:\os\src\tools\makefile.def NOLINK=1 PASS0ONLY=1 MAKEDIR_RELATIVE_TO_BASEDIR=onecore\ds\ds\src\aimx\prod\aimxsrv\client\dll TARGET_PLATFORM=windows CODE_SETS_BUILDING=cs_windows,cs_xbox,cs_phone CODE_SETS_TAGGED=cs_windows,cs_xbox THREAD_ID=3 at e:\os\src\onecore\ds\ds\src\aimx\prod\aimxsrv\client\dll.
1>BUILDC (BuildSocket): RegisterClient rupo-dell:0 (BuildC Worker)
1>BUILDC (PipeSpawn): e:\os\tools\NMakeRust\x64\bin\nmake.exe /nologo BUILDMSG=Stop. BUILD_PASS=PASS0 /nologo /f e:\os\src\tools\makefile.def NOLINK=1 PASS0ONLY=1 MAKEDIR_RELATIVE_TO_BASEDIR=onecore\ds\ds\src\aimx\prod\aimxsrv\powershell TARGET_PLATFORM=windows CODE_SETS_BUILDING=cs_windows,cs_xbox,cs_phone CODE_SETS_TAGGED=cs_windows,cs_xbox THREAD_ID=4 at e:\os\src\onecore\ds\ds\src\aimx\prod\aimxsrv\powershell.
1>BUILDC (BuildSocket): RegisterClient rupo-dell:0 (BuildC Worker)
1>BUILDC (PipeSpawn): e:\os\tools\NMakeRust\x64\bin\nmake.exe /nologo BUILDMSG=Stop. BUILD_PASS=PASS0 /nologo /f e:\os\src\tools\makefile.def NOLINK=1 PASS0ONLY=1 MAKEDIR_RELATIVE_TO_BASEDIR=onecore\ds\ds\src\aimx\prod\aimxsrv\server TARGET_PLATFORM=windows CODE_SETS_BUILDING=cs_windows,cs_xbox,cs_phone CODE_SETS_TAGGED=cs_windows,cs_xbox THREAD_ID=5 at e:\os\src\onecore\ds\ds\src\aimx\prod\aimxsrv\server.
1>BUILDC (PipeSpawn): e:\os\tools\NMakeRust\x64\bin\nmake.exe /nologo BUILDMSG=Stop. BUILD_PASS=PASS0 /nologo /f e:\os\src\tools\makefile.def NOLINK=1 PASS0ONLY=1 MAKEDIR_RELATIVE_TO_BASEDIR=onecore\ds\ds\src\aimx\prod\aimxsrv\dll TARGET_PLATFORM=windows CODE_SETS_BUILDING=cs_windows,cs_xbox,cs_phone CODE_SETS_TAGGED=cs_windows,cs_xbox THREAD_ID=6 at e:\os\src\onecore\ds\ds\src\aimx\prod\aimxsrv\dll.
1>BUILDC (BuildSocket): RegisterClient rupo-dell:0 (BuildC Worker)
1>BUILDC (PipeSpawn): e:\os\tools\NMakeRust\x64\bin\nmake.exe /nologo BUILDMSG=Stop. BUILD_PASS=PASS0 /nologo /f e:\os\src\tools\makefile.def NOLINK=1 PASS0ONLY=1 MAKEDIR_RELATIVE_TO_BASEDIR=onecore\ds\ds\src\aimx\prod\mcpserversample TARGET_PLATFORM=windows CODE_SETS_BUILDING=cs_windows,cs_xbox,cs_phone CODE_SETS_TAGGED=cs_windows,cs_xbox THREAD_ID=7 at e:\os\src\onecore\ds\ds\src\aimx\prod\mcpserversample.
1>BUILDC (BuildSocket): RegisterClient rupo-dell:0 (BuildC Worker)
1>BUILDC (BuildSocket): RegisterClient rupo-dell:0 (BuildC Worker)
1>BUILDC (PipeSpawn): e:\os\tools\NMakeRust\x64\bin\nmake.exe /nologo BUILDMSG=Stop. BUILD_PASS=PASS0 /nologo /f e:\os\src\tools\makefile.def NOLINK=1 PASS0ONLY=  9>[0:00:29.672] [Pass0 ] e:\os\src\onecore\ds\ds\src\aimx\prod\llmclientlib {26}
3009>BUILDMSG: Processing e:\os\src\onecore\ds\ds\src\aimx\prod\llmclientlib
3009>Building generated files in e:\os\src\onecore\ds\ds\src\aimx\prod\llmclientlib *************
3009>'e:\os\tools\NMakeRust\x64\bin\nmake.exe /nologo BUILDMSG=Stop. BUILD_PASS=PASS0 /nologo /f e:\os\src\tools\makefile.def NOLINK=1 PASS0ONLY=1 MAKEDIR_RELATIVE_TO_BASEDIR=onecore\ds\ds\src\aimx\prod\llmclientlib TARGET_PLATFORM=windows CODE_SETS_BUILDING=cs_windows,cs_xbox,cs_phone CODE_SETS_TAGGED=cs_windows,cs_xbox'
BUILD: (ActiveWorkLoad),29.42,,56,0,1,14,16,0,0,0,0,0,0,0,PASS0,0,2000000000,onecore\ds\ds\src\aimx\prod\aimxsrv\idl,RUPO-DELL
BUILD: (ActiveWorkLoad),29.42,,56,0,1,14,16,16,0,1,1,1,1,0,PASS0,0,2000000000,onecore\ds\ds\src\aimx\prod\aimxsrv\client\lib,RUPO-DELL
BUILD: (ActiveWorkLoad),29.42,,56,0,1,14,16,16,0,2,2,2,0,0,PASS0,0,2000000000,onecore\ds\ds\src\aimx\prod\aimxsrv\client\dll,RUPO-DELL
BUILD: (ActiveWorkLoad),29.42,,56,0,1,14,16,16,0,3,3,3,0,0,PASS0,0,2000000000,onecore\ds\ds\src\aimx\prod\aimxsrv\powershell,RUPO-DELL
BUILD: (ActiveWorkLoad),29.42,,56,0,1,14,16,16,0,4,4,4,0,0,PASS0,0,2000000000,onecore\ds\ds\src\aimx\prod\aimxsrv\server,RUPO-DELL
BUILD: (ActiveWorkLoad),29.44,,56,0,1,14,16,16,0,5,5,5,0,0,PASS0,0,2000000000,onecore\ds\ds\src\aimx\prod\aimxsrv\dll,RUPO-DELL
BUILD: (ActiveWorkLoad),29.44,,56,0,1,14,16,16,0,6,6,6,1,0,PASS0,0,2000000000,onecore\ds\ds\src\aimx\prod\mcpserversample,RUPO-DELL
BUILD: (ActiveWorkLoad),29.44,,56,0,1,14,16,16,0,7,7,7,1,0,PASS0,0,2000000000,onecore\ds\ds\src\aimx\prod\adpsmcpsvr,RUPO-DELL
BUILD: (ActiveWorkLoad),29.44,,56,0,1,14,16,16,0,8,8,8,0,0,PASS0,0,2000000000,onecore\ds\ds\src\aimx\prod\llmclientlib,RUPO-DELL
3002>Calculated LAYERINFO_MODULE='OneCoreDS'.
3002>makefile.def: TEMP=e:\os\obj\amd64fre\temp\7216e4cdb80254423ed40736e2139997
3002>makefile.def: BUILDINGINDATT=
3002>[Core OS Undocking] NOT using package ''
3002>UCRT enabled: dir 'e:\os\src\onecore\ds\ds\src\aimx\prod\aimxsrv\client\lib' (target 'aimxclient_s', type 'LIBRARY', nt_target_version '0xA000011')
3002>ObjectsMac.ts: validation succeeded
3002>STL version 120 used in "e:\os\src\onecore\ds\ds\src\aimx\prod\aimxsrv\client\lib" (STL_VER_TELEMETRY)
3002>_NEED_BUILDDATE not defined setting BUILDDATE to an invalid value.
3002>A subdirectory or file e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\aimxsrv\client\lib\objfre\amd64 already exists.
3002> e:\os\tools\Windows.Desktop.Tools.amd64\tools\touch.exe /c e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\aimxsrv\client\lib\objfre\amd64\_PASS0_Marker.log
3002> set BUILDMSG=WPP Processing: e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\aimxsrv\client\lib\objfre\amd64
3002> e:\os\tools\EtwTools\tools\tracewpp.exe  -q    -ext:.cpp.h.hxx                                                    -preserveext:.cpp.h.hxx                                            -scan:..\clientwpp.h                                             -DWPP_CHECK_INIT                                               -p:AIMXCLIENT                                                    -func:TraceCrit{LEVEL=TRACE_LEVEL_CRITICAL}(FLAGS,MSG,...)     -func:TraceErr{LEVEL=TRACE_LEVEL_ERROR}(FLAGS,MSG,...)         -func:TraceWarn{LEVEL=TRACE_LEVEL_WARNING}(FLAGS,MSG,...)      -func:TraceInfo{LEVEL=TRACE_LEVEL_INFORMATION}(FLAGS,MSG,...)  -func:TraceVerb{LEVEL=TRACE_LEVEL_VERBOSE}(FLAGS,MSG,...)      ..\aimxclient.cpp  ..\aimxrpcclient.cpp  ..\memory.cpp  -cfgdir:e:\os\tools\EtwTools\tools\WppConfig\rev1  -odir:e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\aimxsrv\client\lib\objfre\amd64
3002> c:\windows\system32\cmd.exe /c del e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\aimxsrv\client\lib\objfre\amd64\binplace_PASS0.rsp 2>nul
3002> e:\os\tools\powershell\pwsh.exe -NoProfile e:\os\src\tools\NMakeJS\WarningsCop\WarningsCop.ps1 -MakeSubDir "onecore\ds\ds\src\aimx\prod\aimxsrv\client\lib" -Pass PASS0 -BaselineFile e:\os\src\.config\OneCore\WarningsCop.json -OutputDir "e:\os\bin\amd64fre\evidence\WarningsCop\OneCore\onecoreds"
3002>WarningsCop.ps1 : WarningsCop: Processing onecore\ds\ds\src\aimx\prod\aimxsrv\client\lib in pass PASS0
3003>Calculated LAYERINFO_MODULE='OneCoreDS'.
3003>makefile.def: TEMP=e:\os\obj\amd64fre\temp\807d53068b56398b15189de9a3edbfac
3003>makefile.def: BUILDINGINDATT=
3003>[Core OS Undocking] NOT using package ''
3003>UCRT enabled: dir 'e:\os\src\onecore\ds\ds\src\aimx\prod\aimxsrv\client\dll' (target 'aimxclient', type 'DYNLINK', nt_target_version '0xA000011')
3003>ObjectsMac.ts: validation succeeded
3003>STL version 120 used in "e:\os\src\onecore\ds\ds\src\aimx\prod\aimxsrv\client\dll" (STL_VER_TELEMETRY)
3003>_NEED_BUILDDATE not defined setting BUILDDATE to an invalid value.
3003>A subdirectory or file e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\aimxsrv\client\dll\objfre\amd64 already exists.
3003> e:\os\tools\Windows.Desktop.Tools.amd64\tools\touch.exe /c e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\aimxsrv\client\dll\objfre\amd64\_PASS0_Marker.log
3003> set BUILDMSG=WPP Processing: e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\aimxsrv\client\dll\objfre\amd64
3003> e:\os\tools\EtwTools\tools\tracewpp.exe  -q    -ext:.cpp.h.hxx                                                    -preserveext:.cpp.h.hxx                                            -scan:..\clientwpp.h                                             -DWPP_CHECK_INIT                                               -p:AIMXCLIENT                                                    -func:TraceCrit{LEVEL=TRACE_LEVEL_CRITICAL}(FLAGS,MSG,...)     -func:TraceErr{LEVEL=TRACE_LEVEL_ERROR}(FLAGS,MSG,...)         -func:TraceWarn{LEVEL=TRACE_LEVEL_WARNING}(FLAGS,MSG,...)      -func:TraceInfo{LEVEL=TRACE_LEVEL_INFORMATION}(FLAGS,MSG,...)  -func:TraceVerb{LEVEL=TRACE_LEVEL_VERBOSE}(FLAGS,MSG,...)      ..\aimxclient.cpp  ..\aimxrpcclient.cpp  ..\memory.cpp  -cfgdir:e:\os\tools\EtwTools\tools\WppConfig\rev1  -odir:e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\aimxsrv\client\dll\objfre\amd64
3003> e:\os\tools\Windows.Desktop.Tools.amd64\tools\preprocessor.exe -o e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\aimxsrv\client\dll\objfre\amd64\mui.rcc -i e:\os\src\tools\mui.rcconfig
3003> c:\windows\system32\cmd.exe /c del e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\aimxsrv\client\dll\objfre\amd64\binplace_PASS0.rsp 2>nul
3003> e:\os\tools\powershell\pwsh.exe -NoProfile e:\os\src\tools\NMakeJS\WarningsCop\WarningsCop.ps1 -MakeSubDir "onecore\ds\ds\src\aimx\prod\aimxsrv\client\dll" -Pass PASS0 -BaselineFile e:\os\src\.config\OneCore\WarningsCop.json -OutputDir "e:\os\bin\amd64fre\evidence\WarningsCop\OneCore\onecoreds"
3003>WarningsCop.ps1 : WarningsCop: Processing onecore\ds\ds\src\aimx\prod\aimxsrv\client\dll in pass PASS0
3009>Calculated LAYERINFO_MODULE='OneCoreDS'.
3009>makefile.def: TEMP=e:\os\obj\amd64fre\temp\8c4c12f0ae6b16bbf93f153a929f33f6
3009>makefile.def: BUILDINGINDATT=
3009>[Core OS Undocking] NOT using package ''
3009>UCRT enabled: dir 'e:\os\src\onecore\ds\ds\src\aimx\prod\llmclientlib' (target 'llmclientlib', type 'LIBRARY', nt_target_version '0xA000011')
3009>ObjectsMac.ts: validation succeeded
3009>STL version 120 used in "e:\os\src\onecore\ds\ds\src\aimx\prod\llmclientlib" (STL_VER_TELEMETRY)
3009>_NEED_BUILDDATE not defined setting BUILDDATE to an invalid value.
3009>A subdirectory or file e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\llmclientlib\objfre\amd64 already exists.
3009> e:\os\tools\Windows.Desktop.Tools.amd64\tools\touch.exe /c e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\llmclientlib\objfre\amd64\_PASS0_Marker.log
3009> set BUILDMSG=WPP Processing: e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\llmclientlib\objfre\amd64
3009> e:\os\tools\EtwTools\tools\tracewpp.exe  -q    -ext:.cpp.h.hxx                                                    -preserveext:.cpp.h.hxx                                            -scan:..\aimxsrv\inc\wpp.h                                             -DWPP_CHECK_INIT                                               -p:llmclientlib                                                    -func:TraceCrit{LEVEL=TRACE_LEVEL_CRITICAL}(FLAGS,MSG,...)     -func:TraceErr{LEVEL=TRACE_LEVEL_ERROR}(FLAGS,MSG,...)         -func:TraceWarn{LEVEL=TRACE_LEVEL_WARNING}(FLAGS,MSG,...)      -func:TraceInfo{LEVEL=TRACE_LEVEL_INFORMATION}(FLAGS,MSG,...)  -func:TraceVerb{LEVEL=TRACE_LEVEL_VERBOSE}(FLAGS,MSG,...)       llmclientlib.cpp  -cfgdir:e:\os\tools\EtwTools\tools\WppConfig\rev1  -odir:e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\llmclientlib\objfre\amd64
3009> c:\windows\system32\cmd.exe /c del e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\llmclientlib\objfre\amd64\binplace_PASS0.rsp 2>nul
3009> e:\os\tools\powershell\pwsh.exe -NoProfile e:\os\src\tools\NMakeJS\WarningsCop\WarningsCop.ps1 -MakeSubDir "onecore\ds\ds\src\aimx\prod\llmclientlib" -Pass PASS0 -BaselineFile e:\os\src\.config\OneCore\WarningsCop.json -OutputDir "e:\os\bin\amd64fre\evidence\WarningsCop\OneCore\onecoreds"
3009>WarningsCop.ps1 : WarningsCop: Processing onecore\ds\ds\src\aimx\prod\llmclientlib in pass PASS0
3007>Calculated LAYERINFO_MODULE='OneCoreDS'.
3007>makefile.def: TEMP=e:\os\obj\amd64fre\temp\b95b10c95c889e947a12e035b37a102b
3007>makefile.def: BUILDINGINDATT=
3007>[Core OS Undocking] NOT using package ''
3007>UCRT enabled: dir 'e:\os\src\onecore\ds\ds\src\aimx\prod\mcpserversample' (target 'HelloMcpServer', type 'LIBRARY', nt_target_version '0xA000011')
3007>ObjectsMac.ts: validation succeeded
3007>STL version 120 used in "e:\os\src\onecore\ds\ds\src\aimx\prod\mcpserversample" (STL_VER_TELEMETRY)
3007>_NEED_BUILDDATE not defined setting BUILDDATE to an invalid value.
3007>A subdirectory or file e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\mcpserversample\objfre\amd64 already exists.
3007> e:\os\tools\Windows.Desktop.Tools.amd64\tools\touch.exe /c e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\mcpserversample\objfre\amd64\_PASS0_Marker.log
3007> set BUILDMSG=WPP Processing: e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\mcpserversample\objfre\amd64
3007> e:\os\tools\EtwTools\tools\tracewpp.exe  -q    -ext:.cpp.h.hxx                                                                  -preserveext:.cpp.h.hxx                                                          -scan:..\aimxsrv\inc\wpp.h                                                    -DWPP_CHECK_INIT                                                                 -p:AIMXMCPSRV                                                                    -func:TraceCrit{LEVEL=TRACE_LEVEL_CRITICAL}(FLAGS,MSG,...)                       -func:TraceErr{LEVEL=TRACE_LEVEL_ERROR}(FLAGS,MSG,...)                           -func:TraceWarn{LEVEL=TRACE_LEVEL_WARNING}(FLAGS,MSG,...)                        -func:TraceInfo{LEVEL=TRACE_LEVEL_INFORMATION}(FLAGS,MSG,...)                    -func:TraceVerb{LEVEL=TRACE_LEVEL_VERBOSE}(FLAGS,MSG,...)                         HelloMcpServer.cpp  -cfgdir:e:\os\tools\EtwTools\tools\WppConfig\rev1  -odir:e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\mcpserversample\objfre\amd64
3007> e:\os\tools\cppwinrt\cppwinrt.exe @e:\os\obj\amd64fre\temp\b95b10c95c889e947a12e035b37a102b\tmp_30344_1752613371219766600.tmp
3007> c:\windows\system32\cmd.exe /c del e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\mcpserversample\objfre\amd64\binplace_PASS0.rsp 2>nul
3007> e:\os\tools\powershell\pwsh.exe -NoProfile e:\os\src\tools\NMakeJS\WarningsCop\WarningsCop.ps1 -MakeSubDir "onecore\ds\ds\src\aimx\prod\mcpserversample" -Pass PASS0 -BaselineFile e:\os\src\.config\OneCore\WarningsCop.json -OutputDir "e:\os\bin\amd64fre\evidence\WarningsCop\OneCore\onecoreds"
3007>WarningsCop.ps1 : WarningsCop: Processing onecore\ds\ds\src\aimx\prod\mcpserversample in pass PASS0
3005>Calculated LAYERINFO_MODULE='OneCoreDS'.
3005>makefile.def: TEMP=e:\os\obj\amd64fre\temp\d59743811b06aa0fbe90529143503c1f
3005>makefile.def: BUILDINGINDATT=
3005>[Core OS Undocking] NOT using package ''
3005>UCRT enabled: dir 'e:\os\src\onecore\ds\ds\src\aimx\prod\aimxsrv\server' (target 'aimxserver', type 'LIBRARY', nt_target_version '0xA000011')
3005>ObjectsMac.ts: validation succeeded
3005>STL version 120 used in "e:\os\src\onecore\ds\ds\src\aimx\prod\aimxsrv\server" (STL_VER_TELEMETRY)
3005>_NEED_BUILDDATE not defined setting BUILDDATE to an invalid value.
3005>A subdirectory or file e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\aimxsrv\server\objfre\amd64 already exists.
3005> e:\os\tools\Windows.Desktop.Tools.amd64\tools\touch.exe /c e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\aimxsrv\server\objfre\amd64\_PASS0_Marker.log
3005> set BUILDMSG=WPP Processing: e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\aimxsrv\server\objfre\amd64
3005> e:\os\tools\EtwTools\tools\tracewpp.exe  -q    -ext:.cpp.h.hxx                                                    -preserveext:.cpp.h.hxx                                            -scan:..\inc\wpp.h                                             -DWPP_CHECK_INIT                                               -p:AIMXSRV                                                    -func:TraceCrit{LEVEL=TRACE_LEVEL_CRITICAL}(FLAGS,MSG,...)     -func:TraceErr{LEVEL=TRACE_LEVEL_ERROR}(FLAGS,MSG,...)         -func:TraceWarn{LEVEL=TRACE_LEVEL_WARNING}(FLAGS,MSG,...)      -func:TraceInfo{LEVEL=TRACE_LEVEL_INFORMATION}(FLAGS,MSG,...)  -func:TraceVerb{LEVEL=TRACE_LEVEL_VERBOSE}(FLAGS,MSG,...)      aimxrpcserver.cpp  RequestHandler.cpp  Planner.cpp  Orchestrator.cpp  McpStdioClient.cpp  McpSvrMgr.cpp  McpToolManager.cpp  LLMInfer.cpp  AimxLLMConfig.cpp  InProcessMCPServerBase.cpp  InprocessMcpUtils.cpp  SystemPromptManager.cpp  ConversationManager.cpp  -cfgdir:e:\os\tools\EtwTools\tools\WppConfig\rev1  -odir:e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\aimxsrv\server\objfre\amd64
3005> e:\os\tools\cppwinrt\cppwinrt.exe @e:\os\obj\amd64fre\temp\d59743811b06aa0fbe90529143503c1f\tmp_19236_1752613371317755300.tmp
3005> c:\windows\system32\cmd.exe /c del e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\aimxsrv\server\objfre\amd64\binplace_PASS0.rsp 2>nul
3005> e:\os\tools\powershell\pwsh.exe -NoProfile e:\os\src\tools\NMakeJS\WarningsCop\WarningsCop.ps1 -MakeSubDir "onecore\ds\ds\src\aimx\prod\aimxsrv\server" -Pass PASS0 -BaselineFile e:\os\src\.config\OneCore\WarningsCop.json -OutputDir "e:\os\bin\amd64fre\evidence\WarningsCop\OneCore\onecoreds"
3005>WarningsCop.ps1 : WarningsCop: Processing onecore\ds\ds\src\aimx\prod\aimxsrv\server in pass PASS0
3006>Calculated LAYERINFO_MODULE='OneCoreDS'.
3006>makefile.def: TEMP=e:\os\obj\amd64fre\temp\e981abf1048ccb43409f18cba08f549a
3006>makefile.def: BUILDINGINDATT=
3006>[Core OS Undocking] NOT using package ''
3006>UCRT enabled: dir 'e:\os\src\onecore\ds\ds\src\aimx\prod\aimxsrv\dll' (target 'aimxsrv', type 'DYNLINK', nt_target_version '0xA000011')
3006>ObjectsMac.ts: validation succeeded
3006>STL version 120 used in "e:\os\src\onecore\ds\ds\src\aimx\prod\aimxsrv\dll" (STL_VER_TELEMETRY)
3006>_NEED_BUILDDATE not defined setting BUILDDATE to an invalid value.
3006>A subdirectory or file e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\aimxsrv\dll\objfre\amd64 already exists.
3006> e:\os\tools\Windows.Desktop.Tools.amd64\tools\touch.exe /c e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\aimxsrv\dll\objfre\amd64\_PASS0_Marker.log
3006> set BUILDMSG=WPP Processing: e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\aimxsrv\dll\objfre\amd64
3006> e:\os\tools\EtwTools\tools\tracewpp.exe  -q    -ext:.cpp.h.hxx                                                -preserveext:.cpp.h.hxx                                        -scan:..\inc\wpp.h                                             -DWPP_CHECK_INIT                                               -p:AIMXSRV                                                     -dll DllMain                                                   -func:TraceCrit{LEVEL=TRACE_LEVEL_CRITICAL}(FLAGS,MSG,...)     -func:TraceErr{LEVEL=TRACE_LEVEL_ERROR}(FLAGS,MSG,...)         -func:TraceWarn{LEVEL=TRACE_LEVEL_WARNING}(FLAGS,MSG,...)      -func:TraceInfo{LEVEL=TRACE_LEVEL_INFORMATION}(FLAGS,MSG,...)  -func:TraceVerb{LEVEL=TRACE_LEVEL_VERBOSE}(FLAGS,MSG,...)       dllmain.cpp  aimxservice.cpp  -cfgdir:e:\os\tools\EtwTools\tools\WppConfig\rev1  -odir:e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\aimxsrv\dll\objfre\amd64
3006> e:\os\tools\cppwinrt\cppwinrt.exe @e:\os\obj\amd64fre\temp\e981abf1048ccb43409f18cba08f549a\tmp_24032_1752613371235632200.tmp
3006> e:\os\tools\Windows.Desktop.Tools.amd64\tools\preprocessor.exe -o e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\aimxsrv\dll\objfre\amd64\mui.rcc -i e:\os\src\tools\mui.rcconfig
3006> c:\windows\system32\cmd.exe /c del e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\aimxsrv\dll\objfre\amd64\binplace_PASS0.rsp 2>nul
3006> e:\os\tools\powershell\pwsh.exe -NoProfile e:\os\src\tools\NMakeJS\WarningsCop\WarningsCop.ps1 -MakeSubDir "onecore\ds\ds\src\aimx\prod\aimxsrv\dll" -Pass PASS0 -BaselineFile e:\os\src\.config\OneCore\WarningsCop.json -OutputDir "e:\os\bin\amd64fre\evidence\WarningsCop\OneCore\onecoreds"
3006>WarningsCop.ps1 : WarningsCop: Processing onecore\ds\ds\src\aimx\prod\aimxsrv\dll in pass PASS0
3008>Calculated LAYERINFO_MODULE='OneCoreDS'.
3008>makefile.def: TEMP=e:\os\obj\amd64fre\temp\7744ed1ff48a7792ccfb3481d62f10f2
3008>makefile.def: BUILDINGINDATT=
3008>[Core OS Undocking] NOT using package ''
3008>UCRT enabled: dir 'e:\os\src\onecore\ds\ds\src\aimx\prod\adpsmcpsvr' (target 'AdPsMcpSvr', type 'LIBRARY', nt_target_version '0xA000011')
3008>ObjectsMac.ts: validation succeeded
3008>STL version 120 used in "e:\os\src\onecore\ds\ds\src\aimx\prod\adpsmcpsvr" (STL_VER_TELEMETRY)
3008>_NEED_BUILDDATE not defined setting BUILDDATE to an invalid value.
3008>A subdirectory or file e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\adpsmcpsvr\objfre\amd64 already exists.
3008> e:\os\tools\Windows.Desktop.Tools.amd64\tools\touch.exe /c e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\adpsmcpsvr\objfre\amd64\_PASS0_Marker.log
3008> set BUILDMSG=WPP Processing: e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\adpsmcpsvr\objfre\amd64
3008> e:\os\tools\EtwTools\tools\tracewpp.exe  -q    -ext:.cpp.h.hxx                                                                  -preserveext:.cpp.h.hxx                                                          -scan:..\aimxsrv\inc\wpp.h                                                    -DWPP_CHECK_INIT                                                                 -p:AIMXADPSMCPSRV                                                                -func:TraceCrit{LEVEL=TRACE_LEVEL_CRITICAL}(FLAGS,MSG,...)                       -func:TraceErr{LEVEL=TRACE_LEVEL_ERROR}(FLAGS,MSG,...)                           -func:TraceWarn{LEVEL=TRACE_LEVEL_WARNING}(FLAGS,MSG,...)                        -func:TraceInfo{LEVEL=TRACE_LEVEL_INFORMATION}(FLAGS,MSG,...)                    -func:TraceVerb{LEVEL=TRACE_LEVEL_VERBOSE}(FLAGS,MSG,...)                         AdPsMcpSvr.cpp  PowerShellExecutor.cpp  -cfgdir:e:\os\tools\EtwTools\tools\WppConfig\rev1  -odir:e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\adpsmcpsvr\objfre\amd64
3008> e:\os\tools\cppwinrt\cppwinrt.exe @e:\os\obj\amd64fre\temp\7744ed1ff48a7792ccfb3481d62f10f2\tmp_25840_1752613371231875900.tmp
3008> c:\windows\system32\cmd.exe /c del e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\adpsmcpsvr\objfre\amd64\binplace_PASS0.rsp 2>nul
3008> e:\os\tools\powershell\pwsh.exe -NoProfile e:\os\src\tools\NMakeJS\WarningsCop\WarningsCop.ps1 -MakeSubDir "onecore\ds\ds\src\aimx\prod\adpsmcpsvr" -Pass PASS0 -BaselineFile e:\os\src\.config\OneCore\WarningsCop.json -OutputDir "e:\os\bin\amd64fre\evidence\WarningsCop\OneCore\onecoreds"
3008>WarningsCop.ps1 : WarningsCop: Processing onecore\ds\ds\src\aimx\prod\adpsmcpsvr in pass PASS0
3001>Calculated LAYERINFO_MODULE='OneCoreDS'.
3001>makefile.def: TEMP=e:\os\obj\amd64fre\temp\e8a505a068b0611debb1eb50894424af
3001>makefile.def: BUILDINGINDATT=
3001>[Core OS Undocking] NOT using package ''
3001>ObjectsMac.ts: validation succeeded
3001>_NEED_BUILDDATE not defined setting BUILDDATE to an invalid value.
3001>A subdirectory or file e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\aimxsrv\idl\objfre\amd64 already exists.
3001> e:\os\tools\Windows.Desktop.Tools.amd64\tools\touch.exe /c e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\aimxsrv\idl\objfre\amd64\_PASS0_Marker.log
3001> e:\os\tools\MidlCompiler\tools\midl.exe  -sal /amd64  -target NT1012 -oldnames -out e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\aimxsrv\idl\objfre\amd64 -Ie:\os\public\amd64fre\sdk\inc -Ie:\os\public\amd64fre\onecore\external\sdk\inc\MinCore -Ie:\os\public\amd64fre\shared\inc -Ie:\os\public\amd64fre\onecore\external\shared\inc /prefix client "c_" /prefix server "s_" /export aimxrpc.idl
3001>Microsoft (R) 32b/64b MIDL Compiler Version 8.01.0628 
3001>Copyright (c) Microsoft Corporation. All rights reserved.
3001>64 bit Processing .\aimxrpc.idl
3001>aimxrpc.idl
3001>64 bit Processing e:\os\public\amd64fre\onecore\external\shared\inc\wtypes.idl
3001>wtypes.idl
3001>64 bit Processing e:\os\public\amd64fre\onecore\external\shared\inc\wtypesbase.idl
3001>wtypesbase.idl
3001>64 bit Processing e:\os\public\amd64fre\onecore\external\shared\inc\basetsd.h
3001>basetsd.h
3001>64 bit Processing e:\os\public\amd64fre\onecore\external\shared\inc\guiddef.h
3001>guiddef.h
3001>64 bit Processing .\aimxrpc.acf
3001>aimxrpc.acf
3001> c:\windows\system32\cmd.exe /c del e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\aimxsrv\idl\objfre\amd64\binplace_PASS0.rsp 2>nul
3001> e:\os\tools\powershell\pwsh.exe -NoProfile e:\os\src\tools\NMakeJS\WarningsCop\WarningsCop.ps1 -MakeSubDir "onecore\ds\ds\src\aimx\prod\aimxsrv\idl" -Pass PASS0 -BaselineFile e:\os\src\.config\OneCore\WarningsCop.json -OutputDir "e:\os\bin\amd64fre\evidence\WarningsCop\OneCore\onecoreds"
3001>WarningsCop.ps1 : WarningsCop: Processing onecore\ds\ds\src\aimx\prod\aimxsrv\idl in pass PASS0
3004>Calculated LAYERINFO_MODULE='OneCoreDS'.
3004>makefile.def: TEMP=e:\os\obj\amd64fre\temp\2aa30289f1eaf89a53c50b830c06ca9d
3004>makefile.def: BUILDINGINDATT=
3004>[Core OS Undocking] NOT using package ''
3004>ObjectsMac.ts: validation succeeded
3004>Starting recursive call to NMAKE for _MAKING_ASMID_INC
3004>Starting _MAKING_ASMID_INC
3004>Calculated LAYERINFO_MODULE='OneCoreDS'.
3004>makefile.def: TEMP=e:\os\obj\amd64fre\temp\2aa30289f1eaf89a53c50b830c06ca9d
3004>makefile.def: BUILDINGINDATT=
3004>[Core OS Undocking] NOT using package ''
3004>ObjectsMac.ts: _objects.mac is not needed; macro validation will be skipped.
3004>_NEED_BUILDDATE not defined setting BUILDDATE to an invalid value.
3004>Ending _MAKING_ASMID_INC
3004> set BUILDMSG=making _asmid.inc
3004> c:\windows\system32\cmd.exe /c del e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\aimxsrv\powershell\objfre\amd64\_asmid.inc e:\os\obj\amd64fre\temp\2aa30289f1eaf89a53c50b830c06ca9d\_asmid.inc 2>nul
3004> e:\os\tools\LegacyTools\x86\idtool.exe -id aimxpsh,processorArchitecture=msil,version=10.0.0.0,publicKeyToken=31bf3856ad364e35 -GenerateMakefileInc -out e:\os\obj\amd64fre\temp\2aa30289f1eaf89a53c50b830c06ca9d\_asmid.inc
3004> e:\os\src\tools\onecoreuap\internal\AsmIdToAttribs\asmIdToAttribs.cmd "aimxpsh,processorArchitecture=msil,version=10.0.0.0,publicKeyToken=31bf3856ad364e35" e:\os\obj\amd64fre\temp\2aa30289f1eaf89a53c50b830c06ca9d\_asmid.inc tlbimp
3004> e:\os\src\tools\onecoreuap\internal\AsmIdToAttribs\asmIdToAttribs.cmd "aimxpsh,processorArchitecture=msil,version=10.0.0.0,publicKeyToken=31bf3856ad364e35" e:\os\obj\amd64fre\temp\2aa30289f1eaf89a53c50b830c06ca9d\_asmid.inc sn
3004> c:\windows\system32\cmd.exe /c echo _ASSEMBLY_IDENTITY=aimxpsh,processorArchitecture=msil,version=10.0.0.0,publicKeyToken=31bf3856ad364e35 >> e:\os\obj\amd64fre\temp\2aa30289f1eaf89a53c50b830c06ca9d\_asmid.inc
3004> c:\windows\system32\cmd.exe /c copy e:\os\obj\amd64fre\temp\2aa30289f1eaf89a53c50b830c06ca9d\_asmid.inc e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\aimxsrv\powershell\objfre\amd64\_asmid.inc >nul
3004>Finished recursively calling NMAKE for _MAKING_ASMID_INC
3004>_NEED_BUILDDATE not defined setting BUILDDATE to an invalid value.
3004>A subdirectory or file e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\aimxsrv\powershell\objfre\amd64 already exists.
3004> e:\os\tools\Windows.Desktop.Tools.amd64\tools\touch.exe /c e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\aimxsrv\powershell\objfre\amd64\_PASS0_Marker.log
3004> set BUILDMSG=making _asmid.xml
3004> c:\windows\system32\cmd.exe /c del e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\aimxsrv\powershell\objfre\amd64\_asmid.xml e:\os\obj\amd64fre\temp\2aa30289f1eaf89a53c50b830c06ca9d\_asmid.xml 2>nul
3004> e:\os\tools\LegacyTools\x86\idtool.exe -id aimxpsh,processorArchitecture=msil,version=10.0.0.0,publicKeyToken=31bf3856ad364e35 -EmptyComponentManifest -out e:\os\obj\amd64fre\temp\2aa30289f1eaf89a53c50b830c06ca9d\_asmid.xml
3004> c:\windows\system32\cmd.exe /c copy e:\os\obj\amd64fre\temp\2aa30289f1eaf89a53c50b830c06ca9d\_asmid.xml e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\aimxsrv\powershell\objfre\amd64\_asmid.xml >nul
3004> c:\windows\system32\cmd.exe /c del e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\aimxsrv\powershell\objfre\amd64\binplace_PASS0.rsp 2>nul
3004> e:\os\tools\powershell\pwsh.exe -NoProfile e:\os\src\tools\NMakeJS\WarningsCop\WarningsCop.ps1 -MakeSubDir "onecore\ds\ds\src\aimx\prod\aimxsrv\powershell" -Pass PASS0 -BaselineFile e:\os\src\.config\OneCore\WarningsCop.json -OutputDir "e:\os\bin\amd64fre\evidence\WarningsCop\OneCore\onecoreds"
3004>WarningsCop.ps1 : WarningsCop: Processing onecore\ds\ds\src\aimx\prod\aimxsrv\powershell in pass PASS0
BUILD: Pass complete => PASS0
1>  1>[0:00:33.719] [Pass1 ] e:\os\src\onecore\ds\ds\src\aimx\prod\aimxsrv\client\lib {27}
3201>BUILDMSG: Processing e:\os\src\onecore\ds\ds\src\aimx\prod\aimxsrv\client\lib
3201>Compiling e:\os\src\onecore\ds\ds\src\aimx\prod\aimxsrv\client\lib *************
3201>'e:\os\tools\NMakeRust\x64\bin\nmake.exe /nologo BUILDMSG=Stop. /nologo /f e:\os\src\tools\makefile.def BUILD_PASS=PASS1 NOLINK=1 MAKEDIR_RELATIVE_TO_BASEDIR=onecore\ds\ds\src\aimx\prod\aimxsrv\client\lib TARGET_PLATFORM=windows CODE_SETS_BUILDING=cs_windows,cs_xbox,cs_phone CODE_SETS_TAGGED=cs_windows,cs_xbox'
1>  2>[0:00:33.719] [Pass1 ] e:\os\src\onecore\ds\ds\src\aimx\prod\aimxsrv\client\dll {28}
3202>BUILDMSG: Processing e:\os\src\onecore\ds\ds\src\aimx\prod\aimxsrv\client\dll
3202>Compiling e:\os\src\onecore\ds\ds\src\aimx\prod\aimxsrv\client\dll *************
3202>'e:\os\tools\NMakeRust\x64\bin\nmake.exe /nologo BUILDMSG=Stop. /nologo /f e:\os\src\tools\makefile.def BUILD_PASS=PASS1 NOLINK=1 MAKEDIR_RELATIVE_TO_BASEDIR=onecore\ds\ds\src\aimx\prod\aimxsrv\client\dll TARGET_PLATFORM=windows CODE_SETS_BUILDING=cs_windows,cs_xbox,cs_phone CODE_SETS_TAGGED=cs_windows,cs_xbox'
1>  3>[0:00:33.719] [Pass1 ] e:\os\src\onecore\ds\ds\src\aimx\prod\aimxsrv\powershell {29}
1>  4>[0:00:33.719] [Pass1 ] e:\os\src\onecore\ds\ds\src\aimx\prod\aimxsrv\serviceinstaller {30}
1>  5>[0:00:33.719] [Pass1 ] e:\os\src\onecore\ds\ds\src\aimx\prod\aimxsrv\dll {31}
1>  6>[0:00:33.719] [Pass1 ] e:\os\src\onecore\ds\ds\src\aimx\prod\mcpserversample {32}
1>  7>[0:00:33.719] [Pass1 ] e:\os\src\onecore\ds\ds\src\aimx\prod\cpprestsdk {33}
3205>BUILDMSG: Processing e:\os\src\onecore\ds\ds\src\aimx\prod\aimxsrv\dll
3205>Compiling e:\os\src\onecore\ds\ds\src\aimx\prod\aimxsrv\dll *************
3205>'e:\os\tools\NMakeRust\x64\bin\nmake.exe /nologo BUILDMSG=Stop. /nologo /f e:\os\src\tools\makefile.def BUILD_PASS=PASS1 NOLINK=1 MAKEDIR_RELATIVE_TO_BASEDIR=onecore\ds\ds\src\aimx\prod\aimxsrv\dll TARGET_PLATFORM=windows CODE_SETS_BUILDING=cs_windows,cs_xbox,cs_phone CODE_SETS_TAGGED=cs_windows,cs_xbox'
3204>BUILDMSG: Processing e:\os\src\onecore\ds\ds\src\aimx\prod\aimxsrv\serviceinstaller
3204>Compiling e:\os\src\onecore\ds\ds\src\aimx\prod\aimxsrv\serviceinstaller *************
3204>'e:\os\tools\NMakeRust\x64\bin\nmake.exe /nologo BUILDMSG=Stop. /nologo /f e:\os\src\tools\makefile.def BUILD_PASS=PASS1 NOLINK=1 MAKEDIR_RELATIVE_TO_BASEDIR=onecore\ds\ds\src\aimx\prod\aimxsrv\serviceinstaller TARGET_PLATFORM=windows CODE_SETS_BUILDING=cs_windows,cs_xbox,cs_phone CODE_SETS_TAGGED=cs_windows,cs_xbox'
3203>BUILDMSG: Processing e:\os\src\onecore\ds\ds\src\aimx\prod\aimxsrv\powershell
3203>Compiling e:\os\src\onecore\ds\ds\src\aimx\prod\aimxsrv\powershell *************
3203>'e:\os\tools\NMakeRust\x64\bin\nmake.exe /nologo BUILDMSG=Stop. /nologo /f e:\os\src\tools\makefile.def BUILD_PASS=PASS1 NOLINK=1 MAKEDIR_RELATIVE_TO_BASEDIR=onecore\ds\ds\src\aimx\prod\aimxsrv\powershell TARGET_PLATFORM=windows CODE_SETS_BUILDING=cs_windows,cs_xbox,cs_phone CODE_SETS_TAGGED=cs_windows,cs_xbox'
3206>BUILDMSG: Processing e:\os\src\onecore\ds\ds\src\aimx\prod\mcpserversample
3206>Compiling e:\os\src\onecore\ds\ds\src\aimx\prod\mcpserversample *************
3206>'e:\os\tools\NMakeRust\x64\bin\nmake.exe /nologo BUILDMSG=Stop. /nologo /f e:\os\src\tools\makefile.def BUILD_PASS=PASS1 NOLINK=1 MAKEDIR_RELATIVE_TO_BASEDIR=onecore\ds\ds\src\aimx\prod\mcpserversample TARGET_PLATFORM=windows CODE_SETS_BUILDING=cs_windows,cs_xbox,cs_phone CODE_SETS_TAGGED=cs_windows,cs_xbox'
3207>BUILDMSG: Processing e:\os\src\onecore\ds\ds\src\aimx\prod\cpprestsdk
3207>Executing Pass1 MSBuild Tasks e:\os\src\onecore\ds\ds\src\aimx\prod\cpprestsdk *************
3207>'msbuild.cmd "vcpkg.proj" /nologo /p:BuildingInSeparatePasses=true /p:BuildingWithBuildExe=true /clp:NoSummary /verbosity:normal /Target:BuildCompiled /p:Pass=Compile /p:ObjectPath=e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\cpprestsdk\ /p:TARGET_PLATFORM=windows /p:CODE_SETS_BUILDING=cs_windows:cs_xbox:cs_phone /p:CODE_SETS_TAGGED=cs_windows:cs_xbox'
1>  8>[0:00:33.735] [Pass1 ] e:\os\src\onecore\ds\ds\src\aimx\prod\adpsmcpsvr {34}
3208>BUILDMSG: Processing e:\os\src\onecore\ds\ds\src\aimx\prod\adpsmcpsvr
3208>Compiling e:\os\src\onecore\ds\ds\src\aimx\prod\adpsmcpsvr *************
3208>'e:\os\tools\NMakeRust\x64\bin\nmake.exe /nologo BUILDMSG=Stop. /nologo /f e:\os\src\tools\makefile.def BUILD_PASS=PASS1 NOLINK=1 MAKEDIR_RELATIVE_TO_BASEDIR=onecore\ds\ds\src\aimx\prod\adpsmcpsvr TARGET_PLATFORM=windows CODE_SETS_BUILDING=cs_windows,cs_xbox,cs_phone CODE_SETS_TAGGED=cs_windows,cs_xbox'
1>  9>[0:00:33.735] [Pass1 ] e:\os\src\onecore\ds\ds\src\aimx\prod\llmclientlib {35}
3209>BUILDMSG: Processing e:\os\src\onecore\ds\ds\src\aimx\prod\llmclientlib
3209>Compiling e:\os\src\onecore\ds\ds\src\aimx\prod\llmclientlib *************
3209>'e:\os\tools\NMakeRust\x64\bin\nmake.exe /nologo BUILDMSG=Stop. /nologo /f e:\os\src\tools\makefile.def BUILD_PASS=PASS1 NOLINK=1 MAKEDIR_RELATIVE_TO_BASEDIR=onecore\ds\ds\src\aimx\prod\llmclientlib TARGET_PLATFORM=windows CODE_SETS_BUILDING=cs_windows,cs_xbox,cs_phone CODE_SETS_TAGGED=cs_windows,cs_xbox'
1>1 MAKEDIR_RELATIVE_TO_BASEDIR=onecore\ds\ds\src\aimx\prod\adpsmcpsvr TARGET_PLATFORM=windows CODE_SETS_BUILDING=cs_windows,cs_xbox,cs_phone CODE_SETS_TAGGED=cs_windows,cs_xbox THREAD_ID=8 at e:\os\src\onecore\ds\ds\src\aimx\prod\adpsmcpsvr.
1>BUILDC (BuildSocket): RegisterClient rupo-dell:0 (BuildC Worker)
1>BUILDC (PipeSpawn): e:\os\tools\NMakeRust\x64\bin\nmake.exe /nologo BUILDMSG=Stop. BUILD_PASS=PASS0 /nologo /f e:\os\src\tools\makefile.def NOLINK=1 PASS0ONLY=1 MAKEDIR_RELATIVE_TO_BASEDIR=onecore\ds\ds\src\aimx\prod\llmclientlib TARGET_PLATFORM=windows CODE_SETS_BUILDING=cs_windows,cs_xbox,cs_phone CODE_SETS_TAGGED=cs_windows,cs_xbox THREAD_ID=9 at e:\os\src\onecore\ds\ds\src\aimx\prod\llmclientlib.
1>BUILDC (PipeSpawn): e:\os\tools\NMakeRust\x64\bin\nmake.exe /nologo BUILDMSG=Stop. /nologo /f e:\os\src\tools\makefile.def BUILD_PASS=PASS1 NOLINK=1 MAKEDIR_RELATIVE_TO_BASEDIR=onecore\ds\ds\src\aimx\prod\aimxsrv\client\lib TARGET_PLATFORM=windows CODE_SETS_BUILDING=cs_windows,cs_xbox,cs_phone CODE_SETS_TAGGED=cs_windows,cs_xbox THREAD_ID=1 at e:\os\src\onecore\ds\ds\src\aimx\prod\aimxsrv\client\lib.
1>BUILDC (PipeSpawn): e:\os\tools\NMakeRust\x64\bin\nmake.exe /nologo BUILDMSG=Stop. /nologo /f e:\os\src\tools\makefile.def BUILD_PASS=PASS1 NOLINK=1 MAKEDIR_RELATIVE_TO_BASEDIR=onecore\ds\ds\src\aimx\prod\aimxsrv\client\dll TARGET_PLATFORM=windows CODE_SETS_BUILDING=cs_windows,cs_xbox,cs_phone CODE_SETS_TAGGED=cs_windows,cs_xbox THREAD_ID=2 at e:\os\src\onecore\ds\ds\src\aimx\prod\aimxsrv\client\dll.
1>BUILDC (PipeSpawn): e:\os\tools\NMakeRust\x64\bin\nmake.exe /nologo BUILDMSG=Stop. /nologo /f e:\os\src\tools\makefile.def BUILD_PASS=PASS1 NOLINK=1 MAKEDIR_RELATIVE_TO_BASEDIR=onecore\ds\ds\src\aimx\prod\aimxsrv\powershell TARGET_PLATFORM=windows CODE_SETS_BUILDING=cs_windows,cs_xbox,cs_phone CODE_SETS_TAGGED=cs_windows,cs_xbox THREAD_ID=3 at e:\os\src\onecore\ds\ds\src\aimx\prod\aimxsrv\powershell.
1>BUILDC (PipeSpawn): e:\os\tools\NMakeRust\x64\bin\nmake.exe /nologo BUILDMSG=Stop. /nologo /f e:\os\src\tools\makefile.def BUILD_PASS=PASS1 NOLINK=1 MAKEDIR_RELATIVE_TO_BASEDIR=onecore\ds\ds\src\aimx\prod\aimxsrv\serviceinstaller TARGET_PLATFORM=windows CODE_SETS_BUILDING=cs_windows,cs_xbox,cs_phone CODE_SETS_TAGGED=cs_windows,cs_xbox THREAD_ID=4 at e:\os\src\onecore\ds\ds\src\aimx\prod\aimxsrv\serviceinstaller.
1>BUILDC (PipeSpawn): e:\os\tools\NMakeRust\x64\bin\nmake.exe /nologo BUILDMSG=Stop. /nologo /f e:\os\src\tools\makefile.def BUILD_PASS=PASS1 NOLINK=1 MAKEDIR_RELATIVE_TO_BASEDIR=onecore\ds\ds\src\aimx\prod\aimxsrv\dll TARGET_PLATFORM=windows CODE_SETS_BUILDING=cs_windows,cs_xbox,cs_phone CODE_SETS_TAGGED=cs_windows,cs_xbox THREAD_ID=5 at e:\os\src\onecore\ds\ds\src\aimx\prod\aimxsrv\dll.
1>BUILDC (PipeSpawn): e:\os\tools\NMakeRust\x64\bin\nmake.exe /nologo BUILDMSG=Stop. /nologo /f e:\os\src\tools\makefile.def BUILD_PASS=PASS1 NOLINK=1 MAKEDIR_RELATIVE_TO_BASEDIR=onecore\ds\ds\src\aimx\prod\mcpserversample TARGET_PLATFORM=windows CODE_SETS_BUILDING=cs_windows,cs_xbox,cs_phone CODE_SETS_TAGGED=cs_windows,cs_xbox THREAD_ID=6 at e:\os\src\onecore\ds\ds\src\aimx\prod\mcpserversample.
1>BUILDC (PipeSpawn): c:\windows\system32\cmd.exe /c msbuild.cmd "vcpkg.proj" /nologo /p:BuildingInSeparatePasses=true /p:BuildingWithBuildExe=true /clp:NoSummary /verbosity:normal /Target:BuildCompiled /p:Pass=Compile /p:ObjectPath=e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\cpprestsdk\ /p:TARGET_PLATFORM=windows /p:CODE_SETS_BUILDING=cs_windows:cs_xbox:cs_phone /p:CODE_SETS_TAGGED=cs_windows:cs_xbox /p:THREAD_ID=7 at e:\os\src\onecore\ds\ds\src\aimx\prod\cpprestsdk.
1>BUILDC (PipeSpawn): e:\os\tools\NMakeRust\x64\bin\nmake.exe /nologo BUILDMSG=Stop. /nologo /f e:\os\src\tools\makefile.def BUILD_PASS=PASS1 NOLINK=1 MAKEDIR_RELATIVE_TO_BASEDIR=onecore\ds\ds\src\aimx\prod\adpsmcpsvr TARGET_PLATFORM=windows CODE_SETS_BUILDING=cs_windows,cs_xbox,cs_phone CODE_SETS_TAGGED=cs_windows,cs_xbox THREAD_ID=8 at e:\os\src\onecore\ds\ds\src\aimx\prod\adpsmcpsvr.
BUILD: (ActiveWorkLoad),33.48,,57,0,3,55,16,0,0,0,0,0,0,1,PASS1,0,2000000000,onecore\ds\ds\src\aimx\prod\aimxsrv\client\lib,RUPO-DELL
BUILD: (ActiveWorkLoad),33.48,,57,0,3,55,16,16,0,1,1,1,0,1,PASS1,0,2000000000,onecore\ds\ds\src\aimx\prod\aimxsrv\client\dll,RUPO-DELL
BUILD: (ActiveWorkLoad),33.48,,57,0,3,55,16,16,0,2,2,2,0,1,PASS1,0,2000000000,onecore\ds\ds\src\aimx\prod\aimxsrv\powershell,RUPO-DELL
BUILD: (ActiveWorkLoad),33.48,,57,0,3,55,16,16,0,3,3,3,0,1,PASS1,0,2000000000,onecore\ds\ds\src\aimx\prod\aimxsrv\serviceinstaller,RUPO-DELL
BUILD: (ActiveWorkLoad),33.48,,57,0,3,55,16,16,0,4,4,4,0,1,PASS1,0,2000000000,onecore\ds\ds\src\aimx\prod\aimxsrv\dll,RUPO-DELL
BUILD: (ActiveWorkLoad),33.48,,57,0,3,55,16,16,0,5,5,5,0,1,PASS1,0,2000000000,onecore\ds\ds\src\aimx\prod\mcpserversample,RUPO-DELL
BUILD: (ActiveWorkLoad),33.48,,57,0,3,55,16,16,0,6,6,6,0,1,PASS1,0,2000000000,onecore\ds\ds\src\aimx\prod\cpprestsdk,RUPO-DELL
BUILD: (ActiveWorkLoad),33.50,,57,0,3,55,16,16,0,7,7,7,0,1,PASS1,0,2000000000,onecore\ds\ds\src\aimx\prod\adpsmcpsvr,RUPO-DELL
BUILD: (ActiveWorkLoad),33.50,,57,0,3,55,16,16,0,8,8,8,0,1,PASS1,0,2000000000,onecore\ds\ds\src\aimx\prod\llmclientlib,RUPO-DELL
3204>Calculated LAYERINFO_MODULE='OneCoreDS'.
3204>makefile.def: TEMP=e:\os\obj\amd64fre\temp\b216905d32cb063f0e93b5e7df53964a
3204>makefile.def: BUILDINGINDATT=
3204>[Core OS Undocking] NOT using package ''
3204>UCRT enabled: dir 'e:\os\src\onecore\ds\ds\src\aimx\prod\aimxsrv\serviceinstaller' (target 'Aimx.ServiceInstaller', type 'PROGRAM', nt_target_version '0xA000011')
3204>ObjectsMac.ts: validation succeeded
3204>_NEED_BUILDDATE not defined setting BUILDDATE to an invalid value.
3204>A subdirectory or file e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\aimxsrv\serviceinstaller\objfre\amd64 already exists.
3204> e:\os\tools\Windows.Desktop.Tools.amd64\tools\touch.exe /c e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\aimxsrv\serviceinstaller\objfre\amd64\_PASS1_Marker.log
3204> e:\os\tools\Windows.Desktop.Tools.amd64\tools\preprocessor.exe -o e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\aimxsrv\serviceinstaller\objfre\amd64\mui.rcc -i e:\os\src\tools\mui.rcconfig
3204> e:\os\tools\vc\HostX86\amd64\cl.exe @e:\os\obj\amd64fre\temp\b216905d32cb063f0e93b5e7df53964a\cl_1.rsp
3204>Microsoft (R) C/C++ Optimizing Compiler Version 19.42.34444.100 for x64
3204>Copyright (C) Microsoft Corporation.  All rights reserved.
3204>cl /Fo"e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\aimxsrv\serviceinstaller\objfre\amd64/"
3204>   /FC
3204>   /Iamd64
3204>   /I.
3204>   /Ie:\os\src\data\MSRC
3204>   /Ie:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\aimxsrv\serviceinstaller\objfre\amd64
3204>   /Ie:\os\src\onecore\ds\inc
3204>   /Ie:\os\obj\amd64fre\onecore\ds\inc\objfre\amd64
3204>   /Ie:\os\public\amd64fre\internal\onecoreds\inc
3204>   /Ie:\os\public\amd64fre\OneCore\Restricted\DS\inc
3204>   /Ie:\os\public\amd64fre\OneCoreUap\Restricted\DS\inc
3204>   /Ie:\os\public\amd64fre\OneCore\External\DS\inc
3204>   /Ie:\os\public\amd64fre\OneCoreUap\External\DS\inc
3204>   /Ie:\os\public\amd64fre\ClientCore\External\DS\inc
3204>   /Ie:\os\public\amd64fre\OneCore\Internal\DS\inc
3204>   /Ie:\os\public\amd64fre\OneCoreUap\Internal\DS\inc
3204>   /Ie:\os\public\amd64fre\ClientCore\Internal\DS\inc
3204>   /Ie:\os\public\amd64fre\OneCore\Private\DS\inc
3204>   /Ie:\os\public\amd64fre\OneCoreUap\Private\DS\inc
3204>   /Ie:\os\public\amd64fre\ClientCore\Private\DS\inc
3204>   /Ie:\os\public\amd64fre\OneCore\external\DS\inc
3204>   /Ie:\os\public\amd64fre\OneCore\restricted\DS\inc
3204>   /Ie:\os\public\amd64fre\OneCore\internal\DS\inc
3204>   /Ie:\os\public\amd64fre\OneCore\private\DS\inc
3204>   /Ie:\os\public\amd64fre\onecore\external\oak\inc
3204>   /Ie:\os\public\amd64fre\onecoreuap\external\oak\inc
3204>   /Ie:\os\public\amd64fre\shared\inc
3204>   /Ie:\os\public\amd64fre\onecore\external\shared\inc
3204>   /Ie:\os\public\amd64fre\onecoreuap\external\shared\inc
3204>   /Ie:\os\public\amd64fre\onecore\external\shared\inc\MinWin
3204>   /Ie:\os\public\amd64fre\sdk\inc
3204>   /Ie:\os\public\amd64fre\onecore\external\sdk\inc
3204>   /Ie:\os\public\amd64fre\onecoreuap\external\sdk\inc
3204>   /Ie:\os\public\amd64fre\onecore\private\sdk\inc\MinWin
3204>   /Ie:\os\public\amd64fre\onecore\internal\sdk\inc\MinWin
3204>   /Ie:\os\public\amd64fre\onecore\external\sdk\inc\MinWin
3204>   /Ie:\os\public\amd64fre\onecore\internal\sdk\inc\MinCore
3204>   /Ie:\os\public\amd64fre\onecore\external\sdk\inc\MinCore
3204>   /Ie:\os\public\amd64fre\onecore\internal\sdk\inc\ClientCore
3204>   /Ie:\os\public\amd64fre\onecore\external\sdk\inc\ClientCore
3204>   /Ie:\os\public\amd64fre\onecore\internal\sdk\inc\ModernCore
3204>   /Ie:\os\public\amd64fre\onecore\external\sdk\inc\ModernCore
3204>   /Ie:\os\public\amd64fre\shared\inc
3204>   /Ie:\os\public\amd64fre\onecore\external\shared\inc
3204>   /Ie:\os\public\amd64fre\onecoreuap\external\shared\inc
3204>   /Ie:\os\public\amd64fre\onecore\external\ddk\inc
3204>   /Ie:\os\public\amd64fre\onecoreuap\external\ddk\inc
3204>   /Ie:\os\public\amd64fre\onecore\external\ddk\inc\wdm
3204>   /Ie:\os\public\amd64fre\onecoreuap\external\ddk\inc\wdm
3204>   /Ie:\os\public\amd64fre\internal\sdk\inc
3204>   /Ie:\os\public\amd64fre\onecore\private\sdk\inc
3204>   /Ie:\os\public\amd64fre\onecoreuap\private\sdk\inc
3204>   /Ie:\os\public\amd64fre\onecore\internal\sdk\inc
3204>   /Ie:\os\public\amd64fre\onecore\restricted\sdk\inc
3204>   /Ie:\os\public\amd64fre\onecoreuap\internal\sdk\inc
3204>   /Ie:\os\public\amd64fre\onecoreuap\restricted\sdk\inc
3204>   /Ie:\os\public\amd64fre\onecore\internal\sdk\inc\MinWin
3204>   /Ie:\os\public\amd64fre\onecore\internal\sdk\inc\MinWin\fs
3204>   /Ie:\os\public\amd64fre\onecore\internal\sdk\inc\MinCore
3204>   /Ie:\os\public\amd64fre\onecore\internal\sdk\inc\ClientCore
3204>   /Ie:\os\public\amd64fre\onecore\internal\sdk\inc\ModernCore
3204>   /Ie:\os\public\amd64fre\OneCore\Internal\hv\hvsdk\just_built\inc\private
3204>   /Ie:\os\public\amd64fre\OneCore\Internal\hv\hvsdk\just_built\inc\internal
3204>   /Ie:\os\public\amd64fre\sdk\inc\ucrt
3204>   /Ie:\os\public\amd64fre\internal\sdk\inc\ucrt
3204>   /Ie:\os\public\amd64fre\onecore\external\sdk\inc\ucrt
3204>   /Ie:\os\public\amd64fre\onecore\internal\sdk\inc\ucrt
3204>   /D_WIN64
3204>   /D_AMD64_
3204>   /DAMD64
3204>   /DCONDITION_HANDLING=1
3204>   /DNT_INST=0
3204>   /DWIN32=100
3204>   /D_NT1X_=100
3204>   /DWINNT=1
3204>   /D_WIN32_WINNT=0x0A00
3204>   /DWINVER=0x0A00
3204>   /D_WIN32_IE=0x0A00
3204>   /DWIN32_LEAN_AND_MEAN=1
3204>   /DDEVL=1
3204>   /DNDEBUG
3204>   /D_DLL=1
3204>   /D_MT=1
3204>   -DNT_IUM
3204>   -DUNICODE
3204>   -D_UNICODE
3204>   -D_ARM_WINAPI_PARTITION_DESKTOP_SDK_AVAILABLE
3204>   /D_USE_DEV11_CRT
3204>   -D_APISET_MINWIN_VERSION=0x0115
3204>   -D_APISET_MINCORE_VERSION=0x0114
3204>   /DFE_SB
3204>   /DFE_IME
3204>   /DNTDDI_VERSION=0x0A000011
3204>   /DWINBLUE_KBSPRING14
3204>   /DBUILD_WINDOWS
3204>   /DUNDOCKED_WINDOWS_UCRT
3204>   /D__WRL_CONFIGURATION_LEGACY__
3204>   /DBUILD_UMS_ENABLED=1
3204>   /DBUILD_WOW64_ENABLED=1
3204>   /DBUILD_ARM64X_ENABLED=0
3204>   /DEXECUTABLE_WRITES_SUPPORT=0
3204>   -D_USE_DECLSPECS_FOR_SAL=1
3204>   -DUNLOADABLE_DELAYLOAD_IMPLEMENTATION
3204>   -D__PLACEHOLDER_SAL=1
3204>   /c
3204>   /Zc:wchar_t-
3204>   /Zl
3204>   /Zp8
3204>   /Gy
3204>   /W4
3204>   /d1import_no_registry
3204>   /EHs-c-
3204>   /GR-
3204>   /GF
3204>   /GS
3204>   /Z7
3204>   /Oxs
3204>   /GL
3204>   /Z7
3204>   @e:\os\src\tools\NMakeJS\WarningsCop\WarningsCop.Cxx.DefaultErrors.rsp
3204>   /we4308 /we4509 /we4510 /we4532 /we4533 /we4610 /we4700 /we4789
3204>   /w15043
3204>   /Zc:rvalueCast
3204>   -D_UCRT
3204>   -D_CONST_RETURN=
3204>   -D_CRT_SECURE_NO_WARNINGS
3204>   -D_CRT_NON_CONFORMING_SWPRINTFS
3204>   -D_CRT_NONSTDC_NO_WARNINGS
3204>   -D_NO_CPPLIB_VER
3204>   -D_CRT_STDIO_ARBITRARY_WIDE_SPECIFIERS
3204>   /D_CRT_STDIO_INLINE=extern
3204>   /D_NO_CRT_STDIO_INLINE
3204>   /D_ACRTIMP_ALT=
3204>   /D_ALLOW_MSC_VER_MISMATCH
3204>   /D_ALLOW_ITERATOR_DEBUG_LEVEL_MISMATCH
3204>   /D_ALLOW_RUNTIME_LIBRARY_MISMATCH
3204>   /D_SILENCE_STDEXT_HASH_DEPRECATION_WARNINGS
3204>   /D_STL_EXTRA_DISABLED_WARNINGS=4239
3204>   /D_SILENCE_TR1_NAMESPACE_DEPRECATION_WARNING
3204>   /D_SILENCE_ALL_CXX17_DEPRECATION_WARNINGS
3204>   /D_SILENCE_TR2_SYS_NAMESPACE_DEPRECATION_WARNING
3204>   /D_HAS_FUNCTION_ALLOCATOR_SUPPORT=1
3204>   /D_SILENCE_STDEXT_ALLOCATORS_DEPRECATION_WARNING
3204>   /D_HAS_STD_BYTE=0
3204>   /D_ENFORCE_MATCHING_ALLOCATORS=0
3204>   /D_HAS_FUNCTION_ALLOCATOR_SUPPORT=1
3204>   /D_SILENCE_STDEXT_ALLOCATORS_DEPRECATION_WARNING
3204>   /D_FULL_IOBUF
3204>   /d1initAll:Mask11
3204>   /d1initAll:FillPattern0
3204>   /d1nodatetime
3204>   /d1trimfile:e:\os\src\=BASEDIR
3204>   /d1trimfile:e:\os\public\amd64fre\=PUBLIC_ROOT
3204>   /d1trimfile:e:\os\obj\amd64fre\=OBJECT_ROOT
3204>   /d1trimfile:e:\os\bin\amd64fre\=_NTTREE
3204>   /d1trimfile:e:\os\osdep\=OSDEPENDSROOT
3204>   /d2AllowCompatibleILVersions
3204>   /d2Zi+
3204>   /ZH:SHA_256
3204>   /wd4986
3204>   /wd4987
3204>   /wd4471
3204>   /wd4369
3204>   /wd4309
3204>   /wd4754
3204>   /wd4427
3204>   /d2DeepThoughtInliner-
3204>   /d2implyavx512upperregs-
3204>   /Wv:19.23
3204>   /Fwe:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\aimxsrv\serviceinstaller\objfre\amd64\
3204>   @e:\os\obj\amd64fre\objfre\amd64\DMF\logged-warnings.rsp
3204>   /wl4002
3204>   /wl4003
3204>   /wl4005
3204>   /wl4006
3204>   /wl4007
3204>   /wl4008
3204>   /wl4010
3204>   /wl4013
3204>   /wl4015
3204>   /wl4018
3204>   /wl4020
3204>   /wl4022
3204>   /wl4024
3204>   /wl4025
3204>   /wl4026
3204>   /wl4027
3204>   /wl4028
3204>   /wl4029
3204>   /wl4030
3204>   /wl4031
3204>   /wl4033
3204>   /wl4034
3204>   /wl4036
3204>   /wl4038
3204>   /wl4041
3204>   /wl4042
3204>   /wl4045
3204>   /wl4047
3204>   /wl4048
3204>   /wl4049
3204>   /wl4056
3204>   /wl4066
3204>   /wl4067
3204>   /wl4068
3204>   /wl4073
3204>   /wl4074
3204>   /wl4075
3204>   /wl4076
3204>   /wl4077
3204>   /wl4079
3204>   /wl4080
3204>   /wl4081
3204>   /wl4083
3204>   /wl4085
3204>   /wl4086
3204>   /wl4087
3204>   /wl4088
3204>   /wl4089
3204>   /wl4090
3204>   /wl4091
3204>   /wl4094
3204>   /wl4096
3204>   /wl4097
3204>   /wl4098
3204>   /wl4099
3204>   /wl4101
3204>   /wl4102
3204>   /wl4109
3204>   /wl4112
3204>   /wl4113
3204>   /wl4114
3204>   /wl4115
3204>   /wl4116
3204>   /wl4117
3204>   /wl4119
3204>   /wl4120
3204>   /wl4122
3204>   /wl4124
3204>   /wl4129
3204>   /wl4133
3204>   /wl4138
3204>   /wl4141
3204>   /wl4142
3204>   /wl4143
3204>   /wl4144
3204>   /wl4145
3204>   /wl4150
3204>   /wl4153
3204>   /wl4154
3204>   /wl4155
3204>   /wl4156
3204>   /wl4157
3204>   /wl4158
3204>   /wl4159
3204>   /wl4160
3204>   /wl4161
3204>   /wl4162
3204>   /wl4163
3204>   /wl4164
3204>   /wl4166
3204>   /wl4167
3204>   /wl4168
3204>   /wl4172
3204>   /wl4174
3204>   /wl4175
3204>   /wl4176
3204>   /wl4177
3204>   /wl4178
3204>   /wl4180
3204>   /wl4182
3204>   /wl4183
3204>   /wl4185
3204>   /wl4186
3204>   /wl4187
3204>   /wl4190
3204>   /wl4192
3204>   /wl4197
3204>   /wl4200
3204>   /wl4213
3204>   /wl4215
3204>   /wl4216
3204>   /wl4218
3204>   /wl4223
3204>   /wl4224
3204>   /wl4226
3204>   /wl4227
3204>   /wl4228
3204>   /wl4229
3204>   /wl4230
3204>   /wl4237
3204>   /wl4240
3204>   /wl4243
3204>   /wl4244
3204>   /wl4250
3204>   /wl4251
3204>   /wl4258
3204>   /wl4267
3204>   /wl4269
3204>   /wl4272
3204>   /wl4273
3204>   /wl4274
3204>   /wl4275
3204>   /wl4276
3204>   /wl4278
3204>   /wl4280
3204>   /wl4281
3204>   /wl4282
3204>   /wl4283
3204>   /wl4285
3204>   /wl4286
3204>   /wl4288
3204>   /wl4290
3204>   /wl4291
3204>   /wl4293
3204>   /wl4297
3204>   /wl4302
3204>   /wl4305
3204>   /wl4306
3204>   /wl4307
3204>   /wl4309
3204>   /wl4310
3204>   /wl4311
3204>   /wl4312
3204>   /wl4313
3204>   /wl4316
3204>   /wl4319
3204>   /wl4325
3204>   /wl4326
3204>   /wl4329
3204>   /wl4333
3204>   /wl4334
3204>   /wl4335
3204>   /wl4340
3204>   /wl4344
3204>   /wl4346
3204>   /wl4348
3204>   /wl4353
3204>   /wl4356
3204>   /wl4357
3204>   /wl4358
3204>   /wl4359
3204>   /wl4364
3204>   /wl4368
3204>   /wl4369
3204>   /wl4373
3204>   /wl4374
3204>   /wl4375
3204>   /wl4376
3204>   /wl4377
3204>   /wl4378
3204>   /wl4379
3204>   /wl4381
3204>   /wl4382
3204>   /wl4383
3204>   /wl4384
3204>   /wl4390
3204>   /wl4391
3204>   /wl4392
3204>   /wl4393
3204>   /wl4394
3204>   /wl4395
3204>   /wl4396
3204>   /wl4397
3204>   /wl4398
3204>   /wl4399
3204>   /wl4600
3204>   /wl4401
3204>   /wl4402
3204>   /wl4403
3204>   /wl4404
3204>   /wl4405
3204>   /wl4406
3204>   /wl4407
3204>   /wl4409
3204>   /wl4410
3204>   /wl4411
3204>   /wl4414
3204>   /wl4420
3204>   /wl4430
3204>   /wl4436
3204>   /wl4439
3204>   /wl4440
3204>   /wl4441
3204>   /wl4445
3204>   /wl4461
3204>   /wl4462
3204>   /wl4470
3204>   /wl4473
3204>   /wl4477
3204>   /wl4484
3204>   /wl4485
3204>   /wl4486
3204>   /wl4488
3204>   /wl4489
3204>   /wl4490
3204>   /wl4502
3204>   /wl4503
3204>   /wl4506
3204>   /wl4508
3204>   /wl4511
3204>   /wl4518
3204>   /wl4521
3204>   /wl4522
3204>   /wl4523
3204>   /wl4526
3204>   /wl4530
3204>   /wl4534
3204>   /wl4535
3204>   /wl4537
3204>   /wl4538
3204>   /wl4540
3204>   /wl4541
3204>   /wl4543
3204>   /wl4544
3204>   /wl4550
3204>   /wl4551
3204>   /wl4552
3204>   /wl4553
3204>   /wl4554
3204>   /wl4556
3204>   /wl4558
3204>   /wl4561
3204>   /wl4566
3204>   /wl4570
3204>   /wl4572
3204>   /wl4580
3204>   /wl4581
3204>   /wl4584
3204>   /wl4596
3204>   /wl4597
3204>   /wl4602
3204>   /wl4603
3204>   /wl4606
3204>   /wl4612
3204>   /wl4613
3204>   /wl4615
3204>   /wl4616
3204>   /wl4618
3204>   /wl4620
3204>   /wl4621
3204>   /wl4622
3204>   /wl4624
3204>   /wl4627
3204>   /wl4630
3204>   /wl4632
3204>   /wl4633
3204>   /wl4635
3204>   /wl4636
3204>   /wl4637
3204>   /wl4638
3204>   /wl4641
3204>   /wl4645
3204>   /wl4646
3204>   /wl4650
3204>   /wl4651
3204>   /wl4652
3204>   /wl4653
3204>   /wl4655
3204>   /wl4656
3204>   /wl4657
3204>   /wl4659
3204>   /wl4661
3204>   /wl4662
3204>   /wl4667
3204>   /wl4669
3204>   /wl4674
3204>   /wl4677
3204>   /wl4678
3204>   /wl4679
3204>   /wl4683
3204>   /wl4684
3204>   /wl4685
3204>   /wl4687
3204>   /wl4688
3204>   /wl4691
3204>   /wl4693
3204>   /wl4694
3204>   /wl4698
3204>   /wl4711
3204>   /wl4715
3204>   /wl4716
3204>   /wl4717
3204>   /wl4722
3204>   /wl4723
3204>   /wl4724
3204>   /wl4727
3204>   /wl4730
3204>   /wl4731
3204>   /wl4733
3204>   /wl4739
3204>   /wl4742
3204>   /wl4743
3204>   /wl4744
3204>   /wl4747
3204>   /wl4750
3204>   /wl4756
3204>   /wl4768
3204>   /wl4772
3204>   /wl4788
3204>   /wl4793
3204>   /wl4794
3204>   /wl4799
3204>   /wl4803
3204>   /wl4804
3204>   /wl4805
3204>   /wl4806
3204>   /wl4807
3204>   /wl4810
3204>   /wl4811
3204>   /wl4812
3204>   /wl4813
3204>   /wl4817
3204>   /wl4819
3204>   /wl4821
3204>   /wl4823
3204>   /wl4829
3204>   /wl4834
3204>   /wl4835
3204>   /wl4838
3204>   /wl4839
3204>   /wl4867
3204>   /wl4900
3204>   /wl4910
3204>   /wl4912
3204>   /wl4920
3204>   /wl4925
3204>   /wl4926
3204>   /wl4927
3204>   /wl4929
3204>   /wl4930
3204>   /wl4935
3204>   /wl4936
3204>   /wl4939
3204>   /wl4944
3204>   /wl4945
3204>   /wl4947
3204>   /wl4948
3204>   /wl4949
3204>   /wl4950
3204>   /wl4951
3204>   /wl4952
3204>   /wl4953
3204>   /wl4956
3204>   /wl4957
3204>   /wl4958
3204>   /wl4959
3204>   /wl4961
3204>   /wl4964
3204>   /wl4965
3204>   /wl4972
3204>   /wl4984
3204>   /wl4995
3204>   /wl4996
3204>   /wl4997
3204>   /wl4999
3204>   /wl5033
3204>   /wl5037
3204>   /wl5046
3204>   /wl5050
3204>   /wl5055
3204>   /wl5056
3204>   /wl5105
3204>   /wl5208
3204>   /d2Qvec-mathlib-
3204>   /d2Qvec-sse2only
3204>   /Gw
3204>   /Zc:checkGwOdr
3204>   /d1ignorePragmaWarningError
3204>   /wd4316
3204>   /wd4973
3204>   /DDONT_DISABLE_PCH_WARNINGS_IN_WARNING_H
3204>   /d2FH4
3204>   /Brepro
3204>   -D_HAS_MAGIC_STATICS=1
3204>   /Qspectre
3204>   /wd5045
3204>   /d2guardspecanalysismode:v1_0
3204>   /d2guardspecmode2
3204>   /guard:cf
3204>   /d2guardcfgfuncptr-
3204>   /d2guardcfgdispatch
3204>   /guard:ehcont
3204>   -D__PLACEHOLDER_SAL=1
3204>   -wd4425
3204>   @e:\os\obj\amd64fre\objfre\amd64\WarningsCop\OneCore.rsp
3204>   /wl4146 /wl4308 /wl4509 /wl4510 /wl4532 /wl4533 /wl4610 /wl4700 /wl4701 /wl4703 /wl4789
3204>   /FIe:\os\public\amd64fre\onecore\internal\sdk\inc\warning.h
3204>   /std:c++17
3204>   .\main.cpp 
3204>main.cpp
3204> set _createfile=e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\aimxsrv\serviceinstaller\objfre\amd64\delayload.txt
3204>Writing out macros...e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\aimxsrv\serviceinstaller\objfre\amd64\Macros-PASS1.txt
3204>binplace e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\aimxsrv\serviceinstaller\objfre\amd64\Macros-PASS1.txt
3204> e:\os\tools\powershell\pwsh.exe -NoProfile e:\os\src\tools\NMakeJS\WarningsCop\WarningsCop.ps1 -MakeSubDir "onecore\ds\ds\src\aimx\prod\aimxsrv\serviceinstaller" -Pass PASS1 -BaselineFile e:\os\src\.config\OneCore\WarningsCop.json -OutputDir "e:\os\bin\amd64fre\evidence\WarningsCop\OneCore\onecoreds"
3204>WarningsCop.ps1 : WarningsCop: Processing onecore\ds\ds\src\aimx\prod\aimxsrv\serviceinstaller in pass PASS1
3203>Calculated LAYERINFO_MODULE='OneCoreDS'.
3203>makefile.def: TEMP=e:\os\obj\amd64fre\temp\14d6649a56832e8a18caf56735346332
3203>makefile.def: BUILDINGINDATT=
3203>[Core OS Undocking] NOT using package ''
3203>ObjectsMac.ts: validation succeeded
3203>Starting recursive call to NMAKE for _MAKING_ASMID_INC
3203>Starting _MAKING_ASMID_INC
3203>Calculated LAYERINFO_MODULE='OneCoreDS'.
3203>makefile.def: TEMP=e:\os\obj\amd64fre\temp\14d6649a56832e8a18caf56735346332
3203>makefile.def: BUILDINGINDATT=
3203>[Core OS Undocking] NOT using package ''
3203>ObjectsMac.ts: _objects.mac is not needed; macro validation will be skipped.
3203>BUILDMSG: Checking if we need to generate coffbase.mac file
3203>_NEED_BUILDDATE not defined setting BUILDDATE to an invalid value.
3203>Ending _MAKING_ASMID_INC
3203>'e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\aimxsrv\powershell\objfre\amd64\_asmid.inc' is up-to-date
3203>Finished recursively calling NMAKE for _MAKING_ASMID_INC
3203>BUILDMSG: Checking if we need to generate coffbase.mac file
3203>_NEED_BUILDDATE not defined setting BUILDDATE to an invalid value.
3203>A subdirectory or file e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\aimxsrv\powershell\objfre\amd64 already exists.
3203> e:\os\tools\Windows.Desktop.Tools.amd64\tools\touch.exe /c e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\aimxsrv\powershell\objfre\amd64\_PASS1_Marker.log
3203> c:\windows\system32\cmd.exe /c del e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\aimxsrv\powershell\objfre\amd64\_generated.cs e:\os\obj\amd64fre\temp\14d6649a56832e8a18caf56735346332\_generated.cs 2>nul
3203> set createfile=e:\os\obj\amd64fre\temp\14d6649a56832e8a18caf56735346332\_generated.cs
3203> e:\os\src\tools\onecoreuap\internal\AsmIdToAttribs\asmIdToAttribs.cmd "aimxpsh,processorArchitecture=msil,version=10.0.0.0,publicKeyToken=31bf3856ad364e35" e:\os\obj\amd64fre\temp\14d6649a56832e8a18caf56735346332\_generated.cs csharp
3203> c:\windows\system32\cmd.exe /c copy e:\os\obj\amd64fre\temp\14d6649a56832e8a18caf56735346332\_generated.cs e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\aimxsrv\powershell\objfre\amd64\_generated.cs >nul
3203> e:\os\tools\dotnet-runtime-latest-win-x64\dotnet.exe --roll-forward LatestMajor e:\os\tools\dotnet-x64-latest-roslyn\bincore\csc.dll /deterministic /nologo /nostdlib /r:e:\os\public\amd64fre\onecore\internal\sdk\ref\clr48\mscorlib.dll /noconfig /out:e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\aimxsrv\powershell\objfre\amd64\aimxpsh.dll  
3203> /target:library 
3203> /baseaddress:0x10000000 
3203> /optimize+ 
3203> /debug:pdbonly 
3203> /warnaserror+ 
3203> /unsafe- 
3203> /checksumalgorithm:SHA256 
3203> /platform:anycpu 
3203> /nowarn:1699 
3203> /r:e:\os\public\amd64fre\onecore\internal\sdk\ref\clr48\System.metadata_dll 
3203> /r:e:\os\public\amd64fre\onecore\internal\sdk\ref\clr48\System.Core.metadata_dll 
3203> /r:e:\os\public\amd64fre\onecore\internal\sdk\ref\clr48\System.DirectoryServices.metadata_dll 
3203> /r:e:\os\public\amd64fre\onecore\internal\sdk\ref\clr48\System.DirectoryServices.Protocols.metadata_dll 
3203> /r:e:\os\public\amd64fre\onecore\internal\sdk\ref\clr48\System.IO.metadata_dll 
3203> /r:e:\os\public\amd64fre\onecore\internal\sdk\ref\clr48\System.Management.metadata_dll 
3203> /r:e:\os\public\amd64fre\onecore\internal\sdk\ref\clr48\System.Runtime.metadata_dll 
3203> /r:e:\os\public\amd64fre\onecore\internal\sdk\ref\clr48\System.Runtime.InteropServices.metadata_dll 
3203> /r:e:\os\public\amd64fre\onecore\internal\sdk\ref\clr48\System.Runtime.Serialization.metadata_dll 
3203> /r:e:\os\public\amd64fre\onecore\internal\sdk\ref\clr48\System.Runtime.Serialization.Json.metadata_dll 
3203> /r:e:\os\public\amd64fre\onecore\internal\sdk\ref\clr48\System.Xml.metadata_dll 
3203> /r:e:\os\public\amd64fre\onecore\internal\sdk\ref\System.Management.Automation.metadata_dll 
3203> NativeMethods.cs 
3203> AimxServerCmdLets.cs 
3203> FoundryLocalWizard.cs 
3203> e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\aimxsrv\powershell\objfre\amd64\_generated.cs
3203> c:\windows\system32\cmd.exe /c e:\os\src\tools\urtrun.cmd 4.Latest e:\os\tools\FixTSVersionStringAppend\bin\FixTsVersionStringAppend\release\FixTSVersionStringAppend.exe /fts:e:\os\tools\FixTS\FixTS.exe /pe=e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\aimxsrv\powershell\objfre\amd64\aimxpsh.dll
3203>[e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\aimxsrv\powershell\objfre\amd64\aimxpsh.dll]
3203> Lang: 0 (0)
3203>Key FileVersion value changed to 10.0.27902.1000,0.0.65535.65535,0.0.10011.16384,0.0.65535.0,BUILDINFO
3203>Key ProductVersion value changed to 10.0.27902.1000,0.0.65535.65535,0.0.10011.16384,0.0.65535.0,BUILDINFO
3203>processing version resource language 0000 (0000)
3203> e:\os\tools\Windows.Desktop.Tools.amd64\tools\imagecfg.exe /q /l /e e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\aimxsrv\powershell\objfre\amd64\aimxpsh.dll
3203> e:\os\tools\deferredbinplace\DeferredBinplace.exe  e:\os\src\onecore\ds\ds\src\aimx\prod\aimxsrv\powershell  TRUE  e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\aimxsrv\powershell\objfre\amd64\binplace_PASS1.rsp  @e:\os\tools\binplace\binplace.exe  /R e:\os\bin\amd64fre\.  /s e:\os\bin\amd64fre\Symbols.pri\. /j /:DBG /:NOCV  -f -:LOGPDB /:CVTCIL /:SYMBAD e:\os\src\tools\symbad.txt   /:DEST retail      e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\aimxsrv\powershell\objfre\amd64\aimxpsh.dll
3203>binplace e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\aimxsrv\powershell\objfre\amd64\aimxpsh.dll
3203> c:\windows\system32\cmd.exe /c if not exist e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\aimxsrv\powershell\objfre\amd64\aimxpsh.dll.mui (  echo Build_Status  LN_MUI_STS: LGNSTS_MANAGED aimxpsh.dll  )
3203>Build_Status  LN_MUI_STS: LGNSTS_MANAGED aimxpsh.dll  
3203> set BUILDMSG=making aimxpsh.asmmeta_temp e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\aimxsrv\powershell\objfre\amd64\aimxpsh.dll
3203> c:\windows\system32\cmd.exe /c del e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\aimxsrv\powershell\objfre\amd64\aimxpsh.asmmeta_temp e:\os\obj\amd64fre\temp\14d6649a56832e8a18caf56735346332\aimxpsh.asmmeta_temp 2>nul
3203> c:\windows\system32\cmd.exe /c copy e:\os\obj\amd64fre\temp\14d6649a56832e8a18caf56735346332\aimxpsh.asmmeta_temp e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\aimxsrv\powershell\objfre\amd64\aimxpsh.asmmeta_temp >nul
3203> set BUILDMSG=making aimxpsh.asmmeta
3203> c:\windows\system32\cmd.exe /c if exist e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\aimxsrv\powershell\objfre\amd64\aimxpsh.asmmeta asmmeta.cmd /CompareAndUpdateAsmmeta:e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\aimxsrv\powershell\objfre\amd64\aimxpsh.asmmeta_temp /out:e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\aimxsrv\powershell\objfre\amd64\aimxpsh.asmmeta /OBJ_PATH:e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\aimxsrv\powershell /O:objfre\amd64
3203> c:\windows\system32\cmd.exe /c if not exist e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\aimxsrv\powershell\objfre\amd64\aimxpsh.asmmeta e:\os\tools\Windows.Desktop.Tools.amd64\tools\touch.exe /c e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\aimxsrv\powershell\objfre\amd64\aimxpsh.asmmeta.changed
3203> c:\windows\system32\cmd.exe /c if not exist e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\aimxsrv\powershell\objfre\amd64\aimxpsh.asmmeta copy e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\aimxsrv\powershell\objfre\amd64\aimxpsh.asmmeta_temp e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\aimxsrv\powershell\objfre\amd64\aimxpsh.asmmeta
3203>        1 file(s) copied.
3203> c:\windows\system32\cmd.exe /c if exist e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\aimxsrv\powershell\objfre\amd64\aimxpsh.asmmeta_temp del e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\aimxsrv\powershell\objfre\amd64\aimxpsh.asmmeta_temp
3203> set BUILDMSG=making aimxpsh.metadata_dll in Managed.AsmMeta.nmake under _O_BINARY_METADATA target
3203> c:\windows\system32\cmd.exe /c "@if exist e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\aimxsrv\powershell\objfre\amd64\aimxpsh.asmmeta.changed (e:\os\src\tools\ilasm.cmd /DET /nologo /quiet /dll /out:e:\os\obj\amd64fre\temp\14d6649a56832e8a18caf56735346332\aimxpsh.metadata_dll e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\aimxsrv\powershell\objfre\amd64\aimxpsh.asmmeta || @echo error : error ilasm error) else if not exist e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\aimxsrv\powershell\objfre\amd64\aimxpsh.metadata_dll (e:\os\src\tools\ilasm.cmd /DET /nologo /quiet /dll /out:e:\os\obj\amd64fre\temp\14d6649a56832e8a18caf56735346332\aimxpsh.metadata_dll e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\aimxsrv\powershell\objfre\amd64\aimxpsh.asmmeta || @echo error : error ilasm error)"
3203>e:\os\src\onecore\ds\ds\src\aimx\prod\aimxsrv\powershell>e:\os\tools\ilasm\ilasm.exe /DET /nologo /quiet /dll /out:e:\os\obj\amd64fre\temp\14d6649a56832e8a18caf56735346332\aimxpsh.metadata_dll e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\aimxsrv\powershell\objfre\amd64\aimxpsh.asmmeta 
3203> c:\windows\system32\cmd.exe /c if exist e:\os\obj\amd64fre\temp\14d6649a56832e8a18caf56735346332\aimxpsh.metadata_dll c:\windows\system32\cmd.exe /c copy e:\os\obj\amd64fre\temp\14d6649a56832e8a18caf56735346332\aimxpsh.metadata_dll e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\aimxsrv\powershell\objfre\amd64\aimxpsh.metadata_dll >nul
3203> c:\windows\system32\cmd.exe /c del e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\aimxsrv\powershell\objfre\amd64\aimxpsh.asmmeta.changed
3203>Writing out macros...e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\aimxsrv\powershell\objfre\amd64\Macros-PASS1.txt
3203>binplace e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\aimxsrv\powershell\objfre\amd64\Macros-PASS1.txt
3203> e:\os\tools\powershell\pwsh.exe -NoProfile e:\os\src\tools\NMakeJS\WarningsCop\WarningsCop.ps1 -MakeSubDir "onecore\ds\ds\src\aimx\prod\aimxsrv\powershell" -Pass PASS1 -BaselineFile e:\os\src\.config\OneCore\WarningsCop.json -OutputDir "e:\os\bin\amd64fre\evidence\WarningsCop\OneCore\onecoreds"
3203>WarningsCop.ps1 : WarningsCop: Processing onecore\ds\ds\src\aimx\prod\aimxsrv\powershell in pass PASS1
3209>Calculated LAYERINFO_MODULE='OneCoreDS'.
3209>makefile.def: TEMP=e:\os\obj\amd64fre\temp\3500d4807dc5268526ebcf8b6e5b3b7e
3209>makefile.def: BUILDINGINDATT=
3209>[Core OS Undocking] NOT using package ''
3209>UCRT enabled: dir 'e:\os\src\onecore\ds\ds\src\aimx\prod\llmclientlib' (target 'llmclientlib', type 'LIBRARY', nt_target_version '0xA000011')
3209>ObjectsMac.ts: validation succeeded
3209>STL version 120 used in "e:\os\src\onecore\ds\ds\src\aimx\prod\llmclientlib" (STL_VER_TELEMETRY)
3209>_NEED_BUILDDATE not defined setting BUILDDATE to an invalid value.
3209>A subdirectory or file e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\llmclientlib\objfre\amd64 already exists.
3209> e:\os\tools\Windows.Desktop.Tools.amd64\tools\touch.exe /c e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\llmclientlib\objfre\amd64\_PASS1_Marker.log
3209> e:\os\tools\vc\HostX86\amd64\cl.exe @e:\os\obj\amd64fre\temp\3500d4807dc5268526ebcf8b6e5b3b7e\cl_1.rsp
3209>Microsoft (R) C/C++ Optimizing Compiler Version 19.42.34444.100 for x64
3209>Copyright (C) Microsoft Corporation.  All rights reserved.
3209>cl /Fo"e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\llmclientlib\objfre\amd64/"
3209>   /FC
3209>   /Iamd64
3209>   /I.
3209>   /Ie:\os\src\data\MSRC
3209>   /Ie:\os\public\amd64fre\OneCore\Internal\MinWin\Priv_Sdk\Inc
3209>   /Ie:\os\public\amd64fre\OneCoreUap\Internal\BuildMetadata\internal\cppwinrt
3209>   /Ie:\os\public\amd64fre\onecoreuap\restricted\windows\inc
3209>   /Ie:\os\public\amd64fre\OneCoreUap\Internal\BuildMetadata\internal\cppwinrt
3209>   /I..\common
3209>   /I..\common\nlohmann-json\include
3209>   /I..\common\hnswlib
3209>   /I..\aimxsrv\inc
3209>   /Ie:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\llmclientlib\..\aimxsrv\idl\objfre\amd64
3209>   /Ie:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\llmclientlib\objfre\amd64
3209>   /Ie:\os\src\onecore\ds\inc
3209>   /Ie:\os\obj\amd64fre\onecore\ds\inc\objfre\amd64
3209>   /Ie:\os\public\amd64fre\internal\onecoreds\inc
3209>   /Ie:\os\public\amd64fre\OneCore\Restricted\DS\inc
3209>   /Ie:\os\public\amd64fre\OneCoreUap\Restricted\DS\inc
3209>   /Ie:\os\public\amd64fre\OneCore\External\DS\inc
3209>   /Ie:\os\public\amd64fre\OneCoreUap\External\DS\inc
3209>   /Ie:\os\public\amd64fre\ClientCore\External\DS\inc
3209>   /Ie:\os\public\amd64fre\OneCore\Internal\DS\inc
3209>   /Ie:\os\public\amd64fre\OneCoreUap\Internal\DS\inc
3209>   /Ie:\os\public\amd64fre\ClientCore\Internal\DS\inc
3209>   /Ie:\os\public\amd64fre\OneCore\Private\DS\inc
3209>   /Ie:\os\public\amd64fre\OneCoreUap\Private\DS\inc
3209>   /Ie:\os\public\amd64fre\ClientCore\Private\DS\inc
3209>   /Ie:\os\public\amd64fre\OneCore\external\DS\inc
3209>   /Ie:\os\public\amd64fre\OneCore\restricted\DS\inc
3209>   /Ie:\os\public\amd64fre\OneCore\internal\DS\inc
3209>   /Ie:\os\public\amd64fre\OneCore\private\DS\inc
3209>   /Ie:\os\public\amd64fre\onecore\external\oak\inc
3209>   /Ie:\os\public\amd64fre\onecoreuap\external\oak\inc
3209>   /Ie:\os\public\amd64fre\shared\inc
3209>   /Ie:\os\public\amd64fre\onecore\external\shared\inc
3209>   /Ie:\os\public\amd64fre\onecoreuap\external\shared\inc
3209>   /Ie:\os\public\amd64fre\onecore\external\shared\inc\MinWin
3209>   /Ie:\os\public\amd64fre\sdk\inc
3209>   /Ie:\os\public\amd64fre\onecore\external\sdk\inc
3209>   /Ie:\os\public\amd64fre\onecoreuap\external\sdk\inc
3209>   /Ie:\os\public\amd64fre\onecore\private\sdk\inc\MinWin
3209>   /Ie:\os\public\amd64fre\onecore\internal\sdk\inc\MinWin
3209>   /Ie:\os\public\amd64fre\onecore\external\sdk\inc\MinWin
3209>   /Ie:\os\public\amd64fre\onecore\internal\sdk\inc\MinCore
3209>   /Ie:\os\public\amd64fre\onecore\external\sdk\inc\MinCore
3209>   /Ie:\os\public\amd64fre\onecore\internal\sdk\inc\ClientCore
3209>   /Ie:\os\public\amd64fre\onecore\external\sdk\inc\ClientCore
3209>   /Ie:\os\public\amd64fre\onecore\internal\sdk\inc\ModernCore
3209>   /Ie:\os\public\amd64fre\onecore\external\sdk\inc\ModernCore
3209>   /Ie:\os\public\amd64fre\shared\inc
3209>   /Ie:\os\public\amd64fre\onecore\external\shared\inc
3209>   /Ie:\os\public\amd64fre\onecoreuap\external\shared\inc
3209>   /Ie:\os\public\amd64fre\onecore\external\ddk\inc
3209>   /Ie:\os\public\amd64fre\onecoreuap\external\ddk\inc
3209>   /Ie:\os\public\amd64fre\onecore\external\ddk\inc\wdm
3209>   /Ie:\os\public\amd64fre\onecoreuap\external\ddk\inc\wdm
3209>   /Ie:\os\public\amd64fre\internal\sdk\inc
3209>   /Ie:\os\public\amd64fre\onecore\private\sdk\inc
3209>   /Ie:\os\public\amd64fre\onecoreuap\private\sdk\inc
3209>   /Ie:\os\public\amd64fre\onecore\internal\sdk\inc
3209>   /Ie:\os\public\amd64fre\onecore\restricted\sdk\inc
3209>   /Ie:\os\public\amd64fre\onecoreuap\internal\sdk\inc
3209>   /Ie:\os\public\amd64fre\onecoreuap\restricted\sdk\inc
3209>   /Ie:\os\public\amd64fre\onecore\internal\sdk\inc\MinWin
3209>   /Ie:\os\public\amd64fre\onecore\internal\sdk\inc\MinWin\fs
3209>   /Ie:\os\public\amd64fre\onecore\internal\sdk\inc\MinCore
3209>   /Ie:\os\public\amd64fre\onecore\internal\sdk\inc\ClientCore
3209>   /Ie:\os\public\amd64fre\onecore\internal\sdk\inc\ModernCore
3209>   /Ie:\os\public\amd64fre\OneCore\Internal\hv\hvsdk\just_built\inc\private
3209>   /Ie:\os\public\amd64fre\OneCore\Internal\hv\hvsdk\just_built\inc\internal
3209>   /Ie:\os\public\amd64fre\sdk\inc\ucrt
3209>   /Ie:\os\public\amd64fre\internal\sdk\inc\ucrt
3209>   /Ie:\os\public\amd64fre\onecore\external\sdk\inc\ucrt
3209>   /Ie:\os\public\amd64fre\onecore\internal\sdk\inc\ucrt
3209>   /Ie:\os\public\amd64fre\onecore\internal\sdk\inc\ucrt\stl120
3209>   /Ie:\os\public\amd64fre\onecore\internal\sdk\inc\ucrt\stl120
3209>   /D_WIN64
3209>   /D_AMD64_
3209>   /DAMD64
3209>   /DCONDITION_HANDLING=1
3209>   /DNT_INST=0
3209>   /DWIN32=100
3209>   /D_NT1X_=100
3209>   /DWINNT=1
3209>   /D_WIN32_WINNT=0x0A00
3209>   /DWINVER=0x0A00
3209>   /D_WIN32_IE=0x0A00
3209>   /DWIN32_LEAN_AND_MEAN=1
3209>   /DDEVL=1
3209>   /DNDEBUG
3209>   /D_STL120_
3209>   /D_STL140_
3209>   /D_DLL=1
3209>   /D_MT=1
3209>   -DNT_IUM
3209>   -D_DISABLE_CONSTEXPR_MUTEX_CONSTRUCTOR
3209>   -DUNICODE
3209>   -D_ARM_WINAPI_PARTITION_DESKTOP_SDK_AVAILABLE
3209>   /D_USE_DEV11_CRT
3209>   -D_APISET_MINWIN_VERSION=0x0115
3209>   -D_APISET_MINCORE_VERSION=0x0114
3209>   /DFE_SB
3209>   /DFE_IME
3209>   /DNTDDI_VERSION=0x0A000011
3209>   /DWINBLUE_KBSPRING14
3209>   /DBUILD_WINDOWS
3209>   /DUNDOCKED_WINDOWS_UCRT
3209>   /D__WRL_CONFIGURATION_LEGACY__
3209>   /DBUILD_UMS_ENABLED=1
3209>   /DBUILD_WOW64_ENABLED=1
3209>   /DBUILD_ARM64X_ENABLED=0
3209>   /DEXECUTABLE_WRITES_SUPPORT=0
3209>   -D_USE_DECLSPECS_FOR_SAL=1
3209>   /DRUN_WPP
3209>   -D__PLACEHOLDER_SAL=1
3209>   /c
3209>   /Zc:wchar_t-
3209>   /Zl
3209>   /Zp8
3209>   /Gy
3209>   /W4
3209>   /d1import_no_registry
3209>   /EHsc
3209>   /GR-
3209>   /GF
3209>   /GS
3209>   /Z7
3209>   /Oxs
3209>   /GL
3209>   /Z7
3209>   @e:\os\src\tools\NMakeJS\WarningsCop\WarningsCop.Cxx.DefaultErrors.rsp
3209>   /we4308 /we4509 /we4510 /we4532 /we4533 /we4610 /we4700 /we4789
3209>   /w15043
3209>   /Zc:rvalueCast
3209>   -D_UCRT
3209>   -D_CONST_RETURN=
3209>   -D_CRT_SECURE_NO_WARNINGS
3209>   -D_CRT_NON_CONFORMING_SWPRINTFS
3209>   -D_CRT_NONSTDC_NO_WARNINGS
3209>   -D_CRT_STDIO_ARBITRARY_WIDE_SPECIFIERS
3209>   /D_CRT_STDIO_INLINE=extern
3209>   /D_NO_CRT_STDIO_INLINE
3209>   /D_ACRTIMP_ALT=
3209>   /D_SILENCE_STDEXT_HASH_DEPRECATION_WARNINGS
3209>   /D_STL_EXTRA_DISABLED_WARNINGS=4239
3209>   /D_SILENCE_TR1_NAMESPACE_DEPRECATION_WARNING
3209>   /D_SILENCE_ALL_CXX17_DEPRECATION_WARNINGS
3209>   /D_SILENCE_TR2_SYS_NAMESPACE_DEPRECATION_WARNING
3209>   /D_HAS_FUNCTION_ALLOCATOR_SUPPORT=1
3209>   /D_SILENCE_STDEXT_ALLOCATORS_DEPRECATION_WARNING
3209>   /D_HAS_STD_BYTE=0
3209>   /D_ENFORCE_MATCHING_ALLOCATORS=0
3209>   /D_HAS_FUNCTION_ALLOCATOR_SUPPORT=1
3209>   /D_SILENCE_STDEXT_ALLOCATORS_DEPRECATION_WARNING
3209>   /D_FULL_IOBUF
3209>   /d1initAll:Mask11
3209>   /d1initAll:FillPattern0
3209>   /d1nodatetime
3209>   /d1trimfile:e:\os\src\=BASEDIR
3209>   /d1trimfile:e:\os\public\amd64fre\=PUBLIC_ROOT
3209>   /d1trimfile:e:\os\obj\amd64fre\=OBJECT_ROOT
3209>   /d1trimfile:e:\os\bin\amd64fre\=_NTTREE
3209>   /d1trimfile:e:\os\osdep\=OSDEPENDSROOT
3209>   /d2AllowCompatibleILVersions
3209>   /d2Zi+
3209>   /ZH:SHA_256
3209>   /wd4986
3209>   /wd4987
3209>   /wd4471
3209>   /wd4369
3209>   /wd4309
3209>   /wd4754
3209>   /wd4427
3209>   /d2DeepThoughtInliner-
3209>   /d2implyavx512upperregs-
3209>   /Wv:19.23
3209>   /Fwe:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\llmclientlib\objfre\amd64\
3209>   @e:\os\obj\amd64fre\objfre\amd64\DMF\logged-warnings.rsp
3209>   /wl4002
3209>   /wl4003
3209>   /wl4005
3209>   /wl4006
3209>   /wl4007
3209>   /wl4008
3209>   /wl4010
3209>   /wl4013
3209>   /wl4015
3209>   /wl4018
3209>   /wl4020
3209>   /wl4022
3209>   /wl4024
3209>   /wl4025
3209>   /wl4026
3209>   /wl4027
3209>   /wl4028
3209>   /wl4029
3209>   /wl4030
3209>   /wl4031
3209>   /wl4033
3209>   /wl4034
3209>   /wl4036
3209>   /wl4038
3209>   /wl4041
3209>   /wl4042
3209>   /wl4045
3209>   /wl4047
3209>   /wl4048
3209>   /wl4049
3209>   /wl4056
3209>   /wl4066
3209>   /wl4067
3209>   /wl4068
3209>   /wl4073
3209>   /wl4074
3209>   /wl4075
3209>   /wl4076
3209>   /wl4077
3209>   /wl4079
3209>   /wl4080
3209>   /wl4081
3209>   /wl4083
3209>   /wl4085
3209>   /wl4086
3209>   /wl4087
3209>   /wl4088
3209>   /wl4089
3209>   /wl4090
3209>   /wl4091
3209>   /wl4094
3209>   /wl4096
3209>   /wl4097
3209>   /wl4098
3209>   /wl4099
3209>   /wl4101
3209>   /wl4102
3209>   /wl4109
3209>   /wl4112
3209>   /wl4113
3209>   /wl4114
3209>   /wl4115
3209>   /wl4116
3209>   /wl4117
3209>   /wl4119
3209>   /wl4120
3209>   /wl4122
3209>   /wl4124
3209>   /wl4129
3209>   /wl4133
3209>   /wl4138
3209>   /wl4141
3209>   /wl4142
3209>   /wl4143
3209>   /wl4144
3209>   /wl4145
3209>   /wl4150
3209>   /wl4153
3209>   /wl4154
3209>   /wl4155
3209>   /wl4156
3209>   /wl4157
3209>   /wl4158
3209>   /wl4159
3209>   /wl4160
3209>   /wl4161
3209>   /wl4162
3209>   /wl4163
3209>   /wl4164
3209>   /wl4166
3209>   /wl4167
3209>   /wl4168
3209>   /wl4172
3209>   /wl4174
3209>   /wl4175
3209>   /wl4176
3209>   /wl4177
3209>   /wl4178
3209>   /wl4180
3209>   /wl4182
3209>   /wl4183
3209>   /wl4185
3209>   /wl4186
3209>   /wl4187
3209>   /wl4190
3209>   /wl4192
3209>   /wl4197
3209>   /wl4200
3209>   /wl4213
3209>   /wl4215
3209>   /wl4216
3209>   /wl4218
3209>   /wl4223
3209>   /wl4224
3209>   /wl4226
3209>   /wl4227
3209>   /wl4228
3209>   /wl4229
3209>   /wl4230
3209>   /wl4237
3209>   /wl4240
3209>   /wl4243
3209>   /wl4244
3209>   /wl4250
3209>   /wl4251
3209>   /wl4258
3209>   /wl4267
3209>   /wl4269
3209>   /wl4272
3209>   /wl4273
3209>   /wl4274
3209>   /wl4275
3209>   /wl4276
3209>   /wl4278
3209>   /wl4280
3209>   /wl4281
3209>   /wl4282
3209>   /wl4283
3209>   /wl4285
3209>   /wl4286
3209>   /wl4288
3209>   /wl4290
3209>   /wl4291
3209>   /wl4293
3209>   /wl4297
3209>   /wl4302
3209>   /wl4305
3209>   /wl4306
3209>   /wl4307
3209>   /wl4309
3209>   /wl4310
3209>   /wl4311
3209>   /wl4312
3209>   /wl4313
3209>   /wl4316
3209>   /wl4319
3209>   /wl4325
3209>   /wl4326
3209>   /wl4329
3209>   /wl4333
3209>   /wl4334
3209>   /wl4335
3209>   /wl4340
3209>   /wl4344
3209>   /wl4346
3209>   /wl4348
3209>   /wl4353
3209>   /wl4356
3209>   /wl4357
3209>   /wl4358
3209>   /wl4359
3209>   /wl4364
3209>   /wl4368
3209>   /wl4369
3209>   /wl4373
3209>   /wl4374
3209>   /wl4375
3209>   /wl4376
3209>   /wl4377
3209>   /wl4378
3209>   /wl4379
3209>   /wl4381
3209>   /wl4382
3209>   /wl4383
3209>   /wl4384
3209>   /wl4390
3209>   /wl4391
3209>   /wl4392
3209>   /wl4393
3209>   /wl4394
3209>   /wl4395
3209>   /wl4396
3209>   /wl4397
3209>   /wl4398
3209>   /wl4399
3209>   /wl4600
3209>   /wl4401
3209>   /wl4402
3209>   /wl4403
3209>   /wl4404
3209>   /wl4405
3209>   /wl4406
3209>   /wl4407
3209>   /wl4409
3209>   /wl4410
3209>   /wl4411
3209>   /wl4414
3209>   /wl4420
3209>   /wl4430
3209>   /wl4436
3209>   /wl4439
3209>   /wl4440
3209>   /wl4441
3209>   /wl4445
3209>   /wl4461
3209>   /wl4462
3209>   /wl4470
3209>   /wl4473
3209>   /wl4477
3209>   /wl4484
3209>   /wl4485
3209>   /wl4486
3209>   /wl4488
3209>   /wl4489
3209>   /wl4490
3209>   /wl4502
3209>   /wl4503
3209>   /wl4506
3209>   /wl4508
3209>   /wl4511
3209>   /wl4518
3209>   /wl4521
3209>   /wl4522
3209>   /wl4523
3209>   /wl4526
3209>   /wl4530
3209>   /wl4534
3209>   /wl4535
3209>   /wl4537
3209>   /wl4538
3209>   /wl4540
3209>   /wl4541
3209>   /wl4543
3209>   /wl4544
3209>   /wl4550
3209>   /wl4551
3209>   /wl4552
3209>   /wl4553
3209>   /wl4554
3209>   /wl4556
3209>   /wl4558
3209>   /wl4561
3209>   /wl4566
3209>   /wl4570
3209>   /wl4572
3209>   /wl4580
3209>   /wl4581
3209>   /wl4584
3209>   /wl4596
3209>   /wl4597
3209>   /wl4602
3209>   /wl4603
3209>   /wl4606
3209>   /wl4612
3209>   /wl4613
3209>   /wl4615
3209>   /wl4616
3209>   /wl4618
3209>   /wl4620
3209>   /wl4621
3209>   /wl4622
3209>   /wl4624
3209>   /wl4627
3209>   /wl4630
3209>   /wl4632
3209>   /wl4633
3209>   /wl4635
3209>   /wl4636
3209>   /wl4637
3209>   /wl4638
3209>   /wl4641
3209>   /wl4645
3209>   /wl4646
3209>   /wl4650
3209>   /wl4651
3209>   /wl4652
3209>   /wl4653
3209>   /wl4655
3209>   /wl4656
3209>   /wl4657
3209>   /wl4659
3209>   /wl4661
3209>   /wl4662
3209>   /wl4667
3209>   /wl4669
3209>   /wl4674
3209>   /wl4677
3209>   /wl4678
3209>   /wl4679
3209>   /wl4683
3209>   /wl4684
3209>   /wl4685
3209>   /wl4687
3209>   /wl4688
3209>   /wl4691
3209>   /wl4693
3209>   /wl4694
3209>   /wl4698
3209>   /wl4711
3209>   /wl4715
3209>   /wl4716
3209>   /wl4717
3209>   /wl4722
3209>   /wl4723
3209>   /wl4724
3209>   /wl4727
3209>   /wl4730
3209>   /wl4731
3209>   /wl4733
3209>   /wl4739
3209>   /wl4742
3209>   /wl4743
3209>   /wl4744
3209>   /wl4747
3209>   /wl4750
3209>   /wl4756
3209>   /wl4768
3209>   /wl4772
3209>   /wl4788
3209>   /wl4793
3209>   /wl4794
3209>   /wl4799
3209>   /wl4803
3209>   /wl4804
3209>   /wl4805
3209>   /wl4806
3209>   /wl4807
3209>   /wl4810
3209>   /wl4811
3209>   /wl4812
3209>   /wl4813
3209>   /wl4817
3209>   /wl4819
3209>   /wl4821
3209>   /wl4823
3209>   /wl4829
3209>   /wl4834
3209>   /wl4835
3209>   /wl4838
3209>   /wl4839
3209>   /wl4867
3209>   /wl4900
3209>   /wl4910
3209>   /wl4912
3209>   /wl4920
3209>   /wl4925
3209>   /wl4926
3209>   /wl4927
3209>   /wl4929
3209>   /wl4930
3209>   /wl4935
3209>   /wl4936
3209>   /wl4939
3209>   /wl4944
3209>   /wl4945
3209>   /wl4947
3209>   /wl4948
3209>   /wl4949
3209>   /wl4950
3209>   /wl4951
3209>   /wl4952
3209>   /wl4953
3209>   /wl4956
3209>   /wl4957
3209>   /wl4958
3209>   /wl4959
3209>   /wl4961
3209>   /wl4964
3209>   /wl4965
3209>   /wl4972
3209>   /wl4984
3209>   /wl4995
3209>   /wl4996
3209>   /wl4997
3209>   /wl4999
3209>   /wl5033
3209>   /wl5037
3209>   /wl5046
3209>   /wl5050
3209>   /wl5055
3209>   /wl5056
3209>   /wl5105
3209>   /wl5208
3209>   /d2Qvec-mathlib-
3209>   /d2Qvec-sse2only
3209>   /Gw
3209>   /Zc:checkGwOdr
3209>   /d1ignorePragmaWarningError
3209>   /wd4316
3209>   /wd4973
3209>   /DDONT_DISABLE_PCH_WARNINGS_IN_WARNING_H
3209>   /d2FH4
3209>   /Brepro
3209>   -D_HAS_MAGIC_STATICS=1
3209>   /Qspectre
3209>   /wd5045
3209>   /d2guardspecanalysismode:v1_0
3209>   /d2guardspecmode2
3209>   /guard:cf
3209>   /d2guardcfgfuncptr-
3209>   /d2guardcfgdispatch
3209>   /guard:ehcont
3209>   -D__PLACEHOLDER_SAL=1
3209>   -wd4425
3209>   @e:\os\obj\amd64fre\objfre\amd64\WarningsCop\OneCore.rsp
3209>   /wl4146 /wl4308 /wl4509 /wl4510 /wl4532 /wl4533 /wl4610 /wl4700 /wl4701 /wl4703 /wl4789
3209>   /FIe:\os\public\amd64fre\onecore\internal\sdk\inc\warning.h
3209>   /std:c++17
3209>   .\llmclientlib.cpp 
3209>llmclientlib.cpp
3209> e:\os\tools\vc\HostX64\amd64\link.exe /lib /out:e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\llmclientlib\objfre\amd64\llmclientlib.lib /IGNORE:4078,4221,4281,4006,4198   /nodefaultlib /machine:amd64 /ltcg /Brepro @e:\os\obj\amd64fre\temp\3500d4807dc5268526ebcf8b6e5b3b7e\lib_1.rsp
3209>Microsoft (R) Library Manager Version 14.42.34444.100
3209>Copyright (C) Microsoft Corporation.  All rights reserved.
3209>e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\llmclientlib\objfre\amd64\llmclientlib.obj 
3209>Writing out macros...e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\llmclientlib\objfre\amd64\Macros-PASS1.txt
3209>binplace e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\llmclientlib\objfre\amd64\Macros-PASS1.txt
3209> e:\os\tools\powershell\pwsh.exe -NoProfile e:\os\src\tools\NMakeJS\WarningsCop\WarningsCop.ps1 -MakeSubDir "onecore\ds\ds\src\aimx\prod\llmclientlib" -Pass PASS1 -BaselineFile e:\os\src\.config\OneCore\WarningsCop.json -OutputDir "e:\os\bin\amd64fre\evidence\WarningsCop\OneCore\onecoreds"
3209>WarningsCop.ps1 : WarningsCop: Processing onecore\ds\ds\src\aimx\prod\llmclientlib in pass PASS1
3201>Calculated LAYERINFO_MODULE='OneCoreDS'.
3201>makefile.def: TEMP=e:\os\obj\amd64fre\temp\ea2e5286749ba40917a052c5933f39be
3201>makefile.def: BUILDINGINDATT=
3201>[Core OS Undocking] NOT using package ''
3201>UCRT enabled: dir 'e:\os\src\onecore\ds\ds\src\aimx\prod\aimxsrv\client\lib' (target 'aimxclient_s', type 'LIBRARY', nt_target_version '0xA000011')
3201>ObjectsMac.ts: validation succeeded
3201>STL version 120 used in "e:\os\src\onecore\ds\ds\src\aimx\prod\aimxsrv\client\lib" (STL_VER_TELEMETRY)
3201>_NEED_BUILDDATE not defined setting BUILDDATE to an invalid value.
3201>A subdirectory or file e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\aimxsrv\client\lib\objfre\amd64 already exists.
3201> e:\os\tools\Windows.Desktop.Tools.amd64\tools\touch.exe /c e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\aimxsrv\client\lib\objfre\amd64\_PASS1_Marker.log
3201> e:\os\tools\vc\HostX86\amd64\cl.exe @e:\os\obj\amd64fre\temp\ea2e5286749ba40917a052c5933f39be\tmp_31052_1752613375260417100.tmp /Tpe:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\aimxsrv\client\lib\objfre\amd64\pch_hdr.src
3201>Microsoft (R) C/C++ Optimizing Compiler Version 19.42.34444.100 for x64
3201>Copyright (C) Microsoft Corporation.  All rights reserved.
3201>cl 
3201>   /Iamd64
3201>   /I.
3201>   /Ie:\os\src\data\MSRC
3201>   /Ie:\os\public\amd64fre\onecore\external\sdk\inc\atlmfc
3201>   /I..\
3201>   /I..\..\..\common
3201>   /I..\..\..\common\nlohmann
3201>   /Ie:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\aimxsrv\client\lib
3201>   /Ie:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\aimxsrv\client\lib\objfre\amd64
3201>   /Ie:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\aimxsrv\client\lib\..\..\idl\objfre\amd64
3201>   /Ie:\os\src\onecore\ds\security\inc
3201>   /Ie:\os\public\amd64fre\onecore\internal\ds\inc
3201>   /Ie:\os\public\amd64fre\onecore\private\ds\inc\security\base
3201>   /Ie:\os\public\amd64fre\onecore\private\base\inc
3201>   /Ie:\os\public\amd64fre\onecore\internal\base\inc
3201>   /Ie:\os\public\amd64fre\OneCore\Internal\MinWin\Priv_Sdk\Inc
3201>   /Ie:\os\public\amd64fre\OneCore\Internal\MinWin\Priv_Sdk\Inc\lsa
3201>   /Ie:\os\public\amd64fre\OneCore\Internal\MinWin\Priv_Sdk\Inc\apiset
3201>   /Ie:\os\public\amd64fre\OneCore\Internal\MinWin\Priv_Sdk\Inc\ntos
3201>   /Ie:\os\public\amd64fre\OneCore\Private\MinWin\Priv_Sdk\Inc
3201>   /Ie:\os\public\amd64fre\OneCore\Private\MinWin\Priv_Sdk\Inc\lsa
3201>   /Ie:\os\public\amd64fre\OneCore\Internal\MinCore\Priv_Sdk\Inc
3201>   /Ie:\os\public\amd64fre\internal\sdk\inc
3201>   /Ie:\os\public\amd64fre\onecore\internal\sdk\inc
3201>   /Ie:\os\public\amd64fre\OneCore\Internal\OneCore\Priv_Sdk\Inc
3201>   /Ie:\os\src\onecore\ds\ds\src\adai\proto\win32\aimxsrv\inc
3201>   /Ie:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\aimxsrv\client\lib\objfre\amd64
3201>   /Ie:\os\src\onecore\ds\inc
3201>   /Ie:\os\obj\amd64fre\onecore\ds\inc\objfre\amd64
3201>   /Ie:\os\public\amd64fre\internal\onecoreds\inc
3201>   /Ie:\os\public\amd64fre\OneCore\Restricted\DS\inc
3201>   /Ie:\os\public\amd64fre\OneCoreUap\Restricted\DS\inc
3201>   /Ie:\os\public\amd64fre\OneCore\External\DS\inc
3201>   /Ie:\os\public\amd64fre\OneCoreUap\External\DS\inc
3201>   /Ie:\os\public\amd64fre\ClientCore\External\DS\inc
3201>   /Ie:\os\public\amd64fre\OneCore\Internal\DS\inc
3201>   /Ie:\os\public\amd64fre\OneCoreUap\Internal\DS\inc
3201>   /Ie:\os\public\amd64fre\ClientCore\Internal\DS\inc
3201>   /Ie:\os\public\amd64fre\OneCore\Private\DS\inc
3201>   /Ie:\os\public\amd64fre\OneCoreUap\Private\DS\inc
3201>   /Ie:\os\public\amd64fre\ClientCore\Private\DS\inc
3201>   /Ie:\os\public\amd64fre\OneCore\external\DS\inc
3201>   /Ie:\os\public\amd64fre\OneCore\restricted\DS\inc
3201>   /Ie:\os\public\amd64fre\OneCore\internal\DS\inc
3201>   /Ie:\os\public\amd64fre\OneCore\private\DS\inc
3201>   /Ie:\os\public\amd64fre\onecore\external\oak\inc
3201>   /Ie:\os\public\amd64fre\onecoreuap\external\oak\inc
3201>   /Ie:\os\public\amd64fre\shared\inc
3201>   /Ie:\os\public\amd64fre\onecore\external\shared\inc
3201>   /Ie:\os\public\amd64fre\onecoreuap\external\shared\inc
3201>   /Ie:\os\public\amd64fre\onecore\external\shared\inc\MinWin
3201>   /Ie:\os\public\amd64fre\sdk\inc
3201>   /Ie:\os\public\amd64fre\onecore\external\sdk\inc
3201>   /Ie:\os\public\amd64fre\onecoreuap\external\sdk\inc
3201>   /Ie:\os\public\amd64fre\onecore\private\sdk\inc\MinWin
3201>   /Ie:\os\public\amd64fre\onecore\internal\sdk\inc\MinWin
3201>   /Ie:\os\public\amd64fre\onecore\external\sdk\inc\MinWin
3201>   /Ie:\os\public\amd64fre\onecore\internal\sdk\inc\MinCore
3201>   /Ie:\os\public\amd64fre\onecore\external\sdk\inc\MinCore
3201>   /Ie:\os\public\amd64fre\onecore\internal\sdk\inc\ClientCore
3201>   /Ie:\os\public\amd64fre\onecore\external\sdk\inc\ClientCore
3201>   /Ie:\os\public\amd64fre\onecore\internal\sdk\inc\ModernCore
3201>   /Ie:\os\public\amd64fre\onecore\external\sdk\inc\ModernCore
3201>   /Ie:\os\public\amd64fre\shared\inc
3201>   /Ie:\os\public\amd64fre\onecore\external\shared\inc
3201>   /Ie:\os\public\amd64fre\onecoreuap\external\shared\inc
3201>   /Ie:\os\public\amd64fre\onecore\external\ddk\inc
3201>   /Ie:\os\public\amd64fre\onecoreuap\external\ddk\inc
3201>   /Ie:\os\public\amd64fre\onecore\external\ddk\inc\wdm
3201>   /Ie:\os\public\amd64fre\onecoreuap\external\ddk\inc\wdm
3201>   /Ie:\os\public\amd64fre\internal\sdk\inc
3201>   /Ie:\os\public\amd64fre\onecore\private\sdk\inc
3201>   /Ie:\os\public\amd64fre\onecoreuap\private\sdk\inc
3201>   /Ie:\os\public\amd64fre\onecore\internal\sdk\inc
3201>   /Ie:\os\public\amd64fre\onecore\restricted\sdk\inc
3201>   /Ie:\os\public\amd64fre\onecoreuap\internal\sdk\inc
3201>   /Ie:\os\public\amd64fre\onecoreuap\restricted\sdk\inc
3201>   /Ie:\os\public\amd64fre\onecore\internal\sdk\inc\MinWin
3201>   /Ie:\os\public\amd64fre\onecore\internal\sdk\inc\MinWin\fs
3201>   /Ie:\os\public\amd64fre\onecore\internal\sdk\inc\MinCore
3201>   /Ie:\os\public\amd64fre\onecore\internal\sdk\inc\ClientCore
3201>   /Ie:\os\public\amd64fre\onecore\internal\sdk\inc\ModernCore
3201>   /Ie:\os\public\amd64fre\OneCore\Internal\hv\hvsdk\just_built\inc\private
3201>   /Ie:\os\public\amd64fre\OneCore\Internal\hv\hvsdk\just_built\inc\internal
3201>   /Ie:\os\public\amd64fre\sdk\inc\ucrt
3201>   /Ie:\os\public\amd64fre\internal\sdk\inc\ucrt
3201>   /Ie:\os\public\amd64fre\onecore\external\sdk\inc\ucrt
3201>   /Ie:\os\public\amd64fre\onecore\internal\sdk\inc\ucrt
3201>   /Ie:\os\public\amd64fre\onecore\internal\sdk\inc\ucrt\stl120
3201>   /Ie:\os\public\amd64fre\onecore\internal\sdk\inc\ucrt\stl120
3201>   /D_WIN64
3201>   /D_AMD64_
3201>   /DAMD64
3201>   /DCONDITION_HANDLING=1
3201>   /DNT_INST=0
3201>   /DWIN32=100
3201>   /D_NT1X_=100
3201>   /DWINNT=1
3201>   /D_WIN32_WINNT=0x0A00
3201>   /DWINVER=0x0A00
3201>   /D_WIN32_IE=0x0A00
3201>   /DWIN32_LEAN_AND_MEAN=1
3201>   /DDEVL=1
3201>   /DNDEBUG
3201>   /D_STL120_
3201>   /D_STL140_
3201>   /D_DLL=1
3201>   /D_MT=1
3201>   -DNT_IUM
3201>   -DWIN32
3201>   -D_WIN32
3201>   -DUNICODE
3201>   -D_UNICODE
3201>   -D_ARM_WINAPI_PARTITION_DESKTOP_SDK_AVAILABLE
3201>   /D_USE_DEV11_CRT
3201>   -D_APISET_MINWIN_VERSION=0x0115
3201>   -D_APISET_MINCORE_VERSION=0x0114
3201>   /DFE_SB
3201>   /DFE_IME
3201>   /DNTDDI_VERSION=0x0A000011
3201>   /DWINBLUE_KBSPRING14
3201>   /DBUILD_WINDOWS
3201>   /DUNDOCKED_WINDOWS_UCRT
3201>   /D__WRL_CONFIGURATION_LEGACY__
3201>   /DBUILD_UMS_ENABLED=1
3201>   /DBUILD_WOW64_ENABLED=1
3201>   /DBUILD_ARM64X_ENABLED=0
3201>   /DEXECUTABLE_WRITES_SUPPORT=0
3201>   -D_USE_DECLSPECS_FOR_SAL=1
3201>   /DRUN_WPP
3201>   -D__PLACEHOLDER_SAL=1
3201>   /D_ATL_STATIC_REGISTRY
3201>   /c
3201>   /Zc:wchar_t-
3201>   /Zl
3201>   /Zp8
3201>   /Gy
3201>   /W4
3201>   /d1import_no_registry
3201>   /EHsc
3201>   /GR-
3201>   /GF
3201>   /GS
3201>   /Z7
3201>   /Oxs
3201>   /GL
3201>   /Z7
3201>   @e:\os\src\tools\NMakeJS\WarningsCop\WarningsCop.Cxx.DefaultErrors.rsp
3201>   /we4308 /we4509 /we4510 /we4532 /we4533 /we4610 /we4700 /we4789
3201>   /w15043
3201>   /Zc:rvalueCast
3201>   /Zo
3201>   -D_UCRT
3201>   -D_CONST_RETURN=
3201>   -D_CRT_SECURE_NO_WARNINGS
3201>   -D_CRT_NON_CONFORMING_SWPRINTFS
3201>   -D_CRT_NONSTDC_NO_WARNINGS
3201>   -D_CRT_STDIO_ARBITRARY_WIDE_SPECIFIERS
3201>   /D_CRT_STDIO_INLINE=extern
3201>   /D_NO_CRT_STDIO_INLINE
3201>   /D_ACRTIMP_ALT=
3201>   /D_SILENCE_STDEXT_HASH_DEPRECATION_WARNINGS
3201>   /D_STL_EXTRA_DISABLED_WARNINGS=4239
3201>   /D_SILENCE_TR1_NAMESPACE_DEPRECATION_WARNING
3201>   /D_SILENCE_ALL_CXX17_DEPRECATION_WARNINGS
3201>   /D_SILENCE_TR2_SYS_NAMESPACE_DEPRECATION_WARNING
3201>   /D_HAS_FUNCTION_ALLOCATOR_SUPPORT=1
3201>   /D_SILENCE_STDEXT_ALLOCATORS_DEPRECATION_WARNING
3201>   /D_HAS_STD_BYTE=0
3201>   /D_ENFORCE_MATCHING_ALLOCATORS=0
3201>   /D_HAS_FUNCTION_ALLOCATOR_SUPPORT=1
3201>   /D_SILENCE_STDEXT_ALLOCATORS_DEPRECATION_WARNING
3201>   /D_FULL_IOBUF
3201>   /d1initAll:Mask11
3201>   /d1initAll:FillPattern0
3201>   /d1nodatetime
3201>   /d1trimfile:e:\os\src\=BASEDIR
3201>   /d1trimfile:e:\os\public\amd64fre\=PUBLIC_ROOT
3201>   /d1trimfile:e:\os\obj\amd64fre\=OBJECT_ROOT
3201>   /d1trimfile:e:\os\bin\amd64fre\=_NTTREE
3201>   /d1trimfile:e:\os\osdep\=OSDEPENDSROOT
3201>   /d2AllowCompatibleILVersions
3201>   /d2Zi+
3201>   /ZH:SHA_256
3201>   /wd4986
3201>   /wd4987
3201>   /wd4471
3201>   /wd4369
3201>   /wd4309
3201>   /wd4754
3201>   /wd4427
3201>   /d2DeepThoughtInliner-
3201>   /d2implyavx512upperregs-
3201>   /Wv:19.23
3201>   /Fwe:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\aimxsrv\client\lib\objfre\amd64\
3201>   @e:\os\obj\amd64fre\objfre\amd64\DMF\logged-warnings.rsp
3201>   /wl4002
3201>   /wl4003
3201>   /wl4005
3201>   /wl4006
3201>   /wl4007
3201>   /wl4008
3201>   /wl4010
3201>   /wl4013
3201>   /wl4015
3201>   /wl4018
3201>   /wl4020
3201>   /wl4022
3201>   /wl4024
3201>   /wl4025
3201>   /wl4026
3201>   /wl4027
3201>   /wl4028
3201>   /wl4029
3201>   /wl4030
3201>   /wl4031
3201>   /wl4033
3201>   /wl4034
3201>   /wl4036
3201>   /wl4038
3201>   /wl4041
3201>   /wl4042
3201>   /wl4045
3201>   /wl4047
3201>   /wl4048
3201>   /wl4049
3201>   /wl4056
3201>   /wl4066
3201>   /wl4067
3201>   /wl4068
3201>   /wl4073
3201>   /wl4074
3201>   /wl4075
3201>   /wl4076
3201>   /wl4077
3201>   /wl4079
3201>   /wl4080
3201>   /wl4081
3201>   /wl4083
3201>   /wl4085
3201>   /wl4086
3201>   /wl4087
3201>   /wl4088
3201>   /wl4089
3201>   /wl4090
3201>   /wl4091
3201>   /wl4094
3201>   /wl4096
3201>   /wl4097
3201>   /wl4098
3201>   /wl4099
3201>   /wl4101
3201>   /wl4102
3201>   /wl4109
3201>   /wl4112
3201>   /wl4113
3201>   /wl4114
3201>   /wl4115
3201>   /wl4116
3201>   /wl4117
3201>   /wl4119
3201>   /wl4120
3201>   /wl4122
3201>   /wl4124
3201>   /wl4129
3201>   /wl4133
3201>   /wl4138
3201>   /wl4141
3201>   /wl4142
3201>   /wl4143
3201>   /wl4144
3201>   /wl4145
3201>   /wl4150
3201>   /wl4153
3201>   /wl4154
3201>   /wl4155
3201>   /wl4156
3201>   /wl4157
3201>   /wl4158
3201>   /wl4159
3201>   /wl4160
3201>   /wl4161
3201>   /wl4162
3201>   /wl4163
3201>   /wl4164
3201>   /wl4166
3201>   /wl4167
3201>   /wl4168
3201>   /wl4172
3201>   /wl4174
3201>   /wl4175
3201>   /wl4176
3201>   /wl4177
3201>   /wl4178
3201>   /wl4180
3201>   /wl4182
3201>   /wl4183
3201>   /wl4185
3201>   /wl4186
3201>   /wl4187
3201>   /wl4190
3201>   /wl4192
3201>   /wl4197
3201>   /wl4200
3201>   /wl4213
3201>   /wl4215
3201>   /wl4216
3201>   /wl4218
3201>   /wl4223
3201>   /wl4224
3201>   /wl4226
3201>   /wl4227
3201>   /wl4228
3201>   /wl4229
3201>   /wl4230
3201>   /wl4237
3201>   /wl4240
3201>   /wl4243
3201>   /wl4244
3201>   /wl4250
3201>   /wl4251
3201>   /wl4258
3201>   /wl4267
3201>   /wl4269
3201>   /wl4272
3201>   /wl4273
3201>   /wl4274
3201>   /wl4275
3201>   /wl4276
3201>   /wl4278
3201>   /wl4280
3201>   /wl4281
3201>   /wl4282
3201>   /wl4283
3201>   /wl4285
3201>   /wl4286
3201>   /wl4288
3201>   /wl4290
3201>   /wl4291
3201>   /wl4293
3201>   /wl4297
3201>   /wl4302
3201>   /wl4305
3201>   /wl4306
3201>   /wl4307
3201>   /wl4309
3201>   /wl4310
3201>   /wl4311
3201>   /wl4312
3201>   /wl4313
3201>   /wl4316
3201>   /wl4319
3201>   /wl4325
3201>   /wl4326
3201>   /wl4329
3201>   /wl4333
3201>   /wl4334
3201>   /wl4335
3201>   /wl4340
3201>   /wl4344
3201>   /wl4346
3201>   /wl4348
3201>   /wl4353
3201>   /wl4356
3201>   /wl4357
3201>   /wl4358
3201>   /wl4359
3201>   /wl4364
3201>   /wl4368
3201>   /wl4369
3201>   /wl4373
3201>   /wl4374
3201>   /wl4375
3201>   /wl4376
3201>   /wl4377
3201>   /wl4378
3201>   /wl4379
3201>   /wl4381
3201>   /wl4382
3201>   /wl4383
3201>   /wl4384
3201>   /wl4390
3201>   /wl4391
3201>   /wl4392
3201>   /wl4393
3201>   /wl4394
3201>   /wl4395
3201>   /wl4396
3201>   /wl4397
3201>   /wl4398
3201>   /wl4399
3201>   /wl4600
3201>   /wl4401
3201>   /wl4402
3201>   /wl4403
3201>   /wl4404
3201>   /wl4405
3201>   /wl4406
3201>   /wl4407
3201>   /wl4409
3201>   /wl4410
3201>   /wl4411
3201>   /wl4414
3201>   /wl4420
3201>   /wl4430
3201>   /wl4436
3201>   /wl4439
3201>   /wl4440
3201>   /wl4441
3201>   /wl4445
3201>   /wl4461
3201>   /wl4462
3201>   /wl4470
3201>   /wl4473
3201>   /wl4477
3201>   /wl4484
3201>   /wl4485
3201>   /wl4486
3201>   /wl4488
3201>   /wl4489
3201>   /wl4490
3201>   /wl4502
3201>   /wl4503
3201>   /wl4506
3201>   /wl4508
3201>   /wl4511
3201>   /wl4518
3201>   /wl4521
3201>   /wl4522
3201>   /wl4523
3201>   /wl4526
3201>   /wl4530
3201>   /wl4534
3201>   /wl4535
3201>   /wl4537
3201>   /wl4538
3201>   /wl4540
3201>   /wl4541
3201>   /wl4543
3201>   /wl4544
3201>   /wl4550
3201>   /wl4551
3201>   /wl4552
3201>   /wl4553
3201>   /wl4554
3201>   /wl4556
3201>   /wl4558
3201>   /wl4561
3201>   /wl4566
3201>   /wl4570
3201>   /wl4572
3201>   /wl4580
3201>   /wl4581
3201>   /wl4584
3201>   /wl4596
3201>   /wl4597
3201>   /wl4602
3201>   /wl4603
3201>   /wl4606
3201>   /wl4612
3201>   /wl4613
3201>   /wl4615
3201>   /wl4616
3201>   /wl4618
3201>   /wl4620
3201>   /wl4621
3201>   /wl4622
3201>   /wl4624
3201>   /wl4627
3201>   /wl4630
3201>   /wl4632
3201>   /wl4633
3201>   /wl4635
3201>   /wl4636
3201>   /wl4637
3201>   /wl4638
3201>   /wl4641
3201>   /wl4645
3201>   /wl4646
3201>   /wl4650
3201>   /wl4651
3201>   /wl4652
3201>   /wl4653
3201>   /wl4655
3201>   /wl4656
3201>   /wl4657
3201>   /wl4659
3201>   /wl4661
3201>   /wl4662
3201>   /wl4667
3201>   /wl4669
3201>   /wl4674
3201>   /wl4677
3201>   /wl4678
3201>   /wl4679
3201>   /wl4683
3201>   /wl4684
3201>   /wl4685
3201>   /wl4687
3201>   /wl4688
3201>   /wl4691
3201>   /wl4693
3201>   /wl4694
3201>   /wl4698
3201>   /wl4711
3201>   /wl4715
3201>   /wl4716
3201>   /wl4717
3201>   /wl4722
3201>   /wl4723
3201>   /wl4724
3201>   /wl4727
3201>   /wl4730
3201>   /wl4731
3201>   /wl4733
3201>   /wl4739
3201>   /wl4742
3201>   /wl4743
3201>   /wl4744
3201>   /wl4747
3201>   /wl4750
3201>   /wl4756
3201>   /wl4768
3201>   /wl4772
3201>   /wl4788
3201>   /wl4793
3201>   /wl4794
3201>   /wl4799
3201>   /wl4803
3201>   /wl4804
3201>   /wl4805
3201>   /wl4806
3201>   /wl4807
3201>   /wl4810
3201>   /wl4811
3201>   /wl4812
3201>   /wl4813
3201>   /wl4817
3201>   /wl4819
3201>   /wl4821
3201>   /wl4823
3201>   /wl4829
3201>   /wl4834
3201>   /wl4835
3201>   /wl4838
3201>   /wl4839
3201>   /wl4867
3201>   /wl4900
3201>   /wl4910
3201>   /wl4912
3201>   /wl4920
3201>   /wl4925
3201>   /wl4926
3201>   /wl4927
3201>   /wl4929
3201>   /wl4930
3201>   /wl4935
3201>   /wl4936
3201>   /wl4939
3201>   /wl4944
3201>   /wl4945
3201>   /wl4947
3201>   /wl4948
3201>   /wl4949
3201>   /wl4950
3201>   /wl4951
3201>   /wl4952
3201>   /wl4953
3201>   /wl4956
3201>   /wl4957
3201>   /wl4958
3201>   /wl4959
3201>   /wl4961
3201>   /wl4964
3201>   /wl4965
3201>   /wl4972
3201>   /wl4984
3201>   /wl4995
3201>   /wl4996
3201>   /wl4997
3201>   /wl4999
3201>   /wl5033
3201>   /wl5037
3201>   /wl5046
3201>   /wl5050
3201>   /wl5055
3201>   /wl5056
3201>   /wl5105
3201>   /wl5208
3201>   /d2Qvec-mathlib-
3201>   /d2Qvec-sse2only
3201>   /Gw
3201>   /Zc:checkGwOdr
3201>   /d1ignorePragmaWarningError
3201>   /wd4316
3201>   /wd4973
3201>   /DDONT_DISABLE_PCH_WARNINGS_IN_WARNING_H
3201>   /d2FH4
3201>   /Brepro
3201>   -D_HAS_MAGIC_STATICS=1
3201>   /Qspectre
3201>   /wd5045
3201>   /d2guardspecanalysismode:v1_0
3201>   /d2guardspecmode2
3201>   /guard:cf
3201>   /d2guardcfgfuncptr-
3201>   /d2guardcfgdispatch
3201>   /guard:ehcont
3201>   -D__PLACEHOLDER_SAL=1
3201>   -wd4425
3201>   @e:\os\obj\amd64fre\objfre\amd64\WarningsCop\OneCore.rsp
3201>   /wl4146 /wl4308 /wl4509 /wl4510 /wl4532 /wl4533 /wl4610 /wl4700 /wl4701 /wl4703 /wl4789
3201>   /FIe:\os\public\amd64fre\onecore\internal\sdk\inc\warning.h
3201>   /std:c++17
3201>   /Ylaimxclient_s
3201>   /Ycpch.hxx
3201>   /Fpe:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\aimxsrv\client\lib\objfre\amd64\pch.pch
3201>   /Fo"e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\aimxsrv\client\lib\objfre\amd64\pch.obj"
3201>pch_hdr.src
3201> e:\os\tools\vc\HostX86\amd64\cl.exe @e:\os\obj\amd64fre\temp\ea2e5286749ba40917a052c5933f39be\cl_1.rsp
3201>Microsoft (R) C/C++ Optimizing Compiler Version 19.42.34444.100 for x64
3201>Copyright (C) Microsoft Corporation.  All rights reserved.
3201>cl /Fo"e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\aimxsrv\client\lib\objfre\amd64/"
3201>   /FC
3201>   /Iamd64
3201>   /I.
3201>   /Ie:\os\src\data\MSRC
3201>   /Ie:\os\public\amd64fre\onecore\external\sdk\inc\atlmfc
3201>   /I..\
3201>   /I..\..\..\common
3201>   /I..\..\..\common\nlohmann
3201>   /Ie:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\aimxsrv\client\lib
3201>   /Ie:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\aimxsrv\client\lib\objfre\amd64
3201>   /Ie:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\aimxsrv\client\lib\..\..\idl\objfre\amd64
3201>   /Ie:\os\src\onecore\ds\security\inc
3201>   /Ie:\os\public\amd64fre\onecore\internal\ds\inc
3201>   /Ie:\os\public\amd64fre\onecore\private\ds\inc\security\base
3201>   /Ie:\os\public\amd64fre\onecore\private\base\inc
3201>   /Ie:\os\public\amd64fre\onecore\internal\base\inc
3201>   /Ie:\os\public\amd64fre\OneCore\Internal\MinWin\Priv_Sdk\Inc
3201>   /Ie:\os\public\amd64fre\OneCore\Internal\MinWin\Priv_Sdk\Inc\lsa
3201>   /Ie:\os\public\amd64fre\OneCore\Internal\MinWin\Priv_Sdk\Inc\apiset
3201>   /Ie:\os\public\amd64fre\OneCore\Internal\MinWin\Priv_Sdk\Inc\ntos
3201>   /Ie:\os\public\amd64fre\OneCore\Private\MinWin\Priv_Sdk\Inc
3201>   /Ie:\os\public\amd64fre\OneCore\Private\MinWin\Priv_Sdk\Inc\lsa
3201>   /Ie:\os\public\amd64fre\OneCore\Internal\MinCore\Priv_Sdk\Inc
3201>   /Ie:\os\public\amd64fre\internal\sdk\inc
3201>   /Ie:\os\public\amd64fre\onecore\internal\sdk\inc
3201>   /Ie:\os\public\amd64fre\OneCore\Internal\OneCore\Priv_Sdk\Inc
3201>   /Ie:\os\src\onecore\ds\ds\src\adai\proto\win32\aimxsrv\inc
3201>   /Ie:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\aimxsrv\client\lib\objfre\amd64
3201>   /Ie:\os\src\onecore\ds\inc
3201>   /Ie:\os\obj\amd64fre\onecore\ds\inc\objfre\amd64
3201>   /Ie:\os\public\amd64fre\internal\onecoreds\inc
3201>   /Ie:\os\public\amd64fre\OneCore\Restricted\DS\inc
3201>   /Ie:\os\public\amd64fre\OneCoreUap\Restricted\DS\inc
3201>   /Ie:\os\public\amd64fre\OneCore\External\DS\inc
3201>   /Ie:\os\public\amd64fre\OneCoreUap\External\DS\inc
3201>   /Ie:\os\public\amd64fre\ClientCore\External\DS\inc
3201>   /Ie:\os\public\amd64fre\OneCore\Internal\DS\inc
3201>   /Ie:\os\public\amd64fre\OneCoreUap\Internal\DS\inc
3201>   /Ie:\os\public\amd64fre\ClientCore\Internal\DS\inc
3201>   /Ie:\os\public\amd64fre\OneCore\Private\DS\inc
3201>   /Ie:\os\public\amd64fre\OneCoreUap\Private\DS\inc
3201>   /Ie:\os\public\amd64fre\ClientCore\Private\DS\inc
3201>   /Ie:\os\public\amd64fre\OneCore\external\DS\inc
3201>   /Ie:\os\public\amd64fre\OneCore\restricted\DS\inc
3201>   /Ie:\os\public\amd64fre\OneCore\internal\DS\inc
3201>   /Ie:\os\public\amd64fre\OneCore\private\DS\inc
3201>   /Ie:\os\public\amd64fre\onecore\external\oak\inc
3201>   /Ie:\os\public\amd64fre\onecoreuap\external\oak\inc
3201>   /Ie:\os\public\amd64fre\shared\inc
3201>   /Ie:\os\public\amd64fre\onecore\external\shared\inc
3201>   /Ie:\os\public\amd64fre\onecoreuap\external\shared\inc
3201>   /Ie:\os\public\amd64fre\onecore\external\shared\inc\MinWin
3201>   /Ie:\os\public\amd64fre\sdk\inc
3201>   /Ie:\os\public\amd64fre\onecore\external\sdk\inc
3201>   /Ie:\os\public\amd64fre\onecoreuap\external\sdk\inc
3201>   /Ie:\os\public\amd64fre\onecore\private\sdk\inc\MinWin
3201>   /Ie:\os\public\amd64fre\onecore\internal\sdk\inc\MinWin
3201>   /Ie:\os\public\amd64fre\onecore\external\sdk\inc\MinWin
3201>   /Ie:\os\public\amd64fre\onecore\internal\sdk\inc\MinCore
3201>   /Ie:\os\public\amd64fre\onecore\external\sdk\inc\MinCore
3201>   /Ie:\os\public\amd64fre\onecore\internal\sdk\inc\ClientCore
3201>   /Ie:\os\public\amd64fre\onecore\external\sdk\inc\ClientCore
3201>   /Ie:\os\public\amd64fre\onecore\internal\sdk\inc\ModernCore
3201>   /Ie:\os\public\amd64fre\onecore\external\sdk\inc\ModernCore
3201>   /Ie:\os\public\amd64fre\shared\inc
3201>   /Ie:\os\public\amd64fre\onecore\external\shared\inc
3201>   /Ie:\os\public\amd64fre\onecoreuap\external\shared\inc
3201>   /Ie:\os\public\amd64fre\onecore\external\ddk\inc
3201>   /Ie:\os\public\amd64fre\onecoreuap\external\ddk\inc
3201>   /Ie:\os\public\amd64fre\onecore\external\ddk\inc\wdm
3201>   /Ie:\os\public\amd64fre\onecoreuap\external\ddk\inc\wdm
3201>   /Ie:\os\public\amd64fre\internal\sdk\inc
3201>   /Ie:\os\public\amd64fre\onecore\private\sdk\inc
3201>   /Ie:\os\public\amd64fre\onecoreuap\private\sdk\inc
3201>   /Ie:\os\public\amd64fre\onecore\internal\sdk\inc
3201>   /Ie:\os\public\amd64fre\onecore\restricted\sdk\inc
3201>   /Ie:\os\public\amd64fre\onecoreuap\internal\sdk\inc
3201>   /Ie:\os\public\amd64fre\onecoreuap\restricted\sdk\inc
3201>   /Ie:\os\public\amd64fre\onecore\internal\sdk\inc\MinWin
3201>   /Ie:\os\public\amd64fre\onecore\internal\sdk\inc\MinWin\fs
3201>   /Ie:\os\public\amd64fre\onecore\internal\sdk\inc\MinCore
3201>   /Ie:\os\public\amd64fre\onecore\internal\sdk\inc\ClientCore
3201>   /Ie:\os\public\amd64fre\onecore\internal\sdk\inc\ModernCore
3201>   /Ie:\os\public\amd64fre\OneCore\Internal\hv\hvsdk\just_built\inc\private
3201>   /Ie:\os\public\amd64fre\OneCore\Internal\hv\hvsdk\just_built\inc\internal
3201>   /Ie:\os\public\amd64fre\sdk\inc\ucrt
3201>   /Ie:\os\public\amd64fre\internal\sdk\inc\ucrt
3201>   /Ie:\os\public\amd64fre\onecore\external\sdk\inc\ucrt
3201>   /Ie:\os\public\amd64fre\onecore\internal\sdk\inc\ucrt
3201>   /Ie:\os\public\amd64fre\onecore\internal\sdk\inc\ucrt\stl120
3201>   /Ie:\os\public\amd64fre\onecore\internal\sdk\inc\ucrt\stl120
3201>   /D_WIN64
3201>   /D_AMD64_
3201>   /DAMD64
3201>   /DCONDITION_HANDLING=1
3201>   /DNT_INST=0
3201>   /DWIN32=100
3201>   /D_NT1X_=100
3201>   /DWINNT=1
3201>   /D_WIN32_WINNT=0x0A00
3201>   /DWINVER=0x0A00
3201>   /D_WIN32_IE=0x0A00
3201>   /DWIN32_LEAN_AND_MEAN=1
3201>   /DDEVL=1
3201>   /DNDEBUG
3201>   /D_STL120_
3201>   /D_STL140_
3201>   /D_DLL=1
3201>   /D_MT=1
3201>   -DNT_IUM
3201>   -DWIN32
3201>   -D_WIN32
3201>   -DUNICODE
3201>   -D_UNICODE
3201>   -D_ARM_WINAPI_PARTITION_DESKTOP_SDK_AVAILABLE
3201>   /D_USE_DEV11_CRT
3201>   -D_APISET_MINWIN_VERSION=0x0115
3201>   -D_APISET_MINCORE_VERSION=0x0114
3201>   /DFE_SB
3201>   /DFE_IME
3201>   /DNTDDI_VERSION=0x0A000011
3201>   /DWINBLUE_KBSPRING14
3201>   /DBUILD_WINDOWS
3201>   /DUNDOCKED_WINDOWS_UCRT
3201>   /D__WRL_CONFIGURATION_LEGACY__
3201>   /DBUILD_UMS_ENABLED=1
3201>   /DBUILD_WOW64_ENABLED=1
3201>   /DBUILD_ARM64X_ENABLED=0
3201>   /DEXECUTABLE_WRITES_SUPPORT=0
3201>   -D_USE_DECLSPECS_FOR_SAL=1
3201>   /DRUN_WPP
3201>   -D__PLACEHOLDER_SAL=1
3201>   /D_ATL_STATIC_REGISTRY
3201>   /c
3201>   /Zc:wchar_t-
3201>   /Zl
3201>   /Zp8
3201>   /Gy
3201>   /W4
3201>   /d1import_no_registry
3201>   /EHsc
3201>   /GR-
3201>   /GF
3201>   /GS
3201>   /Z7
3201>   /Oxs
3201>   /GL
3201>   /Z7
3201>   @e:\os\src\tools\NMakeJS\WarningsCop\WarningsCop.Cxx.DefaultErrors.rsp
3201>   /we4308 /we4509 /we4510 /we4532 /we4533 /we4610 /we4700 /we4789
3201>   /w15043
3201>   /Zc:rvalueCast
3201>   /Zo
3201>   -D_UCRT
3201>   -D_CONST_RETURN=
3201>   -D_CRT_SECURE_NO_WARNINGS
3201>   -D_CRT_NON_CONFORMING_SWPRINTFS
3201>   -D_CRT_NONSTDC_NO_WARNINGS
3201>   -D_CRT_STDIO_ARBITRARY_WIDE_SPECIFIERS
3201>   /D_CRT_STDIO_INLINE=extern
3201>   /D_NO_CRT_STDIO_INLINE
3201>   /D_ACRTIMP_ALT=
3201>   /D_SILENCE_STDEXT_HASH_DEPRECATION_WARNINGS
3201>   /D_STL_EXTRA_DISABLED_WARNINGS=4239
3201>   /D_SILENCE_TR1_NAMESPACE_DEPRECATION_WARNING
3201>   /D_SILENCE_ALL_CXX17_DEPRECATION_WARNINGS
3201>   /D_SILENCE_TR2_SYS_NAMESPACE_DEPRECATION_WARNING
3201>   /D_HAS_FUNCTION_ALLOCATOR_SUPPORT=1
3201>   /D_SILENCE_STDEXT_ALLOCATORS_DEPRECATION_WARNING
3201>   /D_HAS_STD_BYTE=0
3201>   /D_ENFORCE_MATCHING_ALLOCATORS=0
3201>   /D_HAS_FUNCTION_ALLOCATOR_SUPPORT=1
3201>   /D_SILENCE_STDEXT_ALLOCATORS_DEPRECATION_WARNING
3201>   /D_FULL_IOBUF
3201>   /d1initAll:Mask11
3201>   /d1initAll:FillPattern0
3201>   /d1nodatetime
3201>   /d1trimfile:e:\os\src\=BASEDIR
3201>   /d1trimfile:e:\os\public\amd64fre\=PUBLIC_ROOT
3201>   /d1trimfile:e:\os\obj\amd64fre\=OBJECT_ROOT
3201>   /d1trimfile:e:\os\bin\amd64fre\=_NTTREE
3201>   /d1trimfile:e:\os\osdep\=OSDEPENDSROOT
3201>   /d2AllowCompatibleILVersions
3201>   /d2Zi+
3201>   /ZH:SHA_256
3201>   /wd4986
3201>   /wd4987
3201>   /wd4471
3201>   /wd4369
3201>   /wd4309
3201>   /wd4754
3201>   /wd4427
3201>   /d2DeepThoughtInliner-
3201>   /d2implyavx512upperregs-
3201>   /Wv:19.23
3201>   /Fwe:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\aimxsrv\client\lib\objfre\amd64\
3201>   @e:\os\obj\amd64fre\objfre\amd64\DMF\logged-warnings.rsp
3201>   /wl4002
3201>   /wl4003
3201>   /wl4005
3201>   /wl4006
3201>   /wl4007
3201>   /wl4008
3201>   /wl4010
3201>   /wl4013
3201>   /wl4015
3201>   /wl4018
3201>   /wl4020
3201>   /wl4022
3201>   /wl4024
3201>   /wl4025
3201>   /wl4026
3201>   /wl4027
3201>   /wl4028
3201>   /wl4029
3201>   /wl4030
3201>   /wl4031
3201>   /wl4033
3201>   /wl4034
3201>   /wl4036
3201>   /wl4038
3201>   /wl4041
3201>   /wl4042
3201>   /wl4045
3201>   /wl4047
3201>   /wl4048
3201>   /wl4049
3201>   /wl4056
3201>   /wl4066
3201>   /wl4067
3201>   /wl4068
3201>   /wl4073
3201>   /wl4074
3201>   /wl4075
3201>   /wl4076
3201>   /wl4077
3201>   /wl4079
3201>   /wl4080
3201>   /wl4081
3201>   /wl4083
3201>   /wl4085
3201>   /wl4086
3201>   /wl4087
3201>   /wl4088
3201>   /wl4089
3201>   /wl4090
3201>   /wl4091
3201>   /wl4094
3201>   /wl4096
3201>   /wl4097
3201>   /wl4098
3201>   /wl4099
3201>   /wl4101
3201>   /wl4102
3201>   /wl4109
3201>   /wl4112
3201>   /wl4113
3201>   /wl4114
3201>   /wl4115
3201>   /wl4116
3201>   /wl4117
3201>   /wl4119
3201>   /wl4120
3201>   /wl4122
3201>   /wl4124
3201>   /wl4129
3201>   /wl4133
3201>   /wl4138
3201>   /wl4141
3201>   /wl4142
3201>   /wl4143
3201>   /wl4144
3201>   /wl4145
3201>   /wl4150
3201>   /wl4153
3201>   /wl4154
3201>   /wl4155
3201>   /wl4156
3201>   /wl4157
3201>   /wl4158
3201>   /wl4159
3201>   /wl4160
3201>   /wl4161
3201>   /wl4162
3201>   /wl4163
3201>   /wl4164
3201>   /wl4166
3201>   /wl4167
3201>   /wl4168
3201>   /wl4172
3201>   /wl4174
3201>   /wl4175
3201>   /wl4176
3201>   /wl4177
3201>   /wl4178
3201>   /wl4180
3201>   /wl4182
3201>   /wl4183
3201>   /wl4185
3201>   /wl4186
3201>   /wl4187
3201>   /wl4190
3201>   /wl4192
3201>   /wl4197
3201>   /wl4200
3201>   /wl4213
3201>   /wl4215
3201>   /wl4216
3201>   /wl4218
3201>   /wl4223
3201>   /wl4224
3201>   /wl4226
3201>   /wl4227
3201>   /wl4228
3201>   /wl4229
3201>   /wl4230
3201>   /wl4237
3201>   /wl4240
3201>   /wl4243
3201>   /wl4244
3201>   /wl4250
3201>   /wl4251
3201>   /wl4258
3201>   /wl4267
3201>   /wl4269
3201>   /wl4272
3201>   /wl4273
3201>   /wl4274
3201>   /wl4275
3201>   /wl4276
3201>   /wl4278
3201>   /wl4280
3201>   /wl4281
3201>   /wl4282
3201>   /wl4283
3201>   /wl4285
3201>   /wl4286
3201>   /wl4288
3201>   /wl4290
3201>   /wl4291
3201>   /wl4293
3201>   /wl4297
3201>   /wl4302
3201>   /wl4305
3201>   /wl4306
3201>   /wl4307
3201>   /wl4309
3201>   /wl4310
3201>   /wl4311
3201>   /wl4312
3201>   /wl4313
3201>   /wl4316
3201>   /wl4319
3201>   /wl4325
3201>   /wl4326
3201>   /wl4329
3201>   /wl4333
3201>   /wl4334
3201>   /wl4335
3201>   /wl4340
3201>   /wl4344
3201>   /wl4346
3201>   /wl4348
3201>   /wl4353
3201>   /wl4356
3201>   /wl4357
3201>   /wl4358
3201>   /wl4359
3201>   /wl4364
3201>   /wl4368
3201>   /wl4369
3201>   /wl4373
3201>   /wl4374
3201>   /wl4375
3201>   /wl4376
3201>   /wl4377
3201>   /wl4378
3201>   /wl4379
3201>   /wl4381
3201>   /wl4382
3201>   /wl4383
3201>   /wl4384
3201>   /wl4390
3201>   /wl4391
3201>   /wl4392
3201>   /wl4393
3201>   /wl4394
3201>   /wl4395
3201>   /wl4396
3201>   /wl4397
3201>   /wl4398
3201>   /wl4399
3201>   /wl4600
3201>   /wl4401
3201>   /wl4402
3201>   /wl4403
3201>   /wl4404
3201>   /wl4405
3201>   /wl4406
3201>   /wl4407
3201>   /wl4409
3201>   /wl4410
3201>   /wl4411
3201>   /wl4414
3201>   /wl4420
3201>   /wl4430
3201>   /wl4436
3201>   /wl4439
3201>   /wl4440
3201>   /wl4441
3201>   /wl4445
3201>   /wl4461
3201>   /wl4462
3201>   /wl4470
3201>   /wl4473
3201>   /wl4477
3201>   /wl4484
3201>   /wl4485
3201>   /wl4486
3201>   /wl4488
3201>   /wl4489
3201>   /wl4490
3201>   /wl4502
3201>   /wl4503
3201>   /wl4506
3201>   /wl4508
3201>   /wl4511
3201>   /wl4518
3201>   /wl4521
3201>   /wl4522
3201>   /wl4523
3201>   /wl4526
3201>   /wl4530
3201>   /wl4534
3201>   /wl4535
3201>   /wl4537
3201>   /wl4538
3201>   /wl4540
3201>   /wl4541
3201>   /wl4543
3201>   /wl4544
3201>   /wl4550
3201>   /wl4551
3201>   /wl4552
3201>   /wl4553
3201>   /wl4554
3201>   /wl4556
3201>   /wl4558
3201>   /wl4561
3201>   /wl4566
3201>   /wl4570
3201>   /wl4572
3201>   /wl4580
3201>   /wl4581
3201>   /wl4584
3201>   /wl4596
3201>   /wl4597
3201>   /wl4602
3201>   /wl4603
3201>   /wl4606
3201>   /wl4612
3201>   /wl4613
3201>   /wl4615
3201>   /wl4616
3201>   /wl4618
3201>   /wl4620
3201>   /wl4621
3201>   /wl4622
3201>   /wl4624
3201>   /wl4627
3201>   /wl4630
3201>   /wl4632
3201>   /wl4633
3201>   /wl4635
3201>   /wl4636
3201>   /wl4637
3201>   /wl4638
3201>   /wl4641
3201>   /wl4645
3201>   /wl4646
3201>   /wl4650
3201>   /wl4651
3201>   /wl4652
3201>   /wl4653
3201>   /wl4655
3201>   /wl4656
3201>   /wl4657
3201>   /wl4659
3201>   /wl4661
3201>   /wl4662
3201>   /wl4667
3201>   /wl4669
3201>   /wl4674
3201>   /wl4677
3201>   /wl4678
3201>   /wl4679
3201>   /wl4683
3201>   /wl4684
3201>   /wl4685
3201>   /wl4687
3201>   /wl4688
3201>   /wl4691
3201>   /wl4693
3201>   /wl4694
3201>   /wl4698
3201>   /wl4711
3201>   /wl4715
3201>   /wl4716
3201>   /wl4717
3201>   /wl4722
3201>   /wl4723
3201>   /wl4724
3201>   /wl4727
3201>   /wl4730
3201>   /wl4731
3201>   /wl4733
3201>   /wl4739
3201>   /wl4742
3201>   /wl4743
3201>   /wl4744
3201>   /wl4747
3201>   /wl4750
3201>   /wl4756
3201>   /wl4768
3201>   /wl4772
3201>   /wl4788
3201>   /wl4793
3201>   /wl4794
3201>   /wl4799
3201>   /wl4803
3201>   /wl4804
3201>   /wl4805
3201>   /wl4806
3201>   /wl4807
3201>   /wl4810
3201>   /wl4811
3201>   /wl4812
3201>   /wl4813
3201>   /wl4817
3201>   /wl4819
3201>   /wl4821
3201>   /wl4823
3201>   /wl4829
3201>   /wl4834
3201>   /wl4835
3201>   /wl4838
3201>   /wl4839
3201>   /wl4867
3201>   /wl4900
3201>   /wl4910
3201>   /wl4912
3201>   /wl4920
3201>   /wl4925
3201>   /wl4926
3201>   /wl4927
3201>   /wl4929
3201>   /wl4930
3201>   /wl4935
3201>   /wl4936
3201>   /wl4939
3201>   /wl4944
3201>   /wl4945
3201>   /wl4947
3201>   /wl4948
3201>   /wl4949
3201>   /wl4950
3201>   /wl4951
3201>   /wl4952
3201>   /wl4953
3201>   /wl4956
3201>   /wl4957
3201>   /wl4958
3201>   /wl4959
3201>   /wl4961
3201>   /wl4964
3201>   /wl4965
3201>   /wl4972
3201>   /wl4984
3201>   /wl4995
3201>   /wl4996
3201>   /wl4997
3201>   /wl4999
3201>   /wl5033
3201>   /wl5037
3201>   /wl5046
3201>   /wl5050
3201>   /wl5055
3201>   /wl5056
3201>   /wl5105
3201>   /wl5208
3201>   /d2Qvec-mathlib-
3201>   /d2Qvec-sse2only
3201>   /Gw
3201>   /Zc:checkGwOdr
3201>   /d1ignorePragmaWarningError
3201>   /wd4316
3201>   /wd4973
3201>   /DDONT_DISABLE_PCH_WARNINGS_IN_WARNING_H
3201>   /d2FH4
3201>   /Brepro
3201>   -D_HAS_MAGIC_STATICS=1
3201>   /Qspectre
3201>   /wd5045
3201>   /d2guardspecanalysismode:v1_0
3201>   /d2guardspecmode2
3201>   /guard:cf
3201>   /d2guardcfgfuncptr-
3201>   /d2guardcfgdispatch
3201>   /guard:ehcont
3201>   -D__PLACEHOLDER_SAL=1
3201>   -wd4425
3201>   @e:\os\obj\amd64fre\objfre\amd64\WarningsCop\OneCore.rsp
3201>   /wl4146 /wl4308 /wl4509 /wl4510 /wl4532 /wl4533 /wl4610 /wl4700 /wl4701 /wl4703 /wl4789
3201>   /FIe:\os\public\amd64fre\onecore\internal\sdk\inc\warning.h
3201>   /std:c++17
3201>   /Yupch.hxx /Fpe:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\aimxsrv\client\lib\objfre\amd64\pch.pch
3201>   ..\aimxclient.cpp ..\aimxrpcclient.cpp ..\memory.cpp 
3201>aimxclient.cpp
3201>aimxrpcclient.cpp
3201>memory.cpp
3201> e:\os\tools\vc\HostX64\amd64\link.exe /lib /out:e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\aimxsrv\client\lib\objfre\amd64\aimxclient_s.lib /IGNORE:4078,4221,4281,4006,4198   /nodefaultlib /machine:amd64 /ltcg /Brepro @e:\os\obj\amd64fre\temp\ea2e5286749ba40917a052c5933f39be\lib_1.rsp
3201>Microsoft (R) Library Manager Version 14.42.34444.100
3201>Copyright (C) Microsoft Corporation.  All rights reserved.
3201>e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\aimxsrv\client\lib\objfre\amd64\pch.obj 
3201>e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\aimxsrv\client\lib\objfre\amd64\aimxclient.obj 
3201>e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\aimxsrv\client\lib\objfre\amd64\aimxrpcclient.obj 
3201>e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\aimxsrv\client\lib\objfre\amd64\memory.obj 
3201>Writing out macros...e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\aimxsrv\client\lib\objfre\amd64\Macros-PASS1.txt
3201>binplace e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\aimxsrv\client\lib\objfre\amd64\Macros-PASS1.txt
3201> e:\os\tools\powershell\pwsh.exe -NoProfile e:\os\src\tools\NMakeJS\WarningsCop\WarningsCop.ps1 -MakeSubDir "onecore\ds\ds\src\aimx\prod\aimxsrv\client\lib" -Pass PASS1 -BaselineFile e:\os\src\.config\OneCore\WarningsCop.json -OutputDir "e:\os\bin\amd64fre\evidence\WarningsCop\OneCore\onecoreds"
3201>WarningsCop.ps1 : WarningsCop: Processing onecore\ds\ds\src\aimx\prod\aimxsrv\client\lib in pass PASS1
3202>Calculated LAYERINFO_MODULE='OneCoreDS'.
3202>makefile.def: TEMP=e:\os\obj\amd64fre\temp\192f640bcdca1336790fcd186ea18ad0
3202>makefile.def: BUILDINGINDATT=
3202>[Core OS Undocking] NOT using package ''
3202>UCRT enabled: dir 'e:\os\src\onecore\ds\ds\src\aimx\prod\aimxsrv\client\dll' (target 'aimxclient', type 'DYNLINK', nt_target_version '0xA000011')
3202>ObjectsMac.ts: validation succeeded
3202>STL version 120 used in "e:\os\src\onecore\ds\ds\src\aimx\prod\aimxsrv\client\dll" (STL_VER_TELEMETRY)
3202>_NEED_BUILDDATE not defined setting BUILDDATE to an invalid value.
3202>A subdirectory or file e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\aimxsrv\client\dll\objfre\amd64 already exists.
3202> e:\os\tools\Windows.Desktop.Tools.amd64\tools\touch.exe /c e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\aimxsrv\client\dll\objfre\amd64\_PASS1_Marker.log
3202> e:\os\tools\vc\HostX86\amd64\cl.exe @e:\os\obj\amd64fre\temp\192f640bcdca1336790fcd186ea18ad0\tmp_12456_1752613375274510600.tmp /Tpe:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\aimxsrv\client\dll\objfre\amd64\pch_hdr.src
3202>Microsoft (R) C/C++ Optimizing Compiler Version 19.42.34444.100 for x64
3202>Copyright (C) Microsoft Corporation.  All rights reserved.
3202>cl 
3202>   /Iamd64
3202>   /I.
3202>   /Ie:\os\src\data\MSRC
3202>   /Ie:\os\public\amd64fre\onecore\external\sdk\inc\atlmfc
3202>   /I..\
3202>   /I..\..\..\common
3202>   /I..\..\..\common\nlohmann
3202>   /Ie:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\aimxsrv\client\dll
3202>   /Ie:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\aimxsrv\client\dll\objfre\amd64
3202>   /Ie:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\aimxsrv\client\dll\..\..\idl\objfre\amd64
3202>   /Ie:\os\src\onecore\ds\security\inc
3202>   /Ie:\os\public\amd64fre\onecore\internal\ds\inc
3202>   /Ie:\os\public\amd64fre\onecore\private\ds\inc\security\base
3202>   /Ie:\os\public\amd64fre\onecore\private\base\inc
3202>   /Ie:\os\public\amd64fre\onecore\internal\base\inc
3202>   /Ie:\os\public\amd64fre\OneCore\Internal\MinWin\Priv_Sdk\Inc
3202>   /Ie:\os\public\amd64fre\OneCore\Internal\MinWin\Priv_Sdk\Inc\lsa
3202>   /Ie:\os\public\amd64fre\OneCore\Internal\MinWin\Priv_Sdk\Inc\apiset
3202>   /Ie:\os\public\amd64fre\OneCore\Internal\MinWin\Priv_Sdk\Inc\ntos
3202>   /Ie:\os\public\amd64fre\OneCore\Private\MinWin\Priv_Sdk\Inc
3202>   /Ie:\os\public\amd64fre\OneCore\Private\MinWin\Priv_Sdk\Inc\lsa
3202>   /Ie:\os\public\amd64fre\OneCore\Internal\MinCore\Priv_Sdk\Inc
3202>   /Ie:\os\public\amd64fre\internal\sdk\inc
3202>   /Ie:\os\public\amd64fre\onecore\internal\sdk\inc
3202>   /Ie:\os\public\amd64fre\OneCore\Internal\OneCore\Priv_Sdk\Inc
3202>   /Ie:\os\src\onecore\ds\ds\src\adai\proto\win32\aimxsrv\inc
3202>   /Ie:\os\src\onecore\ds\ds\src\adai\proto\win32\aimxsrv\client
3202>   /Ie:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\aimxsrv\client\dll\objfre\amd64
3202>   /Ie:\os\src\onecore\ds\inc
3202>   /Ie:\os\obj\amd64fre\onecore\ds\inc\objfre\amd64
3202>   /Ie:\os\public\amd64fre\internal\onecoreds\inc
3202>   /Ie:\os\public\amd64fre\OneCore\Restricted\DS\inc
3202>   /Ie:\os\public\amd64fre\OneCoreUap\Restricted\DS\inc
3202>   /Ie:\os\public\amd64fre\OneCore\External\DS\inc
3202>   /Ie:\os\public\amd64fre\OneCoreUap\External\DS\inc
3202>   /Ie:\os\public\amd64fre\ClientCore\External\DS\inc
3202>   /Ie:\os\public\amd64fre\OneCore\Internal\DS\inc
3202>   /Ie:\os\public\amd64fre\OneCoreUap\Internal\DS\inc
3202>   /Ie:\os\public\amd64fre\ClientCore\Internal\DS\inc
3202>   /Ie:\os\public\amd64fre\OneCore\Private\DS\inc
3202>   /Ie:\os\public\amd64fre\OneCoreUap\Private\DS\inc
3202>   /Ie:\os\public\amd64fre\ClientCore\Private\DS\inc
3202>   /Ie:\os\public\amd64fre\OneCore\external\DS\inc
3202>   /Ie:\os\public\amd64fre\OneCore\restricted\DS\inc
3202>   /Ie:\os\public\amd64fre\OneCore\internal\DS\inc
3202>   /Ie:\os\public\amd64fre\OneCore\private\DS\inc
3202>   /Ie:\os\public\amd64fre\onecore\external\oak\inc
3202>   /Ie:\os\public\amd64fre\onecoreuap\external\oak\inc
3202>   /Ie:\os\public\amd64fre\shared\inc
3202>   /Ie:\os\public\amd64fre\onecore\external\shared\inc
3202>   /Ie:\os\public\amd64fre\onecoreuap\external\shared\inc
3202>   /Ie:\os\public\amd64fre\onecore\external\shared\inc\MinWin
3202>   /Ie:\os\public\amd64fre\sdk\inc
3202>   /Ie:\os\public\amd64fre\onecore\external\sdk\inc
3202>   /Ie:\os\public\amd64fre\onecoreuap\external\sdk\inc
3202>   /Ie:\os\public\amd64fre\onecore\private\sdk\inc\MinWin
3202>   /Ie:\os\public\amd64fre\onecore\internal\sdk\inc\MinWin
3202>   /Ie:\os\public\amd64fre\onecore\external\sdk\inc\MinWin
3202>   /Ie:\os\public\amd64fre\onecore\internal\sdk\inc\MinCore
3202>   /Ie:\os\public\amd64fre\onecore\external\sdk\inc\MinCore
3202>   /Ie:\os\public\amd64fre\onecore\internal\sdk\inc\ClientCore
3202>   /Ie:\os\public\amd64fre\onecore\external\sdk\inc\ClientCore
3202>   /Ie:\os\public\amd64fre\onecore\internal\sdk\inc\ModernCore
3202>   /Ie:\os\public\amd64fre\onecore\external\sdk\inc\ModernCore
3202>   /Ie:\os\public\amd64fre\shared\inc
3202>   /Ie:\os\public\amd64fre\onecore\external\shared\inc
3202>   /Ie:\os\public\amd64fre\onecoreuap\external\shared\inc
3202>   /Ie:\os\public\amd64fre\onecore\external\ddk\inc
3202>   /Ie:\os\public\amd64fre\onecoreuap\external\ddk\inc
3202>   /Ie:\os\public\amd64fre\onecore\external\ddk\inc\wdm
3202>   /Ie:\os\public\amd64fre\onecoreuap\external\ddk\inc\wdm
3202>   /Ie:\os\public\amd64fre\internal\sdk\inc
3202>   /Ie:\os\public\amd64fre\onecore\private\sdk\inc
3202>   /Ie:\os\public\amd64fre\onecoreuap\private\sdk\inc
3202>   /Ie:\os\public\amd64fre\onecore\internal\sdk\inc
3202>   /Ie:\os\public\amd64fre\onecore\restricted\sdk\inc
3202>   /Ie:\os\public\amd64fre\onecoreuap\internal\sdk\inc
3202>   /Ie:\os\public\amd64fre\onecoreuap\restricted\sdk\inc
3202>   /Ie:\os\public\amd64fre\onecore\internal\sdk\inc\MinWin
3202>   /Ie:\os\public\amd64fre\onecore\internal\sdk\inc\MinWin\fs
3202>   /Ie:\os\public\amd64fre\onecore\internal\sdk\inc\MinCore
3202>   /Ie:\os\public\amd64fre\onecore\internal\sdk\inc\ClientCore
3202>   /Ie:\os\public\amd64fre\onecore\internal\sdk\inc\ModernCore
3202>   /Ie:\os\public\amd64fre\OneCore\Internal\hv\hvsdk\just_built\inc\private
3202>   /Ie:\os\public\amd64fre\OneCore\Internal\hv\hvsdk\just_built\inc\internal
3202>   /Ie:\os\public\amd64fre\sdk\inc\ucrt
3202>   /Ie:\os\public\amd64fre\internal\sdk\inc\ucrt
3202>   /Ie:\os\public\amd64fre\onecore\external\sdk\inc\ucrt
3202>   /Ie:\os\public\amd64fre\onecore\internal\sdk\inc\ucrt
3202>   /Ie:\os\public\amd64fre\onecore\internal\sdk\inc\ucrt\stl120
3202>   /Ie:\os\public\amd64fre\onecore\internal\sdk\inc\ucrt\stl120
3202>   /D_WIN64
3202>   /D_AMD64_
3202>   /DAMD64
3202>   /DCONDITION_HANDLING=1
3202>   /DNT_INST=0
3202>   /DWIN32=100
3202>   /D_NT1X_=100
3202>   /DWINNT=1
3202>   /D_WIN32_WINNT=0x0A00
3202>   /DWINVER=0x0A00
3202>   /D_WIN32_IE=0x0A00
3202>   /DWIN32_LEAN_AND_MEAN=1
3202>   /DDEVL=1
3202>   /DNDEBUG
3202>   /D_STL120_
3202>   /D_STL140_
3202>   /D_DLL=1
3202>   /D_MT=1
3202>   -DNT_IUM
3202>   -DWIN32
3202>   -D_WIN32
3202>   -DUNICODE
3202>   -D_UNICODE
3202>   -DAIMXCLIENT_EXPORTS
3202>   -D_ARM_WINAPI_PARTITION_DESKTOP_SDK_AVAILABLE
3202>   /D_USE_DEV11_CRT
3202>   -D_APISET_MINWIN_VERSION=0x0115
3202>   -D_APISET_MINCORE_VERSION=0x0114
3202>   /DFE_SB
3202>   /DFE_IME
3202>   /DNTDDI_VERSION=0x0A000011
3202>   /DWINBLUE_KBSPRING14
3202>   /DBUILD_WINDOWS
3202>   /DUNDOCKED_WINDOWS_UCRT
3202>   /D__WRL_CONFIGURATION_LEGACY__
3202>   /DBUILD_UMS_ENABLED=1
3202>   /DBUILD_WOW64_ENABLED=1
3202>   /DBUILD_ARM64X_ENABLED=0
3202>   /DEXECUTABLE_WRITES_SUPPORT=0
3202>   -D_USE_DECLSPECS_FOR_SAL=1
3202>   /DRUN_WPP
3202>   -D__PLACEHOLDER_SAL=1
3202>   /D_ATL_STATIC_REGISTRY
3202>   /D_WINDLL
3202>   /c
3202>   /Zc:wchar_t-
3202>   /Zl
3202>   /Zp8
3202>   /Gy
3202>   /W4
3202>   /d1import_no_registry
3202>   /EHsc
3202>   /GR-
3202>   /GF
3202>   /GS
3202>   /Z7
3202>   /Oxs
3202>   /GL
3202>   /Z7
3202>   @e:\os\src\tools\NMakeJS\WarningsCop\WarningsCop.Cxx.DefaultErrors.rsp
3202>   /we4308 /we4509 /we4510 /we4532 /we4533 /we4610 /we4700 /we4789
3202>   /w15043
3202>   /Zc:rvalueCast
3202>   /Zo
3202>   -D_UCRT
3202>   -D_CONST_RETURN=
3202>   -D_CRT_SECURE_NO_WARNINGS
3202>   -D_CRT_NON_CONFORMING_SWPRINTFS
3202>   -D_CRT_NONSTDC_NO_WARNINGS
3202>   -D_CRT_STDIO_ARBITRARY_WIDE_SPECIFIERS
3202>   /D_CRT_STDIO_INLINE=extern
3202>   /D_NO_CRT_STDIO_INLINE
3202>   /D_ACRTIMP_ALT=
3202>   /D_SILENCE_STDEXT_HASH_DEPRECATION_WARNINGS
3202>   /D_STL_EXTRA_DISABLED_WARNINGS=4239
3202>   /D_SILENCE_TR1_NAMESPACE_DEPRECATION_WARNING
3202>   /D_SILENCE_ALL_CXX17_DEPRECATION_WARNINGS
3202>   /D_SILENCE_TR2_SYS_NAMESPACE_DEPRECATION_WARNING
3202>   /D_HAS_FUNCTION_ALLOCATOR_SUPPORT=1
3202>   /D_SILENCE_STDEXT_ALLOCATORS_DEPRECATION_WARNING
3202>   /D_HAS_STD_BYTE=0
3202>   /D_ENFORCE_MATCHING_ALLOCATORS=0
3202>   /D_HAS_FUNCTION_ALLOCATOR_SUPPORT=1
3202>   /D_SILENCE_STDEXT_ALLOCATORS_DEPRECATION_WARNING
3202>   /D_FULL_IOBUF
3202>   /d1initAll:Mask11
3202>   /d1initAll:FillPattern0
3202>   /d1nodatetime
3202>   /d1trimfile:e:\os\src\=BASEDIR
3202>   /d1trimfile:e:\os\public\amd64fre\=PUBLIC_ROOT
3202>   /d1trimfile:e:\os\obj\amd64fre\=OBJECT_ROOT
3202>   /d1trimfile:e:\os\bin\amd64fre\=_NTTREE
3202>   /d1trimfile:e:\os\osdep\=OSDEPENDSROOT
3202>   /d2AllowCompatibleILVersions
3202>   /d2Zi+
3202>   /ZH:SHA_256
3202>   /wd4986
3202>   /wd4987
3202>   /wd4471
3202>   /wd4369
3202>   /wd4309
3202>   /wd4754
3202>   /wd4427
3202>   /d2DeepThoughtInliner-
3202>   /d2implyavx512upperregs-
3202>   /Wv:19.23
3202>   /Fwe:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\aimxsrv\client\dll\objfre\amd64\
3202>   @e:\os\obj\amd64fre\objfre\amd64\DMF\logged-warnings.rsp
3202>   /wl4002
3202>   /wl4003
3202>   /wl4005
3202>   /wl4006
3202>   /wl4007
3202>   /wl4008
3202>   /wl4010
3202>   /wl4013
3202>   /wl4015
3202>   /wl4018
3202>   /wl4020
3202>   /wl4022
3202>   /wl4024
3202>   /wl4025
3202>   /wl4026
3202>   /wl4027
3202>   /wl4028
3202>   /wl4029
3202>   /wl4030
3202>   /wl4031
3202>   /wl4033
3202>   /wl4034
3202>   /wl4036
3202>   /wl4038
3202>   /wl4041
3202>   /wl4042
3202>   /wl4045
3202>   /wl4047
3202>   /wl4048
3202>   /wl4049
3202>   /wl4056
3202>   /wl4066
3202>   /wl4067
3202>   /wl4068
3202>   /wl4073
3202>   /wl4074
3202>   /wl4075
3202>   /wl4076
3202>   /wl4077
3202>   /wl4079
3202>   /wl4080
3202>   /wl4081
3202>   /wl4083
3202>   /wl4085
3202>   /wl4086
3202>   /wl4087
3202>   /wl4088
3202>   /wl4089
3202>   /wl4090
3202>   /wl4091
3202>   /wl4094
3202>   /wl4096
3202>   /wl4097
3202>   /wl4098
3202>   /wl4099
3202>   /wl4101
3202>   /wl4102
3202>   /wl4109
3202>   /wl4112
3202>   /wl4113
3202>   /wl4114
3202>   /wl4115
3202>   /wl4116
3202>   /wl4117
3202>   /wl4119
3202>   /wl4120
3202>   /wl4122
3202>   /wl4124
3202>   /wl4129
3202>   /wl4133
3202>   /wl4138
3202>   /wl4141
3202>   /wl4142
3202>   /wl4143
3202>   /wl4144
3202>   /wl4145
3202>   /wl4150
3202>   /wl4153
3202>   /wl4154
3202>   /wl4155
3202>   /wl4156
3202>   /wl4157
3202>   /wl4158
3202>   /wl4159
3202>   /wl4160
3202>   /wl4161
3202>   /wl4162
3202>   /wl4163
3202>   /wl4164
3202>   /wl4166
3202>   /wl4167
3202>   /wl4168
3202>   /wl4172
3202>   /wl4174
3202>   /wl4175
3202>   /wl4176
3202>   /wl4177
3202>   /wl4178
3202>   /wl4180
3202>   /wl4182
3202>   /wl4183
3202>   /wl4185
3202>   /wl4186
3202>   /wl4187
3202>   /wl4190
3202>   /wl4192
3202>   /wl4197
3202>   /wl4200
3202>   /wl4213
3202>   /wl4215
3202>   /wl4216
3202>   /wl4218
3202>   /wl4223
3202>   /wl4224
3202>   /wl4226
3202>   /wl4227
3202>   /wl4228
3202>   /wl4229
3202>   /wl4230
3202>   /wl4237
3202>   /wl4240
3202>   /wl4243
3202>   /wl4244
3202>   /wl4250
3202>   /wl4251
3202>   /wl4258
3202>   /wl4267
3202>   /wl4269
3202>   /wl4272
3202>   /wl4273
3202>   /wl4274
3202>   /wl4275
3202>   /wl4276
3202>   /wl4278
3202>   /wl4280
3202>   /wl4281
3202>   /wl4282
3202>   /wl4283
3202>   /wl4285
3202>   /wl4286
3202>   /wl4288
3202>   /wl4290
3202>   /wl4291
3202>   /wl4293
3202>   /wl4297
3202>   /wl4302
3202>   /wl4305
3202>   /wl4306
3202>   /wl4307
3202>   /wl4309
3202>   /wl4310
3202>   /wl4311
3202>   /wl4312
3202>   /wl4313
3202>   /wl4316
3202>   /wl4319
3202>   /wl4325
3202>   /wl4326
3202>   /wl4329
3202>   /wl4333
3202>   /wl4334
3202>   /wl4335
3202>   /wl4340
3202>   /wl4344
3202>   /wl4346
3202>   /wl4348
3202>   /wl4353
3202>   /wl4356
3202>   /wl4357
3202>   /wl4358
3202>   /wl4359
3202>   /wl4364
3202>   /wl4368
3202>   /wl4369
3202>   /wl4373
3202>   /wl4374
3202>   /wl4375
3202>   /wl4376
3202>   /wl4377
3202>   /wl4378
3202>   /wl4379
3202>   /wl4381
3202>   /wl4382
3202>   /wl4383
3202>   /wl4384
3202>   /wl4390
3202>   /wl4391
3202>   /wl4392
3202>   /wl4393
3202>   /wl4394
3202>   /wl4395
3202>   /wl4396
3202>   /wl4397
3202>   /wl4398
3202>   /wl4399
3202>   /wl4600
3202>   /wl4401
3202>   /wl4402
3202>   /wl4403
3202>   /wl4404
3202>   /wl4405
3202>   /wl4406
3202>   /wl4407
3202>   /wl4409
3202>   /wl4410
3202>   /wl4411
3202>   /wl4414
3202>   /wl4420
3202>   /wl4430
3202>   /wl4436
3202>   /wl4439
3202>   /wl4440
3202>   /wl4441
3202>   /wl4445
3202>   /wl4461
3202>   /wl4462
3202>   /wl4470
3202>   /wl4473
3202>   /wl4477
3202>   /wl4484
3202>   /wl4485
3202>   /wl4486
3202>   /wl4488
3202>   /wl4489
3202>   /wl4490
3202>   /wl4502
3202>   /wl4503
3202>   /wl4506
3202>   /wl4508
3202>   /wl4511
3202>   /wl4518
3202>   /wl4521
3202>   /wl4522
3202>   /wl4523
3202>   /wl4526
3202>   /wl4530
3202>   /wl4534
3202>   /wl4535
3202>   /wl4537
3202>   /wl4538
3202>   /wl4540
3202>   /wl4541
3202>   /wl4543
3202>   /wl4544
3202>   /wl4550
3202>   /wl4551
3202>   /wl4552
3202>   /wl4553
3202>   /wl4554
3202>   /wl4556
3202>   /wl4558
3202>   /wl4561
3202>   /wl4566
3202>   /wl4570
3202>   /wl4572
3202>   /wl4580
3202>   /wl4581
3202>   /wl4584
3202>   /wl4596
3202>   /wl4597
3202>   /wl4602
3202>   /wl4603
3202>   /wl4606
3202>   /wl4612
3202>   /wl4613
3202>   /wl4615
3202>   /wl4616
3202>   /wl4618
3202>   /wl4620
3202>   /wl4621
3202>   /wl4622
3202>   /wl4624
3202>   /wl4627
3202>   /wl4630
3202>   /wl4632
3202>   /wl4633
3202>   /wl4635
3202>   /wl4636
3202>   /wl4637
3202>   /wl4638
3202>   /wl4641
3202>   /wl4645
3202>   /wl4646
3202>   /wl4650
3202>   /wl4651
3202>   /wl4652
3202>   /wl4653
3202>   /wl4655
3202>   /wl4656
3202>   /wl4657
3202>   /wl4659
3202>   /wl4661
3202>   /wl4662
3202>   /wl4667
3202>   /wl4669
3202>   /wl4674
3202>   /wl4677
3202>   /wl4678
3202>   /wl4679
3202>   /wl4683
3202>   /wl4684
3202>   /wl4685
3202>   /wl4687
3202>   /wl4688
3202>   /wl4691
3202>   /wl4693
3202>   /wl4694
3202>   /wl4698
3202>   /wl4711
3202>   /wl4715
3202>   /wl4716
3202>   /wl4717
3202>   /wl4722
3202>   /wl4723
3202>   /wl4724
3202>   /wl4727
3202>   /wl4730
3202>   /wl4731
3202>   /wl4733
3202>   /wl4739
3202>   /wl4742
3202>   /wl4743
3202>   /wl4744
3202>   /wl4747
3202>   /wl4750
3202>   /wl4756
3202>   /wl4768
3202>   /wl4772
3202>   /wl4788
3202>   /wl4793
3202>   /wl4794
3202>   /wl4799
3202>   /wl4803
3202>   /wl4804
3202>   /wl4805
3202>   /wl4806
3202>   /wl4807
3202>   /wl4810
3202>   /wl4811
3202>   /wl4812
3202>   /wl4813
3202>   /wl4817
3202>   /wl4819
3202>   /wl4821
3202>   /wl4823
3202>   /wl4829
3202>   /wl4834
3202>   /wl4835
3202>   /wl4838
3202>   /wl4839
3202>   /wl4867
3202>   /wl4900
3202>   /wl4910
3202>   /wl4912
3202>   /wl4920
3202>   /wl4925
3202>   /wl4926
3202>   /wl4927
3202>   /wl4929
3202>   /wl4930
3202>   /wl4935
3202>   /wl4936
3202>   /wl4939
3202>   /wl4944
3202>   /wl4945
3202>   /wl4947
3202>   /wl4948
3202>   /wl4949
3202>   /wl4950
3202>   /wl4951
3202>   /wl4952
3202>   /wl4953
3202>   /wl4956
3202>   /wl4957
3202>   /wl4958
3202>   /wl4959
3202>   /wl4961
3202>   /wl4964
3202>   /wl4965
3202>   /wl4972
3202>   /wl4984
3202>   /wl4995
3202>   /wl4996
3202>   /wl4997
3202>   /wl4999
3202>   /wl5033
3202>   /wl5037
3202>   /wl5046
3202>   /wl5050
3202>   /wl5055
3202>   /wl5056
3202>   /wl5105
3202>   /wl5208
3202>   /d2Qvec-mathlib-
3202>   /d2Qvec-sse2only
3202>   /Gw
3202>   /Zc:checkGwOdr
3202>   /d1ignorePragmaWarningError
3202>   /wd4316
3202>   /wd4973
3202>   /DDONT_DISABLE_PCH_WARNINGS_IN_WARNING_H
3202>   /d2FH4
3202>   /Brepro
3202>   -D_HAS_MAGIC_STATICS=1
3202>   /Qspectre
3202>   /wd5045
3202>   /d2guardspecanalysismode:v1_0
3202>   /d2guardspecmode2
3202>   /guard:cf
3202>   /d2guardcfgfuncptr-
3202>   /d2guardcfgdispatch
3202>   /guard:ehcont
3202>   -D__PLACEHOLDER_SAL=1
3202>   -wd4425
3202>   @e:\os\obj\amd64fre\objfre\amd64\WarningsCop\OneCore.rsp
3202>   /wl4146 /wl4308 /wl4509 /wl4510 /wl4532 /wl4533 /wl4610 /wl4700 /wl4701 /wl4703 /wl4789
3202>   /FIe:\os\public\amd64fre\onecore\internal\sdk\inc\warning.h
3202>   /std:c++17
3202>   /Ylaimxclient
3202>   /Ycpch.hxx
3202>   /Fpe:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\aimxsrv\client\dll\objfre\amd64\pch.pch
3202>   /Fo"e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\aimxsrv\client\dll\objfre\amd64\pch.obj"
3202>pch_hdr.src
3202> e:\os\tools\vc\HostX86\amd64\cl.exe @e:\os\obj\amd64fre\temp\192f640bcdca1336790fcd186ea18ad0\cl_1.rsp
3202>Microsoft (R) C/C++ Optimizing Compiler Version 19.42.34444.100 for x64
3202>Copyright (C) Microsoft Corporation.  All rights reserved.
3202>cl /Fo"e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\aimxsrv\client\dll\objfre\amd64/"
3202>   /FC
3202>   /Iamd64
3202>   /I.
3202>   /Ie:\os\src\data\MSRC
3202>   /Ie:\os\public\amd64fre\onecore\external\sdk\inc\atlmfc
3202>   /I..\
3202>   /I..\..\..\common
3202>   /I..\..\..\common\nlohmann
3202>   /Ie:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\aimxsrv\client\dll
3202>   /Ie:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\aimxsrv\client\dll\objfre\amd64
3202>   /Ie:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\aimxsrv\client\dll\..\..\idl\objfre\amd64
3202>   /Ie:\os\src\onecore\ds\security\inc
3202>   /Ie:\os\public\amd64fre\onecore\internal\ds\inc
3202>   /Ie:\os\public\amd64fre\onecore\private\ds\inc\security\base
3202>   /Ie:\os\public\amd64fre\onecore\private\base\inc
3202>   /Ie:\os\public\amd64fre\onecore\internal\base\inc
3202>   /Ie:\os\public\amd64fre\OneCore\Internal\MinWin\Priv_Sdk\Inc
3202>   /Ie:\os\public\amd64fre\OneCore\Internal\MinWin\Priv_Sdk\Inc\lsa
3202>   /Ie:\os\public\amd64fre\OneCore\Internal\MinWin\Priv_Sdk\Inc\apiset
3202>   /Ie:\os\public\amd64fre\OneCore\Internal\MinWin\Priv_Sdk\Inc\ntos
3202>   /Ie:\os\public\amd64fre\OneCore\Private\MinWin\Priv_Sdk\Inc
3202>   /Ie:\os\public\amd64fre\OneCore\Private\MinWin\Priv_Sdk\Inc\lsa
3202>   /Ie:\os\public\amd64fre\OneCore\Internal\MinCore\Priv_Sdk\Inc
3202>   /Ie:\os\public\amd64fre\internal\sdk\inc
3202>   /Ie:\os\public\amd64fre\onecore\internal\sdk\inc
3202>   /Ie:\os\public\amd64fre\OneCore\Internal\OneCore\Priv_Sdk\Inc
3202>   /Ie:\os\src\onecore\ds\ds\src\adai\proto\win32\aimxsrv\inc
3202>   /Ie:\os\src\onecore\ds\ds\src\adai\proto\win32\aimxsrv\client
3202>   /Ie:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\aimxsrv\client\dll\objfre\amd64
3202>   /Ie:\os\src\onecore\ds\inc
3202>   /Ie:\os\obj\amd64fre\onecore\ds\inc\objfre\amd64
3202>   /Ie:\os\public\amd64fre\internal\onecoreds\inc
3202>   /Ie:\os\public\amd64fre\OneCore\Restricted\DS\inc
3202>   /Ie:\os\public\amd64fre\OneCoreUap\Restricted\DS\inc
3202>   /Ie:\os\public\amd64fre\OneCore\External\DS\inc
3202>   /Ie:\os\public\amd64fre\OneCoreUap\External\DS\inc
3202>   /Ie:\os\public\amd64fre\ClientCore\External\DS\inc
3202>   /Ie:\os\public\amd64fre\OneCore\Internal\DS\inc
3202>   /Ie:\os\public\amd64fre\OneCoreUap\Internal\DS\inc
3202>   /Ie:\os\public\amd64fre\ClientCore\Internal\DS\inc
3202>   /Ie:\os\public\amd64fre\OneCore\Private\DS\inc
3202>   /Ie:\os\public\amd64fre\OneCoreUap\Private\DS\inc
3202>   /Ie:\os\public\amd64fre\ClientCore\Private\DS\inc
3202>   /Ie:\os\public\amd64fre\OneCore\external\DS\inc
3202>   /Ie:\os\public\amd64fre\OneCore\restricted\DS\inc
3202>   /Ie:\os\public\amd64fre\OneCore\internal\DS\inc
3202>   /Ie:\os\public\amd64fre\OneCore\private\DS\inc
3202>   /Ie:\os\public\amd64fre\onecore\external\oak\inc
3202>   /Ie:\os\public\amd64fre\onecoreuap\external\oak\inc
3202>   /Ie:\os\public\amd64fre\shared\inc
3202>   /Ie:\os\public\amd64fre\onecore\external\shared\inc
3202>   /Ie:\os\public\amd64fre\onecoreuap\external\shared\inc
3202>   /Ie:\os\public\amd64fre\onecore\external\shared\inc\MinWin
3202>   /Ie:\os\public\amd64fre\sdk\inc
3202>   /Ie:\os\public\amd64fre\onecore\external\sdk\inc
3202>   /Ie:\os\public\amd64fre\onecoreuap\external\sdk\inc
3202>   /Ie:\os\public\amd64fre\onecore\private\sdk\inc\MinWin
3202>   /Ie:\os\public\amd64fre\onecore\internal\sdk\inc\MinWin
3202>   /Ie:\os\public\amd64fre\onecore\external\sdk\inc\MinWin
3202>   /Ie:\os\public\amd64fre\onecore\internal\sdk\inc\MinCore
3202>   /Ie:\os\public\amd64fre\onecore\external\sdk\inc\MinCore
3202>   /Ie:\os\public\amd64fre\onecore\internal\sdk\inc\ClientCore
3202>   /Ie:\os\public\amd64fre\onecore\external\sdk\inc\ClientCore
3202>   /Ie:\os\public\amd64fre\onecore\internal\sdk\inc\ModernCore
3202>   /Ie:\os\public\amd64fre\onecore\external\sdk\inc\ModernCore
3202>   /Ie:\os\public\amd64fre\shared\inc
3202>   /Ie:\os\public\amd64fre\onecore\external\shared\inc
3202>   /Ie:\os\public\amd64fre\onecoreuap\external\shared\inc
3202>   /Ie:\os\public\amd64fre\onecore\external\ddk\inc
3202>   /Ie:\os\public\amd64fre\onecoreuap\external\ddk\inc
3202>   /Ie:\os\public\amd64fre\onecore\external\ddk\inc\wdm
3202>   /Ie:\os\public\amd64fre\onecoreuap\external\ddk\inc\wdm
3202>   /Ie:\os\public\amd64fre\internal\sdk\inc
3202>   /Ie:\os\public\amd64fre\onecore\private\sdk\inc
3202>   /Ie:\os\public\amd64fre\onecoreuap\private\sdk\inc
3202>   /Ie:\os\public\amd64fre\onecore\internal\sdk\inc
3202>   /Ie:\os\public\amd64fre\onecore\restricted\sdk\inc
3202>   /Ie:\os\public\amd64fre\onecoreuap\internal\sdk\inc
3202>   /Ie:\os\public\amd64fre\onecoreuap\restricted\sdk\inc
3202>   /Ie:\os\public\amd64fre\onecore\internal\sdk\inc\MinWin
3202>   /Ie:\os\public\amd64fre\onecore\internal\sdk\inc\MinWin\fs
3202>   /Ie:\os\public\amd64fre\onecore\internal\sdk\inc\MinCore
3202>   /Ie:\os\public\amd64fre\onecore\internal\sdk\inc\ClientCore
3202>   /Ie:\os\public\amd64fre\onecore\internal\sdk\inc\ModernCore
3202>   /Ie:\os\public\amd64fre\OneCore\Internal\hv\hvsdk\just_built\inc\private
3202>   /Ie:\os\public\amd64fre\OneCore\Internal\hv\hvsdk\just_built\inc\internal
3202>   /Ie:\os\public\amd64fre\sdk\inc\ucrt
3202>   /Ie:\os\public\amd64fre\internal\sdk\inc\ucrt
3202>   /Ie:\os\public\amd64fre\onecore\external\sdk\inc\ucrt
3202>   /Ie:\os\public\amd64fre\onecore\internal\sdk\inc\ucrt
3202>   /Ie:\os\public\amd64fre\onecore\internal\sdk\inc\ucrt\stl120
3202>   /Ie:\os\public\amd64fre\onecore\internal\sdk\inc\ucrt\stl120
3202>   /D_WIN64
3202>   /D_AMD64_
3202>   /DAMD64
3202>   /DCONDITION_HANDLING=1
3202>   /DNT_INST=0
3202>   /DWIN32=100
3202>   /D_NT1X_=100
3202>   /DWINNT=1
3202>   /D_WIN32_WINNT=0x0A00
3202>   /DWINVER=0x0A00
3202>   /D_WIN32_IE=0x0A00
3202>   /DWIN32_LEAN_AND_MEAN=1
3202>   /DDEVL=1
3202>   /DNDEBUG
3202>   /D_STL120_
3202>   /D_STL140_
3202>   /D_DLL=1
3202>   /D_MT=1
3202>   -DNT_IUM
3202>   -DWIN32
3202>   -D_WIN32
3202>   -DUNICODE
3202>   -D_UNICODE
3202>   -DAIMXCLIENT_EXPORTS
3202>   -D_ARM_WINAPI_PARTITION_DESKTOP_SDK_AVAILABLE
3202>   /D_USE_DEV11_CRT
3202>   -D_APISET_MINWIN_VERSION=0x0115
3202>   -D_APISET_MINCORE_VERSION=0x0114
3202>   /DFE_SB
3202>   /DFE_IME
3202>   /DNTDDI_VERSION=0x0A000011
3202>   /DWINBLUE_KBSPRING14
3202>   /DBUILD_WINDOWS
3202>   /DUNDOCKED_WINDOWS_UCRT
3202>   /D__WRL_CONFIGURATION_LEGACY__
3202>   /DBUILD_UMS_ENABLED=1
3202>   /DBUILD_WOW64_ENABLED=1
3202>   /DBUILD_ARM64X_ENABLED=0
3202>   /DEXECUTABLE_WRITES_SUPPORT=0
3202>   -D_USE_DECLSPECS_FOR_SAL=1
3202>   /DRUN_WPP
3202>   -D__PLACEHOLDER_SAL=1
3202>   /D_ATL_STATIC_REGISTRY
3202>   /D_WINDLL
3202>   /c
3202>   /Zc:wchar_t-
3202>   /Zl
3202>   /Zp8
3202>   /Gy
3202>   /W4
3202>   /d1import_no_registry
3202>   /EHsc
3202>   /GR-
3202>   /GF
3202>   /GS
3202>   /Z7
3202>   /Oxs
3202>   /GL
3202>   /Z7
3202>   @e:\os\src\tools\NMakeJS\WarningsCop\WarningsCop.Cxx.DefaultErrors.rsp
3202>   /we4308 /we4509 /we4510 /we4532 /we4533 /we4610 /we4700 /we4789
3202>   /w15043
3202>   /Zc:rvalueCast
3202>   /Zo
3202>   -D_UCRT
3202>   -D_CONST_RETURN=
3202>   -D_CRT_SECURE_NO_WARNINGS
3202>   -D_CRT_NON_CONFORMING_SWPRINTFS
3202>   -D_CRT_NONSTDC_NO_WARNINGS
3202>   -D_CRT_STDIO_ARBITRARY_WIDE_SPECIFIERS
3202>   /D_CRT_STDIO_INLINE=extern
3202>   /D_NO_CRT_STDIO_INLINE
3202>   /D_ACRTIMP_ALT=
3202>   /D_SILENCE_STDEXT_HASH_DEPRECATION_WARNINGS
3202>   /D_STL_EXTRA_DISABLED_WARNINGS=4239
3202>   /D_SILENCE_TR1_NAMESPACE_DEPRECATION_WARNING
3202>   /D_SILENCE_ALL_CXX17_DEPRECATION_WARNINGS
3202>   /D_SILENCE_TR2_SYS_NAMESPACE_DEPRECATION_WARNING
3202>   /D_HAS_FUNCTION_ALLOCATOR_SUPPORT=1
3202>   /D_SILENCE_STDEXT_ALLOCATORS_DEPRECATION_WARNING
3202>   /D_HAS_STD_BYTE=0
3202>   /D_ENFORCE_MATCHING_ALLOCATORS=0
3202>   /D_HAS_FUNCTION_ALLOCATOR_SUPPORT=1
3202>   /D_SILENCE_STDEXT_ALLOCATORS_DEPRECATION_WARNING
3202>   /D_FULL_IOBUF
3202>   /d1initAll:Mask11
3202>   /d1initAll:FillPattern0
3202>   /d1nodatetime
3202>   /d1trimfile:e:\os\src\=BASEDIR
3202>   /d1trimfile:e:\os\public\amd64fre\=PUBLIC_ROOT
3202>   /d1trimfile:e:\os\obj\amd64fre\=OBJECT_ROOT
3202>   /d1trimfile:e:\os\bin\amd64fre\=_NTTREE
3202>   /d1trimfile:e:\os\osdep\=OSDEPENDSROOT
3202>   /d2AllowCompatibleILVersions
3202>   /d2Zi+
3202>   /ZH:SHA_256
3202>   /wd4986
3202>   /wd4987
3202>   /wd4471
3202>   /wd4369
3202>   /wd4309
3202>   /wd4754
3202>   /wd4427
3202>   /d2DeepThoughtInliner-
3202>   /d2implyavx512upperregs-
3202>   /Wv:19.23
3202>   /Fwe:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\aimxsrv\client\dll\objfre\amd64\
3202>   @e:\os\obj\amd64fre\objfre\amd64\DMF\logged-warnings.rsp
3202>   /wl4002
3202>   /wl4003
3202>   /wl4005
3202>   /wl4006
3202>   /wl4007
3202>   /wl4008
3202>   /wl4010
3202>   /wl4013
3202>   /wl4015
3202>   /wl4018
3202>   /wl4020
3202>   /wl4022
3202>   /wl4024
3202>   /wl4025
3202>   /wl4026
3202>   /wl4027
3202>   /wl4028
3202>   /wl4029
3202>   /wl4030
3202>   /wl4031
3202>   /wl4033
3202>   /wl4034
3202>   /wl4036
3202>   /wl4038
3202>   /wl4041
3202>   /wl4042
3202>   /wl4045
3202>   /wl4047
3202>   /wl4048
3202>   /wl4049
3202>   /wl4056
3202>   /wl4066
3202>   /wl4067
3202>   /wl4068
3202>   /wl4073
3202>   /wl4074
3202>   /wl4075
3202>   /wl4076
3202>   /wl4077
3202>   /wl4079
3202>   /wl4080
3202>   /wl4081
3202>   /wl4083
3202>   /wl4085
3202>   /wl4086
3202>   /wl4087
3202>   /wl4088
3202>   /wl4089
3202>   /wl4090
3202>   /wl4091
3202>   /wl4094
3202>   /wl4096
3202>   /wl4097
3202>   /wl4098
3202>   /wl4099
3202>   /wl4101
3202>   /wl4102
3202>   /wl4109
3202>   /wl4112
3202>   /wl4113
3202>   /wl4114
3202>   /wl4115
3202>   /wl4116
3202>   /wl4117
3202>   /wl4119
3202>   /wl4120
3202>   /wl4122
3202>   /wl4124
3202>   /wl4129
3202>   /wl4133
3202>   /wl4138
3202>   /wl4141
3202>   /wl4142
3202>   /wl4143
3202>   /wl4144
3202>   /wl4145
3202>   /wl4150
3202>   /wl4153
3202>   /wl4154
3202>   /wl4155
3202>   /wl4156
3202>   /wl4157
3202>   /wl4158
3202>   /wl4159
3202>   /wl4160
3202>   /wl4161
3202>   /wl4162
3202>   /wl4163
3202>   /wl4164
3202>   /wl4166
3202>   /wl4167
3202>   /wl4168
3202>   /wl4172
3202>   /wl4174
3202>   /wl4175
3202>   /wl4176
3202>   /wl4177
3202>   /wl4178
3202>   /wl4180
3202>   /wl4182
3202>   /wl4183
3202>   /wl4185
3202>   /wl4186
3202>   /wl4187
3202>   /wl4190
3202>   /wl4192
3202>   /wl4197
3202>   /wl4200
3202>   /wl4213
3202>   /wl4215
3202>   /wl4216
3202>   /wl4218
3202>   /wl4223
3202>   /wl4224
3202>   /wl4226
3202>   /wl4227
3202>   /wl4228
3202>   /wl4229
3202>   /wl4230
3202>   /wl4237
3202>   /wl4240
3202>   /wl4243
3202>   /wl4244
3202>   /wl4250
3202>   /wl4251
3202>   /wl4258
3202>   /wl4267
3202>   /wl4269
3202>   /wl4272
3202>   /wl4273
3202>   /wl4274
3202>   /wl4275
3202>   /wl4276
3202>   /wl4278
3202>   /wl4280
3202>   /wl4281
3202>   /wl4282
3202>   /wl4283
3202>   /wl4285
3202>   /wl4286
3202>   /wl4288
3202>   /wl4290
3202>   /wl4291
3202>   /wl4293
3202>   /wl4297
3202>   /wl4302
3202>   /wl4305
3202>   /wl4306
3202>   /wl4307
3202>   /wl4309
3202>   /wl4310
3202>   /wl4311
3202>   /wl4312
3202>   /wl4313
3202>   /wl4316
3202>   /wl4319
3202>   /wl4325
3202>   /wl4326
3202>   /wl4329
3202>   /wl4333
3202>   /wl4334
3202>   /wl4335
3202>   /wl4340
3202>   /wl4344
3202>   /wl4346
3202>   /wl4348
3202>   /wl4353
3202>   /wl4356
3202>   /wl4357
3202>   /wl4358
3202>   /wl4359
3202>   /wl4364
3202>   /wl4368
3202>   /wl4369
3202>   /wl4373
3202>   /wl4374
3202>   /wl4375
3202>   /wl4376
3202>   /wl4377
3202>   /wl4378
3202>   /wl4379
3202>   /wl4381
3202>   /wl4382
3202>   /wl4383
3202>   /wl4384
3202>   /wl4390
3202>   /wl4391
3202>   /wl4392
3202>   /wl4393
3202>   /wl4394
3202>   /wl4395
3202>   /wl4396
3202>   /wl4397
3202>   /wl4398
3202>   /wl4399
3202>   /wl4600
3202>   /wl4401
3202>   /wl4402
3202>   /wl4403
3202>   /wl4404
3202>   /wl4405
3202>   /wl4406
3202>   /wl4407
3202>   /wl4409
3202>   /wl4410
3202>   /wl4411
3202>   /wl4414
3202>   /wl4420
3202>   /wl4430
3202>   /wl4436
3202>   /wl4439
3202>   /wl4440
3202>   /wl4441
3202>   /wl4445
3202>   /wl4461
3202>   /wl4462
3202>   /wl4470
3202>   /wl4473
3202>   /wl4477
3202>   /wl4484
3202>   /wl4485
3202>   /wl4486
3202>   /wl4488
3202>   /wl4489
3202>   /wl4490
3202>   /wl4502
3202>   /wl4503
3202>   /wl4506
3202>   /wl4508
3202>   /wl4511
3202>   /wl4518
3202>   /wl4521
3202>   /wl4522
3202>   /wl4523
3202>   /wl4526
3202>   /wl4530
3202>   /wl4534
3202>   /wl4535
3202>   /wl4537
3202>   /wl4538
3202>   /wl4540
3202>   /wl4541
3202>   /wl4543
3202>   /wl4544
3202>   /wl4550
3202>   /wl4551
3202>   /wl4552
3202>   /wl4553
3202>   /wl4554
3202>   /wl4556
3202>   /wl4558
3202>   /wl4561
3202>   /wl4566
3202>   /wl4570
3202>   /wl4572
3202>   /wl4580
3202>   /wl4581
3202>   /wl4584
3202>   /wl4596
3202>   /wl4597
3202>   /wl4602
3202>   /wl4603
3202>   /wl4606
3202>   /wl4612
3202>   /wl4613
3202>   /wl4615
3202>   /wl4616
3202>   /wl4618
3202>   /wl4620
3202>   /wl4621
3202>   /wl4622
3202>   /wl4624
3202>   /wl4627
3202>   /wl4630
3202>   /wl4632
3202>   /wl4633
3202>   /wl4635
3202>   /wl4636
3202>   /wl4637
3202>   /wl4638
3202>   /wl4641
3202>   /wl4645
3202>   /wl4646
3202>   /wl4650
3202>   /wl4651
3202>   /wl4652
3202>   /wl4653
3202>   /wl4655
3202>   /wl4656
3202>   /wl4657
3202>   /wl4659
3202>   /wl4661
3202>   /wl4662
3202>   /wl4667
3202>   /wl4669
3202>   /wl4674
3202>   /wl4677
3202>   /wl4678
3202>   /wl4679
3202>   /wl4683
3202>   /wl4684
3202>   /wl4685
3202>   /wl4687
3202>   /wl4688
3202>   /wl4691
3202>   /wl4693
3202>   /wl4694
3202>   /wl4698
3202>   /wl4711
3202>   /wl4715
3202>   /wl4716
3202>   /wl4717
3202>   /wl4722
3202>   /wl4723
3202>   /wl4724
3202>   /wl4727
3202>   /wl4730
3202>   /wl4731
3202>   /wl4733
3202>   /wl4739
3202>   /wl4742
3202>   /wl4743
3202>   /wl4744
3202>   /wl4747
3202>   /wl4750
3202>   /wl4756
3202>   /wl4768
3202>   /wl4772
3202>   /wl4788
3202>   /wl4793
3202>   /wl4794
3202>   /wl4799
3202>   /wl4803
3202>   /wl4804
3202>   /wl4805
3202>   /wl4806
3202>   /wl4807
3202>   /wl4810
3202>   /wl4811
3202>   /wl4812
3202>   /wl4813
3202>   /wl4817
3202>   /wl4819
3202>   /wl4821
3202>   /wl4823
3202>   /wl4829
3202>   /wl4834
3202>   /wl4835
3202>   /wl4838
3202>   /wl4839
3202>   /wl4867
3202>   /wl4900
3202>   /wl4910
3202>   /wl4912
3202>   /wl4920
3202>   /wl4925
3202>   /wl4926
3202>   /wl4927
3202>   /wl4929
3202>   /wl4930
3202>   /wl4935
3202>   /wl4936
3202>   /wl4939
3202>   /wl4944
3202>   /wl4945
3202>   /wl4947
3202>   /wl4948
3202>   /wl4949
3202>   /wl4950
3202>   /wl4951
3202>   /wl4952
3202>   /wl4953
3202>   /wl4956
3202>   /wl4957
3202>   /wl4958
3202>   /wl4959
3202>   /wl4961
3202>   /wl4964
3202>   /wl4965
3202>   /wl4972
3202>   /wl4984
3202>   /wl4995
3202>   /wl4996
3202>   /wl4997
3202>   /wl4999
3202>   /wl5033
3202>   /wl5037
3202>   /wl5046
3202>   /wl5050
3202>   /wl5055
3202>   /wl5056
3202>   /wl5105
3202>   /wl5208
3202>   /d2Qvec-mathlib-
3202>   /d2Qvec-sse2only
3202>   /Gw
3202>   /Zc:checkGwOdr
3202>   /d1ignorePragmaWarningError
3202>   /wd4316
3202>   /wd4973
3202>   /DDONT_DISABLE_PCH_WARNINGS_IN_WARNING_H
3202>   /d2FH4
3202>   /Brepro
3202>   -D_HAS_MAGIC_STATICS=1
3202>   /Qspectre
3202>   /wd5045
3202>   /d2guardspecanalysismode:v1_0
3202>   /d2guardspecmode2
3202>   /guard:cf
3202>   /d2guardcfgfuncptr-
3202>   /d2guardcfgdispatch
3202>   /guard:ehcont
3202>   -D__PLACEHOLDER_SAL=1
3202>   -wd4425
3202>   @e:\os\obj\amd64fre\objfre\amd64\WarningsCop\OneCore.rsp
3202>   /wl4146 /wl4308 /wl4509 /wl4510 /wl4532 /wl4533 /wl4610 /wl4700 /wl4701 /wl4703 /wl4789
3202>   /FIe:\os\public\amd64fre\onecore\internal\sdk\inc\warning.h
3202>   /std:c++17
3202>   /Yupch.hxx /Fpe:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\aimxsrv\client\dll\objfre\amd64\pch.pch
3202>   ..\aimxclient.cpp ..\aimxrpcclient.cpp ..\memory.cpp 
3202>aimxclient.cpp
3202>aimxrpcclient.cpp
3202>memory.cpp
3202> e:\os\tools\vc\HostX64\amd64\link.exe /lib /out:e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\aimxsrv\client\dll\objfre\amd64\aimxclient.lib @e:\os\obj\amd64fre\temp\192f640bcdca1336790fcd186ea18ad0\lib_1.rsp
3202>Microsoft (R) Library Manager Version 14.42.34444.100
3202>Copyright (C) Microsoft Corporation.  All rights reserved.
3202>/IGNORE:4078,4221,4281,4006,4198 
3202>/nodefaultlib 
3202>/machine:amd64 
3202>/ltcg 
3202>/Brepro 
3202>/def:aimxclient.def 
3202>e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\aimxsrv\client\dll\objfre\amd64\aimxclient.obj 
3202>e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\aimxsrv\client\dll\objfre\amd64\aimxrpcclient.obj 
3202>e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\aimxsrv\client\dll\objfre\amd64\memory.obj 
3202>e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\aimxsrv\client\dll\objfre\amd64\pch.obj 
3202>   Creating library e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\aimxsrv\client\dll\objfre\amd64\aimxclient.lib and object e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\aimxsrv\client\dll\objfre\amd64\aimxclient.exp
3202>Writing out macros...e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\aimxsrv\client\dll\objfre\amd64\Macros-PASS1.txt
3202>binplace e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\aimxsrv\client\dll\objfre\amd64\Macros-PASS1.txt
3202> e:\os\tools\powershell\pwsh.exe -NoProfile e:\os\src\tools\NMakeJS\WarningsCop\WarningsCop.ps1 -MakeSubDir "onecore\ds\ds\src\aimx\prod\aimxsrv\client\dll" -Pass PASS1 -BaselineFile e:\os\src\.config\OneCore\WarningsCop.json -OutputDir "e:\os\bin\amd64fre\evidence\WarningsCop\OneCore\onecoreds"
3202>WarningsCop.ps1 : WarningsCop: Processing onecore\ds\ds\src\aimx\prod\aimxsrv\client\dll in pass PASS1
3206>Calculated LAYERINFO_MODULE='OneCoreDS'.
3206>makefile.def: TEMP=e:\os\obj\amd64fre\temp\35671a26037c997bcf9b0f93722637cd
3206>makefile.def: BUILDINGINDATT=
3206>[Core OS Undocking] NOT using package ''
3206>UCRT enabled: dir 'e:\os\src\onecore\ds\ds\src\aimx\prod\mcpserversample' (target 'HelloMcpServer', type 'LIBRARY', nt_target_version '0xA000011')
3206>ObjectsMac.ts: validation succeeded
3206>STL version 120 used in "e:\os\src\onecore\ds\ds\src\aimx\prod\mcpserversample" (STL_VER_TELEMETRY)
3206>_NEED_BUILDDATE not defined setting BUILDDATE to an invalid value.
3206>A subdirectory or file e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\mcpserversample\objfre\amd64 already exists.
3206> e:\os\tools\Windows.Desktop.Tools.amd64\tools\touch.exe /c e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\mcpserversample\objfre\amd64\_PASS1_Marker.log
3206> e:\os\tools\vc\HostX86\amd64\cl.exe @e:\os\obj\amd64fre\temp\35671a26037c997bcf9b0f93722637cd\cl_1.rsp
3206>Microsoft (R) C/C++ Optimizing Compiler Version 19.42.34444.100 for x64
3206>Copyright (C) Microsoft Corporation.  All rights reserved.
3206>cl /Fo"e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\mcpserversample\objfre\amd64/"
3206>   /FC
3206>   /Iamd64
3206>   /I.
3206>   /Ie:\os\src\data\MSRC
3206>   /Ie:\os\public\amd64fre\OneCore\Internal\MinWin\Priv_Sdk\Inc
3206>   /Ie:\os\public\amd64fre\OneCoreUap\Internal\BuildMetadata\internal\cppwinrt
3206>   /Ie:\os\public\amd64fre\onecoreuap\restricted\windows\inc
3206>   /I..\common
3206>   /I..\llmclientlib
3206>   /I..\aimxsrv\inc
3206>   /I..\aimxsrv\server
3206>   /I..\aimxsrv\client
3206>   /Ie:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\mcpserversample\..\aimxsrv\idl\objfre\amd64
3206>   /Ie:\os\public\amd64fre\OneCore\Internal\OneCore\Priv_Sdk\Inc
3206>   /Ie:\os\public\amd64fre\onecore\internal\base\inc\appmodel\runtime\winrt
3206>   /Ie:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\mcpserversample\objfre\amd64
3206>   /Ie:\os\src\onecore\ds\inc
3206>   /Ie:\os\obj\amd64fre\onecore\ds\inc\objfre\amd64
3206>   /Ie:\os\public\amd64fre\internal\onecoreds\inc
3206>   /Ie:\os\public\amd64fre\OneCore\Restricted\DS\inc
3206>   /Ie:\os\public\amd64fre\OneCoreUap\Restricted\DS\inc
3206>   /Ie:\os\public\amd64fre\OneCore\External\DS\inc
3206>   /Ie:\os\public\amd64fre\OneCoreUap\External\DS\inc
3206>   /Ie:\os\public\amd64fre\ClientCore\External\DS\inc
3206>   /Ie:\os\public\amd64fre\OneCore\Internal\DS\inc
3206>   /Ie:\os\public\amd64fre\OneCoreUap\Internal\DS\inc
3206>   /Ie:\os\public\amd64fre\ClientCore\Internal\DS\inc
3206>   /Ie:\os\public\amd64fre\OneCore\Private\DS\inc
3206>   /Ie:\os\public\amd64fre\OneCoreUap\Private\DS\inc
3206>   /Ie:\os\public\amd64fre\ClientCore\Private\DS\inc
3206>   /Ie:\os\public\amd64fre\OneCore\external\DS\inc
3206>   /Ie:\os\public\amd64fre\OneCore\restricted\DS\inc
3206>   /Ie:\os\public\amd64fre\OneCore\internal\DS\inc
3206>   /Ie:\os\public\amd64fre\OneCore\private\DS\inc
3206>   /Ie:\os\public\amd64fre\onecore\external\oak\inc
3206>   /Ie:\os\public\amd64fre\onecoreuap\external\oak\inc
3206>   /Ie:\os\public\amd64fre\shared\inc
3206>   /Ie:\os\public\amd64fre\onecore\external\shared\inc
3206>   /Ie:\os\public\amd64fre\onecoreuap\external\shared\inc
3206>   /Ie:\os\public\amd64fre\onecore\external\shared\inc\MinWin
3206>   /Ie:\os\public\amd64fre\sdk\inc
3206>   /Ie:\os\public\amd64fre\onecore\external\sdk\inc
3206>   /Ie:\os\public\amd64fre\onecoreuap\external\sdk\inc
3206>   /Ie:\os\public\amd64fre\onecore\private\sdk\inc\MinWin
3206>   /Ie:\os\public\amd64fre\onecore\internal\sdk\inc\MinWin
3206>   /Ie:\os\public\amd64fre\onecore\external\sdk\inc\MinWin
3206>   /Ie:\os\public\amd64fre\onecore\internal\sdk\inc\MinCore
3206>   /Ie:\os\public\amd64fre\onecore\external\sdk\inc\MinCore
3206>   /Ie:\os\public\amd64fre\onecore\internal\sdk\inc\ClientCore
3206>   /Ie:\os\public\amd64fre\onecore\external\sdk\inc\ClientCore
3206>   /Ie:\os\public\amd64fre\onecore\internal\sdk\inc\ModernCore
3206>   /Ie:\os\public\amd64fre\onecore\external\sdk\inc\ModernCore
3206>   /Ie:\os\public\amd64fre\shared\inc
3206>   /Ie:\os\public\amd64fre\onecore\external\shared\inc
3206>   /Ie:\os\public\amd64fre\onecoreuap\external\shared\inc
3206>   /Ie:\os\public\amd64fre\onecore\external\ddk\inc
3206>   /Ie:\os\public\amd64fre\onecoreuap\external\ddk\inc
3206>   /Ie:\os\public\amd64fre\onecore\external\ddk\inc\wdm
3206>   /Ie:\os\public\amd64fre\onecoreuap\external\ddk\inc\wdm
3206>   /Ie:\os\public\amd64fre\internal\sdk\inc
3206>   /Ie:\os\public\amd64fre\onecore\private\sdk\inc
3206>   /Ie:\os\public\amd64fre\onecoreuap\private\sdk\inc
3206>   /Ie:\os\public\amd64fre\onecore\internal\sdk\inc
3206>   /Ie:\os\public\amd64fre\onecore\restricted\sdk\inc
3206>   /Ie:\os\public\amd64fre\onecoreuap\internal\sdk\inc
3206>   /Ie:\os\public\amd64fre\onecoreuap\restricted\sdk\inc
3206>   /Ie:\os\public\amd64fre\onecore\internal\sdk\inc\MinWin
3206>   /Ie:\os\public\amd64fre\onecore\internal\sdk\inc\MinWin\fs
3206>   /Ie:\os\public\amd64fre\onecore\internal\sdk\inc\MinCore
3206>   /Ie:\os\public\amd64fre\onecore\internal\sdk\inc\ClientCore
3206>   /Ie:\os\public\amd64fre\onecore\internal\sdk\inc\ModernCore
3206>   /Ie:\os\public\amd64fre\OneCore\Internal\hv\hvsdk\just_built\inc\private
3206>   /Ie:\os\public\amd64fre\OneCore\Internal\hv\hvsdk\just_built\inc\internal
3206>   /Ie:\os\public\amd64fre\sdk\inc\ucrt
3206>   /Ie:\os\public\amd64fre\internal\sdk\inc\ucrt
3206>   /Ie:\os\public\amd64fre\onecore\external\sdk\inc\ucrt
3206>   /Ie:\os\public\amd64fre\onecore\internal\sdk\inc\ucrt
3206>   /Ie:\os\public\amd64fre\onecore\internal\sdk\inc\ucrt\stl120
3206>   /Ie:\os\public\amd64fre\onecore\internal\sdk\inc\ucrt\stl120
3206>   /D_WIN64
3206>   /D_AMD64_
3206>   /DAMD64
3206>   /DCONDITION_HANDLING=1
3206>   /DNT_INST=0
3206>   /DWIN32=100
3206>   /D_NT1X_=100
3206>   /DWINNT=1
3206>   /D_WIN32_WINNT=0x0A00
3206>   /DWINVER=0x0A00
3206>   /D_WIN32_IE=0x0A00
3206>   /DWIN32_LEAN_AND_MEAN=1
3206>   /DDEVL=1
3206>   /DNDEBUG
3206>   /D_STL120_
3206>   /D_STL140_
3206>   /D_DLL=1
3206>   /D_MT=1
3206>   -DNT_IUM
3206>   -DWIN32
3206>   -D_WIN32
3206>   -DUNICODE
3206>   -D_UNICODE
3206>   -D_ARM_WINAPI_PARTITION_DESKTOP_SDK_AVAILABLE
3206>   /D_USE_DEV11_CRT
3206>   -D_APISET_MINWIN_VERSION=0x0115
3206>   -D_APISET_MINCORE_VERSION=0x0114
3206>   /DFE_SB
3206>   /DFE_IME
3206>   /DNTDDI_VERSION=0x0A000011
3206>   /DWINBLUE_KBSPRING14
3206>   /DBUILD_WINDOWS
3206>   /DUNDOCKED_WINDOWS_UCRT
3206>   /D__WRL_CONFIGURATION_LEGACY__
3206>   /DBUILD_UMS_ENABLED=1
3206>   /DBUILD_WOW64_ENABLED=1
3206>   /DBUILD_ARM64X_ENABLED=0
3206>   /DEXECUTABLE_WRITES_SUPPORT=0
3206>   -D_USE_DECLSPECS_FOR_SAL=1
3206>   /DRUN_WPP
3206>   -D__PLACEHOLDER_SAL=1
3206>   /c
3206>   /Zc:wchar_t-
3206>   /Zl
3206>   /Zp8
3206>   /Gy
3206>   /W4
3206>   /wd4244
3206>   /EHsc
3206>   /d1import_no_registry
3206>   /EHsc
3206>   /GR-
3206>   /GF
3206>   /GS
3206>   /Z7
3206>   /Oxs
3206>   /GL
3206>   /Z7
3206>   @e:\os\src\tools\NMakeJS\WarningsCop\WarningsCop.Cxx.DefaultErrors.rsp
3206>   /we4308 /we4509 /we4510 /we4532 /we4533 /we4610 /we4700 /we4789
3206>   /w15043
3206>   /Zc:rvalueCast
3206>   -D_UCRT
3206>   -D_CONST_RETURN=
3206>   -D_CRT_SECURE_NO_WARNINGS
3206>   -D_CRT_NON_CONFORMING_SWPRINTFS
3206>   -D_CRT_NONSTDC_NO_WARNINGS
3206>   -D_CRT_STDIO_ARBITRARY_WIDE_SPECIFIERS
3206>   /D_CRT_STDIO_INLINE=extern
3206>   /D_NO_CRT_STDIO_INLINE
3206>   /D_ACRTIMP_ALT=
3206>   /D_SILENCE_STDEXT_HASH_DEPRECATION_WARNINGS
3206>   /D_STL_EXTRA_DISABLED_WARNINGS=4239
3206>   /D_SILENCE_TR1_NAMESPACE_DEPRECATION_WARNING
3206>   /D_SILENCE_ALL_CXX17_DEPRECATION_WARNINGS
3206>   /D_SILENCE_TR2_SYS_NAMESPACE_DEPRECATION_WARNING
3206>   /D_HAS_FUNCTION_ALLOCATOR_SUPPORT=1
3206>   /D_SILENCE_STDEXT_ALLOCATORS_DEPRECATION_WARNING
3206>   /D_HAS_STD_BYTE=0
3206>   /D_ENFORCE_MATCHING_ALLOCATORS=0
3206>   /D_HAS_FUNCTION_ALLOCATOR_SUPPORT=1
3206>   /D_SILENCE_STDEXT_ALLOCATORS_DEPRECATION_WARNING
3206>   /D_FULL_IOBUF
3206>   /d1initAll:Mask11
3206>   /d1initAll:FillPattern0
3206>   /d1nodatetime
3206>   /d1trimfile:e:\os\src\=BASEDIR
3206>   /d1trimfile:e:\os\public\amd64fre\=PUBLIC_ROOT
3206>   /d1trimfile:e:\os\obj\amd64fre\=OBJECT_ROOT
3206>   /d1trimfile:e:\os\bin\amd64fre\=_NTTREE
3206>   /d1trimfile:e:\os\osdep\=OSDEPENDSROOT
3206>   /d2AllowCompatibleILVersions
3206>   /d2Zi+
3206>   /ZH:SHA_256
3206>   /wd4986
3206>   /wd4987
3206>   /wd4471
3206>   /wd4369
3206>   /wd4309
3206>   /wd4754
3206>   /wd4427
3206>   /d2DeepThoughtInliner-
3206>   /d2implyavx512upperregs-
3206>   /Wv:19.23
3206>   /Fwe:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\mcpserversample\objfre\amd64\
3206>   @e:\os\obj\amd64fre\objfre\amd64\DMF\logged-warnings.rsp
3206>   /wl4002
3206>   /wl4003
3206>   /wl4005
3206>   /wl4006
3206>   /wl4007
3206>   /wl4008
3206>   /wl4010
3206>   /wl4013
3206>   /wl4015
3206>   /wl4018
3206>   /wl4020
3206>   /wl4022
3206>   /wl4024
3206>   /wl4025
3206>   /wl4026
3206>   /wl4027
3206>   /wl4028
3206>   /wl4029
3206>   /wl4030
3206>   /wl4031
3206>   /wl4033
3206>   /wl4034
3206>   /wl4036
3206>   /wl4038
3206>   /wl4041
3206>   /wl4042
3206>   /wl4045
3206>   /wl4047
3206>   /wl4048
3206>   /wl4049
3206>   /wl4056
3206>   /wl4066
3206>   /wl4067
3206>   /wl4068
3206>   /wl4073
3206>   /wl4074
3206>   /wl4075
3206>   /wl4076
3206>   /wl4077
3206>   /wl4079
3206>   /wl4080
3206>   /wl4081
3206>   /wl4083
3206>   /wl4085
3206>   /wl4086
3206>   /wl4087
3206>   /wl4088
3206>   /wl4089
3206>   /wl4090
3206>   /wl4091
3206>   /wl4094
3206>   /wl4096
3206>   /wl4097
3206>   /wl4098
3206>   /wl4099
3206>   /wl4101
3206>   /wl4102
3206>   /wl4109
3206>   /wl4112
3206>   /wl4113
3206>   /wl4114
3206>   /wl4115
3206>   /wl4116
3206>   /wl4117
3206>   /wl4119
3206>   /wl4120
3206>   /wl4122
3206>   /wl4124
3206>   /wl4129
3206>   /wl4133
3206>   /wl4138
3206>   /wl4141
3206>   /wl4142
3206>   /wl4143
3206>   /wl4144
3206>   /wl4145
3206>   /wl4150
3206>   /wl4153
3206>   /wl4154
3206>   /wl4155
3206>   /wl4156
3206>   /wl4157
3206>   /wl4158
3206>   /wl4159
3206>   /wl4160
3206>   /wl4161
3206>   /wl4162
3206>   /wl4163
3206>   /wl4164
3206>   /wl4166
3206>   /wl4167
3206>   /wl4168
3206>   /wl4172
3206>   /wl4174
3206>   /wl4175
3206>   /wl4176
3206>   /wl4177
3206>   /wl4178
3206>   /wl4180
3206>   /wl4182
3206>   /wl4183
3206>   /wl4185
3206>   /wl4186
3206>   /wl4187
3206>   /wl4190
3206>   /wl4192
3206>   /wl4197
3206>   /wl4200
3206>   /wl4213
3206>   /wl4215
3206>   /wl4216
3206>   /wl4218
3206>   /wl4223
3206>   /wl4224
3206>   /wl4226
3206>   /wl4227
3206>   /wl4228
3206>   /wl4229
3206>   /wl4230
3206>   /wl4237
3206>   /wl4240
3206>   /wl4243
3206>   /wl4244
3206>   /wl4250
3206>   /wl4251
3206>   /wl4258
3206>   /wl4267
3206>   /wl4269
3206>   /wl4272
3206>   /wl4273
3206>   /wl4274
3206>   /wl4275
3206>   /wl4276
3206>   /wl4278
3206>   /wl4280
3206>   /wl4281
3206>   /wl4282
3206>   /wl4283
3206>   /wl4285
3206>   /wl4286
3206>   /wl4288
3206>   /wl4290
3206>   /wl4291
3206>   /wl4293
3206>   /wl4297
3206>   /wl4302
3206>   /wl4305
3206>   /wl4306
3206>   /wl4307
3206>   /wl4309
3206>   /wl4310
3206>   /wl4311
3206>   /wl4312
3206>   /wl4313
3206>   /wl4316
3206>   /wl4319
3206>   /wl4325
3206>   /wl4326
3206>   /wl4329
3206>   /wl4333
3206>   /wl4334
3206>   /wl4335
3206>   /wl4340
3206>   /wl4344
3206>   /wl4346
3206>   /wl4348
3206>   /wl4353
3206>   /wl4356
3206>   /wl4357
3206>   /wl4358
3206>   /wl4359
3206>   /wl4364
3206>   /wl4368
3206>   /wl4369
3206>   /wl4373
3206>   /wl4374
3206>   /wl4375
3206>   /wl4376
3206>   /wl4377
3206>   /wl4378
3206>   /wl4379
3206>   /wl4381
3206>   /wl4382
3206>   /wl4383
3206>   /wl4384
3206>   /wl4390
3206>   /wl4391
3206>   /wl4392
3206>   /wl4393
3206>   /wl4394
3206>   /wl4395
3206>   /wl4396
3206>   /wl4397
3206>   /wl4398
3206>   /wl4399
3206>   /wl4600
3206>   /wl4401
3206>   /wl4402
3206>   /wl4403
3206>   /wl4404
3206>   /wl4405
3206>   /wl4406
3206>   /wl4407
3206>   /wl4409
3206>   /wl4410
3206>   /wl4411
3206>   /wl4414
3206>   /wl4420
3206>   /wl4430
3206>   /wl4436
3206>   /wl4439
3206>   /wl4440
3206>   /wl4441
3206>   /wl4445
3206>   /wl4461
3206>   /wl4462
3206>   /wl4470
3206>   /wl4473
3206>   /wl4477
3206>   /wl4484
3206>   /wl4485
3206>   /wl4486
3206>   /wl4488
3206>   /wl4489
3206>   /wl4490
3206>   /wl4502
3206>   /wl4503
3206>   /wl4506
3206>   /wl4508
3206>   /wl4511
3206>   /wl4518
3206>   /wl4521
3206>   /wl4522
3206>   /wl4523
3206>   /wl4526
3206>   /wl4530
3206>   /wl4534
3206>   /wl4535
3206>   /wl4537
3206>   /wl4538
3206>   /wl4540
3206>   /wl4541
3206>   /wl4543
3206>   /wl4544
3206>   /wl4550
3206>   /wl4551
3206>   /wl4552
3206>   /wl4553
3206>   /wl4554
3206>   /wl4556
3206>   /wl4558
3206>   /wl4561
3206>   /wl4566
3206>   /wl4570
3206>   /wl4572
3206>   /wl4580
3206>   /wl4581
3206>   /wl4584
3206>   /wl4596
3206>   /wl4597
3206>   /wl4602
3206>   /wl4603
3206>   /wl4606
3206>   /wl4612
3206>   /wl4613
3206>   /wl4615
3206>   /wl4616
3206>   /wl4618
3206>   /wl4620
3206>   /wl4621
3206>   /wl4622
3206>   /wl4624
3206>   /wl4627
3206>   /wl4630
3206>   /wl4632
3206>   /wl4633
3206>   /wl4635
3206>   /wl4636
3206>   /wl4637
3206>   /wl4638
3206>   /wl4641
3206>   /wl4645
3206>   /wl4646
3206>   /wl4650
3206>   /wl4651
3206>   /wl4652
3206>   /wl4653
3206>   /wl4655
3206>   /wl4656
3206>   /wl4657
3206>   /wl4659
3206>   /wl4661
3206>   /wl4662
3206>   /wl4667
3206>   /wl4669
3206>   /wl4674
3206>   /wl4677
3206>   /wl4678
3206>   /wl4679
3206>   /wl4683
3206>   /wl4684
3206>   /wl4685
3206>   /wl4687
3206>   /wl4688
3206>   /wl4691
3206>   /wl4693
3206>   /wl4694
3206>   /wl4698
3206>   /wl4711
3206>   /wl4715
3206>   /wl4716
3206>   /wl4717
3206>   /wl4722
3206>   /wl4723
3206>   /wl4724
3206>   /wl4727
3206>   /wl4730
3206>   /wl4731
3206>   /wl4733
3206>   /wl4739
3206>   /wl4742
3206>   /wl4743
3206>   /wl4744
3206>   /wl4747
3206>   /wl4750
3206>   /wl4756
3206>   /wl4768
3206>   /wl4772
3206>   /wl4788
3206>   /wl4793
3206>   /wl4794
3206>   /wl4799
3206>   /wl4803
3206>   /wl4804
3206>   /wl4805
3206>   /wl4806
3206>   /wl4807
3206>   /wl4810
3206>   /wl4811
3206>   /wl4812
3206>   /wl4813
3206>   /wl4817
3206>   /wl4819
3206>   /wl4821
3206>   /wl4823
3206>   /wl4829
3206>   /wl4834
3206>   /wl4835
3206>   /wl4838
3206>   /wl4839
3206>   /wl4867
3206>   /wl4900
3206>   /wl4910
3206>   /wl4912
3206>   /wl4920
3206>   /wl4925
3206>   /wl4926
3206>   /wl4927
3206>   /wl4929
3206>   /wl4930
3206>   /wl4935
3206>   /wl4936
3206>   /wl4939
3206>   /wl4944
3206>   /wl4945
3206>   /wl4947
3206>   /wl4948
3206>   /wl4949
3206>   /wl4950
3206>   /wl4951
3206>   /wl4952
3206>   /wl4953
3206>   /wl4956
3206>   /wl4957
3206>   /wl4958
3206>   /wl4959
3206>   /wl4961
3206>   /wl4964
3206>   /wl4965
3206>   /wl4972
3206>   /wl4984
3206>   /wl4995
3206>   /wl4996
3206>   /wl4997
3206>   /wl4999
3206>   /wl5033
3206>   /wl5037
3206>   /wl5046
3206>   /wl5050
3206>   /wl5055
3206>   /wl5056
3206>   /wl5105
3206>   /wl5208
3206>   /d2Qvec-mathlib-
3206>   /d2Qvec-sse2only
3206>   /Gw
3206>   /Zc:checkGwOdr
3206>   /d1ignorePragmaWarningError
3206>   /wd4316
3206>   /wd4973
3206>   /DDONT_DISABLE_PCH_WARNINGS_IN_WARNING_H
3206>   /d2FH4
3206>   /Brepro
3206>   -D_HAS_MAGIC_STATICS=1
3206>   /Qspectre
3206>   /wd5045
3206>   /d2guardspecanalysismode:v1_0
3206>   /d2guardspecmode2
3206>   /guard:cf
3206>   /d2guardcfgfuncptr-
3206>   /d2guardcfgdispatch
3206>   /guard:ehcont
3206>   -D__PLACEHOLDER_SAL=1
3206>   -wd4425
3206>   @e:\os\obj\amd64fre\objfre\amd64\WarningsCop\OneCore.rsp
3206>   /wl4146 /wl4308 /wl4509 /wl4510 /wl4532 /wl4533 /wl4610 /wl4700 /wl4701 /wl4703 /wl4789
3206>   /FIe:\os\public\amd64fre\onecore\internal\sdk\inc\warning.h
3206>   /std:c++17
3206>   .\hellomcpserver.cpp 
3206>hellomcpserver.cpp
3206> e:\os\tools\vc\HostX64\amd64\link.exe /lib /out:e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\mcpserversample\objfre\amd64\HelloMcpServer.lib /IGNORE:4078,4221,4281,4006,4198   /nodefaultlib /machine:amd64 /ltcg /Brepro @e:\os\obj\amd64fre\temp\35671a26037c997bcf9b0f93722637cd\lib_1.rsp
3206>Microsoft (R) Library Manager Version 14.42.34444.100
3206>Copyright (C) Microsoft Corporation.  All rights reserved.
3206>e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\mcpserversample\objfre\amd64\hellomcpserver.obj 
3206>Writing out macros...e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\mcpserversample\objfre\amd64\Macros-PASS1.txt
3206>binplace e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\mcpserversample\objfre\amd64\Macros-PASS1.txt
3206> e:\os\tools\powershell\pwsh.exe -NoProfile e:\os\src\tools\NMakeJS\WarningsCop\WarningsCop.ps1 -MakeSubDir "onecore\ds\ds\src\aimx\prod\mcpserversample" -Pass PASS1 -BaselineFile e:\os\src\.config\OneCore\WarningsCop.json -OutputDir "e:\os\bin\amd64fre\evidence\WarningsCop\OneCore\onecoreds"
3206>WarningsCop.ps1 : WarningsCop: Processing onecore\ds\ds\src\aimx\prod\mcpserversample in pass PASS1
3205>Calculated LAYERINFO_MODULE='OneCoreDS'.
3205>makefile.def: TEMP=e:\os\obj\amd64fre\temp\473ea2b47cf95a1173e4930f17d35497
3205>makefile.def: BUILDINGINDATT=
3205>[Core OS Undocking] NOT using package ''
3205>UCRT enabled: dir 'e:\os\src\onecore\ds\ds\src\aimx\prod\aimxsrv\dll' (target 'aimxsrv', type 'DYNLINK', nt_target_version '0xA000011')
3205>ObjectsMac.ts: validation succeeded
3205>STL version 120 used in "e:\os\src\onecore\ds\ds\src\aimx\prod\aimxsrv\dll" (STL_VER_TELEMETRY)
3205>_NEED_BUILDDATE not defined setting BUILDDATE to an invalid value.
3205>A subdirectory or file e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\aimxsrv\dll\objfre\amd64 already exists.
3205> e:\os\tools\Windows.Desktop.Tools.amd64\tools\touch.exe /c e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\aimxsrv\dll\objfre\amd64\_PASS1_Marker.log
3205> e:\os\tools\vc\HostX86\amd64\cl.exe @e:\os\obj\amd64fre\temp\473ea2b47cf95a1173e4930f17d35497\tmp_26480_1752613375277972200.tmp /Tpe:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\aimxsrv\dll\objfre\amd64\pch_hdr.src
3205>Microsoft (R) C/C++ Optimizing Compiler Version 19.42.34444.100 for x64
3205>Copyright (C) Microsoft Corporation.  All rights reserved.
3205>cl 
3205>   /Iamd64
3205>   /I.
3205>   /Ie:\os\src\data\MSRC
3205>   /Ie:\os\public\amd64fre\onecore\external\sdk\inc\atlmfc
3205>   /I..\..\common
3205>   /I..\..\common\nlohmann
3205>   /I..\..\common\httplib
3205>   /I..\..\admcpsrv
3205>   /I..\..\systeminfoMcpSrv
3205>   /I..\..\AdPsMcpSvr
3205>   /I..\client
3205>   /I..\server
3205>   /Ie:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\aimxsrv\dll
3205>   /Ie:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\aimxsrv\dll\objfre\amd64
3205>   /Ie:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\aimxsrv\dll\..\idl\objfre\amd64
3205>   /Ie:\os\src\onecore\ds\security\inc
3205>   /Ie:\os\public\amd64fre\onecore\internal\ds\inc
3205>   /Ie:\os\public\amd64fre\onecore\private\ds\inc\security\base
3205>   /Ie:\os\public\amd64fre\onecore\private\base\inc
3205>   /Ie:\os\public\amd64fre\onecore\internal\base\inc
3205>   /Ie:\os\public\amd64fre\OneCore\Internal\MinWin\Priv_Sdk\Inc
3205>   /Ie:\os\public\amd64fre\OneCore\Internal\MinWin\Priv_Sdk\Inc\lsa
3205>   /Ie:\os\public\amd64fre\OneCore\Internal\MinWin\Priv_Sdk\Inc\apiset
3205>   /Ie:\os\public\amd64fre\OneCore\Private\MinWin\Priv_Sdk\Inc
3205>   /Ie:\os\public\amd64fre\OneCore\Private\MinWin\Priv_Sdk\Inc\lsa
3205>   /Ie:\os\public\amd64fre\OneCore\Internal\MinCore\Priv_Sdk\Inc
3205>   /Ie:\os\src\onecore\ds\ds\src\aimx\prod\aimxsrv\server
3205>   /Ie:\os\src\onecore\ds\ds\src\aimx\prod\aimxsrv\inc
3205>   /Ie:\os\public\amd64fre\internal\sdk\inc
3205>   /Ie:\os\public\amd64fre\onecore\internal\sdk\inc
3205>   /Ie:\os\public\amd64fre\OneCore\Internal\OneCore\Priv_Sdk\Inc
3205>   /Ie:\os\public\amd64fre\onecore\internal\com\inc
3205>   /Ie:\os\public\amd64fre\OneCoreUap\Internal\BuildMetadata\internal\cppwinrt
3205>   /Ie:\os\public\amd64fre\onecore\internal\base\inc\appmodel\runtime\winrt
3205>   /Ie:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\cpprestsdk\objfre\amd64\vcpkg_installed\target-windows\include
3205>   /Ie:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\aimxsrv\dll\objfre\amd64
3205>   /Ie:\os\src\onecore\ds\inc
3205>   /Ie:\os\obj\amd64fre\onecore\ds\inc\objfre\amd64
3205>   /Ie:\os\public\amd64fre\internal\onecoreds\inc
3205>   /Ie:\os\public\amd64fre\OneCore\Restricted\DS\inc
3205>   /Ie:\os\public\amd64fre\OneCoreUap\Restricted\DS\inc
3205>   /Ie:\os\public\amd64fre\OneCore\External\DS\inc
3205>   /Ie:\os\public\amd64fre\OneCoreUap\External\DS\inc
3205>   /Ie:\os\public\amd64fre\ClientCore\External\DS\inc
3205>   /Ie:\os\public\amd64fre\OneCore\Internal\DS\inc
3205>   /Ie:\os\public\amd64fre\OneCoreUap\Internal\DS\inc
3205>   /Ie:\os\public\amd64fre\ClientCore\Internal\DS\inc
3205>   /Ie:\os\public\amd64fre\OneCore\Private\DS\inc
3205>   /Ie:\os\public\amd64fre\OneCoreUap\Private\DS\inc
3205>   /Ie:\os\public\amd64fre\ClientCore\Private\DS\inc
3205>   /Ie:\os\public\amd64fre\OneCore\external\DS\inc
3205>   /Ie:\os\public\amd64fre\OneCore\restricted\DS\inc
3205>   /Ie:\os\public\amd64fre\OneCore\internal\DS\inc
3205>   /Ie:\os\public\amd64fre\OneCore\private\DS\inc
3205>   /Ie:\os\public\amd64fre\onecore\external\oak\inc
3205>   /Ie:\os\public\amd64fre\onecoreuap\external\oak\inc
3205>   /Ie:\os\public\amd64fre\shared\inc
3205>   /Ie:\os\public\amd64fre\onecore\external\shared\inc
3205>   /Ie:\os\public\amd64fre\onecoreuap\external\shared\inc
3205>   /Ie:\os\public\amd64fre\onecore\external\shared\inc\MinWin
3205>   /Ie:\os\public\amd64fre\sdk\inc
3205>   /Ie:\os\public\amd64fre\onecore\external\sdk\inc
3205>   /Ie:\os\public\amd64fre\onecoreuap\external\sdk\inc
3205>   /Ie:\os\public\amd64fre\onecore\private\sdk\inc\MinWin
3205>   /Ie:\os\public\amd64fre\onecore\internal\sdk\inc\MinWin
3205>   /Ie:\os\public\amd64fre\onecore\external\sdk\inc\MinWin
3205>   /Ie:\os\public\amd64fre\onecore\internal\sdk\inc\MinCore
3205>   /Ie:\os\public\amd64fre\onecore\external\sdk\inc\MinCore
3205>   /Ie:\os\public\amd64fre\onecore\internal\sdk\inc\ClientCore
3205>   /Ie:\os\public\amd64fre\onecore\external\sdk\inc\ClientCore
3205>   /Ie:\os\public\amd64fre\onecore\internal\sdk\inc\ModernCore
3205>   /Ie:\os\public\amd64fre\onecore\external\sdk\inc\ModernCore
3205>   /Ie:\os\public\amd64fre\shared\inc
3205>   /Ie:\os\public\amd64fre\onecore\external\shared\inc
3205>   /Ie:\os\public\amd64fre\onecoreuap\external\shared\inc
3205>   /Ie:\os\public\amd64fre\onecore\external\ddk\inc
3205>   /Ie:\os\public\amd64fre\onecoreuap\external\ddk\inc
3205>   /Ie:\os\public\amd64fre\onecore\external\ddk\inc\wdm
3205>   /Ie:\os\public\amd64fre\onecoreuap\external\ddk\inc\wdm
3205>   /Ie:\os\public\amd64fre\internal\sdk\inc
3205>   /Ie:\os\public\amd64fre\onecore\private\sdk\inc
3205>   /Ie:\os\public\amd64fre\onecoreuap\private\sdk\inc
3205>   /Ie:\os\public\amd64fre\onecore\internal\sdk\inc
3205>   /Ie:\os\public\amd64fre\onecore\restricted\sdk\inc
3205>   /Ie:\os\public\amd64fre\onecoreuap\internal\sdk\inc
3205>   /Ie:\os\public\amd64fre\onecoreuap\restricted\sdk\inc
3205>   /Ie:\os\public\amd64fre\onecore\internal\sdk\inc\MinWin
3205>   /Ie:\os\public\amd64fre\onecore\internal\sdk\inc\MinWin\fs
3205>   /Ie:\os\public\amd64fre\onecore\internal\sdk\inc\MinCore
3205>   /Ie:\os\public\amd64fre\onecore\internal\sdk\inc\ClientCore
3205>   /Ie:\os\public\amd64fre\onecore\internal\sdk\inc\ModernCore
3205>   /Ie:\os\public\amd64fre\OneCore\Internal\hv\hvsdk\just_built\inc\private
3205>   /Ie:\os\public\amd64fre\OneCore\Internal\hv\hvsdk\just_built\inc\internal
3205>   /Ie:\os\public\amd64fre\sdk\inc\ucrt
3205>   /Ie:\os\public\amd64fre\internal\sdk\inc\ucrt
3205>   /Ie:\os\public\amd64fre\onecore\external\sdk\inc\ucrt
3205>   /Ie:\os\public\amd64fre\onecore\internal\sdk\inc\ucrt
3205>   /Ie:\os\public\amd64fre\onecore\internal\sdk\inc\ucrt\stl120
3205>   /Ie:\os\public\amd64fre\onecore\internal\sdk\inc\ucrt\stl120
3205>   /D_WIN64
3205>   /D_AMD64_
3205>   /DAMD64
3205>   /DCONDITION_HANDLING=1
3205>   /DNT_INST=0
3205>   /DWIN32=100
3205>   /D_NT1X_=100
3205>   /DWINNT=1
3205>   /D_WIN32_WINNT=0x0A00
3205>   /DWINVER=0x0A00
3205>   /D_WIN32_IE=0x0A00
3205>   /DWIN32_LEAN_AND_MEAN=1
3205>   /DDEVL=1
3205>   /DNDEBUG
3205>   /D_STL120_
3205>   /D_STL140_
3205>   /D_DLL=1
3205>   /D_MT=1
3205>   -DNT_IUM
3205>   -DWIN32
3205>   -D_WIN32
3205>   -DUNICODE
3205>   -D_UNICODE
3205>   -D_ARM_WINAPI_PARTITION_DESKTOP_SDK_AVAILABLE
3205>   /D_USE_DEV11_CRT
3205>   -D_APISET_MINWIN_VERSION=0x0115
3205>   -D_APISET_MINCORE_VERSION=0x0114
3205>   /DFE_SB
3205>   /DFE_IME
3205>   /DNTDDI_VERSION=0x0A000011
3205>   /DWINBLUE_KBSPRING14
3205>   /DBUILD_WINDOWS
3205>   /DUNDOCKED_WINDOWS_UCRT
3205>   /D__WRL_CONFIGURATION_LEGACY__
3205>   /DBUILD_UMS_ENABLED=1
3205>   /DBUILD_WOW64_ENABLED=1
3205>   /DBUILD_ARM64X_ENABLED=0
3205>   /DEXECUTABLE_WRITES_SUPPORT=0
3205>   -D_USE_DECLSPECS_FOR_SAL=1
3205>   /DRUN_WPP
3205>   -DUNLOADABLE_DELAYLOAD_IMPLEMENTATION
3205>   -D__PLACEHOLDER_SAL=1
3205>   /D_ATL_STATIC_REGISTRY
3205>   /D_WINDLL
3205>   /c
3205>   /Zc:wchar_t-
3205>   /Zl
3205>   /Zp8
3205>   /Gy
3205>   /W4
3205>   /wd4244
3205>   /d1import_no_registry
3205>   /EHsc
3205>   /GR-
3205>   /GF
3205>   /GS
3205>   /Z7
3205>   /Oxs
3205>   /GL
3205>   /Z7
3205>   @e:\os\src\tools\NMakeJS\WarningsCop\WarningsCop.Cxx.DefaultErrors.rsp
3205>   /we4308 /we4509 /we4510 /we4532 /we4533 /we4610 /we4700 /we4789
3205>   /w15043
3205>   /Zc:rvalueCast
3205>   /Zo
3205>   -D_UCRT
3205>   -D_CONST_RETURN=
3205>   -D_CRT_SECURE_NO_WARNINGS
3205>   -D_CRT_NON_CONFORMING_SWPRINTFS
3205>   -D_CRT_NONSTDC_NO_WARNINGS
3205>   -D_CRT_STDIO_ARBITRARY_WIDE_SPECIFIERS
3205>   /D_CRT_STDIO_INLINE=extern
3205>   /D_NO_CRT_STDIO_INLINE
3205>   /D_ACRTIMP_ALT=
3205>   /D_SILENCE_STDEXT_HASH_DEPRECATION_WARNINGS
3205>   /D_STL_EXTRA_DISABLED_WARNINGS=4239
3205>   /D_SILENCE_TR1_NAMESPACE_DEPRECATION_WARNING
3205>   /D_SILENCE_ALL_CXX17_DEPRECATION_WARNINGS
3205>   /D_SILENCE_TR2_SYS_NAMESPACE_DEPRECATION_WARNING
3205>   /D_HAS_FUNCTION_ALLOCATOR_SUPPORT=1
3205>   /D_SILENCE_STDEXT_ALLOCATORS_DEPRECATION_WARNING
3205>   /D_HAS_STD_BYTE=0
3205>   /D_ENFORCE_MATCHING_ALLOCATORS=0
3205>   /D_HAS_FUNCTION_ALLOCATOR_SUPPORT=1
3205>   /D_SILENCE_STDEXT_ALLOCATORS_DEPRECATION_WARNING
3205>   /D_FULL_IOBUF
3205>   /d1initAll:Mask11
3205>   /d1initAll:FillPattern0
3205>   /d1nodatetime
3205>   /d1trimfile:e:\os\src\=BASEDIR
3205>   /d1trimfile:e:\os\public\amd64fre\=PUBLIC_ROOT
3205>   /d1trimfile:e:\os\obj\amd64fre\=OBJECT_ROOT
3205>   /d1trimfile:e:\os\bin\amd64fre\=_NTTREE
3205>   /d1trimfile:e:\os\osdep\=OSDEPENDSROOT
3205>   /d2AllowCompatibleILVersions
3205>   /d2Zi+
3205>   /ZH:SHA_256
3205>   /wd4986
3205>   /wd4987
3205>   /wd4471
3205>   /wd4369
3205>   /wd4309
3205>   /wd4754
3205>   /wd4427
3205>   /d2DeepThoughtInliner-
3205>   /d2implyavx512upperregs-
3205>   /Wv:19.23
3205>   /Fwe:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\aimxsrv\dll\objfre\amd64\
3205>   @e:\os\obj\amd64fre\objfre\amd64\DMF\logged-warnings.rsp
3205>   /wl4002
3205>   /wl4003
3205>   /wl4005
3205>   /wl4006
3205>   /wl4007
3205>   /wl4008
3205>   /wl4010
3205>   /wl4013
3205>   /wl4015
3205>   /wl4018
3205>   /wl4020
3205>   /wl4022
3205>   /wl4024
3205>   /wl4025
3205>   /wl4026
3205>   /wl4027
3205>   /wl4028
3205>   /wl4029
3205>   /wl4030
3205>   /wl4031
3205>   /wl4033
3205>   /wl4034
3205>   /wl4036
3205>   /wl4038
3205>   /wl4041
3205>   /wl4042
3205>   /wl4045
3205>   /wl4047
3205>   /wl4048
3205>   /wl4049
3205>   /wl4056
3205>   /wl4066
3205>   /wl4067
3205>   /wl4068
3205>   /wl4073
3205>   /wl4074
3205>   /wl4075
3205>   /wl4076
3205>   /wl4077
3205>   /wl4079
3205>   /wl4080
3205>   /wl4081
3205>   /wl4083
3205>   /wl4085
3205>   /wl4086
3205>   /wl4087
3205>   /wl4088
3205>   /wl4089
3205>   /wl4090
3205>   /wl4091
3205>   /wl4094
3205>   /wl4096
3205>   /wl4097
3205>   /wl4098
3205>   /wl4099
3205>   /wl4101
3205>   /wl4102
3205>   /wl4109
3205>   /wl4112
3205>   /wl4113
3205>   /wl4114
3205>   /wl4115
3205>   /wl4116
3205>   /wl4117
3205>   /wl4119
3205>   /wl4120
3205>   /wl4122
3205>   /wl4124
3205>   /wl4129
3205>   /wl4133
3205>   /wl4138
3205>   /wl4141
3205>   /wl4142
3205>   /wl4143
3205>   /wl4144
3205>   /wl4145
3205>   /wl4150
3205>   /wl4153
3205>   /wl4154
3205>   /wl4155
3205>   /wl4156
3205>   /wl4157
3205>   /wl4158
3205>   /wl4159
3205>   /wl4160
3205>   /wl4161
3205>   /wl4162
3205>   /wl4163
3205>   /wl4164
3205>   /wl4166
3205>   /wl4167
3205>   /wl4168
3205>   /wl4172
3205>   /wl4174
3205>   /wl4175
3205>   /wl4176
3205>   /wl4177
3205>   /wl4178
3205>   /wl4180
3205>   /wl4182
3205>   /wl4183
3205>   /wl4185
3205>   /wl4186
3205>   /wl4187
3205>   /wl4190
3205>   /wl4192
3205>   /wl4197
3205>   /wl4200
3205>   /wl4213
3205>   /wl4215
3205>   /wl4216
3205>   /wl4218
3205>   /wl4223
3205>   /wl4224
3205>   /wl4226
3205>   /wl4227
3205>   /wl4228
3205>   /wl4229
3205>   /wl4230
3205>   /wl4237
3205>   /wl4240
3205>   /wl4243
3205>   /wl4244
3205>   /wl4250
3205>   /wl4251
3205>   /wl4258
3205>   /wl4267
3205>   /wl4269
3205>   /wl4272
3205>   /wl4273
3205>   /wl4274
3205>   /wl4275
3205>   /wl4276
3205>   /wl4278
3205>   /wl4280
3205>   /wl4281
3205>   /wl4282
3205>   /wl4283
3205>   /wl4285
3205>   /wl4286
3205>   /wl4288
3205>   /wl4290
3205>   /wl4291
3205>   /wl4293
3205>   /wl4297
3205>   /wl4302
3205>   /wl4305
3205>   /wl4306
3205>   /wl4307
3205>   /wl4309
3205>   /wl4310
3205>   /wl4311
3205>   /wl4312
3205>   /wl4313
3205>   /wl4316
3205>   /wl4319
3205>   /wl4325
3205>   /wl4326
3205>   /wl4329
3205>   /wl4333
3205>   /wl4334
3205>   /wl4335
3205>   /wl4340
3205>   /wl4344
3205>   /wl4346
3205>   /wl4348
3205>   /wl4353
3205>   /wl4356
3205>   /wl4357
3205>   /wl4358
3205>   /wl4359
3205>   /wl4364
3205>   /wl4368
3205>   /wl4369
3205>   /wl4373
3205>   /wl4374
3205>   /wl4375
3205>   /wl4376
3205>   /wl4377
3205>   /wl4378
3205>   /wl4379
3205>   /wl4381
3205>   /wl4382
3205>   /wl4383
3205>   /wl4384
3205>   /wl4390
3205>   /wl4391
3205>   /wl4392
3205>   /wl4393
3205>   /wl4394
3205>   /wl4395
3205>   /wl4396
3205>   /wl4397
3205>   /wl4398
3205>   /wl4399
3205>   /wl4600
3205>   /wl4401
3205>   /wl4402
3205>   /wl4403
3205>   /wl4404
3205>   /wl4405
3205>   /wl4406
3205>   /wl4407
3205>   /wl4409
3205>   /wl4410
3205>   /wl4411
3205>   /wl4414
3205>   /wl4420
3205>   /wl4430
3205>   /wl4436
3205>   /wl4439
3205>   /wl4440
3205>   /wl4441
3205>   /wl4445
3205>   /wl4461
3205>   /wl4462
3205>   /wl4470
3205>   /wl4473
3205>   /wl4477
3205>   /wl4484
3205>   /wl4485
3205>   /wl4486
3205>   /wl4488
3205>   /wl4489
3205>   /wl4490
3205>   /wl4502
3205>   /wl4503
3205>   /wl4506
3205>   /wl4508
3205>   /wl4511
3205>   /wl4518
3205>   /wl4521
3205>   /wl4522
3205>   /wl4523
3205>   /wl4526
3205>   /wl4530
3205>   /wl4534
3205>   /wl4535
3205>   /wl4537
3205>   /wl4538
3205>   /wl4540
3205>   /wl4541
3205>   /wl4543
3205>   /wl4544
3205>   /wl4550
3205>   /wl4551
3205>   /wl4552
3205>   /wl4553
3205>   /wl4554
3205>   /wl4556
3205>   /wl4558
3205>   /wl4561
3205>   /wl4566
3205>   /wl4570
3205>   /wl4572
3205>   /wl4580
3205>   /wl4581
3205>   /wl4584
3205>   /wl4596
3205>   /wl4597
3205>   /wl4602
3205>   /wl4603
3205>   /wl4606
3205>   /wl4612
3205>   /wl4613
3205>   /wl4615
3205>   /wl4616
3205>   /wl4618
3205>   /wl4620
3205>   /wl4621
3205>   /wl4622
3205>   /wl4624
3205>   /wl4627
3205>   /wl4630
3205>   /wl4632
3205>   /wl4633
3205>   /wl4635
3205>   /wl4636
3205>   /wl4637
3205>   /wl4638
3205>   /wl4641
3205>   /wl4645
3205>   /wl4646
3205>   /wl4650
3205>   /wl4651
3205>   /wl4652
3205>   /wl4653
3205>   /wl4655
3205>   /wl4656
3205>   /wl4657
3205>   /wl4659
3205>   /wl4661
3205>   /wl4662
3205>   /wl4667
3205>   /wl4669
3205>   /wl4674
3205>   /wl4677
3205>   /wl4678
3205>   /wl4679
3205>   /wl4683
3205>   /wl4684
3205>   /wl4685
3205>   /wl4687
3205>   /wl4688
3205>   /wl4691
3205>   /wl4693
3205>   /wl4694
3205>   /wl4698
3205>   /wl4711
3205>   /wl4715
3205>   /wl4716
3205>   /wl4717
3205>   /wl4722
3205>   /wl4723
3205>   /wl4724
3205>   /wl4727
3205>   /wl4730
3205>   /wl4731
3205>   /wl4733
3205>   /wl4739
3205>   /wl4742
3205>   /wl4743
3205>   /wl4744
3205>   /wl4747
3205>   /wl4750
3205>   /wl4756
3205>   /wl4768
3205>   /wl4772
3205>   /wl4788
3205>   /wl4793
3205>   /wl4794
3205>   /wl4799
3205>   /wl4803
3205>   /wl4804
3205>   /wl4805
3205>   /wl4806
3205>   /wl4807
3205>   /wl4810
3205>   /wl4811
3205>   /wl4812
3205>   /wl4813
3205>   /wl4817
3205>   /wl4819
3205>   /wl4821
3205>   /wl4823
3205>   /wl4829
3205>   /wl4834
3205>   /wl4835
3205>   /wl4838
3205>   /wl4839
3205>   /wl4867
3205>   /wl4900
3205>   /wl4910
3205>   /wl4912
3205>   /wl4920
3205>   /wl4925
3205>   /wl4926
3205>   /wl4927
3205>   /wl4929
3205>   /wl4930
3205>   /wl4935
3205>   /wl4936
3205>   /wl4939
3205>   /wl4944
3205>   /wl4945
3205>   /wl4947
3205>   /wl4948
3205>   /wl4949
3205>   /wl4950
3205>   /wl4951
3205>   /wl4952
3205>   /wl4953
3205>   /wl4956
3205>   /wl4957
3205>   /wl4958
3205>   /wl4959
3205>   /wl4961
3205>   /wl4964
3205>   /wl4965
3205>   /wl4972
3205>   /wl4984
3205>   /wl4995
3205>   /wl4996
3205>   /wl4997
3205>   /wl4999
3205>   /wl5033
3205>   /wl5037
3205>   /wl5046
3205>   /wl5050
3205>   /wl5055
3205>   /wl5056
3205>   /wl5105
3205>   /wl5208
3205>   /d2Qvec-mathlib-
3205>   /d2Qvec-sse2only
3205>   /Gw
3205>   /Zc:checkGwOdr
3205>   /d1ignorePragmaWarningError
3205>   /wd4316
3205>   /wd4973
3205>   /DDONT_DISABLE_PCH_WARNINGS_IN_WARNING_H
3205>   /d2FH4
3205>   /Brepro
3205>   -D_HAS_MAGIC_STATICS=1
3205>   /Qspectre
3205>   /wd5045
3205>   /d2guardspecanalysismode:v1_0
3205>   /d2guardspecmode2
3205>   /guard:cf
3205>   /d2guardcfgfuncptr-
3205>   /d2guardcfgdispatch
3205>   /guard:ehcont
3205>   -D__PLACEHOLDER_SAL=1
3205>   -wd4425
3205>   @e:\os\obj\amd64fre\objfre\amd64\WarningsCop\OneCore.rsp
3205>   /wl4146 /wl4308 /wl4509 /wl4510 /wl4532 /wl4533 /wl4610 /wl4700 /wl4701 /wl4703 /wl4789
3205>   /FIe:\os\public\amd64fre\onecore\internal\sdk\inc\warning.h
3205>   /std:c++17
3205>   /Ylaimxsrv
3205>   /Ycpch.hxx
3205>   /Fpe:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\aimxsrv\dll\objfre\amd64\pch.pch
3205>   /Fo"e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\aimxsrv\dll\objfre\amd64\pch.obj"
3205>pch_hdr.src
3205> e:\os\tools\vc\HostX86\amd64\cl.exe @e:\os\obj\amd64fre\temp\473ea2b47cf95a1173e4930f17d35497\cl_1.rsp
3205>Microsoft (R) C/C++ Optimizing Compiler Version 19.42.34444.100 for x64
3205>Copyright (C) Microsoft Corporation.  All rights reserved.
3205>cl /Fo"e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\aimxsrv\dll\objfre\amd64/"
3205>   /FC
3205>   /Iamd64
3205>   /I.
3205>   /Ie:\os\src\data\MSRC
3205>   /Ie:\os\public\amd64fre\onecore\external\sdk\inc\atlmfc
3205>   /I..\..\common
3205>   /I..\..\common\nlohmann
3205>   /I..\..\common\httplib
3205>   /I..\..\admcpsrv
3205>   /I..\..\systeminfoMcpSrv
3205>   /I..\..\AdPsMcpSvr
3205>   /I..\client
3205>   /I..\server
3205>   /Ie:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\aimxsrv\dll
3205>   /Ie:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\aimxsrv\dll\objfre\amd64
3205>   /Ie:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\aimxsrv\dll\..\idl\objfre\amd64
3205>   /Ie:\os\src\onecore\ds\security\inc
3205>   /Ie:\os\public\amd64fre\onecore\internal\ds\inc
3205>   /Ie:\os\public\amd64fre\onecore\private\ds\inc\security\base
3205>   /Ie:\os\public\amd64fre\onecore\private\base\inc
3205>   /Ie:\os\public\amd64fre\onecore\internal\base\inc
3205>   /Ie:\os\public\amd64fre\OneCore\Internal\MinWin\Priv_Sdk\Inc
3205>   /Ie:\os\public\amd64fre\OneCore\Internal\MinWin\Priv_Sdk\Inc\lsa
3205>   /Ie:\os\public\amd64fre\OneCore\Internal\MinWin\Priv_Sdk\Inc\apiset
3205>   /Ie:\os\public\amd64fre\OneCore\Private\MinWin\Priv_Sdk\Inc
3205>   /Ie:\os\public\amd64fre\OneCore\Private\MinWin\Priv_Sdk\Inc\lsa
3205>   /Ie:\os\public\amd64fre\OneCore\Internal\MinCore\Priv_Sdk\Inc
3205>   /Ie:\os\src\onecore\ds\ds\src\aimx\prod\aimxsrv\server
3205>   /Ie:\os\src\onecore\ds\ds\src\aimx\prod\aimxsrv\inc
3205>   /Ie:\os\public\amd64fre\internal\sdk\inc
3205>   /Ie:\os\public\amd64fre\onecore\internal\sdk\inc
3205>   /Ie:\os\public\amd64fre\OneCore\Internal\OneCore\Priv_Sdk\Inc
3205>   /Ie:\os\public\amd64fre\onecore\internal\com\inc
3205>   /Ie:\os\public\amd64fre\OneCoreUap\Internal\BuildMetadata\internal\cppwinrt
3205>   /Ie:\os\public\amd64fre\onecore\internal\base\inc\appmodel\runtime\winrt
3205>   /Ie:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\cpprestsdk\objfre\amd64\vcpkg_installed\target-windows\include
3205>   /Ie:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\aimxsrv\dll\objfre\amd64
3205>   /Ie:\os\src\onecore\ds\inc
3205>   /Ie:\os\obj\amd64fre\onecore\ds\inc\objfre\amd64
3205>   /Ie:\os\public\amd64fre\internal\onecoreds\inc
3205>   /Ie:\os\public\amd64fre\OneCore\Restricted\DS\inc
3205>   /Ie:\os\public\amd64fre\OneCoreUap\Restricted\DS\inc
3205>   /Ie:\os\public\amd64fre\OneCore\External\DS\inc
3205>   /Ie:\os\public\amd64fre\OneCoreUap\External\DS\inc
3205>   /Ie:\os\public\amd64fre\ClientCore\External\DS\inc
3205>   /Ie:\os\public\amd64fre\OneCore\Internal\DS\inc
3205>   /Ie:\os\public\amd64fre\OneCoreUap\Internal\DS\inc
3205>   /Ie:\os\public\amd64fre\ClientCore\Internal\DS\inc
3205>   /Ie:\os\public\amd64fre\OneCore\Private\DS\inc
3205>   /Ie:\os\public\amd64fre\OneCoreUap\Private\DS\inc
3205>   /Ie:\os\public\amd64fre\ClientCore\Private\DS\inc
3205>   /Ie:\os\public\amd64fre\OneCore\external\DS\inc
3205>   /Ie:\os\public\amd64fre\OneCore\restricted\DS\inc
3205>   /Ie:\os\public\amd64fre\OneCore\internal\DS\inc
3205>   /Ie:\os\public\amd64fre\OneCore\private\DS\inc
3205>   /Ie:\os\public\amd64fre\onecore\external\oak\inc
3205>   /Ie:\os\public\amd64fre\onecoreuap\external\oak\inc
3205>   /Ie:\os\public\amd64fre\shared\inc
3205>   /Ie:\os\public\amd64fre\onecore\external\shared\inc
3205>   /Ie:\os\public\amd64fre\onecoreuap\external\shared\inc
3205>   /Ie:\os\public\amd64fre\onecore\external\shared\inc\MinWin
3205>   /Ie:\os\public\amd64fre\sdk\inc
3205>   /Ie:\os\public\amd64fre\onecore\external\sdk\inc
3205>   /Ie:\os\public\amd64fre\onecoreuap\external\sdk\inc
3205>   /Ie:\os\public\amd64fre\onecore\private\sdk\inc\MinWin
3205>   /Ie:\os\public\amd64fre\onecore\internal\sdk\inc\MinWin
3205>   /Ie:\os\public\amd64fre\onecore\external\sdk\inc\MinWin
3205>   /Ie:\os\public\amd64fre\onecore\internal\sdk\inc\MinCore
3205>   /Ie:\os\public\amd64fre\onecore\external\sdk\inc\MinCore
3205>   /Ie:\os\public\amd64fre\onecore\internal\sdk\inc\ClientCore
3205>   /Ie:\os\public\amd64fre\onecore\external\sdk\inc\ClientCore
3205>   /Ie:\os\public\amd64fre\onecore\internal\sdk\inc\ModernCore
3205>   /Ie:\os\public\amd64fre\onecore\external\sdk\inc\ModernCore
3205>   /Ie:\os\public\amd64fre\shared\inc
3205>   /Ie:\os\public\amd64fre\onecore\external\shared\inc
3205>   /Ie:\os\public\amd64fre\onecoreuap\external\shared\inc
3205>   /Ie:\os\public\amd64fre\onecore\external\ddk\inc
3205>   /Ie:\os\public\amd64fre\onecoreuap\external\ddk\inc
3205>   /Ie:\os\public\amd64fre\onecore\external\ddk\inc\wdm
3205>   /Ie:\os\public\amd64fre\onecoreuap\external\ddk\inc\wdm
3205>   /Ie:\os\public\amd64fre\internal\sdk\inc
3205>   /Ie:\os\public\amd64fre\onecore\private\sdk\inc
3205>   /Ie:\os\public\amd64fre\onecoreuap\private\sdk\inc
3205>   /Ie:\os\public\amd64fre\onecore\internal\sdk\inc
3205>   /Ie:\os\public\amd64fre\onecore\restricted\sdk\inc
3205>   /Ie:\os\public\amd64fre\onecoreuap\internal\sdk\inc
3205>   /Ie:\os\public\amd64fre\onecoreuap\restricted\sdk\inc
3205>   /Ie:\os\public\amd64fre\onecore\internal\sdk\inc\MinWin
3205>   /Ie:\os\public\amd64fre\onecore\internal\sdk\inc\MinWin\fs
3205>   /Ie:\os\public\amd64fre\onecore\internal\sdk\inc\MinCore
3205>   /Ie:\os\public\amd64fre\onecore\internal\sdk\inc\ClientCore
3205>   /Ie:\os\public\amd64fre\onecore\internal\sdk\inc\ModernCore
3205>   /Ie:\os\public\amd64fre\OneCore\Internal\hv\hvsdk\just_built\inc\private
3205>   /Ie:\os\public\amd64fre\OneCore\Internal\hv\hvsdk\just_built\inc\internal
3205>   /Ie:\os\public\amd64fre\sdk\inc\ucrt
3205>   /Ie:\os\public\amd64fre\internal\sdk\inc\ucrt
3205>   /Ie:\os\public\amd64fre\onecore\external\sdk\inc\ucrt
3205>   /Ie:\os\public\amd64fre\onecore\internal\sdk\inc\ucrt
3205>   /Ie:\os\public\amd64fre\onecore\internal\sdk\inc\ucrt\stl120
3205>   /Ie:\os\public\amd64fre\onecore\internal\sdk\inc\ucrt\stl120
3205>   /D_WIN64
3205>   /D_AMD64_
3205>   /DAMD64
3205>   /DCONDITION_HANDLING=1
3205>   /DNT_INST=0
3205>   /DWIN32=100
3205>   /D_NT1X_=100
3205>   /DWINNT=1
3205>   /D_WIN32_WINNT=0x0A00
3205>   /DWINVER=0x0A00
3205>   /D_WIN32_IE=0x0A00
3205>   /DWIN32_LEAN_AND_MEAN=1
3205>   /DDEVL=1
3205>   /DNDEBUG
3205>   /D_STL120_
3205>   /D_STL140_
3205>   /D_DLL=1
3205>   /D_MT=1
3205>   -DNT_IUM
3205>   -DWIN32
3205>   -D_WIN32
3205>   -DUNICODE
3205>   -D_UNICODE
3205>   -D_ARM_WINAPI_PARTITION_DESKTOP_SDK_AVAILABLE
3205>   /D_USE_DEV11_CRT
3205>   -D_APISET_MINWIN_VERSION=0x0115
3205>   -D_APISET_MINCORE_VERSION=0x0114
3205>   /DFE_SB
3205>   /DFE_IME
3205>   /DNTDDI_VERSION=0x0A000011
3205>   /DWINBLUE_KBSPRING14
3205>   /DBUILD_WINDOWS
3205>   /DUNDOCKED_WINDOWS_UCRT
3205>   /D__WRL_CONFIGURATION_LEGACY__
3205>   /DBUILD_UMS_ENABLED=1
3205>   /DBUILD_WOW64_ENABLED=1
3205>   /DBUILD_ARM64X_ENABLED=0
3205>   /DEXECUTABLE_WRITES_SUPPORT=0
3205>   -D_USE_DECLSPECS_FOR_SAL=1
3205>   /DRUN_WPP
3205>   -DUNLOADABLE_DELAYLOAD_IMPLEMENTATION
3205>   -D__PLACEHOLDER_SAL=1
3205>   /D_ATL_STATIC_REGISTRY
3205>   /D_WINDLL
3205>   /c
3205>   /Zc:wchar_t-
3205>   /Zl
3205>   /Zp8
3205>   /Gy
3205>   /W4
3205>   /wd4244
3205>   /d1import_no_registry
3205>   /EHsc
3205>   /GR-
3205>   /GF
3205>   /GS
3205>   /Z7
3205>   /Oxs
3205>   /GL
3205>   /Z7
3205>   @e:\os\src\tools\NMakeJS\WarningsCop\WarningsCop.Cxx.DefaultErrors.rsp
3205>   /we4308 /we4509 /we4510 /we4532 /we4533 /we4610 /we4700 /we4789
3205>   /w15043
3205>   /Zc:rvalueCast
3205>   /Zo
3205>   -D_UCRT
3205>   -D_CONST_RETURN=
3205>   -D_CRT_SECURE_NO_WARNINGS
3205>   -D_CRT_NON_CONFORMING_SWPRINTFS
3205>   -D_CRT_NONSTDC_NO_WARNINGS
3205>   -D_CRT_STDIO_ARBITRARY_WIDE_SPECIFIERS
3205>   /D_CRT_STDIO_INLINE=extern
3205>   /D_NO_CRT_STDIO_INLINE
3205>   /D_ACRTIMP_ALT=
3205>   /D_SILENCE_STDEXT_HASH_DEPRECATION_WARNINGS
3205>   /D_STL_EXTRA_DISABLED_WARNINGS=4239
3205>   /D_SILENCE_TR1_NAMESPACE_DEPRECATION_WARNING
3205>   /D_SILENCE_ALL_CXX17_DEPRECATION_WARNINGS
3205>   /D_SILENCE_TR2_SYS_NAMESPACE_DEPRECATION_WARNING
3205>   /D_HAS_FUNCTION_ALLOCATOR_SUPPORT=1
3205>   /D_SILENCE_STDEXT_ALLOCATORS_DEPRECATION_WARNING
3205>   /D_HAS_STD_BYTE=0
3205>   /D_ENFORCE_MATCHING_ALLOCATORS=0
3205>   /D_HAS_FUNCTION_ALLOCATOR_SUPPORT=1
3205>   /D_SILENCE_STDEXT_ALLOCATORS_DEPRECATION_WARNING
3205>   /D_FULL_IOBUF
3205>   /d1initAll:Mask11
3205>   /d1initAll:FillPattern0
3205>   /d1nodatetime
3205>   /d1trimfile:e:\os\src\=BASEDIR
3205>   /d1trimfile:e:\os\public\amd64fre\=PUBLIC_ROOT
3205>   /d1trimfile:e:\os\obj\amd64fre\=OBJECT_ROOT
3205>   /d1trimfile:e:\os\bin\amd64fre\=_NTTREE
3205>   /d1trimfile:e:\os\osdep\=OSDEPENDSROOT
3205>   /d2AllowCompatibleILVersions
3205>   /d2Zi+
3205>   /ZH:SHA_256
3205>   /wd4986
3205>   /wd4987
3205>   /wd4471
3205>   /wd4369
3205>   /wd4309
3205>   /wd4754
3205>   /wd4427
3205>   /d2DeepThoughtInliner-
3205>   /d2implyavx512upperregs-
3205>   /Wv:19.23
3205>   /Fwe:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\aimxsrv\dll\objfre\amd64\
3205>   @e:\os\obj\amd64fre\objfre\amd64\DMF\logged-warnings.rsp
3205>   /wl4002
3205>   /wl4003
3205>   /wl4005
3205>   /wl4006
3205>   /wl4007
3205>   /wl4008
3205>   /wl4010
3205>   /wl4013
3205>   /wl4015
3205>   /wl4018
3205>   /wl4020
3205>   /wl4022
3205>   /wl4024
3205>   /wl4025
3205>   /wl4026
3205>   /wl4027
3205>   /wl4028
3205>   /wl4029
3205>   /wl4030
3205>   /wl4031
3205>   /wl4033
3205>   /wl4034
3205>   /wl4036
3205>   /wl4038
3205>   /wl4041
3205>   /wl4042
3205>   /wl4045
3205>   /wl4047
3205>   /wl4048
3205>   /wl4049
3205>   /wl4056
3205>   /wl4066
3205>   /wl4067
3205>   /wl4068
3205>   /wl4073
3205>   /wl4074
3205>   /wl4075
3205>   /wl4076
3205>   /wl4077
3205>   /wl4079
3205>   /wl4080
3205>   /wl4081
3205>   /wl4083
3205>   /wl4085
3205>   /wl4086
3205>   /wl4087
3205>   /wl4088
3205>   /wl4089
3205>   /wl4090
3205>   /wl4091
3205>   /wl4094
3205>   /wl4096
3205>   /wl4097
3205>   /wl4098
3205>   /wl4099
3205>   /wl4101
3205>   /wl4102
3205>   /wl4109
3205>   /wl4112
3205>   /wl4113
3205>   /wl4114
3205>   /wl4115
3205>   /wl4116
3205>   /wl4117
3205>   /wl4119
3205>   /wl4120
3205>   /wl4122
3205>   /wl4124
3205>   /wl4129
3205>   /wl4133
3205>   /wl4138
3205>   /wl4141
3205>   /wl4142
3205>   /wl4143
3205>   /wl4144
3205>   /wl4145
3205>   /wl4150
3205>   /wl4153
3205>   /wl4154
3205>   /wl4155
3205>   /wl4156
3205>   /wl4157
3205>   /wl4158
3205>   /wl4159
3205>   /wl4160
3205>   /wl4161
3205>   /wl4162
3205>   /wl4163
3205>   /wl4164
3205>   /wl4166
3205>   /wl4167
3205>   /wl4168
3205>   /wl4172
3205>   /wl4174
3205>   /wl4175
3205>   /wl4176
3205>   /wl4177
3205>   /wl4178
3205>   /wl4180
3205>   /wl4182
3205>   /wl4183
3205>   /wl4185
3205>   /wl4186
3205>   /wl4187
3205>   /wl4190
3205>   /wl4192
3205>   /wl4197
3205>   /wl4200
3205>   /wl4213
3205>   /wl4215
3205>   /wl4216
3205>   /wl4218
3205>   /wl4223
3205>   /wl4224
3205>   /wl4226
3205>   /wl4227
3205>   /wl4228
3205>   /wl4229
3205>   /wl4230
3205>   /wl4237
3205>   /wl4240
3205>   /wl4243
3205>   /wl4244
3205>   /wl4250
3205>   /wl4251
3205>   /wl4258
3205>   /wl4267
3205>   /wl4269
3205>   /wl4272
3205>   /wl4273
3205>   /wl4274
3205>   /wl4275
3205>   /wl4276
3205>   /wl4278
3205>   /wl4280
3205>   /wl4281
3205>   /wl4282
3205>   /wl4283
3205>   /wl4285
3205>   /wl4286
3205>   /wl4288
3205>   /wl4290
3205>   /wl4291
3205>   /wl4293
3205>   /wl4297
3205>   /wl4302
3205>   /wl4305
3205>   /wl4306
3205>   /wl4307
3205>   /wl4309
3205>   /wl4310
3205>   /wl4311
3205>   /wl4312
3205>   /wl4313
3205>   /wl4316
3205>   /wl4319
3205>   /wl4325
3205>   /wl4326
3205>   /wl4329
3205>   /wl4333
3205>   /wl4334
3205>   /wl4335
3205>   /wl4340
3205>   /wl4344
3205>   /wl4346
3205>   /wl4348
3205>   /wl4353
3205>   /wl4356
3205>   /wl4357
3205>   /wl4358
3205>   /wl4359
3205>   /wl4364
3205>   /wl4368
3205>   /wl4369
3205>   /wl4373
3205>   /wl4374
3205>   /wl4375
3205>   /wl4376
3205>   /wl4377
3205>   /wl4378
3205>   /wl4379
3205>   /wl4381
3205>   /wl4382
3205>   /wl4383
3205>   /wl4384
3205>   /wl4390
3205>   /wl4391
3205>   /wl4392
3205>   /wl4393
3205>   /wl4394
3205>   /wl4395
3205>   /wl4396
3205>   /wl4397
3205>   /wl4398
3205>   /wl4399
3205>   /wl4600
3205>   /wl4401
3205>   /wl4402
3205>   /wl4403
3205>   /wl4404
3205>   /wl4405
3205>   /wl4406
3205>   /wl4407
3205>   /wl4409
3205>   /wl4410
3205>   /wl4411
3205>   /wl4414
3205>   /wl4420
3205>   /wl4430
3205>   /wl4436
3205>   /wl4439
3205>   /wl4440
3205>   /wl4441
3205>   /wl4445
3205>   /wl4461
3205>   /wl4462
3205>   /wl4470
3205>   /wl4473
3205>   /wl4477
3205>   /wl4484
3205>   /wl4485
3205>   /wl4486
3205>   /wl4488
3205>   /wl4489
3205>   /wl4490
3205>   /wl4502
3205>   /wl4503
3205>   /wl4506
3205>   /wl4508
3205>   /wl4511
3205>   /wl4518
3205>   /wl4521
3205>   /wl4522
3205>   /wl4523
3205>   /wl4526
3205>   /wl4530
3205>   /wl4534
3205>   /wl4535
3205>   /wl4537
3205>   /wl4538
3205>   /wl4540
3205>   /wl4541
3205>   /wl4543
3205>   /wl4544
3205>   /wl4550
3205>   /wl4551
3205>   /wl4552
3205>   /wl4553
3205>   /wl4554
3205>   /wl4556
3205>   /wl4558
3205>   /wl4561
3205>   /wl4566
3205>   /wl4570
3205>   /wl4572
3205>   /wl4580
3205>   /wl4581
3205>   /wl4584
3205>   /wl4596
3205>   /wl4597
3205>   /wl4602
3205>   /wl4603
3205>   /wl4606
3205>   /wl4612
3205>   /wl4613
3205>   /wl4615
3205>   /wl4616
3205>   /wl4618
3205>   /wl4620
3205>   /wl4621
3205>   /wl4622
3205>   /wl4624
3205>   /wl4627
3205>   /wl4630
3205>   /wl4632
3205>   /wl4633
3205>   /wl4635
3205>   /wl4636
3205>   /wl4637
3205>   /wl4638
3205>   /wl4641
3205>   /wl4645
3205>   /wl4646
3205>   /wl4650
3205>   /wl4651
3205>   /wl4652
3205>   /wl4653
3205>   /wl4655
3205>   /wl4656
3205>   /wl4657
3205>   /wl4659
3205>   /wl4661
3205>   /wl4662
3205>   /wl4667
3205>   /wl4669
3205>   /wl4674
3205>   /wl4677
3205>   /wl4678
3205>   /wl4679
3205>   /wl4683
3205>   /wl4684
3205>   /wl4685
3205>   /wl4687
3205>   /wl4688
3205>   /wl4691
3205>   /wl4693
3205>   /wl4694
3205>   /wl4698
3205>   /wl4711
3205>   /wl4715
3205>   /wl4716
3205>   /wl4717
3205>   /wl4722
3205>   /wl4723
3205>   /wl4724
3205>   /wl4727
3205>   /wl4730
3205>   /wl4731
3205>   /wl4733
3205>   /wl4739
3205>   /wl4742
3205>   /wl4743
3205>   /wl4744
3205>   /wl4747
3205>   /wl4750
3205>   /wl4756
3205>   /wl4768
3205>   /wl4772
3205>   /wl4788
3205>   /wl4793
3205>   /wl4794
3205>   /wl4799
3205>   /wl4803
3205>   /wl4804
3205>   /wl4805
3205>   /wl4806
3205>   /wl4807
3205>   /wl4810
3205>   /wl4811
3205>   /wl4812
3205>   /wl4813
3205>   /wl4817
3205>   /wl4819
3205>   /wl4821
3205>   /wl4823
3205>   /wl4829
3205>   /wl4834
3205>   /wl4835
3205>   /wl4838
3205>   /wl4839
3205>   /wl4867
3205>   /wl4900
3205>   /wl4910
3205>   /wl4912
3205>   /wl4920
3205>   /wl4925
3205>   /wl4926
3205>   /wl4927
3205>   /wl4929
3205>   /wl4930
3205>   /wl4935
3205>   /wl4936
3205>   /wl4939
3205>   /wl4944
3205>   /wl4945
3205>   /wl4947
3205>   /wl4948
3205>   /wl4949
3205>   /wl4950
3205>   /wl4951
3205>   /wl4952
3205>   /wl4953
3205>   /wl4956
3205>   /wl4957
3205>   /wl4958
3205>   /wl4959
3205>   /wl4961
3205>   /wl4964
3205>   /wl4965
3205>   /wl4972
3205>   /wl4984
3205>   /wl4995
3205>   /wl4996
3205>   /wl4997
3205>   /wl4999
3205>   /wl5033
3205>   /wl5037
3205>   /wl5046
3205>   /wl5050
3205>   /wl5055
3205>   /wl5056
3205>   /wl5105
3205>   /wl5208
3205>   /d2Qvec-mathlib-
3205>   /d2Qvec-sse2only
3205>   /Gw
3205>   /Zc:checkGwOdr
3205>   /d1ignorePragmaWarningError
3205>   /wd4316
3205>   /wd4973
3205>   /DDONT_DISABLE_PCH_WARNINGS_IN_WARNING_H
3205>   /d2FH4
3205>   /Brepro
3205>   -D_HAS_MAGIC_STATICS=1
3205>   /Qspectre
3205>   /wd5045
3205>   /d2guardspecanalysismode:v1_0
3205>   /d2guardspecmode2
3205>   /guard:cf
3205>   /d2guardcfgfuncptr-
3205>   /d2guardcfgdispatch
3205>   /guard:ehcont
3205>   -D__PLACEHOLDER_SAL=1
3205>   -wd4425
3205>   @e:\os\obj\amd64fre\objfre\amd64\WarningsCop\OneCore.rsp
3205>   /wl4146 /wl4308 /wl4509 /wl4510 /wl4532 /wl4533 /wl4610 /wl4700 /wl4701 /wl4703 /wl4789
3205>   /FIe:\os\public\amd64fre\onecore\internal\sdk\inc\warning.h
3205>   /std:c++17
3205>   /Yupch.hxx /Fpe:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\aimxsrv\dll\objfre\amd64\pch.pch
3205>   .\dllmain.cpp .\aimxservice.cpp 
3205>dllmain.cpp
3205>aimxservice.cpp
3205> set _createfile=e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\aimxsrv\dll\objfre\amd64\delayload.txt
3205> e:\os\tools\vc\HostX64\amd64\link.exe /lib /out:e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\aimxsrv\dll\objfre\amd64\aimxsrv.lib @e:\os\obj\amd64fre\temp\473ea2b47cf95a1173e4930f17d35497\lib_1.rsp
3205>Microsoft (R) Library Manager Version 14.42.34444.100
3205>Copyright (C) Microsoft Corporation.  All rights reserved.
3205>/IGNORE:4078,4221,4281,4006,4198 
3205>/nodefaultlib 
3205>/machine:amd64 
3205>/ltcg 
3205>/Brepro 
3205>/def:aimxsrv.def 
3205>e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\aimxsrv\dll\objfre\amd64\dllmain.obj 
3205>e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\aimxsrv\dll\objfre\amd64\aimxservice.obj 
3205>e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\aimxsrv\dll\objfre\amd64\pch.obj 
3205>   Creating library e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\aimxsrv\dll\objfre\amd64\aimxsrv.lib and object e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\aimxsrv\dll\objfre\amd64\aimxsrv.exp
3205>Writing out macros...e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\aimxsrv\dll\objfre\amd64\Macros-PASS1.txt
3205>binplace e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\aimxsrv\dll\objfre\amd64\Macros-PASS1.txt
3205> e:\os\tools\powershell\pwsh.exe -NoProfile e:\os\src\tools\NMakeJS\WarningsCop\WarningsCop.ps1 -MakeSubDir "onecore\ds\ds\src\aimx\prod\aimxsrv\dll" -Pass PASS1 -BaselineFile e:\os\src\.config\OneCore\WarningsCop.json -OutputDir "e:\os\bin\amd64fre\evidence\WarningsCop\OneCore\onecoreds"
3205>WarningsCop.ps1 : WarningsCop: Processing onecore\ds\ds\src\aimx\prod\aimxsrv\dll in pass PASS1
3208>Calculated LAYERINFO_MODULE='OneCoreDS'.
3208>makefile.def: TEMP=e:\os\obj\amd64fre\temp\621f0494d035e519a9fa1e57076d9949
3208>makefile.def: BUILDINGINDATT=
3208>[Core OS Undocking] NOT using package ''
3208>UCRT enabled: dir 'e:\os\src\onecore\ds\ds\src\aimx\prod\adpsmcpsvr' (target 'AdPsMcpSvr', type 'LIBRARY', nt_target_version '0xA000011')
3208>ObjectsMac.ts: validation succeeded
3208>STL version 120 used in "e:\os\src\onecore\ds\ds\src\aimx\prod\adpsmcpsvr" (STL_VER_TELEMETRY)
3208>_NEED_BUILDDATE not defined setting BUILDDATE to an invalid value.
3208>A subdirectory or file e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\adpsmcpsvr\objfre\amd64 already exists.
3208> e:\os\tools\Windows.Desktop.Tools.amd64\tools\touch.exe /c e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\adpsmcpsvr\objfre\amd64\_PASS1_Marker.log
3208> e:\os\tools\vc\HostX86\amd64\cl.exe @e:\os\obj\amd64fre\temp\621f0494d035e519a9fa1e57076d9949\tmp_31664_1752613375260230900.tmp /Tpe:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\adpsmcpsvr\objfre\amd64\pch_hdr.src
3208>Microsoft (R) C/C++ Optimizing Compiler Version 19.42.34444.100 for x64
3208>Copyright (C) Microsoft Corporation.  All rights reserved.
3208>cl 
3208>   /Iamd64
3208>   /I.
3208>   /Ie:\os\src\data\MSRC
3208>   /Ie:\os\public\amd64fre\OneCore\Internal\MinWin\Priv_Sdk\Inc
3208>   /Ie:\os\public\amd64fre\OneCoreUap\Internal\BuildMetadata\internal\cppwinrt
3208>   /Ie:\os\public\amd64fre\onecoreuap\restricted\windows\inc
3208>   /I..\common
3208>   /I..\llmclientlib
3208>   /I..\aimxsrv\inc
3208>   /I..\aimxsrv\server
3208>   /I..\aimxsrv\client
3208>   /Ie:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\adpsmcpsvr\..\aimxsrv\idl\objfre\amd64
3208>   /Ie:\os\public\amd64fre\OneCore\Internal\OneCore\Priv_Sdk\Inc
3208>   /Ie:\os\public\amd64fre\onecore\internal\base\inc\appmodel\runtime\winrt
3208>   /Ie:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\adpsmcpsvr\objfre\amd64
3208>   /Ie:\os\src\onecore\ds\inc
3208>   /Ie:\os\obj\amd64fre\onecore\ds\inc\objfre\amd64
3208>   /Ie:\os\public\amd64fre\internal\onecoreds\inc
3208>   /Ie:\os\public\amd64fre\OneCore\Restricted\DS\inc
3208>   /Ie:\os\public\amd64fre\OneCoreUap\Restricted\DS\inc
3208>   /Ie:\os\public\amd64fre\OneCore\External\DS\inc
3208>   /Ie:\os\public\amd64fre\OneCoreUap\External\DS\inc
3208>   /Ie:\os\public\amd64fre\ClientCore\External\DS\inc
3208>   /Ie:\os\public\amd64fre\OneCore\Internal\DS\inc
3208>   /Ie:\os\public\amd64fre\OneCoreUap\Internal\DS\inc
3208>   /Ie:\os\public\amd64fre\ClientCore\Internal\DS\inc
3208>   /Ie:\os\public\amd64fre\OneCore\Private\DS\inc
3208>   /Ie:\os\public\amd64fre\OneCoreUap\Private\DS\inc
3208>   /Ie:\os\public\amd64fre\ClientCore\Private\DS\inc
3208>   /Ie:\os\public\amd64fre\OneCore\external\DS\inc
3208>   /Ie:\os\public\amd64fre\OneCore\restricted\DS\inc
3208>   /Ie:\os\public\amd64fre\OneCore\internal\DS\inc
3208>   /Ie:\os\public\amd64fre\OneCore\private\DS\inc
3208>   /Ie:\os\public\amd64fre\onecore\external\oak\inc
3208>   /Ie:\os\public\amd64fre\onecoreuap\external\oak\inc
3208>   /Ie:\os\public\amd64fre\shared\inc
3208>   /Ie:\os\public\amd64fre\onecore\external\shared\inc
3208>   /Ie:\os\public\amd64fre\onecoreuap\external\shared\inc
3208>   /Ie:\os\public\amd64fre\onecore\external\shared\inc\MinWin
3208>   /Ie:\os\public\amd64fre\sdk\inc
3208>   /Ie:\os\public\amd64fre\onecore\external\sdk\inc
3208>   /Ie:\os\public\amd64fre\onecoreuap\external\sdk\inc
3208>   /Ie:\os\public\amd64fre\onecore\private\sdk\inc\MinWin
3208>   /Ie:\os\public\amd64fre\onecore\internal\sdk\inc\MinWin
3208>   /Ie:\os\public\amd64fre\onecore\external\sdk\inc\MinWin
3208>   /Ie:\os\public\amd64fre\onecore\internal\sdk\inc\MinCore
3208>   /Ie:\os\public\amd64fre\onecore\external\sdk\inc\MinCore
3208>   /Ie:\os\public\amd64fre\onecore\internal\sdk\inc\ClientCore
3208>   /Ie:\os\public\amd64fre\onecore\external\sdk\inc\ClientCore
3208>   /Ie:\os\public\amd64fre\onecore\internal\sdk\inc\ModernCore
3208>   /Ie:\os\public\amd64fre\onecore\external\sdk\inc\ModernCore
3208>   /Ie:\os\public\amd64fre\shared\inc
3208>   /Ie:\os\public\amd64fre\onecore\external\shared\inc
3208>   /Ie:\os\public\amd64fre\onecoreuap\external\shared\inc
3208>   /Ie:\os\public\amd64fre\onecore\external\ddk\inc
3208>   /Ie:\os\public\amd64fre\onecoreuap\external\ddk\inc
3208>   /Ie:\os\public\amd64fre\onecore\external\ddk\inc\wdm
3208>   /Ie:\os\public\amd64fre\onecoreuap\external\ddk\inc\wdm
3208>   /Ie:\os\public\amd64fre\internal\sdk\inc
3208>   /Ie:\os\public\amd64fre\onecore\private\sdk\inc
3208>   /Ie:\os\public\amd64fre\onecoreuap\private\sdk\inc
3208>   /Ie:\os\public\amd64fre\onecore\internal\sdk\inc
3208>   /Ie:\os\public\amd64fre\onecore\restricted\sdk\inc
3208>   /Ie:\os\public\amd64fre\onecoreuap\internal\sdk\inc
3208>   /Ie:\os\public\amd64fre\onecoreuap\restricted\sdk\inc
3208>   /Ie:\os\public\amd64fre\onecore\internal\sdk\inc\MinWin
3208>   /Ie:\os\public\amd64fre\onecore\internal\sdk\inc\MinWin\fs
3208>   /Ie:\os\public\amd64fre\onecore\internal\sdk\inc\MinCore
3208>   /Ie:\os\public\amd64fre\onecore\internal\sdk\inc\ClientCore
3208>   /Ie:\os\public\amd64fre\onecore\internal\sdk\inc\ModernCore
3208>   /Ie:\os\public\amd64fre\OneCore\Internal\hv\hvsdk\just_built\inc\private
3208>   /Ie:\os\public\amd64fre\OneCore\Internal\hv\hvsdk\just_built\inc\internal
3208>   /Ie:\os\public\amd64fre\sdk\inc\ucrt
3208>   /Ie:\os\public\amd64fre\internal\sdk\inc\ucrt
3208>   /Ie:\os\public\amd64fre\onecore\external\sdk\inc\ucrt
3208>   /Ie:\os\public\amd64fre\onecore\internal\sdk\inc\ucrt
3208>   /Ie:\os\public\amd64fre\onecore\internal\sdk\inc\ucrt\stl120
3208>   /Ie:\os\public\amd64fre\onecore\internal\sdk\inc\ucrt\stl120
3208>   /D_WIN64
3208>   /D_AMD64_
3208>   /DAMD64
3208>   /DCONDITION_HANDLING=1
3208>   /DNT_INST=0
3208>   /DWIN32=100
3208>   /D_NT1X_=100
3208>   /DWINNT=1
3208>   /D_WIN32_WINNT=0x0A00
3208>   /DWINVER=0x0A00
3208>   /D_WIN32_IE=0x0A00
3208>   /DWIN32_LEAN_AND_MEAN=1
3208>   /DDEVL=1
3208>   /DNDEBUG
3208>   /D_STL120_
3208>   /D_STL140_
3208>   /D_DLL=1
3208>   /D_MT=1
3208>   -DNT_IUM
3208>   -DWIN32
3208>   -D_WIN32
3208>   -DUNICODE
3208>   -D_UNICODE
3208>   -D_ARM_WINAPI_PARTITION_DESKTOP_SDK_AVAILABLE
3208>   /D_USE_DEV11_CRT
3208>   -D_APISET_MINWIN_VERSION=0x0115
3208>   -D_APISET_MINCORE_VERSION=0x0114
3208>   /DFE_SB
3208>   /DFE_IME
3208>   /DNTDDI_VERSION=0x0A000011
3208>   /DWINBLUE_KBSPRING14
3208>   /DBUILD_WINDOWS
3208>   /DUNDOCKED_WINDOWS_UCRT
3208>   /D__WRL_CONFIGURATION_LEGACY__
3208>   /DBUILD_UMS_ENABLED=1
3208>   /DBUILD_WOW64_ENABLED=1
3208>   /DBUILD_ARM64X_ENABLED=0
3208>   /DEXECUTABLE_WRITES_SUPPORT=0
3208>   -D_USE_DECLSPECS_FOR_SAL=1
3208>   /DRUN_WPP
3208>   -D__PLACEHOLDER_SAL=1
3208>   /c
3208>   /Zc:wchar_t-
3208>   /Zl
3208>   /Zp8
3208>   /Gy
3208>   /W4
3208>   /wd4244
3208>   /EHsc
3208>   /d1import_no_registry
3208>   /EHsc
3208>   /GR-
3208>   /GF
3208>   /GS
3208>   /Z7
3208>   /Oxs
3208>   /GL
3208>   /Z7
3208>   @e:\os\src\tools\NMakeJS\WarningsCop\WarningsCop.Cxx.DefaultErrors.rsp
3208>   /we4308 /we4509 /we4510 /we4532 /we4533 /we4610 /we4700 /we4789
3208>   /w15043
3208>   /Zc:rvalueCast
3208>   -D_UCRT
3208>   -D_CONST_RETURN=
3208>   -D_CRT_SECURE_NO_WARNINGS
3208>   -D_CRT_NON_CONFORMING_SWPRINTFS
3208>   -D_CRT_NONSTDC_NO_WARNINGS
3208>   -D_CRT_STDIO_ARBITRARY_WIDE_SPECIFIERS
3208>   /D_CRT_STDIO_INLINE=extern
3208>   /D_NO_CRT_STDIO_INLINE
3208>   /D_ACRTIMP_ALT=
3208>   /D_SILENCE_STDEXT_HASH_DEPRECATION_WARNINGS
3208>   /D_STL_EXTRA_DISABLED_WARNINGS=4239
3208>   /D_SILENCE_TR1_NAMESPACE_DEPRECATION_WARNING
3208>   /D_SILENCE_ALL_CXX17_DEPRECATION_WARNINGS
3208>   /D_SILENCE_TR2_SYS_NAMESPACE_DEPRECATION_WARNING
3208>   /D_HAS_FUNCTION_ALLOCATOR_SUPPORT=1
3208>   /D_SILENCE_STDEXT_ALLOCATORS_DEPRECATION_WARNING
3208>   /D_HAS_STD_BYTE=0
3208>   /D_ENFORCE_MATCHING_ALLOCATORS=0
3208>   /D_HAS_FUNCTION_ALLOCATOR_SUPPORT=1
3208>   /D_SILENCE_STDEXT_ALLOCATORS_DEPRECATION_WARNING
3208>   /D_FULL_IOBUF
3208>   /d1initAll:Mask11
3208>   /d1initAll:FillPattern0
3208>   /d1nodatetime
3208>   /d1trimfile:e:\os\src\=BASEDIR
3208>   /d1trimfile:e:\os\public\amd64fre\=PUBLIC_ROOT
3208>   /d1trimfile:e:\os\obj\amd64fre\=OBJECT_ROOT
3208>   /d1trimfile:e:\os\bin\amd64fre\=_NTTREE
3208>   /d1trimfile:e:\os\osdep\=OSDEPENDSROOT
3208>   /d2AllowCompatibleILVersions
3208>   /d2Zi+
3208>   /ZH:SHA_256
3208>   /wd4986
3208>   /wd4987
3208>   /wd4471
3208>   /wd4369
3208>   /wd4309
3208>   /wd4754
3208>   /wd4427
3208>   /d2DeepThoughtInliner-
3208>   /d2implyavx512upperregs-
3208>   /Wv:19.23
3208>   /Fwe:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\adpsmcpsvr\objfre\amd64\
3208>   @e:\os\obj\amd64fre\objfre\amd64\DMF\logged-warnings.rsp
3208>   /wl4002
3208>   /wl4003
3208>   /wl4005
3208>   /wl4006
3208>   /wl4007
3208>   /wl4008
3208>   /wl4010
3208>   /wl4013
3208>   /wl4015
3208>   /wl4018
3208>   /wl4020
3208>   /wl4022
3208>   /wl4024
3208>   /wl4025
3208>   /wl4026
3208>   /wl4027
3208>   /wl4028
3208>   /wl4029
3208>   /wl4030
3208>   /wl4031
3208>   /wl4033
3208>   /wl4034
3208>   /wl4036
3208>   /wl4038
3208>   /wl4041
3208>   /wl4042
3208>   /wl4045
3208>   /wl4047
3208>   /wl4048
3208>   /wl4049
3208>   /wl4056
3208>   /wl4066
3208>   /wl4067
3208>   /wl4068
3208>   /wl4073
3208>   /wl4074
3208>   /wl4075
3208>   /wl4076
3208>   /wl4077
3208>   /wl4079
3208>   /wl4080
3208>   /wl4081
3208>   /wl4083
3208>   /wl4085
3208>   /wl4086
3208>   /wl4087
3208>   /wl4088
3208>   /wl4089
3208>   /wl4090
3208>   /wl4091
3208>   /wl4094
3208>   /wl4096
3208>   /wl4097
3208>   /wl4098
3208>   /wl4099
3208>   /wl4101
3208>   /wl4102
3208>   /wl4109
3208>   /wl4112
3208>   /wl4113
3208>   /wl4114
3208>   /wl4115
3208>   /wl4116
3208>   /wl4117
3208>   /wl4119
3208>   /wl4120
3208>   /wl4122
3208>   /wl4124
3208>   /wl4129
3208>   /wl4133
3208>   /wl4138
3208>   /wl4141
3208>   /wl4142
3208>   /wl4143
3208>   /wl4144
3208>   /wl4145
3208>   /wl4150
3208>   /wl4153
3208>   /wl4154
3208>   /wl4155
3208>   /wl4156
3208>   /wl4157
3208>   /wl4158
3208>   /wl4159
3208>   /wl4160
3208>   /wl4161
3208>   /wl4162
3208>   /wl4163
3208>   /wl4164
3208>   /wl4166
3208>   /wl4167
3208>   /wl4168
3208>   /wl4172
3208>   /wl4174
3208>   /wl4175
3208>   /wl4176
3208>   /wl4177
3208>   /wl4178
3208>   /wl4180
3208>   /wl4182
3208>   /wl4183
3208>   /wl4185
3208>   /wl4186
3208>   /wl4187
3208>   /wl4190
3208>   /wl4192
3208>   /wl4197
3208>   /wl4200
3208>   /wl4213
3208>   /wl4215
3208>   /wl4216
3208>   /wl4218
3208>   /wl4223
3208>   /wl4224
3208>   /wl4226
3208>   /wl4227
3208>   /wl4228
3208>   /wl4229
3208>   /wl4230
3208>   /wl4237
3208>   /wl4240
3208>   /wl4243
3208>   /wl4244
3208>   /wl4250
3208>   /wl4251
3208>   /wl4258
3208>   /wl4267
3208>   /wl4269
3208>   /wl4272
3208>   /wl4273
3208>   /wl4274
3208>   /wl4275
3208>   /wl4276
3208>   /wl4278
3208>   /wl4280
3208>   /wl4281
3208>   /wl4282
3208>   /wl4283
3208>   /wl4285
3208>   /wl4286
3208>   /wl4288
3208>   /wl4290
3208>   /wl4291
3208>   /wl4293
3208>   /wl4297
3208>   /wl4302
3208>   /wl4305
3208>   /wl4306
3208>   /wl4307
3208>   /wl4309
3208>   /wl4310
3208>   /wl4311
3208>   /wl4312
3208>   /wl4313
3208>   /wl4316
3208>   /wl4319
3208>   /wl4325
3208>   /wl4326
3208>   /wl4329
3208>   /wl4333
3208>   /wl4334
3208>   /wl4335
3208>   /wl4340
3208>   /wl4344
3208>   /wl4346
3208>   /wl4348
3208>   /wl4353
3208>   /wl4356
3208>   /wl4357
3208>   /wl4358
3208>   /wl4359
3208>   /wl4364
3208>   /wl4368
3208>   /wl4369
3208>   /wl4373
3208>   /wl4374
3208>   /wl4375
3208>   /wl4376
3208>   /wl4377
3208>   /wl4378
3208>   /wl4379
3208>   /wl4381
3208>   /wl4382
3208>   /wl4383
3208>   /wl4384
3208>   /wl4390
3208>   /wl4391
3208>   /wl4392
3208>   /wl4393
3208>   /wl4394
3208>   /wl4395
3208>   /wl4396
3208>   /wl4397
3208>   /wl4398
3208>   /wl4399
3208>   /wl4600
3208>   /wl4401
3208>   /wl4402
3208>   /wl4403
3208>   /wl4404
3208>   /wl4405
3208>   /wl4406
3208>   /wl4407
3208>   /wl4409
3208>   /wl4410
3208>   /wl4411
3208>   /wl4414
3208>   /wl4420
3208>   /wl4430
3208>   /wl4436
3208>   /wl4439
3208>   /wl4440
3208>   /wl4441
3208>   /wl4445
3208>   /wl4461
3208>   /wl4462
3208>   /wl4470
3208>   /wl4473
3208>   /wl4477
3208>   /wl4484
3208>   /wl4485
3208>   /wl4486
3208>   /wl4488
3208>   /wl4489
3208>   /wl4490
3208>   /wl4502
3208>   /wl4503
3208>   /wl4506
3208>   /wl4508
3208>   /wl4511
3208>   /wl4518
3208>   /wl4521
3208>   /wl4522
3208>   /wl4523
3208>   /wl4526
3208>   /wl4530
3208>   /wl4534
3208>   /wl4535
3208>   /wl4537
3208>   /wl4538
3208>   /wl4540
3208>   /wl4541
3208>   /wl4543
3208>   /wl4544
3208>   /wl4550
3208>   /wl4551
3208>   /wl4552
3208>   /wl4553
3208>   /wl4554
3208>   /wl4556
3208>   /wl4558
3208>   /wl4561
3208>   /wl4566
3208>   /wl4570
3208>   /wl4572
3208>   /wl4580
3208>   /wl4581
3208>   /wl4584
3208>   /wl4596
3208>   /wl4597
3208>   /wl4602
3208>   /wl4603
3208>   /wl4606
3208>   /wl4612
3208>   /wl4613
3208>   /wl4615
3208>   /wl4616
3208>   /wl4618
3208>   /wl4620
3208>   /wl4621
3208>   /wl4622
3208>   /wl4624
3208>   /wl4627
3208>   /wl4630
3208>   /wl4632
3208>   /wl4633
3208>   /wl4635
3208>   /wl4636
3208>   /wl4637
3208>   /wl4638
3208>   /wl4641
3208>   /wl4645
3208>   /wl4646
3208>   /wl4650
3208>   /wl4651
3208>   /wl4652
3208>   /wl4653
3208>   /wl4655
3208>   /wl4656
3208>   /wl4657
3208>   /wl4659
3208>   /wl4661
3208>   /wl4662
3208>   /wl4667
3208>   /wl4669
3208>   /wl4674
3208>   /wl4677
3208>   /wl4678
3208>   /wl4679
3208>   /wl4683
3208>   /wl4684
3208>   /wl4685
3208>   /wl4687
3208>   /wl4688
3208>   /wl4691
3208>   /wl4693
3208>   /wl4694
3208>   /wl4698
3208>   /wl4711
3208>   /wl4715
3208>   /wl4716
3208>   /wl4717
3208>   /wl4722
3208>   /wl4723
3208>   /wl4724
3208>   /wl4727
3208>   /wl4730
3208>   /wl4731
3208>   /wl4733
3208>   /wl4739
3208>   /wl4742
3208>   /wl4743
3208>   /wl4744
3208>   /wl4747
3208>   /wl4750
3208>   /wl4756
3208>   /wl4768
3208>   /wl4772
3208>   /wl4788
3208>   /wl4793
3208>   /wl4794
3208>   /wl4799
3208>   /wl4803
3208>   /wl4804
3208>   /wl4805
3208>   /wl4806
3208>   /wl4807
3208>   /wl4810
3208>   /wl4811
3208>   /wl4812
3208>   /wl4813
3208>   /wl4817
3208>   /wl4819
3208>   /wl4821
3208>   /wl4823
3208>   /wl4829
3208>   /wl4834
3208>   /wl4835
3208>   /wl4838
3208>   /wl4839
3208>   /wl4867
3208>   /wl4900
3208>   /wl4910
3208>   /wl4912
3208>   /wl4920
3208>   /wl4925
3208>   /wl4926
3208>   /wl4927
3208>   /wl4929
3208>   /wl4930
3208>   /wl4935
3208>   /wl4936
3208>   /wl4939
3208>   /wl4944
3208>   /wl4945
3208>   /wl4947
3208>   /wl4948
3208>   /wl4949
3208>   /wl4950
3208>   /wl4951
3208>   /wl4952
3208>   /wl4953
3208>   /wl4956
3208>   /wl4957
3208>   /wl4958
3208>   /wl4959
3208>   /wl4961
3208>   /wl4964
3208>   /wl4965
3208>   /wl4972
3208>   /wl4984
3208>   /wl4995
3208>   /wl4996
3208>   /wl4997
3208>   /wl4999
3208>   /wl5033
3208>   /wl5037
3208>   /wl5046
3208>   /wl5050
3208>   /wl5055
3208>   /wl5056
3208>   /wl5105
3208>   /wl5208
3208>   /d2Qvec-mathlib-
3208>   /d2Qvec-sse2only
3208>   /Gw
3208>   /Zc:checkGwOdr
3208>   /d1ignorePragmaWarningError
3208>   /wd4316
3208>   /wd4973
3208>   /DDONT_DISABLE_PCH_WARNINGS_IN_WARNING_H
3208>   /d2FH4
3208>   /Brepro
3208>   -D_HAS_MAGIC_STATICS=1
3208>   /Qspectre
3208>   /wd5045
3208>   /d2guardspecanalysismode:v1_0
3208>   /d2guardspecmode2
3208>   /guard:cf
3208>   /d2guardcfgfuncptr-
3208>   /d2guardcfgdispatch
3208>   /guard:ehcont
3208>   -D__PLACEHOLDER_SAL=1
3208>   -wd4425
3208>   @e:\os\obj\amd64fre\objfre\amd64\WarningsCop\OneCore.rsp
3208>   /wl4146 /wl4308 /wl4509 /wl4510 /wl4532 /wl4533 /wl4610 /wl4700 /wl4701 /wl4703 /wl4789
3208>   /FIe:\os\public\amd64fre\onecore\internal\sdk\inc\warning.h
3208>   /std:c++17
3208>   /YlAdPsMcpSvr
3208>   /Ycpch.hxx
3208>   /Fpe:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\adpsmcpsvr\objfre\amd64\pch.pch
3208>   /Fo"e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\adpsmcpsvr\objfre\amd64\pch.obj"
3208>pch_hdr.src
3208> e:\os\tools\vc\HostX86\amd64\cl.exe @e:\os\obj\amd64fre\temp\621f0494d035e519a9fa1e57076d9949\cl_1.rsp
3208>Microsoft (R) C/C++ Optimizing Compiler Version 19.42.34444.100 for x64
3208>Copyright (C) Microsoft Corporation.  All rights reserved.
3208>cl /Fo"e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\adpsmcpsvr\objfre\amd64/"
3208>   /FC
3208>   /Iamd64
3208>   /I.
3208>   /Ie:\os\src\data\MSRC
3208>   /Ie:\os\public\amd64fre\OneCore\Internal\MinWin\Priv_Sdk\Inc
3208>   /Ie:\os\public\amd64fre\OneCoreUap\Internal\BuildMetadata\internal\cppwinrt
3208>   /Ie:\os\public\amd64fre\onecoreuap\restricted\windows\inc
3208>   /I..\common
3208>   /I..\llmclientlib
3208>   /I..\aimxsrv\inc
3208>   /I..\aimxsrv\server
3208>   /I..\aimxsrv\client
3208>   /Ie:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\adpsmcpsvr\..\aimxsrv\idl\objfre\amd64
3208>   /Ie:\os\public\amd64fre\OneCore\Internal\OneCore\Priv_Sdk\Inc
3208>   /Ie:\os\public\amd64fre\onecore\internal\base\inc\appmodel\runtime\winrt
3208>   /Ie:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\adpsmcpsvr\objfre\amd64
3208>   /Ie:\os\src\onecore\ds\inc
3208>   /Ie:\os\obj\amd64fre\onecore\ds\inc\objfre\amd64
3208>   /Ie:\os\public\amd64fre\internal\onecoreds\inc
3208>   /Ie:\os\public\amd64fre\OneCore\Restricted\DS\inc
3208>   /Ie:\os\public\amd64fre\OneCoreUap\Restricted\DS\inc
3208>   /Ie:\os\public\amd64fre\OneCore\External\DS\inc
3208>   /Ie:\os\public\amd64fre\OneCoreUap\External\DS\inc
3208>   /Ie:\os\public\amd64fre\ClientCore\External\DS\inc
3208>   /Ie:\os\public\amd64fre\OneCore\Internal\DS\inc
3208>   /Ie:\os\public\amd64fre\OneCoreUap\Internal\DS\inc
3208>   /Ie:\os\public\amd64fre\ClientCore\Internal\DS\inc
3208>   /Ie:\os\public\amd64fre\OneCore\Private\DS\inc
3208>   /Ie:\os\public\amd64fre\OneCoreUap\Private\DS\inc
3208>   /Ie:\os\public\amd64fre\ClientCore\Private\DS\inc
3208>   /Ie:\os\public\amd64fre\OneCore\external\DS\inc
3208>   /Ie:\os\public\amd64fre\OneCore\restricted\DS\inc
3208>   /Ie:\os\public\amd64fre\OneCore\internal\DS\inc
3208>   /Ie:\os\public\amd64fre\OneCore\private\DS\inc
3208>   /Ie:\os\public\amd64fre\onecore\external\oak\inc
3208>   /Ie:\os\public\amd64fre\onecoreuap\external\oak\inc
3208>   /Ie:\os\public\amd64fre\shared\inc
3208>   /Ie:\os\public\amd64fre\onecore\external\shared\inc
3208>   /Ie:\os\public\amd64fre\onecoreuap\external\shared\inc
3208>   /Ie:\os\public\amd64fre\onecore\external\shared\inc\MinWin
3208>   /Ie:\os\public\amd64fre\sdk\inc
3208>   /Ie:\os\public\amd64fre\onecore\external\sdk\inc
3208>   /Ie:\os\public\amd64fre\onecoreuap\external\sdk\inc
3208>   /Ie:\os\public\amd64fre\onecore\private\sdk\inc\MinWin
3208>   /Ie:\os\public\amd64fre\onecore\internal\sdk\inc\MinWin
3208>   /Ie:\os\public\amd64fre\onecore\external\sdk\inc\MinWin
3208>   /Ie:\os\public\amd64fre\onecore\internal\sdk\inc\MinCore
3208>   /Ie:\os\public\amd64fre\onecore\external\sdk\inc\MinCore
3208>   /Ie:\os\public\amd64fre\onecore\internal\sdk\inc\ClientCore
3208>   /Ie:\os\public\amd64fre\onecore\external\sdk\inc\ClientCore
3208>   /Ie:\os\public\amd64fre\onecore\internal\sdk\inc\ModernCore
3208>   /Ie:\os\public\amd64fre\onecore\external\sdk\inc\ModernCore
3208>   /Ie:\os\public\amd64fre\shared\inc
3208>   /Ie:\os\public\amd64fre\onecore\external\shared\inc
3208>   /Ie:\os\public\amd64fre\onecoreuap\external\shared\inc
3208>   /Ie:\os\public\amd64fre\onecore\external\ddk\inc
3208>   /Ie:\os\public\amd64fre\onecoreuap\external\ddk\inc
3208>   /Ie:\os\public\amd64fre\onecore\external\ddk\inc\wdm
3208>   /Ie:\os\public\amd64fre\onecoreuap\external\ddk\inc\wdm
3208>   /Ie:\os\public\amd64fre\internal\sdk\inc
3208>   /Ie:\os\public\amd64fre\onecore\private\sdk\inc
3208>   /Ie:\os\public\amd64fre\onecoreuap\private\sdk\inc
3208>   /Ie:\os\public\amd64fre\onecore\internal\sdk\inc
3208>   /Ie:\os\public\amd64fre\onecore\restricted\sdk\inc
3208>   /Ie:\os\public\amd64fre\onecoreuap\internal\sdk\inc
3208>   /Ie:\os\public\amd64fre\onecoreuap\restricted\sdk\inc
3208>   /Ie:\os\public\amd64fre\onecore\internal\sdk\inc\MinWin
3208>   /Ie:\os\public\amd64fre\onecore\internal\sdk\inc\MinWin\fs
3208>   /Ie:\os\public\amd64fre\onecore\internal\sdk\inc\MinCore
3208>   /Ie:\os\public\amd64fre\onecore\internal\sdk\inc\ClientCore
3208>   /Ie:\os\public\amd64fre\onecore\internal\sdk\inc\ModernCore
3208>   /Ie:\os\public\amd64fre\OneCore\Internal\hv\hvsdk\just_built\inc\private
3208>   /Ie:\os\public\amd64fre\OneCore\Internal\hv\hvsdk\just_built\inc\internal
3208>   /Ie:\os\public\amd64fre\sdk\inc\ucrt
3208>   /Ie:\os\public\amd64fre\internal\sdk\inc\ucrt
3208>   /Ie:\os\public\amd64fre\onecore\external\sdk\inc\ucrt
3208>   /Ie:\os\public\amd64fre\onecore\internal\sdk\inc\ucrt
3208>   /Ie:\os\public\amd64fre\onecore\internal\sdk\inc\ucrt\stl120
3208>   /Ie:\os\public\amd64fre\onecore\internal\sdk\inc\ucrt\stl120
3208>   /D_WIN64
3208>   /D_AMD64_
3208>   /DAMD64
3208>   /DCONDITION_HANDLING=1
3208>   /DNT_INST=0
3208>   /DWIN32=100
3208>   /D_NT1X_=100
3208>   /DWINNT=1
3208>   /D_WIN32_WINNT=0x0A00
3208>   /DWINVER=0x0A00
3208>   /D_WIN32_IE=0x0A00
3208>   /DWIN32_LEAN_AND_MEAN=1
3208>   /DDEVL=1
3208>   /DNDEBUG
3208>   /D_STL120_
3208>   /D_STL140_
3208>   /D_DLL=1
3208>   /D_MT=1
3208>   -DNT_IUM
3208>   -DWIN32
3208>   -D_WIN32
3208>   -DUNICODE
3208>   -D_UNICODE
3208>   -D_ARM_WINAPI_PARTITION_DESKTOP_SDK_AVAILABLE
3208>   /D_USE_DEV11_CRT
3208>   -D_APISET_MINWIN_VERSION=0x0115
3208>   -D_APISET_MINCORE_VERSION=0x0114
3208>   /DFE_SB
3208>   /DFE_IME
3208>   /DNTDDI_VERSION=0x0A000011
3208>   /DWINBLUE_KBSPRING14
3208>   /DBUILD_WINDOWS
3208>   /DUNDOCKED_WINDOWS_UCRT
3208>   /D__WRL_CONFIGURATION_LEGACY__
3208>   /DBUILD_UMS_ENABLED=1
3208>   /DBUILD_WOW64_ENABLED=1
3208>   /DBUILD_ARM64X_ENABLED=0
3208>   /DEXECUTABLE_WRITES_SUPPORT=0
3208>   -D_USE_DECLSPECS_FOR_SAL=1
3208>   /DRUN_WPP
3208>   -D__PLACEHOLDER_SAL=1
3208>   /c
3208>   /Zc:wchar_t-
3208>   /Zl
3208>   /Zp8
3208>   /Gy
3208>   /W4
3208>   /wd4244
3208>   /EHsc
3208>   /d1import_no_registry
3208>   /EHsc
3208>   /GR-
3208>   /GF
3208>   /GS
3208>   /Z7
3208>   /Oxs
3208>   /GL
3208>   /Z7
3208>   @e:\os\src\tools\NMakeJS\WarningsCop\WarningsCop.Cxx.DefaultErrors.rsp
3208>   /we4308 /we4509 /we4510 /we4532 /we4533 /we4610 /we4700 /we4789
3208>   /w15043
3208>   /Zc:rvalueCast
3208>   -D_UCRT
3208>   -D_CONST_RETURN=
3208>   -D_CRT_SECURE_NO_WARNINGS
3208>   -D_CRT_NON_CONFORMING_SWPRINTFS
3208>   -D_CRT_NONSTDC_NO_WARNINGS
3208>   -D_CRT_STDIO_ARBITRARY_WIDE_SPECIFIERS
3208>   /D_CRT_STDIO_INLINE=extern
3208>   /D_NO_CRT_STDIO_INLINE
3208>   /D_ACRTIMP_ALT=
3208>   /D_SILENCE_STDEXT_HASH_DEPRECATION_WARNINGS
3208>   /D_STL_EXTRA_DISABLED_WARNINGS=4239
3208>   /D_SILENCE_TR1_NAMESPACE_DEPRECATION_WARNING
3208>   /D_SILENCE_ALL_CXX17_DEPRECATION_WARNINGS
3208>   /D_SILENCE_TR2_SYS_NAMESPACE_DEPRECATION_WARNING
3208>   /D_HAS_FUNCTION_ALLOCATOR_SUPPORT=1
3208>   /D_SILENCE_STDEXT_ALLOCATORS_DEPRECATION_WARNING
3208>   /D_HAS_STD_BYTE=0
3208>   /D_ENFORCE_MATCHING_ALLOCATORS=0
3208>   /D_HAS_FUNCTION_ALLOCATOR_SUPPORT=1
3208>   /D_SILENCE_STDEXT_ALLOCATORS_DEPRECATION_WARNING
3208>   /D_FULL_IOBUF
3208>   /d1initAll:Mask11
3208>   /d1initAll:FillPattern0
3208>   /d1nodatetime
3208>   /d1trimfile:e:\os\src\=BASEDIR
3208>   /d1trimfile:e:\os\public\amd64fre\=PUBLIC_ROOT
3208>   /d1trimfile:e:\os\obj\amd64fre\=OBJECT_ROOT
3208>   /d1trimfile:e:\os\bin\amd64fre\=_NTTREE
3208>   /d1trimfile:e:\os\osdep\=OSDEPENDSROOT
3208>   /d2AllowCompatibleILVersions
3208>   /d2Zi+
3208>   /ZH:SHA_256
3208>   /wd4986
3208>   /wd4987
3208>   /wd4471
3208>   /wd4369
3208>   /wd4309
3208>   /wd4754
3208>   /wd4427
3208>   /d2DeepThoughtInliner-
3208>   /d2implyavx512upperregs-
3208>   /Wv:19.23
3208>   /Fwe:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\adpsmcpsvr\objfre\amd64\
3208>   @e:\os\obj\amd64fre\objfre\amd64\DMF\logged-warnings.rsp
3208>   /wl4002
3208>   /wl4003
3208>   /wl4005
3208>   /wl4006
3208>   /wl4007
3208>   /wl4008
3208>   /wl4010
3208>   /wl4013
3208>   /wl4015
3208>   /wl4018
3208>   /wl4020
3208>   /wl4022
3208>   /wl4024
3208>   /wl4025
3208>   /wl4026
3208>   /wl4027
3208>   /wl4028
3208>   /wl4029
3208>   /wl4030
3208>   /wl4031
3208>   /wl4033
3208>   /wl4034
3208>   /wl4036
3208>   /wl4038
3208>   /wl4041
3208>   /wl4042
3208>   /wl4045
3208>   /wl4047
3208>   /wl4048
3208>   /wl4049
3208>   /wl4056
3208>   /wl4066
3208>   /wl4067
3208>   /wl4068
3208>   /wl4073
3208>   /wl4074
3208>   /wl4075
3208>   /wl4076
3208>   /wl4077
3208>   /wl4079
3208>   /wl4080
3208>   /wl4081
3208>   /wl4083
3208>   /wl4085
3208>   /wl4086
3208>   /wl4087
3208>   /wl4088
3208>   /wl4089
3208>   /wl4090
3208>   /wl4091
3208>   /wl4094
3208>   /wl4096
3208>   /wl4097
3208>   /wl4098
3208>   /wl4099
3208>   /wl4101
3208>   /wl4102
3208>   /wl4109
3208>   /wl4112
3208>   /wl4113
3208>   /wl4114
3208>   /wl4115
3208>   /wl4116
3208>   /wl4117
3208>   /wl4119
3208>   /wl4120
3208>   /wl4122
3208>   /wl4124
3208>   /wl4129
3208>   /wl4133
3208>   /wl4138
3208>   /wl4141
3208>   /wl4142
3208>   /wl4143
3208>   /wl4144
3208>   /wl4145
3208>   /wl4150
3208>   /wl4153
3208>   /wl4154
3208>   /wl4155
3208>   /wl4156
3208>   /wl4157
3208>   /wl4158
3208>   /wl4159
3208>   /wl4160
3208>   /wl4161
3208>   /wl4162
3208>   /wl4163
3208>   /wl4164
3208>   /wl4166
3208>   /wl4167
3208>   /wl4168
3208>   /wl4172
3208>   /wl4174
3208>   /wl4175
3208>   /wl4176
3208>   /wl4177
3208>   /wl4178
3208>   /wl4180
3208>   /wl4182
3208>   /wl4183
3208>   /wl4185
3208>   /wl4186
3208>   /wl4187
3208>   /wl4190
3208>   /wl4192
3208>   /wl4197
3208>   /wl4200
3208>   /wl4213
3208>   /wl4215
3208>   /wl4216
3208>   /wl4218
3208>   /wl4223
3208>   /wl4224
3208>   /wl4226
3208>   /wl4227
3208>   /wl4228
3208>   /wl4229
3208>   /wl4230
3208>   /wl4237
3208>   /wl4240
3208>   /wl4243
3208>   /wl4244
3208>   /wl4250
3208>   /wl4251
3208>   /wl4258
3208>   /wl4267
3208>   /wl4269
3208>   /wl4272
3208>   /wl4273
3208>   /wl4274
3208>   /wl4275
3208>   /wl4276
3208>   /wl4278
3208>   /wl4280
3208>   /wl4281
3208>   /wl4282
3208>   /wl4283
3208>   /wl4285
3208>   /wl4286
3208>   /wl4288
3208>   /wl4290
3208>   /wl4291
3208>   /wl4293
3208>   /wl4297
3208>   /wl4302
3208>   /wl4305
3208>   /wl4306
3208>   /wl4307
3208>   /wl4309
3208>   /wl4310
3208>   /wl4311
3208>   /wl4312
3208>   /wl4313
3208>   /wl4316
3208>   /wl4319
3208>   /wl4325
3208>   /wl4326
3208>   /wl4329
3208>   /wl4333
3208>   /wl4334
3208>   /wl4335
3208>   /wl4340
3208>   /wl4344
3208>   /wl4346
3208>   /wl4348
3208>   /wl4353
3208>   /wl4356
3208>   /wl4357
3208>   /wl4358
3208>   /wl4359
3208>   /wl4364
3208>   /wl4368
3208>   /wl4369
3208>   /wl4373
3208>   /wl4374
3208>   /wl4375
3208>   /wl4376
3208>   /wl4377
3208>   /wl4378
3208>   /wl4379
3208>   /wl4381
3208>   /wl4382
3208>   /wl4383
3208>   /wl4384
3208>   /wl4390
3208>   /wl4391
3208>   /wl4392
3208>   /wl4393
3208>   /wl4394
3208>   /wl4395
3208>   /wl4396
3208>   /wl4397
3208>   /wl4398
3208>   /wl4399
3208>   /wl4600
3208>   /wl4401
3208>   /wl4402
3208>   /wl4403
3208>   /wl4404
3208>   /wl4405
3208>   /wl4406
3208>   /wl4407
3208>   /wl4409
3208>   /wl4410
3208>   /wl4411
3208>   /wl4414
3208>   /wl4420
3208>   /wl4430
3208>   /wl4436
3208>   /wl4439
3208>   /wl4440
3208>   /wl4441
3208>   /wl4445
3208>   /wl4461
3208>   /wl4462
3208>   /wl4470
3208>   /wl4473
3208>   /wl4477
3208>   /wl4484
3208>   /wl4485
3208>   /wl4486
3208>   /wl4488
3208>   /wl4489
3208>   /wl4490
3208>   /wl4502
3208>   /wl4503
3208>   /wl4506
3208>   /wl4508
3208>   /wl4511
3208>   /wl4518
3208>   /wl4521
3208>   /wl4522
3208>   /wl4523
3208>   /wl4526
3208>   /wl4530
3208>   /wl4534
3208>   /wl4535
3208>   /wl4537
3208>   /wl4538
3208>   /wl4540
3208>   /wl4541
3208>   /wl4543
3208>   /wl4544
3208>   /wl4550
3208>   /wl4551
3208>   /wl4552
3208>   /wl4553
3208>   /wl4554
3208>   /wl4556
3208>   /wl4558
3208>   /wl4561
3208>   /wl4566
3208>   /wl4570
3208>   /wl4572
3208>   /wl4580
3208>   /wl4581
3208>   /wl4584
3208>   /wl4596
3208>   /wl4597
3208>   /wl4602
3208>   /wl4603
3208>   /wl4606
3208>   /wl4612
3208>   /wl4613
3208>   /wl4615
3208>   /wl4616
3208>   /wl4618
3208>   /wl4620
3208>   /wl4621
3208>   /wl4622
3208>   /wl4624
3208>   /wl4627
3208>   /wl4630
3208>   /wl4632
3208>   /wl4633
3208>   /wl4635
3208>   /wl4636
3208>   /wl4637
3208>   /wl4638
3208>   /wl4641
3208>   /wl4645
3208>   /wl4646
3208>   /wl4650
3208>   /wl4651
3208>   /wl4652
3208>   /wl4653
3208>   /wl4655
3208>   /wl4656
3208>   /wl4657
3208>   /wl4659
3208>   /wl4661
3208>   /wl4662
3208>   /wl4667
3208>   /wl4669
3208>   /wl4674
3208>   /wl4677
3208>   /wl4678
3208>   /wl4679
3208>   /wl4683
3208>   /wl4684
3208>   /wl4685
3208>   /wl4687
3208>   /wl4688
3208>   /wl4691
3208>   /wl4693
3208>   /wl4694
3208>   /wl4698
3208>   /wl4711
3208>   /wl4715
3208>   /wl4716
3208>   /wl4717
3208>   /wl4722
3208>   /wl4723
3208>   /wl4724
3208>   /wl4727
3208>   /wl4730
3208>   /wl4731
3208>   /wl4733
3208>   /wl4739
3208>   /wl4742
3208>   /wl4743
3208>   /wl4744
3208>   /wl4747
3208>   /wl4750
3208>   /wl4756
3208>   /wl4768
3208>   /wl4772
3208>   /wl4788
3208>   /wl4793
3208>   /wl4794
3208>   /wl4799
3208>   /wl4803
3208>   /wl4804
3208>   /wl4805
3208>   /wl4806
3208>   /wl4807
3208>   /wl4810
3208>   /wl4811
3208>   /wl4812
3208>   /wl4813
3208>   /wl4817
3208>   /wl4819
3208>   /wl4821
3208>   /wl4823
3208>   /wl4829
3208>   /wl4834
3208>   /wl4835
3208>   /wl4838
3208>   /wl4839
3208>   /wl4867
3208>   /wl4900
3208>   /wl4910
3208>   /wl4912
3208>   /wl4920
3208>   /wl4925
3208>   /wl4926
3208>   /wl4927
3208>   /wl4929
3208>   /wl4930
3208>   /wl4935
3208>   /wl4936
3208>   /wl4939
3208>   /wl4944
3208>   /wl4945
3208>   /wl4947
3208>   /wl4948
3208>   /wl4949
3208>   /wl4950
3208>   /wl4951
3208>   /wl4952
3208>   /wl4953
3208>   /wl4956
3208>   /wl4957
3208>   /wl4958
3208>   /wl4959
3208>   /wl4961
3208>   /wl4964
3208>   /wl4965
3208>   /wl4972
3208>   /wl4984
3208>   /wl4995
3208>   /wl4996
3208>   /wl4997
3208>   /wl4999
3208>   /wl5033
3208>   /wl5037
3208>   /wl5046
3208>   /wl5050
3208>   /wl5055
3208>   /wl5056
3208>   /wl5105
3208>   /wl5208
3208>   /d2Qvec-mathlib-
3208>   /d2Qvec-sse2only
3208>   /Gw
3208>   /Zc:checkGwOdr
3208>   /d1ignorePragmaWarningError
3208>   /wd4316
3208>   /wd4973
3208>   /DDONT_DISABLE_PCH_WARNINGS_IN_WARNING_H
3208>   /d2FH4
3208>   /Brepro
3208>   -D_HAS_MAGIC_STATICS=1
3208>   /Qspectre
3208>   /wd5045
3208>   /d2guardspecanalysismode:v1_0
3208>   /d2guardspecmode2
3208>   /guard:cf
3208>   /d2guardcfgfuncptr-
3208>   /d2guardcfgdispatch
3208>   /guard:ehcont
3208>   -D__PLACEHOLDER_SAL=1
3208>   -wd4425
3208>   @e:\os\obj\amd64fre\objfre\amd64\WarningsCop\OneCore.rsp
3208>   /wl4146 /wl4308 /wl4509 /wl4510 /wl4532 /wl4533 /wl4610 /wl4700 /wl4701 /wl4703 /wl4789
3208>   /FIe:\os\public\amd64fre\onecore\internal\sdk\inc\warning.h
3208>   /std:c++17
3208>   /Yupch.hxx /Fpe:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\adpsmcpsvr\objfre\amd64\pch.pch
3208>   .\adpsmcpsvr.cpp .\powershellexecutor.cpp 
3208>adpsmcpsvr.cpp
3208>powershellexecutor.cpp
3208> e:\os\tools\vc\HostX64\amd64\link.exe /lib /out:e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\adpsmcpsvr\objfre\amd64\AdPsMcpSvr.lib /IGNORE:4078,4221,4281,4006,4198   /nodefaultlib /machine:amd64 /ltcg /Brepro @e:\os\obj\amd64fre\temp\621f0494d035e519a9fa1e57076d9949\lib_1.rsp
3208>Microsoft (R) Library Manager Version 14.42.34444.100
3208>Copyright (C) Microsoft Corporation.  All rights reserved.
3208>e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\adpsmcpsvr\objfre\amd64\pch.obj 
3208>e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\adpsmcpsvr\objfre\amd64\adpsmcpsvr.obj 
3208>e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\adpsmcpsvr\objfre\amd64\powershellexecutor.obj 
3208>Writing out macros...e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\adpsmcpsvr\objfre\amd64\Macros-PASS1.txt
3208>binplace e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\adpsmcpsvr\objfre\amd64\Macros-PASS1.txt
3208> e:\os\tools\powershell\pwsh.exe -NoProfile e:\os\src\tools\NMakeJS\WarningsCop\WarningsCop.ps1 -MakeSubDir "onecore\ds\ds\src\aimx\prod\adpsmcpsvr" -Pass PASS1 -BaselineFile e:\os\src\.config\OneCore\WarningsCop.json -OutputDir "e:\os\bin\amd64fre\evidence\WarningsCop\OneCore\onecoreds"
3208>WarningsCop.ps1 : WarningsCop: Processing onecore\ds\ds\src\aimx\prod\adpsmcpsvr in pass PASS1
3207>[33;1mWARNING: Deprecated version found: 4.0, changing back to 12.0[0m
3207>c:\windows\system32\cmd.exe /c E:\os\tools\wines_msbuild\_lowercd.cmd e:\os\src\tools\urtrun.cmd 4.Latest E:\os\tools\wines_msbuild\MSBuild\Current\Bin\MSBuild.exe -toolsVersion:12.0 vcpkg.proj -NoLogo /clp:NoSummary /p:BuildingWithBuildExe=true /p:THREAD_ID=7 /p:ObjectPath=e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\cpprestsdk\ /p:BuildingInSeparatePasses=true /p:TARGET_PLATFORM=windows /p:Pass=Compile /p:CODE_SETS_BUILDING=cs_windows:cs_xbox:cs_phone /p:CODE_SETS_TAGGED=cs_windows:cs_xbox /v:normal /t:BuildCompiled  
3207>Build started 7/15/2025 2:02:56 PM.
3207>Project "e:\os\src\onecore\ds\ds\src\aimx\prod\cpprestsdk\vcpkg.proj" on node 1 (BuildCompiled target(s)).
3207>RunVCPkg:
3207>  Removing directory "e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\cpprestsdk\objfre\amd64\vcpkg_empty".
3207>  Creating directory "e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\cpprestsdk\objfre\amd64\vcpkg_empty".
3207>  e:\os\src\tools\vcpkg\printConfigValue.bat vcpkg.usedownloadcache
3207>  true
3207>  e:\os\src\tools\vcpkg\printConfigValue.bat vcpkg.prepopulatedownloadcache
3207>  true
3207>  e:\os\src\tools\vcpkg\printConfigValue.bat vcpkg.boosthangdetectionseconds
3207>  2400
3207>  e:\os\src\tools\vcpkg\printConfigValue.bat vcpkg.componentconfigurationtimeout
3207>  7200
3207>  e:\os\src\tools\vcpkg\printConfigValue.bat vcpkg.usebinarycache
3207>  true
3207>  e:\os\src\tools\vcpkg\printConfigValue.bat vcpkg.logfilehashes
3207>  false
3207>  e:\os\src\tools\vcpkg\printConfigValue.bat vcpkg.registerwithcg
3207>  false
3207>  e:\os\src\tools\vcpkg\printConfigValue.bat vcpkg.usenugetcache
3207>  true
3207>  Hashed 26276723 bytes across 729 files in 9699ms: e0bfee1fdba01d196a9d4d3133217ed0fa40687e3a3955277f4821493e63ce49aeaa93e4079466d9501179b82e0e34adb8c20bea3b8c1838205d8f56b69808ef
3207>  Deleting Directory: e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\cpprestsdk\objfre\amd64\vcpkg_buildtrees\cpprestsdk
3207>  Deleting Directory: e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\cpprestsdk\objfre\amd64\vcpkg_buildtrees\detect_compiler\target-windows-rel\CMakeFiles\3.30.1\CompilerIdC\tmp
3207>  Deleting Directory: e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\cpprestsdk\objfre\amd64\vcpkg_buildtrees\detect_compiler\target-windows-rel\CMakeFiles\3.30.1\CompilerIdC
3207>  Deleting Directory: e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\cpprestsdk\objfre\amd64\vcpkg_buildtrees\detect_compiler\target-windows-rel\CMakeFiles\3.30.1\CompilerIdCXX\tmp
3207>  Deleting Directory: e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\cpprestsdk\objfre\amd64\vcpkg_buildtrees\detect_compiler\target-windows-rel\CMakeFiles\3.30.1\CompilerIdCXX
3207>  Deleting Directory: e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\cpprestsdk\objfre\amd64\vcpkg_buildtrees\detect_compiler\target-windows-rel\CMakeFiles\3.30.1
3207>  Deleting Directory: e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\cpprestsdk\objfre\amd64\vcpkg_buildtrees\detect_compiler\target-windows-rel\CMakeFiles\pkgRedirects
3207>  Deleting Directory: e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\cpprestsdk\objfre\amd64\vcpkg_buildtrees\detect_compiler\target-windows-rel\CMakeFiles\ShowIncludes
3207>  Deleting Directory: e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\cpprestsdk\objfre\amd64\vcpkg_buildtrees\detect_compiler\target-windows-rel\CMakeFiles
3207>  Deleting Directory: e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\cpprestsdk\objfre\amd64\vcpkg_buildtrees\detect_compiler\target-windows-rel\vcpkg-parallel-configure
3207>  Deleting Directory: e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\cpprestsdk\objfre\amd64\vcpkg_buildtrees\detect_compiler\target-windows-rel
3207>  Deleting Directory: e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\cpprestsdk\objfre\amd64\vcpkg_buildtrees\detect_compiler
3207>  Deleting Directory: e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\cpprestsdk\objfre\amd64\vcpkg_buildtrees
3207>  Deleting Directory: e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\cpprestsdk\objfre\amd64\vcpkg_downloads\tools\7zip-24.09-windows\Lang
3207>  Deleting Directory: e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\cpprestsdk\objfre\amd64\vcpkg_downloads\tools\7zip-24.09-windows
3207>  Deleting Directory: e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\cpprestsdk\objfre\amd64\vcpkg_downloads\tools\7zr-24.09-windows
3207>  Deleting Directory: e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\cpprestsdk\objfre\amd64\vcpkg_downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\bin
3207>  Deleting Directory: e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\cpprestsdk\objfre\amd64\vcpkg_downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\doc\cmake\cmcppdap
3207>  Deleting Directory: e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\cpprestsdk\objfre\amd64\vcpkg_downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\doc\cmake\cmcurl
3207>  Deleting Directory: e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\cpprestsdk\objfre\amd64\vcpkg_downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\doc\cmake\cmlibarchive
3207>  Deleting Directory: e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\cpprestsdk\objfre\amd64\vcpkg_downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\doc\cmake\cmliblzma
3207>  Deleting Directory: e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\cpprestsdk\objfre\amd64\vcpkg_downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\doc\cmake\cmlibrhash
3207>  Deleting Directory: e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\cpprestsdk\objfre\amd64\vcpkg_downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\doc\cmake\cmlibuv
3207>  Deleting Directory: e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\cpprestsdk\objfre\amd64\vcpkg_downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\doc\cmake\cmnghttp2
3207>  Deleting Directory: e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\cpprestsdk\objfre\amd64\vcpkg_downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\doc\cmake\cmsys
3207>  Deleting Directory: e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\cpprestsdk\objfre\amd64\vcpkg_downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\doc\cmake\cmzlib
3207>  Deleting Directory: e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\cpprestsdk\objfre\amd64\vcpkg_downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\doc\cmake\cmzstd
3207>  Deleting Directory: e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\cpprestsdk\objfre\amd64\vcpkg_downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\doc\cmake\html\command
3207>  Deleting Directory: e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\cpprestsdk\objfre\amd64\vcpkg_downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\doc\cmake\html\cpack_gen
3207>  Deleting Directory: e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\cpprestsdk\objfre\amd64\vcpkg_downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\doc\cmake\html\envvar
3207>  Deleting Directory: e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\cpprestsdk\objfre\amd64\vcpkg_downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\doc\cmake\html\generator
3207>  Deleting Directory: e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\cpprestsdk\objfre\amd64\vcpkg_downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\doc\cmake\html\guide\ide-integration
3207>  Deleting Directory: e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\cpprestsdk\objfre\amd64\vcpkg_downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\doc\cmake\html\guide\importing-exporting
3207>  Deleting Directory: e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\cpprestsdk\objfre\amd64\vcpkg_downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\doc\cmake\html\guide\tutorial
3207>  Deleting Directory: e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\cpprestsdk\objfre\amd64\vcpkg_downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\doc\cmake\html\guide\user-interaction
3207>  Deleting Directory: e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\cpprestsdk\objfre\amd64\vcpkg_downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\doc\cmake\html\guide\using-dependencies
3207>  Deleting Directory: e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\cpprestsdk\objfre\amd64\vcpkg_downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\doc\cmake\html\guide
3207>  Deleting Directory: e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\cpprestsdk\objfre\amd64\vcpkg_downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\doc\cmake\html\manual
3207>  Deleting Directory: e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\cpprestsdk\objfre\amd64\vcpkg_downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\doc\cmake\html\module
3207>  Deleting Directory: e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\cpprestsdk\objfre\amd64\vcpkg_downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\doc\cmake\html\policy
3207>  Deleting Directory: e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\cpprestsdk\objfre\amd64\vcpkg_downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\doc\cmake\html\prop_cache
3207>  Deleting Directory: e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\cpprestsdk\objfre\amd64\vcpkg_downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\doc\cmake\html\prop_dir
3207>  Deleting Directory: e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\cpprestsdk\objfre\amd64\vcpkg_downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\doc\cmake\html\prop_gbl
3207>  Deleting Directory: e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\cpprestsdk\objfre\amd64\vcpkg_downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\doc\cmake\html\prop_inst
3207>  Deleting Directory: e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\cpprestsdk\objfre\amd64\vcpkg_downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\doc\cmake\html\prop_sf
3207>  Deleting Directory: e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\cpprestsdk\objfre\amd64\vcpkg_downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\doc\cmake\html\prop_test
3207>  Deleting Directory: e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\cpprestsdk\objfre\amd64\vcpkg_downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\doc\cmake\html\prop_tgt
3207>  Deleting Directory: e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\cpprestsdk\objfre\amd64\vcpkg_downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\doc\cmake\html\release
3207>  Deleting Directory: e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\cpprestsdk\objfre\amd64\vcpkg_downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\doc\cmake\html\variable
3207>  Deleting Directory: e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\cpprestsdk\objfre\amd64\vcpkg_downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\doc\cmake\html\_downloads\3e2d73bff478d88a7de0de736ba5e361
3207>  Deleting Directory: e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\cpprestsdk\objfre\amd64\vcpkg_downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\doc\cmake\html\_downloads
3207>  Deleting Directory: e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\cpprestsdk\objfre\amd64\vcpkg_downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\doc\cmake\html\_images
3207>  Deleting Directory: e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\cpprestsdk\objfre\amd64\vcpkg_downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\doc\cmake\html\_sources\command
3207>  Deleting Directory: e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\cpprestsdk\objfre\amd64\vcpkg_downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\doc\cmake\html\_sources\cpack_gen
3207>  Deleting Directory: e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\cpprestsdk\objfre\amd64\vcpkg_downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\doc\cmake\html\_sources\envvar
3207>  Deleting Directory: e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\cpprestsdk\objfre\amd64\vcpkg_downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\doc\cmake\html\_sources\generator
3207>  Deleting Directory: e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\cpprestsdk\objfre\amd64\vcpkg_downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\doc\cmake\html\_sources\guide\ide-integration
3207>  Deleting Directory: e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\cpprestsdk\objfre\amd64\vcpkg_downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\doc\cmake\html\_sources\guide\importing-exporting
3207>  Deleting Directory: e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\cpprestsdk\objfre\amd64\vcpkg_downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\doc\cmake\html\_sources\guide\tutorial
3207>  Deleting Directory: e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\cpprestsdk\objfre\amd64\vcpkg_downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\doc\cmake\html\_sources\guide\user-interaction
3207>  Deleting Directory: e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\cpprestsdk\objfre\amd64\vcpkg_downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\doc\cmake\html\_sources\guide\using-dependencies
3207>  Deleting Directory: e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\cpprestsdk\objfre\amd64\vcpkg_downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\doc\cmake\html\_sources\guide
3207>  Deleting Directory: e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\cpprestsdk\objfre\amd64\vcpkg_downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\doc\cmake\html\_sources\manual
3207>  Deleting Directory: e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\cpprestsdk\objfre\amd64\vcpkg_downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\doc\cmake\html\_sources\module
3207>  Deleting Directory: e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\cpprestsdk\objfre\amd64\vcpkg_downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\doc\cmake\html\_sources\policy
3207>  Deleting Directory: e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\cpprestsdk\objfre\amd64\vcpkg_downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\doc\cmake\html\_sources\prop_cache
3207>  Deleting Directory: e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\cpprestsdk\objfre\amd64\vcpkg_downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\doc\cmake\html\_sources\prop_dir
3207>  Deleting Directory: e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\cpprestsdk\objfre\amd64\vcpkg_downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\doc\cmake\html\_sources\prop_gbl
3207>  Deleting Directory: e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\cpprestsdk\objfre\amd64\vcpkg_downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\doc\cmake\html\_sources\prop_inst
3207>  Deleting Directory: e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\cpprestsdk\objfre\amd64\vcpkg_downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\doc\cmake\html\_sources\prop_sf
3207>  Deleting Directory: e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\cpprestsdk\objfre\amd64\vcpkg_downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\doc\cmake\html\_sources\prop_test
3207>  Deleting Directory: e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\cpprestsdk\objfre\amd64\vcpkg_downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\doc\cmake\html\_sources\prop_tgt
3207>  Deleting Directory: e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\cpprestsdk\objfre\amd64\vcpkg_downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\doc\cmake\html\_sources\release
3207>  Deleting Directory: e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\cpprestsdk\objfre\amd64\vcpkg_downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\doc\cmake\html\_sources\variable
3207>  Deleting Directory: e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\cpprestsdk\objfre\amd64\vcpkg_downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\doc\cmake\html\_sources
3207>  Deleting Directory: e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\cpprestsdk\objfre\amd64\vcpkg_downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\doc\cmake\html\_static
3207>  Deleting Directory: e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\cpprestsdk\objfre\amd64\vcpkg_downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\doc\cmake\html
3207>  Deleting Directory: e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\cpprestsdk\objfre\amd64\vcpkg_downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\doc\cmake
3207>  Deleting Directory: e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\cpprestsdk\objfre\amd64\vcpkg_downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\doc
3207>  Deleting Directory: e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\cpprestsdk\objfre\amd64\vcpkg_downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\man\man1
3207>  Deleting Directory: e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\cpprestsdk\objfre\amd64\vcpkg_downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\man\man7
3207>  Deleting Directory: e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\cpprestsdk\objfre\amd64\vcpkg_downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\man
3207>  Deleting Directory: e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\cpprestsdk\objfre\amd64\vcpkg_downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\aclocal
3207>  Deleting Directory: e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\cpprestsdk\objfre\amd64\vcpkg_downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\bash-completion\completions
3207>  Deleting Directory: e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\cpprestsdk\objfre\amd64\vcpkg_downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\bash-completion
3207>  Deleting Directory: e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\cpprestsdk\objfre\amd64\vcpkg_downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Help\command
3207>  Deleting Directory: e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\cpprestsdk\objfre\amd64\vcpkg_downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Help\cpack_gen
3207>  Deleting Directory: e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\cpprestsdk\objfre\amd64\vcpkg_downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Help\envvar
3207>  Deleting Directory: e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\cpprestsdk\objfre\amd64\vcpkg_downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Help\generator
3207>  Deleting Directory: e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\cpprestsdk\objfre\amd64\vcpkg_downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Help\include
3207>  Deleting Directory: e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\cpprestsdk\objfre\amd64\vcpkg_downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Help\manual\presets
3207>  Deleting Directory: e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\cpprestsdk\objfre\amd64\vcpkg_downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Help\manual
3207>  Deleting Directory: e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\cpprestsdk\objfre\amd64\vcpkg_downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Help\module
3207>  Deleting Directory: e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\cpprestsdk\objfre\amd64\vcpkg_downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Help\policy
3207>  Deleting Directory: e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\cpprestsdk\objfre\amd64\vcpkg_downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Help\prop_cache
3207>  Deleting Directory: e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\cpprestsdk\objfre\amd64\vcpkg_downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Help\prop_dir
3207>  Deleting Directory: e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\cpprestsdk\objfre\amd64\vcpkg_downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Help\prop_gbl
3207>  Deleting Directory: e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\cpprestsdk\objfre\amd64\vcpkg_downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Help\prop_inst
3207>  Deleting Directory: e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\cpprestsdk\objfre\amd64\vcpkg_downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Help\prop_sf
3207>  Deleting Directory: e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\cpprestsdk\objfre\amd64\vcpkg_downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Help\prop_test
3207>  Deleting Directory: e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\cpprestsdk\objfre\amd64\vcpkg_downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Help\prop_tgt
3207>  Deleting Directory: e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\cpprestsdk\objfre\amd64\vcpkg_downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Help\release
3207>  Deleting Directory: e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\cpprestsdk\objfre\amd64\vcpkg_downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Help\variable
3207>  Deleting Directory: e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\cpprestsdk\objfre\amd64\vcpkg_downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Help
3207>  Deleting Directory: e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\cpprestsdk\objfre\amd64\vcpkg_downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\include
3207>  Deleting Directory: e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\cpprestsdk\objfre\amd64\vcpkg_downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Licenses
3207>  Deleting Directory: e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\cpprestsdk\objfre\amd64\vcpkg_downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\AndroidTestUtilities
3207>  Deleting Directory: e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\cpprestsdk\objfre\amd64\vcpkg_downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\CheckIPOSupported
3207>  Deleting Directory: e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\cpprestsdk\objfre\amd64\vcpkg_downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\CMakeAddFortranSubdirectory
3207>  Deleting Directory: e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\cpprestsdk\objfre\amd64\vcpkg_downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Compiler\XL-Fortran
3207>  Deleting Directory: e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\cpprestsdk\objfre\amd64\vcpkg_downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Compiler
3207>  Deleting Directory: e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\cpprestsdk\objfre\amd64\vcpkg_downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\CompilerId
3207>  Deleting Directory: e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\cpprestsdk\objfre\amd64\vcpkg_downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\ExternalProject
3207>  Deleting Directory: e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\cpprestsdk\objfre\amd64\vcpkg_downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\FetchContent
3207>  Deleting Directory: e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\cpprestsdk\objfre\amd64\vcpkg_downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\FindCUDA
3207>  Deleting Directory: e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\cpprestsdk\objfre\amd64\vcpkg_downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\FindMPI
3207>  Deleting Directory: e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\cpprestsdk\objfre\amd64\vcpkg_downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\FindPython
3207>  Deleting Directory: e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\cpprestsdk\objfre\amd64\vcpkg_downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\FortranCInterface\Verify
3207>  Deleting Directory: e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\cpprestsdk\objfre\amd64\vcpkg_downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\FortranCInterface
3207>  Deleting Directory: e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\cpprestsdk\objfre\amd64\vcpkg_downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\IntelVSImplicitPath
3207>  Deleting Directory: e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\cpprestsdk\objfre\amd64\vcpkg_downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Internal\CPack\WIX-v3
3207>  Deleting Directory: e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\cpprestsdk\objfre\amd64\vcpkg_downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Internal\CPack
3207>  Deleting Directory: e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\cpprestsdk\objfre\amd64\vcpkg_downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Internal\OSRelease
3207>  Deleting Directory: e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\cpprestsdk\objfre\amd64\vcpkg_downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Internal
3207>  Deleting Directory: e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\cpprestsdk\objfre\amd64\vcpkg_downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Platform\AIX
3207>  Deleting Directory: e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\cpprestsdk\objfre\amd64\vcpkg_downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Platform\Android
3207>  Deleting Directory: e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\cpprestsdk\objfre\amd64\vcpkg_downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Platform
3207>  Deleting Directory: e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\cpprestsdk\objfre\amd64\vcpkg_downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\UseJava
3207>  Deleting Directory: e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\cpprestsdk\objfre\amd64\vcpkg_downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\UseSWIG
3207>  Deleting Directory: e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\cpprestsdk\objfre\amd64\vcpkg_downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules
3207>  Deleting Directory: e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\cpprestsdk\objfre\amd64\vcpkg_downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Templates\MSBuild\FlagTables
3207>  Deleting Directory: e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\cpprestsdk\objfre\amd64\vcpkg_downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Templates\MSBuild
3207>  Deleting Directory: e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\cpprestsdk\objfre\amd64\vcpkg_downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Templates\Windows
3207>  Deleting Directory: e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\cpprestsdk\objfre\amd64\vcpkg_downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Templates
3207>  Deleting Directory: e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\cpprestsdk\objfre\amd64\vcpkg_downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30
3207>  Deleting Directory: e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\cpprestsdk\objfre\amd64\vcpkg_downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\emacs\site-lisp
3207>  Deleting Directory: e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\cpprestsdk\objfre\amd64\vcpkg_downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\emacs
3207>  Deleting Directory: e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\cpprestsdk\objfre\amd64\vcpkg_downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\vim\vimfiles\indent
3207>  Deleting Directory: e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\cpprestsdk\objfre\amd64\vcpkg_downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\vim\vimfiles\syntax
3207>  Deleting Directory: e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\cpprestsdk\objfre\amd64\vcpkg_downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\vim\vimfiles
3207>  Deleting Directory: e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\cpprestsdk\objfre\amd64\vcpkg_downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\vim
3207>  Deleting Directory: e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\cpprestsdk\objfre\amd64\vcpkg_downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share
3207>  Deleting Directory: e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\cpprestsdk\objfre\amd64\vcpkg_downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386
3207>  Deleting Directory: e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\cpprestsdk\objfre\amd64\vcpkg_downloads\tools\cmake-3.30.1-windows
3207>  Deleting Directory: e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\cpprestsdk\objfre\amd64\vcpkg_downloads\tools\ninja\1.12.1-windows
3207>  Deleting Directory: e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\cpprestsdk\objfre\amd64\vcpkg_downloads\tools\ninja
3207>  Deleting Directory: e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\cpprestsdk\objfre\amd64\vcpkg_downloads\tools\nuget-6.13.2-windows
3207>  Deleting Directory: e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\cpprestsdk\objfre\amd64\vcpkg_downloads\tools
3207>  Deleting Directory: e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\cpprestsdk\objfre\amd64\vcpkg_downloads
3207>  Deleting Directory: e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\cpprestsdk\objfre\amd64\vcpkg_installed\target-windows\include\cpprest\details
3207>  Deleting Directory: e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\cpprestsdk\objfre\amd64\vcpkg_installed\target-windows\include\cpprest
3207>  Deleting Directory: e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\cpprestsdk\objfre\amd64\vcpkg_installed\target-windows\include\pplx
3207>  Deleting Directory: e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\cpprestsdk\objfre\amd64\vcpkg_installed\target-windows\include
3207>  Deleting Directory: e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\cpprestsdk\objfre\amd64\vcpkg_installed\target-windows\lib
3207>  Deleting Directory: e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\cpprestsdk\objfre\amd64\vcpkg_installed\target-windows\share\cpprestsdk
3207>  Deleting Directory: e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\cpprestsdk\objfre\amd64\vcpkg_installed\target-windows\share
3207>  Deleting Directory: e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\cpprestsdk\objfre\amd64\vcpkg_installed\target-windows
3207>  Deleting Directory: e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\cpprestsdk\objfre\amd64\vcpkg_installed\vcpkg\info
3207>  Deleting Directory: e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\cpprestsdk\objfre\amd64\vcpkg_installed\vcpkg\updates
3207>  Deleting Directory: e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\cpprestsdk\objfre\amd64\vcpkg_installed\vcpkg
3207>  Deleting Directory: e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\cpprestsdk\objfre\amd64\vcpkg_installed
3207>  Deleting Directory: e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\cpprestsdk\objfre\amd64\vcpkg_packages\cpprestsdk_target-windows\include\cpprest\details
3207>  Deleting Directory: e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\cpprestsdk\objfre\amd64\vcpkg_packages\cpprestsdk_target-windows\include\cpprest
3207>  Deleting Directory: e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\cpprestsdk\objfre\amd64\vcpkg_packages\cpprestsdk_target-windows\include\pplx
3207>  Deleting Directory: e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\cpprestsdk\objfre\amd64\vcpkg_packages\cpprestsdk_target-windows\include
3207>  Deleting Directory: e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\cpprestsdk\objfre\amd64\vcpkg_packages\cpprestsdk_target-windows\lib
3207>  Deleting Directory: e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\cpprestsdk\objfre\amd64\vcpkg_packages\cpprestsdk_target-windows\share\cpprestsdk
3207>  Deleting Directory: e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\cpprestsdk\objfre\amd64\vcpkg_packages\cpprestsdk_target-windows\share
3207>  Deleting Directory: e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\cpprestsdk\objfre\amd64\vcpkg_packages\cpprestsdk_target-windows
3207>  Deleting Directory: e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\cpprestsdk\objfre\amd64\vcpkg_packages\detect_compiler_target-windows
3207>  Deleting Directory: e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\cpprestsdk\objfre\amd64\vcpkg_packages
3207>  BUILDMSG VCPkg is building the following packages:
3207>  BUILDMSG    cpprestsdk 2.10.18 [core] (for target); port directory = tools\vcpkg\overlay-ports\cpprestsdk
3207>  BUILDMSG Starting package 1/1: cpprestsdk:target-windows@2.10.18...
3207>  VCPkg Performance:
3207>  ReadTransferCount: 281290520 bytes
3207>  ReadOperationCount: 81650 bytes
3207>  WriteTransferCount: 286990472 bytes
3207>  WriteOperationCount: 16185 bytes
3207>  PeakJobMemoryUsed: 380313600 bytes
3207>  PeakProcessMemoryUsed: 184074240 bytes
3207>  TotalkernelTime: 84218750
3207>  TotalUserTime: 63437500
3207>  TotalProcesses: 68
3207>  TotalTerminatedProcesses: 0
3207>  Time Taken to to run vcpkg.exe: 24.3467351s
3207>  Disk space used by vcpkg: 342742643 bytes
3207>  Complete VCPkg build log is in e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\cpprestsdk\objfre\amd64\vcpkg_logs\VCPkgBuild.log
3207>   -:DEST VCPkg\onecore\ds\ds\src\aimx\prod\cpprestsdk e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\cpprestsdk\objfre\amd64\vcpkg_logs\*.*
3207>  The system cannot find the drive specified.
3207>   -:DEST 3rdPartyCatalogs\onecore\ds\ds\src\aimx\prod\cpprestsdk e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\cpprestsdk\objfre\amd64\vcpkg.cat.json
3207>  The system cannot find the drive specified.
3207>  Removing directory "e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\cpprestsdk\objfre\amd64\vcpkg_spdx".
3207>  Creating directory "e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\cpprestsdk\objfre\amd64\vcpkg_spdx".
3207>   -:DEST evidence\VCPkg\onecore\ds\ds\src\aimx\prod\cpprestsdk e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\cpprestsdk\objfre\amd64\vcpkg_spdx\*.*
3207>  The system cannot find the drive specified.
3207>  Removing directory "e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\cpprestsdk\objfre\amd64\vcpkg_cg".
3207>  Creating directory "e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\cpprestsdk\objfre\amd64\vcpkg_cg".
3207>  Allow-list check succeeded
3207>Done Building Project "e:\os\src\onecore\ds\ds\src\aimx\prod\cpprestsdk\vcpkg.proj" (BuildCompiled target(s)).
1>BUILDC (PipeSpawn): e:\os\tools\NMakeRust\x64\bin\nmake.exe /nologo BUILDMSG=Stop. /nologo /f e:\os\src\tool  1>[0:01:15.344] [Pass1 ] e:\os\src\onecore\ds\ds\src\aimx\prod\aimxsrv\server {36}
3201>BUILDMSG: Processing e:\os\src\onecore\ds\ds\src\aimx\prod\aimxsrv\server
3201>Compiling e:\os\src\onecore\ds\ds\src\aimx\prod\aimxsrv\server *************
3201>'e:\os\tools\NMakeRust\x64\bin\nmake.exe /nologo BUILDMSG=Stop. /nologo /f e:\os\src\tools\makefile.def BUILD_PASS=PASS1 NOLINK=1 MAKEDIR_RELATIVE_TO_BASEDIR=onecore\ds\ds\src\aimx\prod\aimxsrv\server TARGET_PLATFORM=windows CODE_SETS_BUILDING=cs_windows,cs_xbox,cs_phone CODE_SETS_TAGGED=cs_windows,cs_xbox'
BUILD: (ActiveWorkLoad),75.11,,56,0,1,16,16,0,0,9,0,0,0,0,PASS1,0,2000000000,<*> onecore\ds\ds\src\aimx\prod\aimxsrv\server,RUPO-DELL
3201>Calculated LAYERINFO_MODULE='OneCoreDS'.
3201>makefile.def: TEMP=e:\os\obj\amd64fre\temp\9435b82d6494576420d89fadc5711cb4
3201>makefile.def: BUILDINGINDATT=
3201>[Core OS Undocking] NOT using package ''
3201>UCRT enabled: dir 'e:\os\src\onecore\ds\ds\src\aimx\prod\aimxsrv\server' (target 'aimxserver', type 'LIBRARY', nt_target_version '0xA000011')
3201>ObjectsMac.ts: validation succeeded
3201>STL version 120 used in "e:\os\src\onecore\ds\ds\src\aimx\prod\aimxsrv\server" (STL_VER_TELEMETRY)
3201>_NEED_BUILDDATE not defined setting BUILDDATE to an invalid value.
3201>A subdirectory or file e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\aimxsrv\server\objfre\amd64 already exists.
3201> e:\os\tools\Windows.Desktop.Tools.amd64\tools\touch.exe /c e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\aimxsrv\server\objfre\amd64\_PASS1_Marker.log
3201> e:\os\tools\vc\HostX86\amd64\cl.exe @e:\os\obj\amd64fre\temp\9435b82d6494576420d89fadc5711cb4\tmp_21644_1752613416549853000.tmp /Tpe:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\aimxsrv\server\objfre\amd64\pch_hdr.src
3201>Microsoft (R) C/C++ Optimizing Compiler Version 19.42.34444.100 for x64
3201>Copyright (C) Microsoft Corporation.  All rights reserved.
3201>cl 
3201>   /Iamd64
3201>   /I.
3201>   /Ie:\os\src\data\MSRC
3201>   /Ie:\os\public\amd64fre\onecore\external\sdk\inc\atlmfc
3201>   /I..\..\common
3201>   /I..\..\common\nlohmann
3201>   /I..\..\common\httplib
3201>   /I..\..\admcpsrv
3201>   /I..\..\llmclientlib
3201>   /I..\..\agents\ADToolAgent
3201>   /Ie:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\aimxsrv\server
3201>   /Ie:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\aimxsrv\server\objfre\amd64
3201>   /Ie:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\aimxsrv\server\..\idl\objfre\amd64
3201>   /Ie:\os\src\onecore\ds\security\inc
3201>   /Ie:\os\public\amd64fre\onecore\internal\ds\inc
3201>   /Ie:\os\public\amd64fre\onecore\private\ds\inc\security\base
3201>   /Ie:\os\public\amd64fre\onecore\private\base\inc
3201>   /Ie:\os\public\amd64fre\onecore\internal\base\inc
3201>   /Ie:\os\public\amd64fre\OneCore\Internal\MinWin\Priv_Sdk\Inc
3201>   /Ie:\os\public\amd64fre\OneCore\Internal\MinWin\Priv_Sdk\Inc\lsa
3201>   /Ie:\os\public\amd64fre\OneCore\Internal\MinWin\Priv_Sdk\Inc\apiset
3201>   /Ie:\os\public\amd64fre\OneCore\Private\MinWin\Priv_Sdk\Inc
3201>   /Ie:\os\public\amd64fre\OneCore\Private\MinWin\Priv_Sdk\Inc\lsa
3201>   /Ie:\os\public\amd64fre\OneCore\Internal\MinCore\Priv_Sdk\Inc
3201>   /Ie:\os\src\onecore\ds\ds\src\adai\proto\win32\aimxsrv\idl
3201>   /Ie:\os\public\amd64fre\internal\sdk\inc
3201>   /Ie:\os\public\amd64fre\onecore\internal\sdk\inc
3201>   /Ie:\os\public\amd64fre\OneCore\Internal\OneCore\Priv_Sdk\Inc
3201>   /Ie:\os\public\amd64fre\onecore\internal\base\inc\appmodel\runtime\winrt
3201>   /Ie:\os\public\amd64fre\OneCoreUap\Internal\BuildMetadata\internal\cppwinrt
3201>   /Ie:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\cpprestsdk\objfre\amd64\vcpkg_installed\target-windows\include
3201>   /Ie:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\aimxsrv\server\objfre\amd64
3201>   /Ie:\os\src\onecore\ds\inc
3201>   /Ie:\os\obj\amd64fre\onecore\ds\inc\objfre\amd64
3201>   /Ie:\os\public\amd64fre\internal\onecoreds\inc
3201>   /Ie:\os\public\amd64fre\OneCore\Restricted\DS\inc
3201>   /Ie:\os\public\amd64fre\OneCoreUap\Restricted\DS\inc
3201>   /Ie:\os\public\amd64fre\OneCore\External\DS\inc
3201>   /Ie:\os\public\amd64fre\OneCoreUap\External\DS\inc
3201>   /Ie:\os\public\amd64fre\ClientCore\External\DS\inc
3201>   /Ie:\os\public\amd64fre\OneCore\Internal\DS\inc
3201>   /Ie:\os\public\amd64fre\OneCoreUap\Internal\DS\inc
3201>   /Ie:\os\public\amd64fre\ClientCore\Internal\DS\inc
3201>   /Ie:\os\public\amd64fre\OneCore\Private\DS\inc
3201>   /Ie:\os\public\amd64fre\OneCoreUap\Private\DS\inc
3201>   /Ie:\os\public\amd64fre\ClientCore\Private\DS\inc
3201>   /Ie:\os\public\amd64fre\OneCore\external\DS\inc
3201>   /Ie:\os\public\amd64fre\OneCore\restricted\DS\inc
3201>   /Ie:\os\public\amd64fre\OneCore\internal\DS\inc
3201>   /Ie:\os\public\amd64fre\OneCore\private\DS\inc
3201>   /Ie:\os\public\amd64fre\onecore\external\oak\inc
3201>   /Ie:\os\public\amd64fre\onecoreuap\external\oak\inc
3201>   /Ie:\os\public\amd64fre\shared\inc
3201>   /Ie:\os\public\amd64fre\onecore\external\shared\inc
3201>   /Ie:\os\public\amd64fre\onecoreuap\external\shared\inc
3201>   /Ie:\os\public\amd64fre\onecore\external\shared\inc\MinWin
3201>   /Ie:\os\public\amd64fre\sdk\inc
3201>   /Ie:\os\public\amd64fre\onecore\external\sdk\inc
3201>   /Ie:\os\public\amd64fre\onecoreuap\external\sdk\inc
3201>   /Ie:\os\public\amd64fre\onecore\private\sdk\inc\MinWin
3201>   /Ie:\os\public\amd64fre\onecore\internal\sdk\inc\MinWin
3201>   /Ie:\os\public\amd64fre\onecore\external\sdk\inc\MinWin
3201>   /Ie:\os\public\amd64fre\onecore\internal\sdk\inc\MinCore
3201>   /Ie:\os\public\amd64fre\onecore\external\sdk\inc\MinCore
3201>   /Ie:\os\public\amd64fre\onecore\internal\sdk\inc\ClientCore
3201>   /Ie:\os\public\amd64fre\onecore\external\sdk\inc\ClientCore
3201>   /Ie:\os\public\amd64fre\onecore\internal\sdk\inc\ModernCore
3201>   /Ie:\os\public\amd64fre\onecore\external\sdk\inc\ModernCore
3201>   /Ie:\os\public\amd64fre\shared\inc
3201>   /Ie:\os\public\amd64fre\onecore\external\shared\inc
3201>   /Ie:\os\public\amd64fre\onecoreuap\external\shared\inc
3201>   /Ie:\os\public\amd64fre\onecore\external\ddk\inc
3201>   /Ie:\os\public\amd64fre\onecoreuap\external\ddk\inc
3201>   /Ie:\os\public\amd64fre\onecore\external\ddk\inc\wdm
3201>   /Ie:\os\public\amd64fre\onecoreuap\external\ddk\inc\wdm
3201>   /Ie:\os\public\amd64fre\internal\sdk\inc
3201>   /Ie:\os\public\amd64fre\onecore\private\sdk\inc
3201>   /Ie:\os\public\amd64fre\onecoreuap\private\sdk\inc
3201>   /Ie:\os\public\amd64fre\onecore\internal\sdk\inc
3201>   /Ie:\os\public\amd64fre\onecore\restricted\sdk\inc
3201>   /Ie:\os\public\amd64fre\onecoreuap\internal\sdk\inc
3201>   /Ie:\os\public\amd64fre\onecoreuap\restricted\sdk\inc
3201>   /Ie:\os\public\amd64fre\onecore\internal\sdk\inc\MinWin
3201>   /Ie:\os\public\amd64fre\onecore\internal\sdk\inc\MinWin\fs
3201>   /Ie:\os\public\amd64fre\onecore\internal\sdk\inc\MinCore
3201>   /Ie:\os\public\amd64fre\onecore\internal\sdk\inc\ClientCore
3201>   /Ie:\os\public\amd64fre\onecore\internal\sdk\inc\ModernCore
3201>   /Ie:\os\public\amd64fre\OneCore\Internal\hv\hvsdk\just_built\inc\private
3201>   /Ie:\os\public\amd64fre\OneCore\Internal\hv\hvsdk\just_built\inc\internal
3201>   /Ie:\os\public\amd64fre\sdk\inc\ucrt
3201>   /Ie:\os\public\amd64fre\internal\sdk\inc\ucrt
3201>   /Ie:\os\public\amd64fre\onecore\external\sdk\inc\ucrt
3201>   /Ie:\os\public\amd64fre\onecore\internal\sdk\inc\ucrt
3201>   /Ie:\os\public\amd64fre\onecore\internal\sdk\inc\ucrt\stl120
3201>   /Ie:\os\public\amd64fre\onecore\internal\sdk\inc\ucrt\stl120
3201>   /D_WIN64
3201>   /D_AMD64_
3201>   /DAMD64
3201>   /DCONDITION_HANDLING=1
3201>   /DNT_INST=0
3201>   /DWIN32=100
3201>   /D_NT1X_=100
3201>   /DWINNT=1
3201>   /D_WIN32_WINNT=0x0A00
3201>   /DWINVER=0x0A00
3201>   /D_WIN32_IE=0x0A00
3201>   /DWIN32_LEAN_AND_MEAN=1
3201>   /DDEVL=1
3201>   /DNDEBUG
3201>   /D_STL120_
3201>   /D_STL140_
3201>   /D_DLL=1
3201>   /D_MT=1
3201>   -DNT_IUM
3201>   -DWIN32
3201>   -D_WIN32
3201>   -DUNICODE
3201>   -D_UNICODE
3201>   -D_ARM_WINAPI_PARTITION_DESKTOP_SDK_AVAILABLE
3201>   /D_USE_DEV11_CRT
3201>   -D_APISET_MINWIN_VERSION=0x0115
3201>   -D_APISET_MINCORE_VERSION=0x0114
3201>   /DFE_SB
3201>   /DFE_IME
3201>   /DNTDDI_VERSION=0x0A000011
3201>   /DWINBLUE_KBSPRING14
3201>   /DBUILD_WINDOWS
3201>   /DUNDOCKED_WINDOWS_UCRT
3201>   /D__WRL_CONFIGURATION_LEGACY__
3201>   /DBUILD_UMS_ENABLED=1
3201>   /DBUILD_WOW64_ENABLED=1
3201>   /DBUILD_ARM64X_ENABLED=0
3201>   /DEXECUTABLE_WRITES_SUPPORT=0
3201>   -D_USE_DECLSPECS_FOR_SAL=1
3201>   /DRUN_WPP
3201>   -D__PLACEHOLDER_SAL=1
3201>   /D_ATL_STATIC_REGISTRY
3201>   /c
3201>   /Zc:wchar_t-
3201>   /Zl
3201>   /Zp8
3201>   /Gy
3201>   /W4
3201>   /d1import_no_registry
3201>   /EHsc
3201>   /GR-
3201>   /GF
3201>   /GS
3201>   /Z7
3201>   /Oxs
3201>   /GL
3201>   /Z7
3201>   @e:\os\src\tools\NMakeJS\WarningsCop\WarningsCop.Cxx.DefaultErrors.rsp
3201>   /we4308 /we4509 /we4510 /we4532 /we4533 /we4610 /we4700 /we4789
3201>   /w15043
3201>   /Zc:rvalueCast
3201>   /Zo
3201>   -D_UCRT
3201>   -D_CONST_RETURN=
3201>   -D_CRT_SECURE_NO_WARNINGS
3201>   -D_CRT_NON_CONFORMING_SWPRINTFS
3201>   -D_CRT_NONSTDC_NO_WARNINGS
3201>   -D_CRT_STDIO_ARBITRARY_WIDE_SPECIFIERS
3201>   /D_CRT_STDIO_INLINE=extern
3201>   /D_NO_CRT_STDIO_INLINE
3201>   /D_ACRTIMP_ALT=
3201>   /D_SILENCE_STDEXT_HASH_DEPRECATION_WARNINGS
3201>   /D_STL_EXTRA_DISABLED_WARNINGS=4239
3201>   /D_SILENCE_TR1_NAMESPACE_DEPRECATION_WARNING
3201>   /D_SILENCE_ALL_CXX17_DEPRECATION_WARNINGS
3201>   /D_SILENCE_TR2_SYS_NAMESPACE_DEPRECATION_WARNING
3201>   /D_HAS_FUNCTION_ALLOCATOR_SUPPORT=1
3201>   /D_SILENCE_STDEXT_ALLOCATORS_DEPRECATION_WARNING
3201>   /D_HAS_STD_BYTE=0
3201>   /D_ENFORCE_MATCHING_ALLOCATORS=0
3201>   /D_HAS_FUNCTION_ALLOCATOR_SUPPORT=1
3201>   /D_SILENCE_STDEXT_ALLOCATORS_DEPRECATION_WARNING
3201>   /D_FULL_IOBUF
3201>   /d1initAll:Mask11
3201>   /d1initAll:FillPattern0
3201>   /d1nodatetime
3201>   /d1trimfile:e:\os\src\=BASEDIR
3201>   /d1trimfile:e:\os\public\amd64fre\=PUBLIC_ROOT
3201>   /d1trimfile:e:\os\obj\amd64fre\=OBJECT_ROOT
3201>   /d1trimfile:e:\os\bin\amd64fre\=_NTTREE
3201>   /d1trimfile:e:\os\osdep\=OSDEPENDSROOT
3201>   /d2AllowCompatibleILVersions
3201>   /d2Zi+
3201>   /ZH:SHA_256
3201>   /wd4986
3201>   /wd4987
3201>   /wd4471
3201>   /wd4369
3201>   /wd4309
3201>   /wd4754
3201>   /wd4427
3201>   /d2DeepThoughtInliner-
3201>   /d2implyavx512upperregs-
3201>   /Wv:19.23
3201>   /Fwe:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\aimxsrv\server\objfre\amd64\
3201>   @e:\os\obj\amd64fre\objfre\amd64\DMF\logged-warnings.rsp
3201>   /wl4002
3201>   /wl4003
3201>   /wl4005
3201>   /wl4006
3201>   /wl4007
3201>   /wl4008
3201>   /wl4010
3201>   /wl4013
3201>   /wl4015
3201>   /wl4018
3201>   /wl4020
3201>   /wl4022
3201>   /wl4024
3201>   /wl4025
3201>   /wl4026
3201>   /wl4027
3201>   /wl4028
3201>   /wl4029
3201>   /wl4030
3201>   /wl4031
3201>   /wl4033
3201>   /wl4034
3201>   /wl4036
3201>   /wl4038
3201>   /wl4041
3201>   /wl4042
3201>   /wl4045
3201>   /wl4047
3201>   /wl4048
3201>   /wl4049
3201>   /wl4056
3201>   /wl4066
3201>   /wl4067
3201>   /wl4068
3201>   /wl4073
3201>   /wl4074
3201>   /wl4075
3201>   /wl4076
3201>   /wl4077
3201>   /wl4079
3201>   /wl4080
3201>   /wl4081
3201>   /wl4083
3201>   /wl4085
3201>   /wl4086
3201>   /wl4087
3201>   /wl4088
3201>   /wl4089
3201>   /wl4090
3201>   /wl4091
3201>   /wl4094
3201>   /wl4096
3201>   /wl4097
3201>   /wl4098
3201>   /wl4099
3201>   /wl4101
3201>   /wl4102
3201>   /wl4109
3201>   /wl4112
3201>   /wl4113
3201>   /wl4114
3201>   /wl4115
3201>   /wl4116
3201>   /wl4117
3201>   /wl4119
3201>   /wl4120
3201>   /wl4122
3201>   /wl4124
3201>   /wl4129
3201>   /wl4133
3201>   /wl4138
3201>   /wl4141
3201>   /wl4142
3201>   /wl4143
3201>   /wl4144
3201>   /wl4145
3201>   /wl4150
3201>   /wl4153
3201>   /wl4154
3201>   /wl4155
3201>   /wl4156
3201>   /wl4157
3201>   /wl4158
3201>   /wl4159
3201>   /wl4160
3201>   /wl4161
3201>   /wl4162
3201>   /wl4163
3201>   /wl4164
3201>   /wl4166
3201>   /wl4167
3201>   /wl4168
3201>   /wl4172
3201>   /wl4174
3201>   /wl4175
3201>   /wl4176
3201>   /wl4177
3201>   /wl4178
3201>   /wl4180
3201>   /wl4182
3201>   /wl4183
3201>   /wl4185
3201>   /wl4186
3201>   /wl4187
3201>   /wl4190
3201>   /wl4192
3201>   /wl4197
3201>   /wl4200
3201>   /wl4213
3201>   /wl4215
3201>   /wl4216
3201>   /wl4218
3201>   /wl4223
3201>   /wl4224
3201>   /wl4226
3201>   /wl4227
3201>   /wl4228
3201>   /wl4229
3201>   /wl4230
3201>   /wl4237
3201>   /wl4240
3201>   /wl4243
3201>   /wl4244
3201>   /wl4250
3201>   /wl4251
3201>   /wl4258
3201>   /wl4267
3201>   /wl4269
3201>   /wl4272
3201>   /wl4273
3201>   /wl4274
3201>   /wl4275
3201>   /wl4276
3201>   /wl4278
3201>   /wl4280
3201>   /wl4281
3201>   /wl4282
3201>   /wl4283
3201>   /wl4285
3201>   /wl4286
3201>   /wl4288
3201>   /wl4290
3201>   /wl4291
3201>   /wl4293
3201>   /wl4297
3201>   /wl4302
3201>   /wl4305
3201>   /wl4306
3201>   /wl4307
3201>   /wl4309
3201>   /wl4310
3201>   /wl4311
3201>   /wl4312
3201>   /wl4313
3201>   /wl4316
3201>   /wl4319
3201>   /wl4325
3201>   /wl4326
3201>   /wl4329
3201>   /wl4333
3201>   /wl4334
3201>   /wl4335
3201>   /wl4340
3201>   /wl4344
3201>   /wl4346
3201>   /wl4348
3201>   /wl4353
3201>   /wl4356
3201>   /wl4357
3201>   /wl4358
3201>   /wl4359
3201>   /wl4364
3201>   /wl4368
3201>   /wl4369
3201>   /wl4373
3201>   /wl4374
3201>   /wl4375
3201>   /wl4376
3201>   /wl4377
3201>   /wl4378
3201>   /wl4379
3201>   /wl4381
3201>   /wl4382
3201>   /wl4383
3201>   /wl4384
3201>   /wl4390
3201>   /wl4391
3201>   /wl4392
3201>   /wl4393
3201>   /wl4394
3201>   /wl4395
3201>   /wl4396
3201>   /wl4397
3201>   /wl4398
3201>   /wl4399
3201>   /wl4600
3201>   /wl4401
3201>   /wl4402
3201>   /wl4403
3201>   /wl4404
3201>   /wl4405
3201>   /wl4406
3201>   /wl4407
3201>   /wl4409
3201>   /wl4410
3201>   /wl4411
3201>   /wl4414
3201>   /wl4420
3201>   /wl4430
3201>   /wl4436
3201>   /wl4439
3201>   /wl4440
3201>   /wl4441
3201>   /wl4445
3201>   /wl4461
3201>   /wl4462
3201>   /wl4470
3201>   /wl4473
3201>   /wl4477
3201>   /wl4484
3201>   /wl4485
3201>   /wl4486
3201>   /wl4488
3201>   /wl4489
3201>   /wl4490
3201>   /wl4502
3201>   /wl4503
3201>   /wl4506
3201>   /wl4508
3201>   /wl4511
3201>   /wl4518
3201>   /wl4521
3201>   /wl4522
3201>   /wl4523
3201>   /wl4526
3201>   /wl4530
3201>   /wl4534
3201>   /wl4535
3201>   /wl4537
3201>   /wl4538
3201>   /wl4540
3201>   /wl4541
3201>   /wl4543
3201>   /wl4544
3201>   /wl4550
3201>   /wl4551
3201>   /wl4552
3201>   /wl4553
3201>   /wl4554
3201>   /wl4556
3201>   /wl4558
3201>   /wl4561
3201>   /wl4566
3201>   /wl4570
3201>   /wl4572
3201>   /wl4580
3201>   /wl4581
3201>   /wl4584
3201>   /wl4596
3201>   /wl4597
3201>   /wl4602
3201>   /wl4603
3201>   /wl4606
3201>   /wl4612
3201>   /wl4613
3201>   /wl4615
3201>   /wl4616
3201>   /wl4618
3201>   /wl4620
3201>   /wl4621
3201>   /wl4622
3201>   /wl4624
3201>   /wl4627
3201>   /wl4630
3201>   /wl4632
3201>   /wl4633
3201>   /wl4635
3201>   /wl4636
3201>   /wl4637
3201>   /wl4638
3201>   /wl4641
3201>   /wl4645
3201>   /wl4646
3201>   /wl4650
3201>   /wl4651
3201>   /wl4652
3201>   /wl4653
3201>   /wl4655
3201>   /wl4656
3201>   /wl4657
3201>   /wl4659
3201>   /wl4661
3201>   /wl4662
3201>   /wl4667
3201>   /wl4669
3201>   /wl4674
3201>   /wl4677
3201>   /wl4678
3201>   /wl4679
3201>   /wl4683
3201>   /wl4684
3201>   /wl4685
3201>   /wl4687
3201>   /wl4688
3201>   /wl4691
3201>   /wl4693
3201>   /wl4694
3201>   /wl4698
3201>   /wl4711
3201>   /wl4715
3201>   /wl4716
3201>   /wl4717
3201>   /wl4722
3201>   /wl4723
3201>   /wl4724
3201>   /wl4727
3201>   /wl4730
3201>   /wl4731
3201>   /wl4733
3201>   /wl4739
3201>   /wl4742
3201>   /wl4743
3201>   /wl4744
3201>   /wl4747
3201>   /wl4750
3201>   /wl4756
3201>   /wl4768
3201>   /wl4772
3201>   /wl4788
3201>   /wl4793
3201>   /wl4794
3201>   /wl4799
3201>   /wl4803
3201>   /wl4804
3201>   /wl4805
3201>   /wl4806
3201>   /wl4807
3201>   /wl4810
3201>   /wl4811
3201>   /wl4812
3201>   /wl4813
3201>   /wl4817
3201>   /wl4819
3201>   /wl4821
3201>   /wl4823
3201>   /wl4829
3201>   /wl4834
3201>   /wl4835
3201>   /wl4838
3201>   /wl4839
3201>   /wl4867
3201>   /wl4900
3201>   /wl4910
3201>   /wl4912
3201>   /wl4920
3201>   /wl4925
3201>   /wl4926
3201>   /wl4927
3201>   /wl4929
3201>   /wl4930
3201>   /wl4935
3201>   /wl4936
3201>   /wl4939
3201>   /wl4944
3201>   /wl4945
3201>   /wl4947
3201>   /wl4948
3201>   /wl4949
3201>   /wl4950
3201>   /wl4951
3201>   /wl4952
3201>   /wl4953
3201>   /wl4956
3201>   /wl4957
3201>   /wl4958
3201>   /wl4959
3201>   /wl4961
3201>   /wl4964
3201>   /wl4965
3201>   /wl4972
3201>   /wl4984
3201>   /wl4995
3201>   /wl4996
3201>   /wl4997
3201>   /wl4999
3201>   /wl5033
3201>   /wl5037
3201>   /wl5046
3201>   /wl5050
3201>   /wl5055
3201>   /wl5056
3201>   /wl5105
3201>   /wl5208
3201>   /d2Qvec-mathlib-
3201>   /d2Qvec-sse2only
3201>   /Gw
3201>   /Zc:checkGwOdr
3201>   /d1ignorePragmaWarningError
3201>   /wd4316
3201>   /wd4973
3201>   /DDONT_DISABLE_PCH_WARNINGS_IN_WARNING_H
3201>   /d2FH4
3201>   /Brepro
3201>   -D_HAS_MAGIC_STATICS=1
3201>   /Qspectre
3201>   /wd5045
3201>   /d2guardspecanalysismode:v1_0
3201>   /d2guardspecmode2
3201>   /guard:cf
3201>   /d2guardcfgfuncptr-
3201>   /d2guardcfgdispatch
3201>   /guard:ehcont
3201>   -D__PLACEHOLDER_SAL=1
3201>   -wd4425
3201>   @e:\os\obj\amd64fre\objfre\amd64\WarningsCop\OneCore.rsp
3201>   /wl4146 /wl4308 /wl4509 /wl4510 /wl4532 /wl4533 /wl4610 /wl4700 /wl4701 /wl4703 /wl4789
3201>   /FIe:\os\public\amd64fre\onecore\internal\sdk\inc\warning.h
3201>   /std:c++17
3201>   /Ylaimxserver
3201>   /Ycpch.hxx
3201>   /Fpe:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\aimxsrv\server\objfre\amd64\pch.pch
3201>   /Fo"e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\aimxsrv\server\objfre\amd64\pch.obj"
3201>pch_hdr.src
3201> e:\os\tools\vc\HostX86\amd64\cl.exe @e:\os\obj\amd64fre\temp\9435b82d6494576420d89fadc5711cb4\cl_1.rsp
3201>Microsoft (R) C/C++ Optimizing Compiler Version 19.42.34444.100 for x64
3201>Copyright (C) Microsoft Corporation.  All rights reserved.
3201>cl /Fo"e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\aimxsrv\server\objfre\amd64/"
3201>   /FC
3201>   /Iamd64
3201>   /I.
3201>   /Ie:\os\src\data\MSRC
3201>   /Ie:\os\public\amd64fre\onecore\external\sdk\inc\atlmfc
3201>   /I..\..\common
3201>   /I..\..\common\nlohmann
3201>   /I..\..\common\httplib
3201>   /I..\..\admcpsrv
3201>   /I..\..\llmclientlib
3201>   /I..\..\agents\ADToolAgent
3201>   /Ie:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\aimxsrv\server
3201>   /Ie:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\aimxsrv\server\objfre\amd64
3201>   /Ie:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\aimxsrv\server\..\idl\objfre\amd64
3201>   /Ie:\os\src\onecore\ds\security\inc
3201>   /Ie:\os\public\amd64fre\onecore\internal\ds\inc
3201>   /Ie:\os\public\amd64fre\onecore\private\ds\inc\security\base
3201>   /Ie:\os\public\amd64fre\onecore\private\base\inc
3201>   /Ie:\os\public\amd64fre\onecore\internal\base\inc
3201>   /Ie:\os\public\amd64fre\OneCore\Internal\MinWin\Priv_Sdk\Inc
3201>   /Ie:\os\public\amd64fre\OneCore\Internal\MinWin\Priv_Sdk\Inc\lsa
3201>   /Ie:\os\public\amd64fre\OneCore\Internal\MinWin\Priv_Sdk\Inc\apiset
3201>   /Ie:\os\public\amd64fre\OneCore\Private\MinWin\Priv_Sdk\Inc
3201>   /Ie:\os\public\amd64fre\OneCore\Private\MinWin\Priv_Sdk\Inc\lsa
3201>   /Ie:\os\public\amd64fre\OneCore\Internal\MinCore\Priv_Sdk\Inc
3201>   /Ie:\os\src\onecore\ds\ds\src\adai\proto\win32\aimxsrv\idl
3201>   /Ie:\os\public\amd64fre\internal\sdk\inc
3201>   /Ie:\os\public\amd64fre\onecore\internal\sdk\inc
3201>   /Ie:\os\public\amd64fre\OneCore\Internal\OneCore\Priv_Sdk\Inc
3201>   /Ie:\os\public\amd64fre\onecore\internal\base\inc\appmodel\runtime\winrt
3201>   /Ie:\os\public\amd64fre\OneCoreUap\Internal\BuildMetadata\internal\cppwinrt
3201>   /Ie:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\cpprestsdk\objfre\amd64\vcpkg_installed\target-windows\include
3201>   /Ie:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\aimxsrv\server\objfre\amd64
3201>   /Ie:\os\src\onecore\ds\inc
3201>   /Ie:\os\obj\amd64fre\onecore\ds\inc\objfre\amd64
3201>   /Ie:\os\public\amd64fre\internal\onecoreds\inc
3201>   /Ie:\os\public\amd64fre\OneCore\Restricted\DS\inc
3201>   /Ie:\os\public\amd64fre\OneCoreUap\Restricted\DS\inc
3201>   /Ie:\os\public\amd64fre\OneCore\External\DS\inc
3201>   /Ie:\os\public\amd64fre\OneCoreUap\External\DS\inc
3201>   /Ie:\os\public\amd64fre\ClientCore\External\DS\inc
3201>   /Ie:\os\public\amd64fre\OneCore\Internal\DS\inc
3201>   /Ie:\os\public\amd64fre\OneCoreUap\Internal\DS\inc
3201>   /Ie:\os\public\amd64fre\ClientCore\Internal\DS\inc
3201>   /Ie:\os\public\amd64fre\OneCore\Private\DS\inc
3201>   /Ie:\os\public\amd64fre\OneCoreUap\Private\DS\inc
3201>   /Ie:\os\public\amd64fre\ClientCore\Private\DS\inc
3201>   /Ie:\os\public\amd64fre\OneCore\external\DS\inc
3201>   /Ie:\os\public\amd64fre\OneCore\restricted\DS\inc
3201>   /Ie:\os\public\amd64fre\OneCore\internal\DS\inc
3201>   /Ie:\os\public\amd64fre\OneCore\private\DS\inc
3201>   /Ie:\os\public\amd64fre\onecore\external\oak\inc
3201>   /Ie:\os\public\amd64fre\onecoreuap\external\oak\inc
3201>   /Ie:\os\public\amd64fre\shared\inc
3201>   /Ie:\os\public\amd64fre\onecore\external\shared\inc
3201>   /Ie:\os\public\amd64fre\onecoreuap\external\shared\inc
3201>   /Ie:\os\public\amd64fre\onecore\external\shared\inc\MinWin
3201>   /Ie:\os\public\amd64fre\sdk\inc
3201>   /Ie:\os\public\amd64fre\onecore\external\sdk\inc
3201>   /Ie:\os\public\amd64fre\onecoreuap\external\sdk\inc
3201>   /Ie:\os\public\amd64fre\onecore\private\sdk\inc\MinWin
3201>   /Ie:\os\public\amd64fre\onecore\internal\sdk\inc\MinWin
3201>   /Ie:\os\public\amd64fre\onecore\external\sdk\inc\MinWin
3201>   /Ie:\os\public\amd64fre\onecore\internal\sdk\inc\MinCore
3201>   /Ie:\os\public\amd64fre\onecore\external\sdk\inc\MinCore
3201>   /Ie:\os\public\amd64fre\onecore\internal\sdk\inc\ClientCore
3201>   /Ie:\os\public\amd64fre\onecore\external\sdk\inc\ClientCore
3201>   /Ie:\os\public\amd64fre\onecore\internal\sdk\inc\ModernCore
3201>   /Ie:\os\public\amd64fre\onecore\external\sdk\inc\ModernCore
3201>   /Ie:\os\public\amd64fre\shared\inc
3201>   /Ie:\os\public\amd64fre\onecore\external\shared\inc
3201>   /Ie:\os\public\amd64fre\onecoreuap\external\shared\inc
3201>   /Ie:\os\public\amd64fre\onecore\external\ddk\inc
3201>   /Ie:\os\public\amd64fre\onecoreuap\external\ddk\inc
3201>   /Ie:\os\public\amd64fre\onecore\external\ddk\inc\wdm
3201>   /Ie:\os\public\amd64fre\onecoreuap\external\ddk\inc\wdm
3201>   /Ie:\os\public\amd64fre\internal\sdk\inc
3201>   /Ie:\os\public\amd64fre\onecore\private\sdk\inc
3201>   /Ie:\os\public\amd64fre\onecoreuap\private\sdk\inc
3201>   /Ie:\os\public\amd64fre\onecore\internal\sdk\inc
3201>   /Ie:\os\public\amd64fre\onecore\restricted\sdk\inc
3201>   /Ie:\os\public\amd64fre\onecoreuap\internal\sdk\inc
3201>   /Ie:\os\public\amd64fre\onecoreuap\restricted\sdk\inc
3201>   /Ie:\os\public\amd64fre\onecore\internal\sdk\inc\MinWin
3201>   /Ie:\os\public\amd64fre\onecore\internal\sdk\inc\MinWin\fs
3201>   /Ie:\os\public\amd64fre\onecore\internal\sdk\inc\MinCore
3201>   /Ie:\os\public\amd64fre\onecore\internal\sdk\inc\ClientCore
3201>   /Ie:\os\public\amd64fre\onecore\internal\sdk\inc\ModernCore
3201>   /Ie:\os\public\amd64fre\OneCore\Internal\hv\hvsdk\just_built\inc\private
3201>   /Ie:\os\public\amd64fre\OneCore\Internal\hv\hvsdk\just_built\inc\internal
3201>   /Ie:\os\public\amd64fre\sdk\inc\ucrt
3201>   /Ie:\os\public\amd64fre\internal\sdk\inc\ucrt
3201>   /Ie:\os\public\amd64fre\onecore\external\sdk\inc\ucrt
3201>   /Ie:\os\public\amd64fre\onecore\internal\sdk\inc\ucrt
3201>   /Ie:\os\public\amd64fre\onecore\internal\sdk\inc\ucrt\stl120
3201>   /Ie:\os\public\amd64fre\onecore\internal\sdk\inc\ucrt\stl120
3201>   /D_WIN64
3201>   /D_AMD64_
3201>   /DAMD64
3201>   /DCONDITION_HANDLING=1
3201>   /DNT_INST=0
3201>   /DWIN32=100
3201>   /D_NT1X_=100
3201>   /DWINNT=1
3201>   /D_WIN32_WINNT=0x0A00
3201>   /DWINVER=0x0A00
3201>   /D_WIN32_IE=0x0A00
3201>   /DWIN32_LEAN_AND_MEAN=1
3201>   /DDEVL=1
3201>   /DNDEBUG
3201>   /D_STL120_
3201>   /D_STL140_
3201>   /D_DLL=1
3201>   /D_MT=1
3201>   -DNT_IUM
3201>   -DWIN32
3201>   -D_WIN32
3201>   -DUNICODE
3201>   -D_UNICODE
3201>   -D_ARM_WINAPI_PARTITION_DESKTOP_SDK_AVAILABLE
3201>   /D_USE_DEV11_CRT
3201>   -D_APISET_MINWIN_VERSION=0x0115
3201>   -D_APISET_MINCORE_VERSION=0x0114
3201>   /DFE_SB
3201>   /DFE_IME
3201>   /DNTDDI_VERSION=0x0A000011
3201>   /DWINBLUE_KBSPRING14
3201>   /DBUILD_WINDOWS
3201>   /DUNDOCKED_WINDOWS_UCRT
3201>   /D__WRL_CONFIGURATION_LEGACY__
3201>   /DBUILD_UMS_ENABLED=1
3201>   /DBUILD_WOW64_ENABLED=1
3201>   /DBUILD_ARM64X_ENABLED=0
3201>   /DEXECUTABLE_WRITES_SUPPORT=0
3201>   -D_USE_DECLSPECS_FOR_SAL=1
3201>   /DRUN_WPP
3201>   -D__PLACEHOLDER_SAL=1
3201>   /D_ATL_STATIC_REGISTRY
3201>   /c
3201>   /Zc:wchar_t-
3201>   /Zl
3201>   /Zp8
3201>   /Gy
3201>   /W4
3201>   /d1import_no_registry
3201>   /EHsc
3201>   /GR-
3201>   /GF
3201>   /GS
3201>   /Z7
3201>   /Oxs
3201>   /GL
3201>   /Z7
3201>   @e:\os\src\tools\NMakeJS\WarningsCop\WarningsCop.Cxx.DefaultErrors.rsp
3201>   /we4308 /we4509 /we4510 /we4532 /we4533 /we4610 /we4700 /we4789
3201>   /w15043
3201>   /Zc:rvalueCast
3201>   /Zo
3201>   -D_UCRT
3201>   -D_CONST_RETURN=
3201>   -D_CRT_SECURE_NO_WARNINGS
3201>   -D_CRT_NON_CONFORMING_SWPRINTFS
3201>   -D_CRT_NONSTDC_NO_WARNINGS
3201>   -D_CRT_STDIO_ARBITRARY_WIDE_SPECIFIERS
3201>   /D_CRT_STDIO_INLINE=extern
3201>   /D_NO_CRT_STDIO_INLINE
3201>   /D_ACRTIMP_ALT=
3201>   /D_SILENCE_STDEXT_HASH_DEPRECATION_WARNINGS
3201>   /D_STL_EXTRA_DISABLED_WARNINGS=4239
3201>   /D_SILENCE_TR1_NAMESPACE_DEPRECATION_WARNING
3201>   /D_SILENCE_ALL_CXX17_DEPRECATION_WARNINGS
3201>   /D_SILENCE_TR2_SYS_NAMESPACE_DEPRECATION_WARNING
3201>   /D_HAS_FUNCTION_ALLOCATOR_SUPPORT=1
3201>   /D_SILENCE_STDEXT_ALLOCATORS_DEPRECATION_WARNING
3201>   /D_HAS_STD_BYTE=0
3201>   /D_ENFORCE_MATCHING_ALLOCATORS=0
3201>   /D_HAS_FUNCTION_ALLOCATOR_SUPPORT=1
3201>   /D_SILENCE_STDEXT_ALLOCATORS_DEPRECATION_WARNING
3201>   /D_FULL_IOBUF
3201>   /d1initAll:Mask11
3201>   /d1initAll:FillPattern0
3201>   /d1nodatetime
3201>   /d1trimfile:e:\os\src\=BASEDIR
3201>   /d1trimfile:e:\os\public\amd64fre\=PUBLIC_ROOT
3201>   /d1trimfile:e:\os\obj\amd64fre\=OBJECT_ROOT
3201>   /d1trimfile:e:\os\bin\amd64fre\=_NTTREE
3201>   /d1trimfile:e:\os\osdep\=OSDEPENDSROOT
3201>   /d2AllowCompatibleILVersions
3201>   /d2Zi+
3201>   /ZH:SHA_256
3201>   /wd4986
3201>   /wd4987
3201>   /wd4471
3201>   /wd4369
3201>   /wd4309
3201>   /wd4754
3201>   /wd4427
3201>   /d2DeepThoughtInliner-
3201>   /d2implyavx512upperregs-
3201>   /Wv:19.23
3201>   /Fwe:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\aimxsrv\server\objfre\amd64\
3201>   @e:\os\obj\amd64fre\objfre\amd64\DMF\logged-warnings.rsp
3201>   /wl4002
3201>   /wl4003
3201>   /wl4005
3201>   /wl4006
3201>   /wl4007
3201>   /wl4008
3201>   /wl4010
3201>   /wl4013
3201>   /wl4015
3201>   /wl4018
3201>   /wl4020
3201>   /wl4022
3201>   /wl4024
3201>   /wl4025
3201>   /wl4026
3201>   /wl4027
3201>   /wl4028
3201>   /wl4029
3201>   /wl4030
3201>   /wl4031
3201>   /wl4033
3201>   /wl4034
3201>   /wl4036
3201>   /wl4038
3201>   /wl4041
3201>   /wl4042
3201>   /wl4045
3201>   /wl4047
3201>   /wl4048
3201>   /wl4049
3201>   /wl4056
3201>   /wl4066
3201>   /wl4067
3201>   /wl4068
3201>   /wl4073
3201>   /wl4074
3201>   /wl4075
3201>   /wl4076
3201>   /wl4077
3201>   /wl4079
3201>   /wl4080
3201>   /wl4081
3201>   /wl4083
3201>   /wl4085
3201>   /wl4086
3201>   /wl4087
3201>   /wl4088
3201>   /wl4089
3201>   /wl4090
3201>   /wl4091
3201>   /wl4094
3201>   /wl4096
3201>   /wl4097
3201>   /wl4098
3201>   /wl4099
3201>   /wl4101
3201>   /wl4102
3201>   /wl4109
3201>   /wl4112
3201>   /wl4113
3201>   /wl4114
3201>   /wl4115
3201>   /wl4116
3201>   /wl4117
3201>   /wl4119
3201>   /wl4120
3201>   /wl4122
3201>   /wl4124
3201>   /wl4129
3201>   /wl4133
3201>   /wl4138
3201>   /wl4141
3201>   /wl4142
3201>   /wl4143
3201>   /wl4144
3201>   /wl4145
3201>   /wl4150
3201>   /wl4153
3201>   /wl4154
3201>   /wl4155
3201>   /wl4156
3201>   /wl4157
3201>   /wl4158
3201>   /wl4159
3201>   /wl4160
3201>   /wl4161
3201>   /wl4162
3201>   /wl4163
3201>   /wl4164
3201>   /wl4166
3201>   /wl4167
3201>   /wl4168
3201>   /wl4172
3201>   /wl4174
3201>   /wl4175
3201>   /wl4176
3201>   /wl4177
3201>   /wl4178
3201>   /wl4180
3201>   /wl4182
3201>   /wl4183
3201>   /wl4185
3201>   /wl4186
3201>   /wl4187
3201>   /wl4190
3201>   /wl4192
3201>   /wl4197
3201>   /wl4200
3201>   /wl4213
3201>   /wl4215
3201>   /wl4216
3201>   /wl4218
3201>   /wl4223
3201>   /wl4224
3201>   /wl4226
3201>   /wl4227
3201>   /wl4228
3201>   /wl4229
3201>   /wl4230
3201>   /wl4237
3201>   /wl4240
3201>   /wl4243
3201>   /wl4244
3201>   /wl4250
3201>   /wl4251
3201>   /wl4258
3201>   /wl4267
3201>   /wl4269
3201>   /wl4272
3201>   /wl4273
3201>   /wl4274
3201>   /wl4275
3201>   /wl4276
3201>   /wl4278
3201>   /wl4280
3201>   /wl4281
3201>   /wl4282
3201>   /wl4283
3201>   /wl4285
3201>   /wl4286
3201>   /wl4288
3201>   /wl4290
3201>   /wl4291
3201>   /wl4293
3201>   /wl4297
3201>   /wl4302
3201>   /wl4305
3201>   /wl4306
3201>   /wl4307
3201>   /wl4309
3201>   /wl4310
3201>   /wl4311
3201>   /wl4312
3201>   /wl4313
3201>   /wl4316
3201>   /wl4319
3201>   /wl4325
3201>   /wl4326
3201>   /wl4329
3201>   /wl4333
3201>   /wl4334
3201>   /wl4335
3201>   /wl4340
3201>   /wl4344
3201>   /wl4346
3201>   /wl4348
3201>   /wl4353
3201>   /wl4356
3201>   /wl4357
3201>   /wl4358
3201>   /wl4359
3201>   /wl4364
3201>   /wl4368
3201>   /wl4369
3201>   /wl4373
3201>   /wl4374
3201>   /wl4375
3201>   /wl4376
3201>   /wl4377
3201>   /wl4378
3201>   /wl4379
3201>   /wl4381
3201>   /wl4382
3201>   /wl4383
3201>   /wl4384
3201>   /wl4390
3201>   /wl4391
3201>   /wl4392
3201>   /wl4393
3201>   /wl4394
3201>   /wl4395
3201>   /wl4396
3201>   /wl4397
3201>   /wl4398
3201>   /wl4399
3201>   /wl4600
3201>   /wl4401
3201>   /wl4402
3201>   /wl4403
3201>   /wl4404
3201>   /wl4405
3201>   /wl4406
3201>   /wl4407
3201>   /wl4409
3201>   /wl4410
3201>   /wl4411
3201>   /wl4414
3201>   /wl4420
3201>   /wl4430
3201>   /wl4436
3201>   /wl4439
3201>   /wl4440
3201>   /wl4441
3201>   /wl4445
3201>   /wl4461
3201>   /wl4462
3201>   /wl4470
3201>   /wl4473
3201>   /wl4477
3201>   /wl4484
3201>   /wl4485
3201>   /wl4486
3201>   /wl4488
3201>   /wl4489
3201>   /wl4490
3201>   /wl4502
3201>   /wl4503
3201>   /wl4506
3201>   /wl4508
3201>   /wl4511
3201>   /wl4518
3201>   /wl4521
3201>   /wl4522
3201>   /wl4523
3201>   /wl4526
3201>   /wl4530
3201>   /wl4534
3201>   /wl4535
3201>   /wl4537
3201>   /wl4538
3201>   /wl4540
3201>   /wl4541
3201>   /wl4543
3201>   /wl4544
3201>   /wl4550
3201>   /wl4551
3201>   /wl4552
3201>   /wl4553
3201>   /wl4554
3201>   /wl4556
3201>   /wl4558
3201>   /wl4561
3201>   /wl4566
3201>   /wl4570
3201>   /wl4572
3201>   /wl4580
3201>   /wl4581
3201>   /wl4584
3201>   /wl4596
3201>   /wl4597
3201>   /wl4602
3201>   /wl4603
3201>   /wl4606
3201>   /wl4612
3201>   /wl4613
3201>   /wl4615
3201>   /wl4616
3201>   /wl4618
3201>   /wl4620
3201>   /wl4621
3201>   /wl4622
3201>   /wl4624
3201>   /wl4627
3201>   /wl4630
3201>   /wl4632
3201>   /wl4633
3201>   /wl4635
3201>   /wl4636
3201>   /wl4637
3201>   /wl4638
3201>   /wl4641
3201>   /wl4645
3201>   /wl4646
3201>   /wl4650
3201>   /wl4651
3201>   /wl4652
3201>   /wl4653
3201>   /wl4655
3201>   /wl4656
3201>   /wl4657
3201>   /wl4659
3201>   /wl4661
3201>   /wl4662
3201>   /wl4667
3201>   /wl4669
3201>   /wl4674
3201>   /wl4677
3201>   /wl4678
3201>   /wl4679
3201>   /wl4683
3201>   /wl4684
3201>   /wl4685
3201>   /wl4687
3201>   /wl4688
3201>   /wl4691
3201>   /wl4693
3201>   /wl4694
3201>   /wl4698
3201>   /wl4711
3201>   /wl4715
3201>   /wl4716
3201>   /wl4717
3201>   /wl4722
3201>   /wl4723
3201>   /wl4724
3201>   /wl4727
3201>   /wl4730
3201>   /wl4731
3201>   /wl4733
3201>   /wl4739
3201>   /wl4742
3201>   /wl4743
3201>   /wl4744
3201>   /wl4747
3201>   /wl4750
3201>   /wl4756
3201>   /wl4768
3201>   /wl4772
3201>   /wl4788
3201>   /wl4793
3201>   /wl4794
3201>   /wl4799
3201>   /wl4803
3201>   /wl4804
3201>   /wl4805
3201>   /wl4806
3201>   /wl4807
3201>   /wl4810
3201>   /wl4811
3201>   /wl4812
3201>   /wl4813
3201>   /wl4817
3201>   /wl4819
3201>   /wl4821
3201>   /wl4823
3201>   /wl4829
3201>   /wl4834
3201>   /wl4835
3201>   /wl4838
3201>   /wl4839
3201>   /wl4867
3201>   /wl4900
3201>   /wl4910
3201>   /wl4912
3201>   /wl4920
3201>   /wl4925
3201>   /wl4926
3201>   /wl4927
3201>   /wl4929
3201>   /wl4930
3201>   /wl4935
3201>   /wl4936
3201>   /wl4939
3201>   /wl4944
3201>   /wl4945
3201>   /wl4947
3201>   /wl4948
3201>   /wl4949
3201>   /wl4950
3201>   /wl4951
3201>   /wl4952
3201>   /wl4953
3201>   /wl4956
3201>   /wl4957
3201>   /wl4958
3201>   /wl4959
3201>   /wl4961
3201>   /wl4964
3201>   /wl4965
3201>   /wl4972
3201>   /wl4984
3201>   /wl4995
3201>   /wl4996
3201>   /wl4997
3201>   /wl4999
3201>   /wl5033
3201>   /wl5037
3201>   /wl5046
3201>   /wl5050
3201>   /wl5055
3201>   /wl5056
3201>   /wl5105
3201>   /wl5208
3201>   /d2Qvec-mathlib-
3201>   /d2Qvec-sse2only
3201>   /Gw
3201>   /Zc:checkGwOdr
3201>   /d1ignorePragmaWarningError
3201>   /wd4316
3201>   /wd4973
3201>   /DDONT_DISABLE_PCH_WARNINGS_IN_WARNING_H
3201>   /d2FH4
3201>   /Brepro
3201>   -D_HAS_MAGIC_STATICS=1
3201>   /Qspectre
3201>   /wd5045
3201>   /d2guardspecanalysismode:v1_0
3201>   /d2guardspecmode2
3201>   /guard:cf
3201>   /d2guardcfgfuncptr-
3201>   /d2guardcfgdispatch
3201>   /guard:ehcont
3201>   -D__PLACEHOLDER_SAL=1
3201>   -wd4425
3201>   @e:\os\obj\amd64fre\objfre\amd64\WarningsCop\OneCore.rsp
3201>   /wl4146 /wl4308 /wl4509 /wl4510 /wl4532 /wl4533 /wl4610 /wl4700 /wl4701 /wl4703 /wl4789
3201>   /FIe:\os\public\amd64fre\onecore\internal\sdk\inc\warning.h
3201>   /std:c++17
3201>   /Yupch.hxx /Fpe:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\aimxsrv\server\objfre\amd64\pch.pch
3201>   .\aimxrpcserver.cpp .\requesthandler.cpp .\planner.cpp .\orchestrator.cpp .\mcpstdioclient.cpp .\mcpsvrmgr.cpp .\mcptoolmanager.cpp .\llminfer.cpp .\aimxllmconfig.cpp .\inprocessmcpserverbase.cpp .\inprocessmcputils.cpp .\systempromptmanager.cpp .\conversationmanager.cpp 
3201>aimxrpcserver.cpp
3201>requesthandler.cpp
3201>planner.cpp
3201>orchestrator.cpp
3201>mcpstdioclient.cpp
3201>mcpsvrmgr.cpp
3201>mcptoolmanager.cpp
3201>llminfer.cpp
3201>aimxllmconfig.cpp
3201>inprocessmcpserverbase.cpp
3201>inprocessmcputils.cpp
3201>systempromptmanager.cpp
3201>conversationmanager.cpp
3201> e:\os\tools\vc\HostX64\amd64\link.exe /lib /out:e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\aimxsrv\server\objfre\amd64\aimxserver.lib /IGNORE:4078,4221,4281,4006,4198   /nodefaultlib /machine:amd64 /ltcg /Brepro @e:\os\obj\amd64fre\temp\9435b82d6494576420d89fadc5711cb4\lib_1.rsp
3201>Microsoft (R) Library Manager Version 14.42.34444.100
3201>Copyright (C) Microsoft Corporation.  All rights reserved.
3201>e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\aimxsrv\server\objfre\amd64\pch.obj 
3201>e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\aimxsrv\server\objfre\amd64\aimxrpcserver.obj 
3201>e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\aimxsrv\server\objfre\amd64\requesthandler.obj 
3201>e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\aimxsrv\server\objfre\amd64\planner.obj 
3201>e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\aimxsrv\server\objfre\amd64\orchestrator.obj 
3201>e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\aimxsrv\server\objfre\amd64\mcpstdioclient.obj 
3201>e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\aimxsrv\server\objfre\amd64\mcpsvrmgr.obj 
3201>e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\aimxsrv\server\objfre\amd64\mcptoolmanager.obj 
3201>e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\aimxsrv\server\objfre\amd64\llminfer.obj 
3201>e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\aimxsrv\server\objfre\amd64\aimxllmconfig.obj 
3201>e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\aimxsrv\server\objfre\amd64\inprocessmcpserverbase.obj 
3201>e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\aimxsrv\server\objfre\amd64\inprocessmcputils.obj 
3201>e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\aimxsrv\server\objfre\amd64\systempromptmanager.obj 
3201>e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\aimxsrv\server\objfre\amd64\conversationmanager.obj 
3201>Writing out macros...e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\aimxsrv\server\objfre\amd64\Macros-PASS1.txt
3201>binplace e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\aimxsrv\server\objfre\amd64\Macros-PASS1.txt
3201> e:\os\tools\powershell\pwsh.exe -NoProfile e:\os\src\tools\NMakeJS\WarningsCop\WarningsCop.ps1 -MakeSubDir "onecore\ds\ds\src\aimx\prod\aimxsrv\server" -Pass PASS1 -BaselineFile e:\os\src\.config\OneCore\WarningsCop.json -OutputDir "e:\os\bin\amd64fre\evidence\WarningsCop\OneCore\onecoreds"
3201>WarningsCop.ps1 : WarningsCop: Processing onecore\ds\ds\src\aimx\prod\aimxsrv\server in pass PASS1
BUILD: Pass complete => PASS1
1>  1>[0:01:38.547] [Pass2 ] e:\os\src\onecore\ds\ds\src\aimx\prod\aimxsrv\client\dll {37}
3001>BUILDMSG: Processing e:\os\src\onecore\ds\ds\src\aimx\prod\aimxsrv\client\dll
3001>Linking for e:\os\src\onecore\ds\ds\src\aimx\prod\aimxsrv\client\dll *************
3001>'e:\os\tools\NMakeRust\x64\bin\nmake.exe /nologo BUILDMSG=Stop. /nologo /f e:\os\src\tools\makefile.def BUILD_PASS=PASS2 LINKONLY=1 NOPASS0=1 MAKEDLL=1 MAKEDIR_RELATIVE_TO_BASEDIR=onecore\ds\ds\src\aimx\prod\aimxsrv\client\dll TARGET_PLATFORM=windows CODE_SETS_BUILDING=cs_windows,cs_xbox,cs_phone CODE_SETS_TAGGED=cs_windows,cs_xbox'
1>  2>[0:01:38.547] [Pass2 ] e:\os\src\onecore\ds\ds\src\aimx\prod\aimxsrv\powershell {38}
3002>BUILDMSG: Processing e:\os\src\onecore\ds\ds\src\aimx\prod\aimxsrv\powershell
3002>Linking for e:\os\src\onecore\ds\ds\src\aimx\prod\aimxsrv\powershell *************
3002>'e:\os\tools\NMakeRust\x64\bin\nmake.exe /nologo BUILDMSG=Stop. /nologo /f e:\os\src\tools\makefile.def BUILD_PASS=PASS2 LINKONLY=1 NOPASS0=1 MAKEDLL=1 MAKEDIR_RELATIVE_TO_BASEDIR=onecore\ds\ds\src\aimx\prod\aimxsrv\powershell TARGET_PLATFORM=windows CODE_SETS_BUILDING=cs_windows,cs_xbox,cs_phone CODE_SETS_TAGGED=cs_windows,cs_xbox'
1>  3>[0:01:38.547] [Pass2 ] e:\os\src\onecore\ds\ds\src\aimx\prod\aimxsrv\serviceinstaller {39}
3003>BUILDMSG: Processing e:\os\src\onecore\ds\ds\src\aimx\prod\aimxsrv\serviceinstaller
3003>Linking for e:\os\src\onecore\ds\ds\src\aimx\prod\aimxsrv\serviceinstaller *************
3003>'e:\os\tools\NMakeRust\x64\bin\nmake.exe /nologo BUILDMSG=Stop. /nologo /f e:\os\src\tools\makefile.def BUILD_PASS=PASS2 LINKONLY=1 NOPASS0=1 MAKEDIR_RELATIVE_TO_BASEDIR=onecore\ds\ds\src\aimx\prod\aimxsrv\serviceinstaller TARGET_PLATFORM=windows CODE_SETS_BUILDING=cs_windows,cs_xbox,cs_phone CODE_SETS_TAGGED=cs_windows,cs_xbox'
3004>BUILDMSG: Processing e:\os\src\onecore\ds\ds\src\aimx\prod\aimxsrv\dll
3004>Linking for e:\os\src\onecore\ds\ds\src\aimx\prod\aimxsrv\dll *************
3004>'e:\os\tools\NMakeRust\x64\bin\nmake.exe /nologo BUILDMSG=Stop. /nologo /f e:\os\src\tools\makefile.def BUILD_PASS=PASS2 LINKONLY=1 NOPASS0=1 MAKEDLL=1 MAKEDIR_RELATIVE_TO_BASEDIR=onecore\ds\ds\src\aimx\prod\aimxsrv\dll TARGET_PLATFORM=windows CODE_SETS_BUILDING=cs_windows,cs_xbox,cs_phone CODE_SETS_TAGGED=cs_windows,cs_xbox'
1>  4>[0:01:38.547] [Pass2 ] e:\os\src\onecore\ds\ds\src\aimx\prod\aimxsrv\dll {40}
BUILD: (ActiveWorkLoad),98.31,,56,0,3,14,16,0,0,0,0,0,0,0,PASS2,0,2000000000,onecore\ds\ds\src\aimx\prod\aimxsrv\client\dll,RUPO-DELL
BUILD: (ActiveWorkLoad),98.31,,56,0,3,14,16,16,0,1,1,1,0,0,PASS2,0,2000000000,onecore\ds\ds\src\aimx\prod\aimxsrv\powershell,RUPO-DELL
BUILD: (ActiveWorkLoad),98.31,,56,0,3,14,16,16,0,2,2,2,0,0,PASS2,0,2000000000,onecore\ds\ds\src\aimx\prod\aimxsrv\serviceinstaller,RUPO-DELL
BUILD: (ActiveWorkLoad),98.31,,56,0,3,14,16,16,0,3,3,3,0,0,PASS2,0,2000000000,onecore\ds\ds\src\aimx\prod\aimxsrv\dll,RUPO-DELL
3002>Calculated LAYERINFO_MODULE='OneCoreDS'.
3002>makefile.def: TEMP=e:\os\obj\amd64fre\temp\e8ef7b1ecaf25e98aec0a48354cc7bb3
3002>makefile.def: BUILDINGINDATT=
3002>[Core OS Undocking] NOT using package ''
3002>ObjectsMac.ts: validation succeeded
3002>Starting recursive call to NMAKE for _MAKING_ASMID_INC
3002>Starting _MAKING_ASMID_INC
3002>Calculated LAYERINFO_MODULE='OneCoreDS'.
3002>makefile.def: TEMP=e:\os\obj\amd64fre\temp\e8ef7b1ecaf25e98aec0a48354cc7bb3
3002>makefile.def: BUILDINGINDATT=
3002>[Core OS Undocking] NOT using package ''
3002>ObjectsMac.ts: _objects.mac is not needed; macro validation will be skipped.
3002>BUILDMSG: Checking if we need to generate coffbase.mac file
3002>_NEED_BUILDDATE not defined setting BUILDDATE to an invalid value.
3002>BUILDMSG: Optional SPD_INPUT location is e:\os\pgo\amd64fre\spds\\\aimxpsh.dll.spd
3002>OSSCop: checkBaselines: reading privateOssData from e:\os\src\tools\analysis\LastKnownState\PrivateOSSData.json and publication baseline from e:\os\src\tools\analysis\LastKnownState\OSSCop_publication_baseline.json and consumption baseline from e:\os\src\.config\OneCore\build\..\OSSCop_baseline.json
3002>OSSCop: getOSSBaselines: Parsing baselines looking for macroDefFiles [tools\makefile.def]
3002>Ending _MAKING_ASMID_INC
3002>'e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\aimxsrv\powershell\objfre\amd64\_asmid.inc' is up-to-date
3002>Finished recursively calling NMAKE for _MAKING_ASMID_INC
3002>BUILDMSG: Checking if we need to generate coffbase.mac file
3002>_NEED_BUILDDATE not defined setting BUILDDATE to an invalid value.
3002>BUILDMSG: Optional SPD_INPUT location is e:\os\pgo\amd64fre\spds\\\aimxpsh.dll.spd
3002>OSSCop: checkBaselines: reading privateOssData from e:\os\src\tools\analysis\LastKnownState\PrivateOSSData.json and publication baseline from e:\os\src\tools\analysis\LastKnownState\OSSCop_publication_baseline.json and consumption baseline from e:\os\src\.config\OneCore\build\..\OSSCop_baseline.json
3002>OSSCop: getOSSBaselines: Parsing baselines looking for macroDefFiles [tools\makefile.def]
3002>A subdirectory or file e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\aimxsrv\powershell\objfre\amd64 already exists.
3002> e:\os\tools\Windows.Desktop.Tools.amd64\tools\touch.exe /c e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\aimxsrv\powershell\objfre\amd64\_PASS2_Marker.log
3002> e:\os\osdep\feature.toggles\tools\ft_scraper.exe -pdb:e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\aimxsrv\powershell\objfre\amd64\aimxpsh.pdb -out:e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\aimxsrv\powershell\objfre\amd64\ft_scraped_aimxpsh.ft -binplace_path:bvtbin\feature_toggles\scraped\OneCore\onecore\ds\ds\src\aimx\prod\aimxsrv\powershell
3002>binplace e:\os\src\onecore\ds\ds\src\aimx\prod\aimxsrv\powershell\AIMX.psd1
3002> c:\windows\system32\cmd.exe /c if exist e:\os\obj\amd64fre\temp\e8ef7b1ecaf25e98aec0a48354cc7bb3\post_link_concurrent.rsp e:\os\tools\Windows.Desktop.Tools\tools\contool.exe @e:\os\obj\amd64fre\temp\e8ef7b1ecaf25e98aec0a48354cc7bb3\post_link_concurrent.rsp
3002> c:\windows\system32\cmd.exe /c if exist e:\os\obj\amd64fre\temp\e8ef7b1ecaf25e98aec0a48354cc7bb3\post_link_concurrent2.rsp e:\os\tools\Windows.Desktop.Tools\tools\contool.exe @e:\os\obj\amd64fre\temp\e8ef7b1ecaf25e98aec0a48354cc7bb3\post_link_concurrent2.rsp
3002>Writing out macros...e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\aimxsrv\powershell\objfre\amd64\Macros-PASS2.txt
3002>binplace e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\aimxsrv\powershell\objfre\amd64\Macros-PASS2.txt
3002> e:\os\tools\powershell\pwsh.exe -NoProfile -Command e:\os\src\tools\NMakeJS\CheckCFlags\CheckCFlags.ps1 -MakeSubDir "onecore\ds\ds\src\aimx\prod\aimxsrv\powershell" -Pass PASS2 -BaselineFile e:\os\src\.config\OneCore\CheckCFlags.json -OutputDir "e:\os\bin\amd64fre\build_logs\DMFDebt\build_logs\CheckCFlags\OneCore\onecoreds"
3002>CheckCFlags.ps1 : Processing onecore\ds\ds\src\aimx\prod\aimxsrv\powershell in pass PASS2
3002>CheckCFlags.ps1 : Running e:\os\src\tools\urtrun64.cmd 4.Latest e:\os\tools\checkcflags\cs\checkcflags2.exe Scan-List /ListFile:e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\aimxsrv\powershell\objfre\amd64\CheckCFlags_scanlist.txt /PolicyConfigFilePath:E:\os\src\tools\NMakeJS\CheckCFlags\CCF_PolicyConfig.json /LogDirectory:e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\aimxsrv\powershell\objfre\amd64
3002> e:\os\tools\powershell\pwsh.exe -NoProfile e:\os\src\tools\NMakeJS\WarningsCop\WarningsCop.ps1 -MakeSubDir "onecore\ds\ds\src\aimx\prod\aimxsrv\powershell" -Pass PASS2 -BaselineFile e:\os\src\.config\OneCore\WarningsCop.json -OutputDir "e:\os\bin\amd64fre\evidence\WarningsCop\OneCore\onecoreds"
3002>WarningsCop.ps1 : WarningsCop: Processing onecore\ds\ds\src\aimx\prod\aimxsrv\powershell in pass PASS2
3001>Calculated LAYERINFO_MODULE='OneCoreDS'.
3001>makefile.def: TEMP=e:\os\obj\amd64fre\temp\f1855e530ce0c7d4d7f15f633265d075
3001>makefile.def: BUILDINGINDATT=
3001>[Core OS Undocking] NOT using package ''
3001>UCRT enabled: dir 'e:\os\src\onecore\ds\ds\src\aimx\prod\aimxsrv\client\dll' (target 'aimxclient', type 'DYNLINK', nt_target_version '0xA000011')
3001>ObjectsMac.ts: validation succeeded
3001>STL version 120 used in "e:\os\src\onecore\ds\ds\src\aimx\prod\aimxsrv\client\dll" (STL_VER_TELEMETRY)
3001>_NEED_BUILDDATE not defined setting BUILDDATE to an invalid value.
3001>BUILDMSG: Optional SPD_INPUT location is e:\os\pgo\amd64fre\spds\\\aimxclient.dll.spd
3001>OSSCop: checkBaselines: reading privateOssData from e:\os\src\tools\analysis\LastKnownState\PrivateOSSData.json and publication baseline from e:\os\src\tools\analysis\LastKnownState\OSSCop_publication_baseline.json and consumption baseline from e:\os\src\.config\OneCore\build\..\OSSCop_baseline.json
3001>OSSCop: getOSSBaselines: Parsing baselines looking for macroDefFiles [onecore\ds\ds\src\aimx\prod\aimxsrv\client\dll\sources, tools\makefile.ucrt, tools\makefile.def]
3001>no publication found; not an error.
3001>no publication found; not an error.
3001>no publication found; not an error.
3001>no publication found; not an error.
3001>no publication found; not an error.
3001>no publication found; not an error.
3001>A subdirectory or file e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\aimxsrv\client\dll\objfre\amd64 already exists.
3001> e:\os\tools\Windows.Desktop.Tools.amd64\tools\touch.exe /c e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\aimxsrv\client\dll\objfre\amd64\_PASS2_Marker.log
3001> e:\os\tools\vc\HostX64\amd64\link.exe /out:e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\aimxsrv\client\dll\objfre\amd64\aimxclient.dll  /machine:amd64 @e:\os\obj\amd64fre\temp\f1855e530ce0c7d4d7f15f633265d075\lnk.rsp
3001>Microsoft (R) Incremental Linker Version 14.42.34444.100
3001>Copyright (C) Microsoft Corporation.  All rights reserved.
3001>/filealign:0x1000 
3001>/INCLUDE:__PLEASE_LINK_WITH_legacy_stdio_wide_specifiers.lib 
3001>/INCLUDE:__scrt_stdio_legacy_msvcrt_compatibility 
3001>/NOVCFEATURE 
3001>/d2:-DeepThoughtInliner- 
3001>/d2:-DisableWPASpecializeParam 
3001>/d2:-implyavx512upperregs- 
3001>/RunBelow4GB 
3001>/nopdbprefetch 
3001>-d2:-TypeProp- 
3001>-d2:-SpecDevirt- 
3001>/RetryOnFileOpenFailure 
3001>e:\os\public\amd64fre\OneCore\Internal\MinWin\Priv_Sdk\Lib\hotpatchspareglobals.obj 
3001>/map 
3001>/pdbinject:mapfile 
3001>/baserelocclustering 
3001>/SPGO 
3001>/SPD:e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\aimxsrv\client\dll\objfre\amd64\aimxclient.dll.spd 
3001>-ignore:4199 
3001>/MERGE:_PAGE=PAGE 
3001>/MERGE:_TEXT=.text 
3001>/MERGE:_RDATA=.rdata 
3001>/OPT:REF 
3001>/OPT:ICF 
3001>/IGNORE:4078,4221,4281,4006,4198 
3001>/INCREMENTAL:NO 
3001>/release 
3001>/NODEFAULTLIB 
3001>/debug 
3001>/debugtype:cv,fixup,pdata 
3001>/version:10.0 
3001>/osversion:10.0 
3001>/ltcg 
3001>/funcoverride 
3001>/d2:-FH4 
3001>/Brepro 
3001>/PDBDLL:mspdbcore.dll 
3001>/functionpadmin:6 
3001>/MERGE:.orpc=.text 
3001>/hotpatchcompatible 
3001>/d2:-guardspecload 
3001>/d2:-guardspecanalysismode:v1_0 
3001>/d2:-guardspecmode2 
3001>/ignore:4291 
3001>/DynamicValueFixupSym:mm_shared_user_data_va=0x7FFE0000 
3001>/DynamicValueFixupSym:ki_user_shared_data=0xFFFFF78000000000 
3001>/guard:cf 
3001>/d2:-guardcfgfuncptr- 
3001>/merge:.gfids=.rdata 
3001>/d2:-guardcfgdispatch 
3001>/guard:ehcont 
3001>/CETCOMPAT 
3001>/pdbcompress 
3001>/STACK:0x40000,0x1000 
3001>/dll 
3001>/subsystem:console,10.00 
3001>/entry:_DllMainCRTStartup 
3001>e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\aimxsrv\client\dll\objfre\amd64\pch.obj 
3001>e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\aimxsrv\client\dll\objfre\amd64\aimxclient.exp 
3001>e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\aimxsrv\client\dll\objfre\amd64\aimxclient.obj 
3001>e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\aimxsrv\client\dll\objfre\amd64\aimxrpcclient.obj 
3001>e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\aimxsrv\client\dll\objfre\amd64\memory.obj 
3001>e:\os\public\amd64fre\onecore\external\sdk\lib\atls.lib 
3001>e:\os\public\amd64fre\onecore\external\sdk\lib\atlthunk.lib 
3001>e:\os\public\amd64fre\onecore\internal\sdk\lib\ucrt\msvcprt.lib 
3001>e:\os\public\amd64fre\onecore\internal\sdk\lib\ucrt\legacy_stdio_wide_specifiers.lib 
3001>e:\os\public\amd64fre\onecore\internal\sdk\lib\ucrt\legacy_stdio_msvcrt_compatibility.lib 
3001>e:\os\public\amd64fre\onecore\internal\sdk\lib\ucrt\msvcrt.lib 
3001>e:\os\public\amd64fre\onecore\internal\sdk\lib\ucrt\osmode_function_map.obj 
3001>e:\os\public\amd64fre\onecore\external\sdk\lib\ucrt.lib 
3001>e:\os\public\amd64fre\onecore\internal\sdk\lib\ucrt_private.lib 
3001>e:\os\public\amd64fre\onecore\internal\sdk\lib\ucrt\legacy_stdio_definitions.lib 
3001>e:\os\public\amd64fre\onecore\external\sdk\lib\MinWin\ntdll.lib 
3001>e:\os\public\amd64fre\onecore\external\sdk\lib\MinWin\rpcrt4.lib 
3001>e:\os\public\amd64fre\onecore\external\sdk\lib\onecore.lib 
3001>e:\os\public\amd64fre\onecore\internal\sdk\lib\MinWin\1.21\api-ms-win-security-sddl-l1.lib 
3001>e:\os\public\amd64fre\onecore\internal\sdk\lib\MinWin\1.21\api-ms-win-security-base-l1.lib 
3001>e:\os\public\amd64fre\onecore\internal\sdk\lib\guard_support.lib 
3001>/pdbrpc:no 
3001>Generating code
3001>SPD e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\aimxsrv\client\dll\objfre\amd64\aimxclient.dll.spd not found, compiling without profile guided optimizations
3001>Finished generating code
3001> c:\windows\system32\cmd.exe /c del e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\aimxsrv\client\dll\objfre\amd64\aimxclient.ilk.persistent e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\aimxsrv\client\dll\objfre\amd64\aimxclient.dll.persistent e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\aimxsrv\client\dll\objfre\amd64\aimxclient.pdb.persistent 2>nul
3001> c:\windows\system32\cmd.exe /c e:\os\src\tools\urtrun.cmd 4.Latest e:\os\tools\FixTSVersionStringAppend\bin\FixTsVersionStringAppend\release\FixTSVersionStringAppend.exe /fts:e:\os\tools\FixTS\FixTS.exe /pe=e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\aimxsrv\client\dll\objfre\amd64\aimxclient.dll
3001>[e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\aimxsrv\client\dll\objfre\amd64\aimxclient.dll]
3001>No version resources present.
3001>Microsoft (R) COFF/PE Editor Version 14.42.34444.100
3001>Copyright (C) Microsoft Corporation.  All rights reserved.
3001>Updating PDB GUID and/or timestamp in e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\aimxsrv\client\dll\objfre\amd64\aimxclient.dll
3001>Updating PDB GUID and/or timestamp in e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\aimxsrv\client\dll\objfre\amd64\aimxclient.pdb
3001>Original RSDSKEY:{6B484F6E-4EE0-F524-6845-B3A8D2E9EC8A}:1
3001>New RSDSKEY:     {6B484F6E-4EE0-F524-6845-B3A8D2E9EC8A}:1
3001> e:\os\tools\perl\bin\perl.exe e:\os\src\tools\FeatureStaging\LogInliningFailures.pl /makedir:onecore\ds\ds\src\aimx\prod\aimxsrv\client\dll e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\aimxsrv\client\dll\objfre\amd64\aimxclient.dll
3001> e:\os\tools\deferredbinplace\DeferredBinplace.exe  e:\os\src\onecore\ds\ds\src\aimx\prod\aimxsrv\client\dll  PASS2  e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\aimxsrv\client\dll\objfre\amd64\binplace_PASS2.rsp  @e:\os\tools\binplace\binplace.exe  /R e:\os\bin\amd64fre\.  /s e:\os\bin\amd64fre\Symbols.pri\. /j /:DBG /:NOCV  -f -:LOGPDB /:CVTCIL /:SYMBAD e:\os\src\tools\symbad.txt /:TMF   /:DEST retail      e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\aimxsrv\client\dll\objfre\amd64\aimxclient.dll
3001>binplace e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\aimxsrv\client\dll\objfre\amd64\aimxclient.dll
3001>BINPLACE : INFORMATION BNP0017: Can't detect version resource in e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\aimxsrv\client\dll\objfre\amd64\aimxclient.dll. 0x000000B7 - Cannot create a file when that file already exists.
3001> c:\windows\system32\cmd.exe /c if not exist e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\aimxsrv\client\dll\objfre\amd64\aimxclient.dll.mui (  echo Build_Status  LN_MUI_STS: LGNSTS_UNKNOWN aimxclient.dll  )
3001>Build_Status  LN_MUI_STS: LGNSTS_UNKNOWN aimxclient.dll  
3001> e:\os\osdep\feature.toggles\tools\ft_scraper.exe -pdb:e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\aimxsrv\client\dll\objfre\amd64\aimxclient.pdb -out:e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\aimxsrv\client\dll\objfre\amd64\ft_scraped_aimxclient.ft -binplace_path:bvtbin\feature_toggles\scraped\OneCore\onecore\ds\ds\src\aimx\prod\aimxsrv\client\dll
3001> c:\windows\system32\cmd.exe /c if exist e:\os\obj\amd64fre\temp\f1855e530ce0c7d4d7f15f633265d075\post_link_concurrent.rsp e:\os\tools\Windows.Desktop.Tools\tools\contool.exe @e:\os\obj\amd64fre\temp\f1855e530ce0c7d4d7f15f633265d075\post_link_concurrent.rsp
3001> c:\windows\system32\cmd.exe /c if exist e:\os\obj\amd64fre\temp\f1855e530ce0c7d4d7f15f633265d075\post_link_concurrent2.rsp e:\os\tools\Windows.Desktop.Tools\tools\contool.exe @e:\os\obj\amd64fre\temp\f1855e530ce0c7d4d7f15f633265d075\post_link_concurrent2.rsp
3001>Writing out macros...e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\aimxsrv\client\dll\objfre\amd64\Macros-PASS2.txt
3001>binplace e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\aimxsrv\client\dll\objfre\amd64\Macros-PASS2.txt
3001> e:\os\tools\powershell\pwsh.exe -NoProfile -Command e:\os\src\tools\NMakeJS\CheckCFlags\CheckCFlags.ps1 -MakeSubDir "onecore\ds\ds\src\aimx\prod\aimxsrv\client\dll" -Pass PASS2 -BaselineFile e:\os\src\.config\OneCore\CheckCFlags.json -OutputDir "e:\os\bin\amd64fre\build_logs\DMFDebt\build_logs\CheckCFlags\OneCore\onecoreds"
3001>CheckCFlags.ps1 : Processing onecore\ds\ds\src\aimx\prod\aimxsrv\client\dll in pass PASS2
3001>CheckCFlags.ps1 : Running e:\os\src\tools\urtrun64.cmd 4.Latest e:\os\tools\checkcflags\cs\checkcflags2.exe Scan-List /ListFile:e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\aimxsrv\client\dll\objfre\amd64\CheckCFlags_scanlist.txt /PolicyConfigFilePath:E:\os\src\tools\NMakeJS\CheckCFlags\CCF_PolicyConfig.json /LogDirectory:e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\aimxsrv\client\dll\objfre\amd64
3001> e:\os\tools\powershell\pwsh.exe -NoProfile e:\os\src\tools\NMakeJS\WarningsCop\WarningsCop.ps1 -MakeSubDir "onecore\ds\ds\src\aimx\prod\aimxsrv\client\dll" -Pass PASS2 -BaselineFile e:\os\src\.config\OneCore\WarningsCop.json -OutputDir "e:\os\bin\amd64fre\evidence\WarningsCop\OneCore\onecoreds"
3001>WarningsCop.ps1 : WarningsCop: Processing onecore\ds\ds\src\aimx\prod\aimxsrv\client\dll in pass PASS2
3003>Calculated LAYERINFO_MODULE='OneCoreDS'.
3003>makefile.def: TEMP=e:\os\obj\amd64fre\temp\b3caf5d42c176218556b53c8137facfb
3003>makefile.def: BUILDINGINDATT=
3003>[Core OS Undocking] NOT using package ''
3003>UCRT enabled: dir 'e:\os\src\onecore\ds\ds\src\aimx\prod\aimxsrv\serviceinstaller' (target 'Aimx.ServiceInstaller', type 'PROGRAM', nt_target_version '0xA000011')
3003>ObjectsMac.ts: validation succeeded
3003>_NEED_BUILDDATE not defined setting BUILDDATE to an invalid value.
3003>BUILDMSG: Optional SPD_INPUT location is e:\os\pgo\amd64fre\spds\\\Aimx.ServiceInstaller.exe.spd
3003>c:\windows\system32\cmd.exe /c e:\os\src\tools\urtrun.cmd 4.Latest e:\os\tools\apisettools\apiset.expanddelayload.exe /d:e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\aimxsrv\serviceinstaller\objfre\amd64\delayload.txt /o:e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\aimxsrv\serviceinstaller\objfre\amd64\delayload_expansion.inc
3003>OSSCop: checkBaselines: reading privateOssData from e:\os\src\tools\analysis\LastKnownState\PrivateOSSData.json and publication baseline from e:\os\src\tools\analysis\LastKnownState\OSSCop_publication_baseline.json and consumption baseline from e:\os\src\.config\OneCore\build\..\OSSCop_baseline.json
3003>OSSCop: getOSSBaselines: Parsing baselines looking for macroDefFiles [onecore\ds\ds\src\aimx\prod\aimxsrv\serviceinstaller\sources, tools\makefile.ucrt, tools\makefile.def]
3003>no publication found; not an error.
3003>no publication found; not an error.
3003>no publication found; not an error.
3003>no publication found; not an error.
3003>no publication found; not an error.
3003>A subdirectory or file e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\aimxsrv\serviceinstaller\objfre\amd64 already exists.
3003> e:\os\tools\Windows.Desktop.Tools.amd64\tools\touch.exe /c e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\aimxsrv\serviceinstaller\objfre\amd64\_PASS2_Marker.log
3003> e:\os\tools\vc\HostX64\amd64\link.exe /out:e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\aimxsrv\serviceinstaller\objfre\amd64\Aimx.ServiceInstaller.exe  /machine:amd64 @e:\os\obj\amd64fre\temp\b3caf5d42c176218556b53c8137facfb\lnk.rsp
3003>Microsoft (R) Incremental Linker Version 14.42.34444.100
3003>Copyright (C) Microsoft Corporation.  All rights reserved.
3003>/filealign:0x1000 
3003>/INCLUDE:__PLEASE_LINK_WITH_legacy_stdio_wide_specifiers.lib 
3003>/INCLUDE:__scrt_stdio_legacy_msvcrt_compatibility 
3003>/NOVCFEATURE 
3003>/d2:-DeepThoughtInliner- 
3003>/d2:-DisableWPASpecializeParam 
3003>/d2:-implyavx512upperregs- 
3003>/RunBelow4GB 
3003>/nopdbprefetch 
3003>-d2:-TypeProp- 
3003>-d2:-SpecDevirt- 
3003>/RetryOnFileOpenFailure 
3003>e:\os\public\amd64fre\OneCore\Internal\MinWin\Priv_Sdk\Lib\hotpatchspareglobals.obj 
3003>/map 
3003>/pdbinject:mapfile 
3003>/baserelocclustering 
3003>/SPGO 
3003>/SPD:e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\aimxsrv\serviceinstaller\objfre\amd64\Aimx.ServiceInstaller.exe.spd 
3003>-ignore:4199 
3003>/MERGE:_PAGE=PAGE 
3003>/MERGE:_TEXT=.text 
3003>/MERGE:_RDATA=.rdata 
3003>/OPT:REF 
3003>/OPT:ICF 
3003>/IGNORE:4078,4221,4281,4006,4198 
3003>/INCREMENTAL:NO 
3003>/release 
3003>/NODEFAULTLIB 
3003>/debug 
3003>/debugtype:cv,fixup,pdata 
3003>/version:10.0 
3003>/osversion:10.0 
3003>/ltcg 
3003>/funcoverride 
3003>/d2:-FH4 
3003>/Brepro 
3003>/PDBDLL:mspdbcore.dll 
3003>/functionpadmin:6 
3003>/MERGE:.orpc=.text 
3003>/hotpatchcompatible 
3003>/d2:-guardspecload 
3003>/d2:-guardspecanalysismode:v1_0 
3003>/d2:-guardspecmode2 
3003>/ignore:4291 
3003>/DynamicValueFixupSym:mm_shared_user_data_va=0x7FFE0000 
3003>/DynamicValueFixupSym:ki_user_shared_data=0xFFFFF78000000000 
3003>/guard:cf 
3003>/d2:-guardcfgfuncptr- 
3003>/merge:.gfids=.rdata 
3003>/d2:-guardcfgdispatch 
3003>/guard:ehcont 
3003>/pdbcompress 
3003>/delayload:api-ms-win-core-crt-l2*.dll 
3003>/delayload:api-ms-win-service-management-l1*.dll 
3003>/delayload:userenv.dll 
3003>/STACK:0x80000,0x2000 
3003>/tsaware 
3003>/highentropyva 
3003>/subsystem:console,10.00 
3003>/entry:wmainCRTStartup 
3003>e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\aimxsrv\serviceinstaller\objfre\amd64\main.obj 
3003>e:\os\public\amd64fre\onecore\external\sdk\lib\dloadhelper.lib 
3003>e:\os\public\amd64fre\onecore\internal\sdk\lib\ucrt\legacy_stdio_wide_specifiers.lib 
3003>e:\os\public\amd64fre\onecore\internal\sdk\lib\ucrt\legacy_stdio_msvcrt_compatibility.lib 
3003>e:\os\public\amd64fre\onecore\internal\sdk\lib\ucrt\msvcrt.lib 
3003>e:\os\public\amd64fre\onecore\internal\sdk\lib\ucrt\osmode_function_map.obj 
3003>e:\os\public\amd64fre\onecore\external\sdk\lib\ucrt.lib 
3003>e:\os\public\amd64fre\onecore\internal\sdk\lib\ucrt_private.lib 
3003>e:\os\public\amd64fre\onecore\internal\sdk\lib\ucrt\legacy_stdio_definitions.lib 
3003>e:\os\public\amd64fre\onecore\external\sdk\lib\mincore.lib 
3003>e:\os\public\amd64fre\onecore\internal\sdk\lib\MinWin\1.21\api-ms-win-core-crt-l2.lib 
3003>e:\os\public\amd64fre\onecore\internal\sdk\lib\guard_support.lib 
3003>e:\os\public\amd64fre\OneCore\Internal\MinWin\Priv_Sdk\Lib\1.21\api-ms-win-core-delayload-l1.lib 
3003>/pdbrpc:no 
3003>Generating code
3003>SPD e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\aimxsrv\serviceinstaller\objfre\amd64\Aimx.ServiceInstaller.exe.spd not found, compiling without profile guided optimizations
3003>Finished generating code
3003> c:\windows\system32\cmd.exe /c del e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\aimxsrv\serviceinstaller\objfre\amd64\Aimx.ServiceInstaller.ilk.persistent e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\aimxsrv\serviceinstaller\objfre\amd64\Aimx.ServiceInstaller.exe.persistent e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\aimxsrv\serviceinstaller\objfre\amd64\Aimx.ServiceInstaller.pdb.persistent 2>nul
3003> c:\windows\system32\cmd.exe /c e:\os\src\tools\urtrun.cmd 4.Latest e:\os\tools\FixTSVersionStringAppend\bin\FixTsVersionStringAppend\release\FixTSVersionStringAppend.exe /fts:e:\os\tools\FixTS\FixTS.exe /pe=e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\aimxsrv\serviceinstaller\objfre\amd64\Aimx.ServiceInstaller.exe
3003>[e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\aimxsrv\serviceinstaller\objfre\amd64\Aimx.ServiceInstaller.exe]
3003>No version resources present.
3003>Microsoft (R) COFF/PE Editor Version 14.42.34444.100
3003>Copyright (C) Microsoft Corporation.  All rights reserved.
3003>Updating PDB GUID and/or timestamp in e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\aimxsrv\serviceinstaller\objfre\amd64\Aimx.ServiceInstaller.exe
3003>Updating PDB GUID and/or timestamp in e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\aimxsrv\serviceinstaller\objfre\amd64\Aimx.ServiceInstaller.pdb
3003>Original RSDSKEY:{5D255CF1-52EE-B5BC-A0AA-1106D7F3F709}:1
3003>New RSDSKEY:     {5D255CF1-52EE-B5BC-A0AA-1106D7F3F709}:1
3003> e:\os\tools\perl\bin\perl.exe e:\os\src\tools\FeatureStaging\LogInliningFailures.pl /makedir:onecore\ds\ds\src\aimx\prod\aimxsrv\serviceinstaller e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\aimxsrv\serviceinstaller\objfre\amd64\Aimx.ServiceInstaller.exe
3003> e:\os\tools\deferredbinplace\DeferredBinplace.exe  e:\os\src\onecore\ds\ds\src\aimx\prod\aimxsrv\serviceinstaller  PASS2  e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\aimxsrv\serviceinstaller\objfre\amd64\binplace_PASS2.rsp  @e:\os\tools\binplace\binplace.exe  /R e:\os\bin\amd64fre\.  /s e:\os\bin\amd64fre\Symbols.pri\. /j /:DBG /:NOCV  -f -:LOGPDB /:CVTCIL /:SYMBAD e:\os\src\tools\symbad.txt   /:DEST retail      e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\aimxsrv\serviceinstaller\objfre\amd64\Aimx.ServiceInstaller.exe
3003>binplace e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\aimxsrv\serviceinstaller\objfre\amd64\Aimx.ServiceInstaller.exe
3003>BINPLACE : INFORMATION BNP0017: Can't detect version resource in e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\aimxsrv\serviceinstaller\objfre\amd64\Aimx.ServiceInstaller.exe. 0x000000B7 - Cannot create a file when that file already exists.
3003> c:\windows\system32\cmd.exe /c if not exist e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\aimxsrv\serviceinstaller\objfre\amd64\Aimx.ServiceInstaller.exe.mui (  echo Build_Status  LN_MUI_STS: LGNSTS_UNKNOWN Aimx.ServiceInstaller.exe  )
3003>Build_Status  LN_MUI_STS: LGNSTS_UNKNOWN Aimx.ServiceInstaller.exe  
3003> e:\os\tools\perl\bin\perl.exe e:\os\src\tools\check_delayload.pl e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\aimxsrv\serviceinstaller\objfre\amd64\Aimx.ServiceInstaller.exe
3003>(check_delayload.pl) Command: check_delayload e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\aimxsrv\serviceinstaller\objfre\amd64\Aimx.ServiceInstaller.exe
3003>(check_delayload.pl) Started at Tue Jul 15 14:04:01 2025
3003>(check_delayload.pl) reading e:\os\public\amd64fre\onecore\internal\minwin\priv_sdk\lib\dload.dload
3003>All delayloadable dlls are already delay loaded.
3003>The following dlls require stubs before they can be delay loaded:
3003> api-ms-win-core-debug-l1-1-0.dll (1 imports):
3003>  IsDebuggerPresent
3003> api-ms-win-core-delayload-l1-1-0.dll (1 imports):
3003>  DelayLoadFailureHook
3003> api-ms-win-core-delayload-l1-1-1.dll (1 imports):
3003>  ResolveDelayLoadedAPI
3003> api-ms-win-core-errorhandling-l1-1-0.dll (3 imports):
3003>  GetLastError
3003>  SetUnhandledExceptionFilter
3003>  UnhandledExceptionFilter
3003> api-ms-win-core-interlocked-l1-1-0.dll (1 imports):
3003>  InitializeSListHead
3003> api-ms-win-core-libraryloader-l1-2-0.dll (1 imports):
3003>  GetModuleHandleW
3003> api-ms-win-core-processenvironment-l1-1-0.dll (1 imports):
3003>  ExpandEnvironmentStringsW
3003> api-ms-win-core-processthreads-l1-1-0.dll (4 imports):
3003>  GetCurrentProcess
3003>  GetCurrentProcessId
3003>  GetCurrentThreadId
3003>  TerminateProcess
3003> api-ms-win-core-processthreads-l1-1-1.dll (1 imports):
3003>  IsProcessorFeaturePresent
3003> api-ms-win-core-profile-l1-1-0.dll (1 imports):
3003>  QueryPerformanceCounter
3003> api-ms-win-core-registry-l1-1-0.dll (4 imports):
3003>  RegCloseKey
3003>  RegCreateKeyExW
3003>  RegOpenKeyExW
3003>  RegSetValueExW
3003> api-ms-win-core-sysinfo-l1-1-0.dll (1 imports):
3003>  GetSystemTimeAsFileTime
3003> api-ms-win-crt-private-l1-1-0.dll (23 imports):
3003>  __C_specific_handler
3003>  __current_exception
3003>  __current_exception_context
3003>  _o___acrt_iob_func
3003>  _o___p___argc
3003>  _o___p___wargv
3003>  _o___p__commode
3003>  _o___stdio_common_vfwprintf
3003>  _o__cexit
3003>  _o__configthreadlocale
3003>  _o__configure_wide_argv
3003>  _o__crt_atexit
3003>  _o__exit
3003>  _o__get_initial_wide_environment
3003>  _o__initialize_onexit_table
3003>  _o__initialize_wide_environment
3003>  _o__register_onexit_function
3003>  _o__seh_filter_exe
3003>  _o__set_app_type
3003>  _o__set_fmode
3003>  _o__set_new_mode
3003>  _o_exit
3003>  _o_terminate
3003> api-ms-win-crt-runtime-l1-1-0.dll (4 imports):
3003>  _c_exit
3003>  _initterm
3003>  _initterm_e
3003>  _register_thread_local_exe_atexit_callback
3003> api-ms-win-crt-string-l1-1-0.dll (1 imports):
3003>  memset
3003>(check_delayload.pl) Ended at Tue Jul 15 14:04:02 2025 with ExitCode:0
3003>(check_delayload.pl) Elapsed time 1 seconds
3003> e:\os\osdep\feature.toggles\tools\ft_scraper.exe -pdb:e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\aimxsrv\serviceinstaller\objfre\amd64\Aimx.ServiceInstaller.pdb -out:e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\aimxsrv\serviceinstaller\objfre\amd64\ft_scraped_Aimx.ServiceInstaller.ft -binplace_path:bvtbin\feature_toggles\scraped\OneCore\onecore\ds\ds\src\aimx\prod\aimxsrv\serviceinstaller
3003> c:\windows\system32\cmd.exe /c if exist e:\os\obj\amd64fre\temp\b3caf5d42c176218556b53c8137facfb\post_link_concurrent.rsp e:\os\tools\Windows.Desktop.Tools\tools\contool.exe @e:\os\obj\amd64fre\temp\b3caf5d42c176218556b53c8137facfb\post_link_concurrent.rsp
3003> c:\windows\system32\cmd.exe /c if exist e:\os\obj\amd64fre\temp\b3caf5d42c176218556b53c8137facfb\post_link_concurrent2.rsp e:\os\tools\Windows.Desktop.Tools\tools\contool.exe @e:\os\obj\amd64fre\temp\b3caf5d42c176218556b53c8137facfb\post_link_concurrent2.rsp
3003>Writing out macros...e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\aimxsrv\serviceinstaller\objfre\amd64\Macros-PASS2.txt
3003>binplace e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\aimxsrv\serviceinstaller\objfre\amd64\Macros-PASS2.txt
3003> e:\os\tools\powershell\pwsh.exe -NoProfile -Command e:\os\src\tools\NMakeJS\CheckCFlags\CheckCFlags.ps1 -MakeSubDir "onecore\ds\ds\src\aimx\prod\aimxsrv\serviceinstaller" -Pass PASS2 -BaselineFile e:\os\src\.config\OneCore\CheckCFlags.json -OutputDir "e:\os\bin\amd64fre\build_logs\DMFDebt\build_logs\CheckCFlags\OneCore\onecoreds"
3003>CheckCFlags.ps1 : Processing onecore\ds\ds\src\aimx\prod\aimxsrv\serviceinstaller in pass PASS2
3003>CheckCFlags.ps1 : Running e:\os\src\tools\urtrun64.cmd 4.Latest e:\os\tools\checkcflags\cs\checkcflags2.exe Scan-List /ListFile:e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\aimxsrv\serviceinstaller\objfre\amd64\CheckCFlags_scanlist.txt /PolicyConfigFilePath:E:\os\src\tools\NMakeJS\CheckCFlags\CCF_PolicyConfig.json /LogDirectory:e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\aimxsrv\serviceinstaller\objfre\amd64
3003> e:\os\tools\powershell\pwsh.exe -NoProfile e:\os\src\tools\NMakeJS\WarningsCop\WarningsCop.ps1 -MakeSubDir "onecore\ds\ds\src\aimx\prod\aimxsrv\serviceinstaller" -Pass PASS2 -BaselineFile e:\os\src\.config\OneCore\WarningsCop.json -OutputDir "e:\os\bin\amd64fre\evidence\WarningsCop\OneCore\onecoreds"
3003>WarningsCop.ps1 : WarningsCop: Processing onecore\ds\ds\src\aimx\prod\aimxsrv\serviceinstaller in pass PASS2
3004>Calculated LAYERINFO_MODULE='OneCoreDS'.
3004>makefile.def: TEMP=e:\os\obj\amd64fre\temp\a30bd931a68835c0cac2a7031568a859
3004>makefile.def: BUILDINGINDATT=
3004>[Core OS Undocking] NOT using package ''
3004>UCRT enabled: dir 'e:\os\src\onecore\ds\ds\src\aimx\prod\aimxsrv\dll' (target 'aimxsrv', type 'DYNLINK', nt_target_version '0xA000011')
3004>ObjectsMac.ts: validation succeeded
3004>STL version 120 used in "e:\os\src\onecore\ds\ds\src\aimx\prod\aimxsrv\dll" (STL_VER_TELEMETRY)
3004>_NEED_BUILDDATE not defined setting BUILDDATE to an invalid value.
3004>BUILDMSG: Optional SPD_INPUT location is e:\os\pgo\amd64fre\spds\\\aimxsrv.dll.spd
3004>c:\windows\system32\cmd.exe /c e:\os\src\tools\urtrun.cmd 4.Latest e:\os\tools\apisettools\apiset.expanddelayload.exe /d:e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\aimxsrv\dll\objfre\amd64\delayload.txt /o:e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\aimxsrv\dll\objfre\amd64\delayload_expansion.inc
3004>OSSCop: checkBaselines: reading privateOssData from e:\os\src\tools\analysis\LastKnownState\PrivateOSSData.json and publication baseline from e:\os\src\tools\analysis\LastKnownState\OSSCop_publication_baseline.json and consumption baseline from e:\os\src\.config\OneCore\build\..\OSSCop_baseline.json
3004>OSSCop: getOSSBaselines: Parsing baselines looking for macroDefFiles [onecore\ds\ds\src\aimx\prod\aimxsrv\dll\sources, onecore\ds\ds\src\aimx\prod\cpprestsdk\consume.inc, tools\makefile.ucrt, tools\makefile.def]
3004>no publication found; not an error.
3004>no publication found; not an error.
3004>no publication found; not an error.
3004>no publication found; not an error.
3004>no publication found; not an error.
3004>no publication found; not an error.
3004>no publication found; not an error.
3004>no publication found; not an error.
3004>no publication found; not an error.
3004>no publication found; not an error.
3004>no publication found; not an error.
3004>no publication found; not an error.
3004>no publication found; not an error.
3004>no publication found; not an error.
3004>no publication found; not an error.
3004>no publication found; not an error.
3004>no publication found; not an error.
3004>A subdirectory or file e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\aimxsrv\dll\objfre\amd64 already exists.
3004> e:\os\tools\Windows.Desktop.Tools.amd64\tools\touch.exe /c e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\aimxsrv\dll\objfre\amd64\_PASS2_Marker.log
3004> e:\os\tools\vc\HostX64\amd64\link.exe /out:e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\aimxsrv\dll\objfre\amd64\aimxsrv.dll  /machine:amd64 @e:\os\obj\amd64fre\temp\a30bd931a68835c0cac2a7031568a859\lnk.rsp
3004>Microsoft (R) Incremental Linker Version 14.42.34444.100
3004>Copyright (C) Microsoft Corporation.  All rights reserved.
3004>/filealign:0x1000 
3004>/INCLUDE:__PLEASE_LINK_WITH_legacy_stdio_wide_specifiers.lib 
3004>/INCLUDE:__scrt_stdio_legacy_msvcrt_compatibility 
3004>/NOVCFEATURE 
3004>/d2:-DeepThoughtInliner- 
3004>/d2:-DisableWPASpecializeParam 
3004>/d2:-implyavx512upperregs- 
3004>/RunBelow4GB 
3004>/nopdbprefetch 
3004>-d2:-TypeProp- 
3004>-d2:-SpecDevirt- 
3004>/RetryOnFileOpenFailure 
3004>e:\os\public\amd64fre\OneCore\Internal\MinWin\Priv_Sdk\Lib\hotpatchspareglobals.obj 
3004>/map 
3004>/pdbinject:mapfile 
3004>/baserelocclustering 
3004>/SPGO 
3004>/SPD:e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\aimxsrv\dll\objfre\amd64\aimxsrv.dll.spd 
3004>-ignore:4199 
3004>/MERGE:_PAGE=PAGE 
3004>/MERGE:_TEXT=.text 
3004>/MERGE:_RDATA=.rdata 
3004>/OPT:REF 
3004>/OPT:ICF 
3004>/IGNORE:4078,4221,4281,4006,4198 
3004>/INCREMENTAL:NO 
3004>/release 
3004>/NODEFAULTLIB 
3004>/debug 
3004>/debugtype:cv,fixup,pdata 
3004>/version:10.0 
3004>/osversion:10.0 
3004>/ltcg 
3004>/funcoverride 
3004>/d2:-FH4 
3004>/Brepro 
3004>/PDBDLL:mspdbcore.dll 
3004>/functionpadmin:6 
3004>/MERGE:.orpc=.text 
3004>/hotpatchcompatible 
3004>/d2:-guardspecload 
3004>/d2:-guardspecanalysismode:v1_0 
3004>/d2:-guardspecmode2 
3004>/ignore:4291 
3004>/DynamicValueFixupSym:mm_shared_user_data_va=0x7FFE0000 
3004>/DynamicValueFixupSym:ki_user_shared_data=0xFFFFF78000000000 
3004>/guard:cf 
3004>/d2:-guardcfgfuncptr- 
3004>/merge:.gfids=.rdata 
3004>/d2:-guardcfgdispatch 
3004>/guard:ehcont 
3004>/CETCOMPAT 
3004>/pdbcompress 
3004>/delayload:api-ms-win-security-base-l1*.dll 
3004>/delayload:api-ms-win-security-sddl-l1*.dll 
3004>/delayload:ntdsapi.dll 
3004>/delayload:rpcrt4.dll 
3004>/delayload:secur32.dll 
3004>/STACK:0x40000,0x1000 
3004>/dll 
3004>/subsystem:windows,10.00 
3004>/entry:_DllMainCRTStartup 
3004>e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\aimxsrv\dll\objfre\amd64\pch.obj 
3004>e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\aimxsrv\dll\objfre\amd64\aimxsrv.exp 
3004>e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\aimxsrv\dll\objfre\amd64\dllmain.obj 
3004>e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\aimxsrv\dll\objfre\amd64\aimxservice.obj 
3004>e:\os\public\amd64fre\onecore\external\sdk\lib\dloadhelper.lib 
3004>e:\os\public\amd64fre\onecore\external\sdk\lib\atls.lib 
3004>e:\os\public\amd64fre\onecore\external\sdk\lib\atlthunk.lib 
3004>e:\os\public\amd64fre\onecore\internal\sdk\lib\ucrt\msvcprt.lib 
3004>e:\os\public\amd64fre\onecore\internal\sdk\lib\ucrt\legacy_stdio_wide_specifiers.lib 
3004>e:\os\public\amd64fre\onecore\internal\sdk\lib\ucrt\legacy_stdio_msvcrt_compatibility.lib 
3004>e:\os\public\amd64fre\onecore\internal\sdk\lib\ucrt\msvcrt.lib 
3004>e:\os\public\amd64fre\onecore\internal\sdk\lib\ucrt\osmode_function_map.obj 
3004>e:\os\public\amd64fre\onecore\external\sdk\lib\ucrt.lib 
3004>e:\os\public\amd64fre\onecore\internal\sdk\lib\ucrt_private.lib 
3004>e:\os\public\amd64fre\onecore\internal\sdk\lib\ucrt\legacy_stdio_definitions.lib 
3004>e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\aimxsrv\dll\..\client\lib\objfre\amd64\aimxclient_s.lib 
3004>e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\aimxsrv\dll\..\server\objfre\amd64\aimxserver.lib 
3004>e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\aimxsrv\dll\..\..\llmclientlib\objfre\amd64\llmclientlib.lib 
3004>e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\aimxsrv\dll\..\..\MCPServerSample\objfre\amd64\HelloMcpServer.lib 
3004>e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\aimxsrv\dll\..\..\AdPsMcpSvr\objfre\amd64\AdPsMcpSvr.lib 
3004>e:\os\public\amd64fre\onecore\external\sdk\lib\MinWin\ntdll.lib 
3004>e:\os\public\amd64fre\onecore\external\sdk\lib\MinWin\rpcrt4.lib 
3004>e:\os\public\amd64fre\onecore\external\sdk\lib\secur32.lib 
3004>e:\os\public\amd64fre\onecore\external\sdk\lib\ntdsapi.lib 
3004>e:\os\public\amd64fre\onecore\external\sdk\lib\netapi32.lib 
3004>e:\os\public\amd64fre\onecore\external\sdk\lib\onecore.lib 
3004>e:\os\public\amd64fre\onecore\internal\sdk\lib\MinWin\1.21\api-ms-win-security-sddl-l1.lib 
3004>e:\os\public\amd64fre\onecore\internal\sdk\lib\MinWin\1.21\api-ms-win-security-base-l1.lib 
3004>e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\cpprestsdk\objfre\amd64\vcpkg_installed\target-windows\lib\cpprest_2_10.lib 
3004>e:\os\public\amd64fre\onecore\internal\sdk\lib\guard_support.lib 
3004>e:\os\public\amd64fre\OneCore\Internal\MinWin\Priv_Sdk\Lib\1.21\api-ms-win-core-delayload-l1.lib 
3004>/pdbrpc:no 
3004>Generating code
3004>SPD e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\aimxsrv\dll\objfre\amd64\aimxsrv.dll.spd not found, compiling without profile guided optimizations
3004>Finished generating code
3004> c:\windows\system32\cmd.exe /c del e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\aimxsrv\dll\objfre\amd64\aimxsrv.ilk.persistent e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\aimxsrv\dll\objfre\amd64\aimxsrv.dll.persistent e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\aimxsrv\dll\objfre\amd64\aimxsrv.pdb.persistent 2>nul
3004> c:\windows\system32\cmd.exe /c e:\os\src\tools\urtrun.cmd 4.Latest e:\os\tools\FixTSVersionStringAppend\bin\FixTsVersionStringAppend\release\FixTSVersionStringAppend.exe /fts:e:\os\tools\FixTS\FixTS.exe /pe=e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\aimxsrv\dll\objfre\amd64\aimxsrv.dll
3004>[e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\aimxsrv\dll\objfre\amd64\aimxsrv.dll]
3004>No version resources present.
3004>Microsoft (R) COFF/PE Editor Version 14.42.34444.100
3004>Copyright (C) Microsoft Corporation.  All rights reserved.
3004>Updating PDB GUID and/or timestamp in e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\aimxsrv\dll\objfre\amd64\aimxsrv.dll
3004>Updating PDB GUID and/or timestamp in e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\aimxsrv\dll\objfre\amd64\aimxsrv.pdb
3004>Original RSDSKEY:{84EC1106-CB2A-54E2-34ED-CA29BF0AC77F}:1
3004>New RSDSKEY:     {84EC1106-CB2A-54E2-34ED-CA29BF0AC77F}:1
3004> e:\os\tools\perl\bin\perl.exe e:\os\src\tools\FeatureStaging\LogInliningFailures.pl /makedir:onecore\ds\ds\src\aimx\prod\aimxsrv\dll e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\aimxsrv\dll\objfre\amd64\aimxsrv.dll
3004> e:\os\tools\deferredbinplace\DeferredBinplace.exe  e:\os\src\onecore\ds\ds\src\aimx\prod\aimxsrv\dll  PASS2  e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\aimxsrv\dll\objfre\amd64\binplace_PASS2.rsp  @e:\os\tools\binplace\binplace.exe  /R e:\os\bin\amd64fre\.  /s e:\os\bin\amd64fre\Symbols.pri\. /j /:DBG /:NOCV  -f -:LOGPDB /:CVTCIL /:SYMBAD e:\os\src\tools\symbad.txt /:TMF   /:DEST retail      e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\aimxsrv\dll\objfre\amd64\aimxsrv.dll
3004>binplace e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\aimxsrv\dll\objfre\amd64\aimxsrv.dll
3004>BINPLACE : INFORMATION BNP0017: Can't detect version resource in e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\aimxsrv\dll\objfre\amd64\aimxsrv.dll. 0x000000B7 - Cannot create a file when that file already exists.
3004> c:\windows\system32\cmd.exe /c if not exist e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\aimxsrv\dll\objfre\amd64\aimxsrv.dll.mui (  echo Build_Status  LN_MUI_STS: LGNSTS_UNKNOWN aimxsrv.dll  )
3004>Build_Status  LN_MUI_STS: LGNSTS_UNKNOWN aimxsrv.dll  
3004> e:\os\tools\perl\bin\perl.exe e:\os\src\tools\check_delayload.pl e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\aimxsrv\dll\objfre\amd64\aimxsrv.dll
3004>(check_delayload.pl) Command: check_delayload e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\aimxsrv\dll\objfre\amd64\aimxsrv.dll
3004>(check_delayload.pl) Started at Tue Jul 15 14:04:08 2025
3004>(check_delayload.pl) reading e:\os\public\amd64fre\onecore\internal\minwin\priv_sdk\lib\dload.dload
3004>All delayloadable dlls are already delay loaded.
3004>The following dlls require stubs before they can be delay loaded:
3004> api-ms-win-core-debug-l1-1-0.dll (3 imports):
3004>  DebugBreak
3004>  IsDebuggerPresent
3004>  OutputDebugStringW
3004> api-ms-win-core-delayload-l1-1-0.dll (1 imports):
3004>  DelayLoadFailureHook
3004> api-ms-win-core-delayload-l1-1-1.dll (1 imports):
3004>  ResolveDelayLoadedAPI
3004> api-ms-win-core-errorhandling-l1-1-0.dll (4 imports):
3004>  GetLastError
3004>  SetLastError
3004>  SetUnhandledExceptionFilter
3004>  UnhandledExceptionFilter
3004> api-ms-win-core-file-l1-1-0.dll (7 imports):
3004>  CreateDirectoryW
3004>  CreateFileW
3004>  DeleteFileW
3004>  FindClose
3004>  FindFirstFileW
3004>  GetFileAttributesExW
3004>  ReadFile
3004> api-ms-win-core-file-l1-2-0.dll (1 imports):
3004>  GetTempPathW
3004> api-ms-win-core-file-l2-1-0.dll (2 imports):
3004>  GetFileInformationByHandleEx
3004>  MoveFileExW
3004> api-ms-win-core-handle-l1-1-0.dll (1 imports):
3004>  CloseHandle
3004> api-ms-win-core-heap-l1-1-0.dll (3 imports):
3004>  GetProcessHeap
3004>  HeapAlloc
3004>  HeapFree
3004> api-ms-win-core-heap-l2-1-0.dll (2 imports):
3004>  GlobalFree
3004>  LocalFree
3004> api-ms-win-core-interlocked-l1-1-0.dll (1 imports):
3004>  InitializeSListHead
3004> api-ms-win-core-libraryloader-l1-2-0.dll (6 imports):
3004>  DisableThreadLibraryCalls
3004>  GetModuleFileNameA
3004>  GetModuleHandleA
3004>  GetModuleHandleExW
3004>  GetModuleHandleW
3004>  GetProcAddress
3004> api-ms-win-core-localization-l1-2-0.dll (3 imports):
3004>  FormatMessageA
3004>  FormatMessageW
3004>  GetLocaleInfoEx
3004> api-ms-win-core-namedpipe-l1-1-0.dll (1 imports):
3004>  CreatePipe
3004> api-ms-win-core-processenvironment-l1-1-0.dll (2 imports):
3004>  GetStdHandle
3004>  SearchPathW
3004> api-ms-win-core-processthreads-l1-1-0.dll (7 imports):
3004>  CreateProcessW
3004>  CreateThread
3004>  GetCurrentProcess
3004>  GetCurrentProcessId
3004>  GetCurrentThreadId
3004>  GetExitCodeProcess
3004>  TerminateProcess
3004> api-ms-win-core-processthreads-l1-1-1.dll (1 imports):
3004>  IsProcessorFeaturePresent
3004> api-ms-win-core-profile-l1-1-0.dll (1 imports):
3004>  QueryPerformanceCounter
3004> api-ms-win-core-registry-l1-1-0.dll (6 imports):
3004>  RegCloseKey
3004>  RegGetValueW
3004>  RegOpenKeyExA
3004>  RegOpenKeyExW
3004>  RegQueryValueExA
3004>  RegQueryValueExW
3004> api-ms-win-core-string-l1-1-0.dll (2 imports):
3004>  MultiByteToWideChar
3004>  WideCharToMultiByte
3004> api-ms-win-core-synch-l1-1-0.dll (16 imports):
3004>  AcquireSRWLockExclusive
3004>  AcquireSRWLockShared
3004>  CreateEventExW
3004>  CreateEventW
3004>  DeleteCriticalSection
3004>  EnterCriticalSection
3004>  InitializeCriticalSection
3004>  InitializeCriticalSectionAndSpinCount
3004>  InitializeCriticalSectionEx
3004>  LeaveCriticalSection
3004>  ReleaseSRWLockExclusive
3004>  ReleaseSRWLockShared
3004>  ResetEvent
3004>  SetEvent
3004>  WaitForSingleObject
3004>  WaitForSingleObjectEx
3004> api-ms-win-core-synch-l1-2-0.dll (1 imports):
3004>  Sleep
3004> api-ms-win-core-sysinfo-l1-1-0.dll (2 imports):
3004>  GetSystemTimeAsFileTime
3004>  GetTickCount
3004> api-ms-win-core-threadpool-l1-2-0.dll (3 imports):
3004>  CloseThreadpoolWork
3004>  CreateThreadpoolWork
3004>  SubmitThreadpoolWork
3004> api-ms-win-core-timezone-l1-1-0.dll (1 imports):
3004>  FileTimeToSystemTime
3004> api-ms-win-crt-private-l1-1-0.dll (67 imports):
3004>  _CxxThrowException
3004>  __C_specific_handler
3004>  __CxxFrameHandler3
3004>  __CxxFrameHandler4
3004>  __current_exception
3004>  __current_exception_context
3004>  __std_terminate
3004>  _o____lc_codepage_func
3004>  _o___std_exception_copy
3004>  _o___std_exception_destroy
3004>  _o___std_type_info_destroy_list
3004>  _o___stdio_common_vsprintf
3004>  _o___stdio_common_vsprintf_s
3004>  _o___stdio_common_vswprintf
3004>  _o___stdio_common_vswprintf_s
3004>  _o__callnewh
3004>  _o__cexit
3004>  _o__configure_narrow_argv
3004>  _o__crt_atexit
3004>  _o__dsign
3004>  _o__errno
3004>  _o__execute_onexit_table
3004>  _o__fseeki64
3004>  _o__get_stream_buffer_pointers
3004>  _o__initialize_narrow_environment
3004>  _o__initialize_onexit_table
3004>  _o__invalid_parameter_noinfo
3004>  _o__invalid_parameter_noinfo_noreturn
3004>  _o__localtime64_s
3004>  _o__lock_file
3004>  _o__purecall
3004>  _o__register_onexit_function
3004>  _o__seh_filter_dll
3004>  _o__unlock_file
3004>  _o__wcsicmp
3004>  _o_abort
3004>  _o_ceilf
3004>  _o_fclose
3004>  _o_fflush
3004>  _o_fgetc
3004>  _o_fgetpos
3004>  _o_fputc
3004>  _o_fread
3004>  _o_free
3004>  _o_fsetpos
3004>  _o_fwrite
3004>  _o_isalpha
3004>  _o_isdigit
3004>  _o_localeconv
3004>  _o_malloc
3004>  _o_realloc
3004>  _o_setvbuf
3004>  _o_strftime
3004>  _o_strtod
3004>  _o_strtoll
3004>  _o_strtoull
3004>  _o_terminate
3004>  _o_towlower
3004>  _o_ungetc
3004>  _o_wcscpy_s
3004>  _o_wcstof
3004>  _o_wcstol
3004>  memchr
3004>  memcmp
3004>  memcpy
3004>  memmove
3004>  strchr
3004> api-ms-win-crt-runtime-l1-1-0.dll (2 imports):
3004>  _initterm
3004>  _initterm_e
3004> api-ms-win-crt-string-l1-1-0.dll (2 imports):
3004>  memset
3004>  wcsnlen
3004> api-ms-win-crt-time-l1-1-0.dll (1 imports):
3004>  _time64
3004> api-ms-win-eventing-classicprovider-l1-1-0.dll (6 imports):
3004>  GetTraceEnableFlags
3004>  GetTraceEnableLevel
3004>  GetTraceLoggerHandle
3004>  RegisterTraceGuidsW
3004>  TraceMessage
3004>  UnregisterTraceGuids
3004> msvcp_win.dll (120 imports):
3004>  ??0?$basic_ios@DU?$char_traits@D@std@@@std@@IEAA@XZ
3004>  ??0?$basic_ios@GU?$char_traits@G@std@@@std@@IEAA@XZ
3004>  ??0?$basic_iostream@GU?$char_traits@G@std@@@std@@QEAA@PEAV?$basic_streambuf@GU?$char_traits@G@std@@@1@@Z
3004>  ??0?$basic_istream@GU?$char_traits@G@std@@@std@@QEAA@PEAV?$basic_streambuf@GU?$char_traits@G@std@@@1@_N@Z
3004>  ??0?$basic_ostream@DU?$char_traits@D@std@@@std@@QEAA@PEAV?$basic_streambuf@DU?$char_traits@D@std@@@1@_N@Z
3004>  ??0?$basic_ostream@GU?$char_traits@G@std@@@std@@QEAA@PEAV?$basic_streambuf@GU?$char_traits@G@std@@@1@_N@Z
3004>  ??0?$basic_streambuf@DU?$char_traits@D@std@@@std@@IEAA@XZ
3004>  ??0?$basic_streambuf@GU?$char_traits@G@std@@@std@@IEAA@XZ
3004>  ??0_Locinfo@std@@QEAA@PEBD@Z
3004>  ??0_Lockit@std@@QEAA@H@Z
3004>  ??0facet@locale@std@@IEAA@_K@Z
3004>  ??1?$basic_ios@DU?$char_traits@D@std@@@std@@UEAA@XZ
3004>  ??1?$basic_ios@GU?$char_traits@G@std@@@std@@UEAA@XZ
3004>  ??1?$basic_iostream@GU?$char_traits@G@std@@@std@@UEAA@XZ
3004>  ??1?$basic_istream@GU?$char_traits@G@std@@@std@@UEAA@XZ
3004>  ??1?$basic_ostream@DU?$char_traits@D@std@@@std@@UEAA@XZ
3004>  ??1?$basic_ostream@GU?$char_traits@G@std@@@std@@UEAA@XZ
3004>  ??1?$basic_streambuf@DU?$char_traits@D@std@@@std@@UEAA@XZ
3004>  ??1?$basic_streambuf@GU?$char_traits@G@std@@@std@@UEAA@XZ
3004>  ??1_Locinfo@std@@QEAA@XZ
3004>  ??1_Lockit@std@@QEAA@XZ
3004>  ??1facet@locale@std@@MEAA@XZ
3004>  ??5?$basic_istream@GU?$char_traits@G@std@@@std@@QEAAAEAV01@AEAH@Z
3004>  ??5?$basic_istream@GU?$char_traits@G@std@@@std@@QEAAAEAV01@AEA_K@Z
3004>  ??6?$basic_ostream@DU?$char_traits@D@std@@@std@@QEAAAEAV01@P6AAEAV01@AEAV01@@Z@Z
3004>  ??6?$basic_ostream@GU?$char_traits@G@std@@@std@@QEAAAEAV01@_K@Z
3004>  ??Bid@locale@std@@QEAA_KXZ
3004>  ??Bios_base@std@@QEBA_NXZ
3004>  ?_Decref@facet@locale@std@@UEAAPEAV_Facet_base@3@XZ
3004>  ?_Fiopen@std@@YAPEAU_iobuf@@PEBDHH@Z
3004>  ?_Getcat@?$codecvt@DDU_Mbstatet@@@std@@SA_KPEAPEBVfacet@locale@2@PEBV42@@Z
3004>  ?_Getcat@?$ctype@G@std@@SA_KPEAPEBVfacet@locale@2@PEBV42@@Z
3004>  ?_Getcoll@_Locinfo@std@@QEBA?AU_Collvec@@XZ
3004>  ?_Getgloballocale@locale@std@@CAPEAV_Locimp@12@XZ
3004>  ?_Incref@facet@locale@std@@UEAAXXZ
3004>  ?_Init@?$basic_streambuf@DU?$char_traits@D@std@@@std@@IEAAXXZ
3004>  ?_Init@locale@std@@CAPEAV_Locimp@12@_N@Z
3004>  ?_Ipfx@?$basic_istream@GU?$char_traits@G@std@@@std@@QEAA_N_N@Z
3004>  ?_Lock@?$basic_streambuf@DU?$char_traits@D@std@@@std@@UEAAXXZ
3004>  ?_Lock@?$basic_streambuf@GU?$char_traits@G@std@@@std@@UEAAXXZ
3004>  ?_Osfx@?$basic_ostream@DU?$char_traits@D@std@@@std@@QEAAXXZ
3004>  ?_Osfx@?$basic_ostream@GU?$char_traits@G@std@@@std@@QEAAXXZ
3004>  ?_Syserror_map@std@@YAPEBDH@Z
3004>  ?_Throw_Cpp_error@std@@YAXH@Z
3004>  ?_Unlock@?$basic_streambuf@DU?$char_traits@D@std@@@std@@UEAAXXZ
3004>  ?_Unlock@?$basic_streambuf@GU?$char_traits@G@std@@@std@@UEAAXXZ
3004>  ?_Winerror_map@std@@YAHH@Z
3004>  ?_Xbad_alloc@std@@YAXXZ
3004>  ?_Xbad_function_call@std@@YAXXZ
3004>  ?_Xinvalid_argument@std@@YAXPEBD@Z
3004>  ?_Xlength_error@std@@YAXPEBD@Z
3004>  ?_Xout_of_range@std@@YAXPEBD@Z
3004>  ?_Xregex_error@std@@YAXW4error_type@regex_constants@1@@Z
3004>  ?__ExceptionPtrAssign@@YAXPEAXPEBX@Z
3004>  ?__ExceptionPtrCompare@@YA_NPEBX0@Z
3004>  ?__ExceptionPtrCopy@@YAXPEAXPEBX@Z
3004>  ?__ExceptionPtrCopyException@@YAXPEAXPEBX1@Z
3004>  ?__ExceptionPtrCreate@@YAXPEAX@Z
3004>  ?__ExceptionPtrCurrentException@@YAXPEAX@Z
3004>  ?__ExceptionPtrDestroy@@YAXPEAX@Z
3004>  ?__ExceptionPtrRethrow@@YAXPEBX@Z
3004>  ?__ExceptionPtrToBool@@YA_NPEBX@Z
3004>  ?always_noconv@codecvt_base@std@@QEBA_NXZ
3004>  ?bad@ios_base@std@@QEBA_NXZ
3004>  ?classic@locale@std@@SAAEBV12@XZ
3004>  ?clear@?$basic_ios@DU?$char_traits@D@std@@@std@@QEAAXH_N@Z
3004>  ?eback@?$basic_streambuf@GU?$char_traits@G@std@@@std@@IEBAPEAGXZ
3004>  ?egptr@?$basic_streambuf@GU?$char_traits@G@std@@@std@@IEBAPEAGXZ
3004>  ?eof@ios_base@std@@QEBA_NXZ
3004>  ?epptr@?$basic_streambuf@GU?$char_traits@G@std@@@std@@IEBAPEAGXZ
3004>  ?fail@ios_base@std@@QEBA_NXZ
3004>  ?flush@?$basic_ostream@DU?$char_traits@D@std@@@std@@QEAAAEAV12@XZ
3004>  ?flush@?$basic_ostream@GU?$char_traits@G@std@@@std@@QEAAAEAV12@XZ
3004>  ?getloc@?$basic_streambuf@DU?$char_traits@D@std@@@std@@QEBA?AVlocale@2@XZ
3004>  ?good@ios_base@std@@QEBA_NXZ
3004>  ?gptr@?$basic_streambuf@GU?$char_traits@G@std@@@std@@IEBAPEAGXZ
3004>  ?id@?$codecvt@DDU_Mbstatet@@@std@@2V0locale@2@A
3004>  ?id@?$collate@G@std@@2V0locale@2@A
3004>  ?id@?$ctype@G@std@@2V0locale@2@A
3004>  ?imbue@?$basic_ios@GU?$char_traits@G@std@@@std@@QEAA?AVlocale@2@AEBV32@@Z
3004>  ?imbue@?$basic_streambuf@DU?$char_traits@D@std@@@std@@MEAAXAEBVlocale@2@@Z
3004>  ?imbue@?$basic_streambuf@GU?$char_traits@G@std@@@std@@MEAAXAEBVlocale@2@@Z
3004>  ?in@?$codecvt@DDU_Mbstatet@@@std@@QEBAHAEAU_Mbstatet@@PEBD1AEAPEBDPEAD3AEAPEAD@Z
3004>  ?is@?$ctype@G@std@@QEBA_NFG@Z
3004>  ?out@?$codecvt@DDU_Mbstatet@@@std@@QEBAHAEAU_Mbstatet@@PEBD1AEAPEBDPEAD3AEAPEAD@Z
3004>  ?pbase@?$basic_streambuf@GU?$char_traits@G@std@@@std@@IEBAPEAGXZ
3004>  ?pptr@?$basic_streambuf@GU?$char_traits@G@std@@@std@@IEBAPEAGXZ
3004>  ?put@?$basic_ostream@DU?$char_traits@D@std@@@std@@QEAAAEAV12@D@Z
3004>  ?sbumpc@?$basic_streambuf@GU?$char_traits@G@std@@@std@@QEAAGXZ
3004>  ?setbuf@?$basic_streambuf@DU?$char_traits@D@std@@@std@@MEAAPEAV12@PEAD_J@Z
3004>  ?setbuf@?$basic_streambuf@GU?$char_traits@G@std@@@std@@MEAAPEAV12@PEAG_J@Z
3004>  ?setstate@?$basic_ios@DU?$char_traits@D@std@@@std@@QEAAXH_N@Z
3004>  ?setstate@?$basic_ios@GU?$char_traits@G@std@@@std@@QEAAXH_N@Z
3004>  ?sgetc@?$basic_streambuf@GU?$char_traits@G@std@@@std@@QEAAGXZ
3004>  ?showmanyc@?$basic_streambuf@DU?$char_traits@D@std@@@std@@MEAA_JXZ
3004>  ?showmanyc@?$basic_streambuf@GU?$char_traits@G@std@@@std@@MEAA_JXZ
3004>  ?snextc@?$basic_streambuf@GU?$char_traits@G@std@@@std@@QEAAGXZ
3004>  ?sputc@?$basic_streambuf@DU?$char_traits@D@std@@@std@@QEAAHD@Z
3004>  ?sputc@?$basic_streambuf@GU?$char_traits@G@std@@@std@@QEAAGG@Z
3004>  ?sputn@?$basic_streambuf@DU?$char_traits@D@std@@@std@@QEAA_JPEBD_J@Z
3004>  ?sputn@?$basic_streambuf@GU?$char_traits@G@std@@@std@@QEAA_JPEBG_J@Z
3004>  ?sync@?$basic_streambuf@DU?$char_traits@D@std@@@std@@MEAAHXZ
3004>  ?sync@?$basic_streambuf@GU?$char_traits@G@std@@@std@@MEAAHXZ
3004>  ?tolower@?$ctype@G@std@@QEBAGG@Z
3004>  ?tolower@?$ctype@G@std@@QEBAPEBGPEAGPEBG@Z
3004>  ?uflow@?$basic_streambuf@DU?$char_traits@D@std@@@std@@MEAAHXZ
3004>  ?uflow@?$basic_streambuf@GU?$char_traits@G@std@@@std@@MEAAGXZ
3004>  ?uncaught_exception@std@@YA_NXZ
3004>  ?unshift@?$codecvt@DDU_Mbstatet@@@std@@QEBAHAEAU_Mbstatet@@PEAD1AEAPEAD@Z
3004>  ?widen@?$basic_ios@DU?$char_traits@D@std@@@std@@QEBADD@Z
3004>  ?widen@?$basic_ios@GU?$char_traits@G@std@@@std@@QEBAGD@Z
3004>  ?xsgetn@?$basic_streambuf@DU?$char_traits@D@std@@@std@@MEAA_JPEAD_J@Z
3004>  ?xsgetn@?$basic_streambuf@GU?$char_traits@G@std@@@std@@MEAA_JPEAG_J@Z
3004>  ?xsputn@?$basic_streambuf@DU?$char_traits@D@std@@@std@@MEAA_JPEBD_J@Z
3004>  ?xsputn@?$basic_streambuf@GU?$char_traits@G@std@@@std@@MEAA_JPEBG_J@Z
3004>  _Mtx_lock
3004>  _Mtx_unlock
3004>  _Wcscoll
3004>  _Wcsxfrm
3004>  _Xtime_get_ticks
3004>(check_delayload.pl) Ended at Tue Jul 15 14:04:08 2025 with ExitCode:0
3004>(check_delayload.pl) Elapsed time 0 seconds
3004> e:\os\osdep\feature.toggles\tools\ft_scraper.exe -pdb:e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\aimxsrv\dll\objfre\amd64\aimxsrv.pdb -out:e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\aimxsrv\dll\objfre\amd64\ft_scraped_aimxsrv.ft -binplace_path:bvtbin\feature_toggles\scraped\OneCore\onecore\ds\ds\src\aimx\prod\aimxsrv\dll
3004> c:\windows\system32\cmd.exe /c if exist e:\os\obj\amd64fre\temp\a30bd931a68835c0cac2a7031568a859\post_link_concurrent.rsp e:\os\tools\Windows.Desktop.Tools\tools\contool.exe @e:\os\obj\amd64fre\temp\a30bd931a68835c0cac2a7031568a859\post_link_concurrent.rsp
3004> c:\windows\system32\cmd.exe /c if exist e:\os\obj\amd64fre\temp\a30bd931a68835c0cac2a7031568a859\post_link_concurrent2.rsp e:\os\tools\Windows.Desktop.Tools\tools\contool.exe @e:\os\obj\amd64fre\temp\a30bd931a68835c0cac2a7031568a859\post_link_concurrent2.rsp
3004>Writing out macros...e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\aimxsrv\dll\objfre\amd64\Macros-PASS2.txt
3004>binplace e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\aimxsrv\dll\objfre\amd64\Macros-PASS2.txt
3004> e:\os\tools\powershell\pwsh.exe -NoProfile -Command e:\os\src\tools\NMakeJS\CheckCFlags\CheckCFlags.ps1 -MakeSubDir "onecore\ds\ds\src\aimx\prod\aimxsrv\dll" -Pass PASS2 -BaselineFile e:\os\src\.config\OneCore\CheckCFlags.json -OutputDir "e:\os\bin\amd64fre\build_logs\DMFDebt\build_logs\CheckCFlags\OneCore\onecoreds"
3004>CheckCFlags.ps1 : Processing onecore\ds\ds\src\aimx\prod\aimxsrv\dll in pass PASS2
3004>CheckCFlags.ps1 : Running e:\os\src\tools\urtrun64.cmd 4.Latest e:\os\tools\checkcflags\cs\checkcflags2.exe Scan-List /ListFile:e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\aimxsrv\dll\objfre\amd64\CheckCFlags_scanlist.txt /PolicyConfigFilePath:E:\os\src\tools\NMakeJS\CheckCFlags\CCF_PolicyConfig.json /LogDirectory:e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\aimxsrv\dll\objfre\amd64
3004> e:\os\tools\powershell\pwsh.exe -NoProfile e:\os\src\tools\NMakeJS\WarningsCop\WarningsCop.ps1 -MakeSubDir "onecore\ds\ds\src\aimx\prod\aimxsrv\dll" -Pass PASS2 -BaselineFile e:\os\src\.config\OneCore\WarningsCop.json -OutputDir "e:\os\bin\amd64fre\evidence\WarningsCop\OneCore\onecoreds"
3004>WarningsCop.ps1 : WarningsCop: Processing onecore\ds\ds\src\aimx\prod\aimxsrv\dll in pass PASS2
BUILD: Pass complete => PASS2

PERF: Waiting for performance monitor thread to terminate.
PERF: Terminating perf data collector thread.
1>info: Microsoft.Internal.Trace.Database.Core.TraceManager[0]
1>      Finalizing the trace reading...
1>info: Microsoft.Internal.Trace.Database.Core.AccessManager[0]
1>      Total Accesses = 51419
1>info: Microsoft.Internal.Trace.Database.Core.FileManager[0]
1>      Total Files = 19453
1>info: Microsoft.Internal.Trace.Database.Core.ProcessManager[0]
1>      Total Processes = 428
1>info: Microsoft.Internal.Trace.Tracer.EtwTraceAdapter[0]
1>      Total Process Time = 166.25
1>info: Microsoft.Internal.Trace.Tracer.EtwTraceAdapter[0]
1>      (Logging time = 4.134894400000003, Detour time = 7.583872799999999
1>info: Microsoft.Internal.Trace.Database.Core.TraceManager[0]
1>      Finalizing the trace reading...
1>warn: Microsoft.Internal.Trace.Database.Core.FileManager[0]
1>      Including all files for analysis because the Exclusion Evaluation rules are not loaded.
1>warn: Microsoft.Internal.Trace.Database.Core.DirectoryManager[0]
1>      Including all directories for analysis because the Exclusion Evaluation rules are not loaded.
1>info: Microsoft.Internal.Trace.Database.Core.DirectoryManager[0]
1>      Analyzing trace to infer dependencies...
1>info: Microsoft.Internal.Trace.Database.IO.BinaryTraceWriter[0]
1>      File e:\os\src\onecore\ds\ds\src\aimx\prod\aimxsrv\buildfre.trc already exists. Implicitly overwriting as the native code would have done.
1>info: Microsoft.Internal.Trace.Database.IO.BinaryTraceWriter[0]
1>      Writing e:\os\src\onecore\ds\ds\src\aimx\prod\aimxsrv\buildfre.trc
1>info: Microsoft.Internal.Trace.Database.IO.Table6[0]
1>      File table total write time = 00:00:00.2569668
1>info: Microsoft.Internal.Trace.Database.IO.Table6[0]
1>      File total bytes written = 828865
1>info: Microsoft.Internal.Trace.Database.IO.Table6[0]
1>      Process table total write time = 00:00:00.0638922
1>info: Microsoft.Internal.Trace.Database.IO.Table6[0]
1>      Process total bytes written = 91770
1>info: Microsoft.Internal.Trace.Database.IO.Table6[0]
1>      Access table total write time = 00:00:00.0273090
1>info: Microsoft.Internal.Trace.Database.IO.Table6[0]
1>      Access total bytes written = 238446
1>info: Microsoft.Internal.Trace.Database.IO.Table6[0]
1>      Task table total write time = 00:00:00.0003782
1>info: Microsoft.Internal.Trace.Database.IO.Table6[0]
1>      Task total bytes written = 21
1>info: Microsoft.Internal.Trace.Database.IO.Table6[0]
1>      EnvironmentAccess table total write time = 00:00:00.0001629
1>info: Microsoft.Internal.Trace.Database.IO.Table6[0]
1>      EnvironmentAccess total bytes written = 21
1>info: Microsoft.Internal.Trace.Database.IO.BinaryTraceWriter[0]
1>      Trace file written to e:\os\src\onecore\ds\ds\src\aimx\prod\aimxsrv\buildfre.trc
1>TRACER : Microsoft (R) Build Tracer
1>TRACER : Copyright (C) Microsoft Corporation. All rights reserved.
1>TRACER : Starting Realtime Build Trace File Logger *************-4aa0-a34e-55f9c8ec57e3...
1>TRACER : Enabling trace provider...
1>TRACER (BuildSocket): RegisterClient rupo-dell:0 (Tracer)
1>TRACER : Tracer Satellite: Satellite command disabled. (No value in environment variable TRACER_SATELLITE_COMMAND)
1>TRACER : Launching: "e:\os\tools\corebuild\amd64\buildc.exeTRACER :  /hostname localhost /hostport 29026TRACER : "
1>TRACER : Tracer Satellite: Stop Process: Nothing to do.
1>TRACER : Disabling trace provider...
1>TRACER (event): ETW Trace Session
1>TRACER (event): =================
1>TRACER (event): Buffers Allocated: 32
1>TRACER (event): Buffers Written: 777
1>TRACER (event): Buffer Size: 512KB
1>TRACER (event): Buffers Lost: 0
1>TRACER (event): Real Time Buffers Lost: 0
1>TRACER (event): Events Lost: 0
1>TRACER : Stopping Build Trace File Logger *************-4aa0-a34e-55f9c8ec57e3...
1>Running analyzer on build trace...
  *************
1>'toolredirector.exe analyzernative -merge:e:\os\obj\amd64fre\objfre\amd64\build.ldg -reportconfig:e:\os\src\build\config\core\dbb_report_config.xml -in:bin e:\os\src\onecore\ds\ds\src\aimx\prod\aimxsrv\buildfre.trc'
1>warn: Microsoft.Internal.Trace.Reporting.Reports.ExternalAccessReport.ExternalAccessReportExclusions[0]
1>      Provided file '' does not exist or is empty. Moving to fallback file 'e:\os\tools\Analyzer\amd64\DefaultConfigFiles\ExternalAccessReportExclusions.json'
1>ANALYZER : Microsoft (R) Build Trace Analyzer [Build 8.0.250519001+6b8e35f5c0ee29c4c18a354e33cc8b695d5695ad]
1>ANALYZER : Copyright (C) Microsoft Corporation. All rights reserved.
1>[14:04:12.320] Parsing error policy from 'e:\os\src\build\config\Core\AnalyzerEnforcedErrors.json'...
1>TRACEREPORT : Processing e:\os\src\build\config\core\dbb_report_config.xml
1>ANALYZER : Processing e:\os\src\build\config\Core\dbb_exclusions.xml
1>ANALYZER : ---------------------------
1>Reading input file
1>---------------------------
1>ANALYZER : Processing e:\os\src\onecore\ds\ds\src\aimx\prod\aimxsrv\buildfre.trc
1>ANALYZER : 
1>Read access table time = 0.010s
1>ANALYZER : 
1>Trace start time: 07/15/2025 02:02:20 PM
1>ANALYZER : 
1>Finalizing the trace reading...
1>ANALYZER : Total Files = 19453ANALYZER : 
1>ANALYZER : Total Processes = 428ANALYZER : 
1>ANALYZER : Total Accesses = 49289ANALYZER : 
1>ANALYZER : Total parse time = 0.034s
1>ANALYZER : Loading additional trace file e:\os\obj\amd64fre\objfre\amd64\build.ldg
1>ANALYZER : Processing e:\os\obj\amd64fre\objfre\amd64\build.ldg
1>ANALYZER : Parse time (e:\os\obj\amd64fre\objfre\amd64\build.ldg) = 0.007s
1>ANALYZER : Merge time = 0.031s
1>ANALYZER : 
1>Finalizing the trace reading...
1>ANALYZER : Total Files = 19252ANALYZER : 
1>ANALYZER : Total Accesses = 49289ANALYZER : 
1>ANALYZER : Total analysis time = 0.029s
1>ANALYZER : Write time (e:\os\obj\amd64fre\objfre\amd64\build.ldg) = 0.018s
1>TRACEREPORT : Parsing Baseline Files...
1>TRACEREPORT : Processing e:\os\src\.config\Desktop\build\dbb_report_baselines.xml
1>TRACEREPORT (configparser): The baseline file 'e:\os\src\.config\GameCore\build\dbb_report_baselines.xml' was not found. Ignoring it.
1>TRACEREPORT (configparser): The baseline file 'e:\os\src\.config\IOT\build\dbb_report_baselines.xml' was not found. Ignoring it.
1>TRACEREPORT : Processing e:\os\src\.config\NanoServer\build\dbb_report_baselines.xml
1>TRACEREPORT : Processing e:\os\src\.config\OSClient\build\dbb_report_baselines.xml
1>TRACEREPORT : Processing e:\os\src\.config\OneCore\build\dbb_report_baselines.xml
1>TRACEREPORT : Processing e:\os\src\.config\PCShell\build\dbb_report_baselines.xml
1>TRACEREPORT : Processing e:\os\src\.config\ServerCommon\build\dbb_report_baselines.xml
1>TRACEREPORT : Processing e:\os\src\.config\Server\build\dbb_report_baselines.xml
1>TRACEREPORT : Processing e:\os\src\.config\ShellCommonDesktopBase\build\dbb_report_baselines.xml
1>TRACEREPORT : Processing e:\os\src\.config\Xbox\build\dbb_report_baselines.xml
1>TRACEREPORT : Processing e:\os\src\.config\clientcore\build\dbb_report_baselines.xml
1>TRACEREPORT : Processing e:\os\src\.config\editions\build\dbb_report_baselines.xml
1>TRACEREPORT : Processing e:\os\src\.config\onecoreuap\build\dbb_report_baselines.xml
1>TRACEREPORT : Processing e:\os\src\.config\shellcommon\build\dbb_report_baselines.xml
1>TRACEREPORT : Processing e:\os\src\.config\xbox\build\dbb_report_baselines.xml
1>TRACEREPORT : Generating reports...
1>TRACEREPORT : Processing e:\os\cdg\amd64fre\build.cdg
1>TRACEREPORT : 
1>Finalizing the trace reading...
1>TRACEREPORT : Total Files = 115465TRACEREPORT : 
1>TRACEREPORT : Running all reports in parallel...
1>TRACEREPORT : Reports may call libraries that use the global logger, and hence the parallelism may cause output to commingle.
1>TRACEREPORT : All report-specific output will be buffered and printed later.
1>TRACEREPORT : Started running the 'badpublish-cs' report in parallel.
1>TRACEREPORT : Started running the 'buildrules-cs' report in parallel.
1>TRACEREPORT : Started running the 'chunkanalyzer-cs' report in parallel.
1>TRACEREPORT : Started running the 'crosslayer-cs' report in parallel.
1>TRACEREPORT : Started running the 'externalaccess-cs' report in parallel.
1>TRACEREPORT : Started running the 'illegalprocess' report in parallel.
1>TRACEREPORT : Started running the 'includes-cs' report in parallel.
1>TRACEREPORT : Started running the 'multiwritestrict-cs' report in parallel.
1>TRACEREPORT : Started running the 'nttree-cs' report in parallel.
1>TRACEREPORT : Started running the 'objroot-cs' report in parallel.
1>TRACEREPORT : Started running the 'ostools-cs' report in parallel.
1>TRACinfo: Microsoft.Internal.Trace.Reporting.Reports.TempFilesReport[0]
1>      Total 7,759 Temp Files Write records
1>EREPORT : Started running the 'restricteddependency-cs' report in parallel.
1>TRACEREPORT : Started running the 'sdxwrite-cs' report in parallel.
1>TRACEREPORT : Started running the 'sourcesdep-cs' report in parallel.
1>TRACEREPORT : Started running the 'tempfiles-cs' report in parallel.
1>TRACEREPORT : Started running the 'tools-cs' report in parallel.
1>TRACEREPORT : Started running the 'unc-cs' report in parallel.
1>TRACEREPORT : Started running the 'vpacknttree-cs' report in parallel.
1>TRACEREPORT : Started running the 'vpackstrict-cs' report in parallel.
1>TRACEREPORT : Finished running the 'illegalprocess' report in parallel:
1>(illegalprocess) Return Code = 0 (success).
1>(illegalprocess) Runtime = 0.029s.
1>(illegalprocess) Errors = 0.
1>(illegalprocess) Baselined Errors = 0.
1>(illegalprocess) Suppressed Errors = 0.
1>(illegalprocess) Messages = 4.
1>TRACEREPORT : Finished running the 'restricteddependency-cs' report in parallel:
1>(restricteddependency-cs) Return Code = 0 (success).
1>(restricteddependency-cs) Runtime = 0.028s.
1>(restricteddependency-cs) Errors = 0.
1>(restricteddependency-cs) Baselined Errors = 0.
1>(restricteddependency-cs) Suppressed Errors = 0.
1>(restricteddependency-cs) Messages = 4.
1>TRACEREPORT : Finished running the 'multiwritestrict-cs' report in parallel:
1>(multiwritestrict-cs) Return Code = 0 (success).
1>(multiwritestrict-cs) Runtime = 0.029s.
1>(multiwritestrict-cs) Errors = 0.
1>(multiwritestrict-cs) Baselined Errors = 0.
1>(multiwritestrict-cs) Suppressed Errors = 0.
1>(multiwritestrict-cs) Messages = 4.
1>TRACEREPORT : Finished running the 'tools-cs' report in parallel:
1>(tools-cs) Return Code = 0 (success).
1>(tools-cs) Runtime = 0.028s.
1>(tools-cs) Errors = 0.
1>(tools-cs) Baselined Errors = 0.
1>(tools-cs) Suppressed Errors = 0.
1>(tools-cs) Messages = 4.
1>TRACEREPORT : Finished running the 'chunkanalyzer-cs' report in parallel:
1>(chunkanalyzer-cs) Return Code = 0 (success).
1>(chunkanalyzer-cs) Runtime = 0.029s.
1>(chunkanalyzer-cs) Errors = 0.
1>(chunkanalyzer-cs) Baselined Errors = 0.
1>(chunkanalyzer-cs) Suppressed Errors = 0.
1>(chunkanalyzer-cs) Messages = 4.
1>TRACEREPORT : Finished running the 'includes-cs' report in parallel:
1>(includes-cs) Return Code = 0 (success).
1>(includes-cs) Runtime = 0.055s.
1>(includes-cs) Errors = 0.
1>(includes-cs) Baselined Errors = 0.
1>(includes-cs) Suppressed Errors = 0.
1>(includes-cs) Messages = 4.
1>TRACEREPORT : Finished running the 'badpublish-cs' report in parallel:
1>(badpublish-cs) Return Code = 0 (success).
1>(badpublish-cs) Runtime = 0.056s.
1>(badpublish-cs) Errors = 0.
1>(badpublish-cs) Baselined Errors = 0.
1>(badpublish-cs) Suppressed Errors = 0.
1>(badpublish-cs) Messages = 4.
1>TRACEREPORT : Finished running the 'unc-cs' report in parallel:
1>(unc-cs) Return Code = 0 (success).
1>(unc-cs) Runtime = 0.059s.
1>(unc-cs) Errors = 0.
1>(unc-cs) Baselined Errors = 0.
1>(unc-cs) Suppressed Errors = 0.
1>(unc-cs) Messages = 4.
1>TRACEREPORT : Finished running the 'ostools-cs' report in parallel:
1>(ostools-cs) Return Code = 0 (success).
1>(ostools-cs) Runtime = 0.063s.
1>(ostools-cs) Errors = 0.
1>(ostools-cs) Baselined Errors = 0.
1>(ostools-cs) Suppressed Errors = 0.
1>(ostools-cs) Messages = 4.
1>TRACEREPORT : Finished running the 'sdxwrite-cs' report in parallel:
1>(sdxwrite-cs) Return Code = 0 (success).
1>(sdxwrite-cs) Runtime = 0.063s.
1>(sdxwrite-cs) Errors = 0.
1>(sdxwrite-cs) Baselined Errors = 0.
1>(sdxwrite-cs) Suppressed Errors = 0.
1>(sdxwrite-cs) Messages = 4.
1>TRACEREPORT : Finished running the 'crosslayer-cs' report in parallel:
1>(crosslayer-cs) Return Code = 0 (success).
1>(crosslayer-cs) Runtime = 0.080s.
1>(crosslayer-cs) Errors = 0.
1>(crosslayer-cs) Baselined Errors = 0.
1>(crosslayer-cs) Suppressed Errors = 0.
1>(crosslayer-cs) Messages = 4.
1>TRACEREPORT : Finished running the 'nttree-cs' report in parallel:
1>(nttree-cs) Return Code = 0 (success).
1>(nttree-cs) Runtime = 0.083s.
1>(nttree-cs) Errors = 0.
1>(nttree-cs) Baselined Errors = 0.
1>(nttree-cs) Suppressed Errors = 0.
1>(nttree-cs) Messages = 4.
1>TRACEREPORT : Finished running the 'sourcesdep-cs' report in parallel:
1>(sourcesdep-cs) Return Code = 0 (success).
1>(sourcesdep-cs) Runtime = 0.126s.
1>(sourcesdep-cs) Erroinfo: Microsoft.Internal.Trace.Reporting.Reports.ExternalAccessReport.ExternalAccessReportExclusions[0]
1>      Loaded exclusions from 'e:\os\tools\Analyzer\amd64\DefaultConfigFiles\ExternalAccessReportExclusions.json'.
1>rs = 0.
1>(sourcesdep-cs) Baselined Errors = 0.
1>(sourcesdep-cs) Suppressed Errors = 0.
1>(sourcesdep-cs) Messages = 5.
1>TRACEREPORT : Finished running the 'vpacknttree-cs' report in parallel:
1>(vpacknttree-cs) Return Code = 0 (success).
1>(vpacknttree-cs) Runtime = 0.145s.
1>(vpacknttree-cs) Errors = 0.
1>(vpacknttree-cs) Baselined Errors = 0.
1>(vpacknttree-cs) Suppressed Errors = 0.
1>(vpacknttree-cs) Messages = 2.
1>TRACEREPORT : Finished running the 'externalaccess-cs' report in parallel:
1>(externalaccess-cs) Return Code = 0 (success).
1>(externalaccess-cs) Runtime = 0.156s.
1>(externalaccess-cs) Errors = 0.
1>(externalaccess-cs) Baselined Errors = 0.
1>(externalaccess-cs) Suppressed Errors = 0.
1>(externalaccess-cs) Messages = 4.
1>TRACEREPORT : Finished running the 'vpackstrict-cs' report in parallel:
1>(vpackstrict-cs) Return Code = 0 (success).
1>(vpackstrict-cs) Runtime = 0.156s.
1>(vpackstrict-cs) Errors = 0.
1>(vpackstrict-cs) Baselined Errors = 0.
1>(vpackstrict-cs) Suppressed Errors = 0.
1>(vpackstrict-cs) Messages = 11.
1>TRACEREPORT : Finished running the 'objroot-cs' report in parallel:
1>(objroot-cs) Return Code = 0 (success).
1>(objroot-cs) Runtime = 0.171s.
1>(objroot-cs) Errors = 0.
1>(objroot-cs) Baselined Errors = 0.
1>(objroot-cs) Suppressed Errors = 0.
1>(objroot-cs) Messages = 4.
1>TRACEREPORT : Finished running the 'tempfiles-cs' report in parallel:
1>(tempfiles-cs) Return Code = 0 (success).
1>(tempfiles-cs) Runtime = 0.330s.
1>(tempfiles-cs) Errors = 0.
1>(tempfiles-cs) Baselined Errors = 0.
1>(tempfiles-cs) Suppressed Errors = 0.
1>(tempfiles-cs) Messages = 4.
1>TRACEREPORT : Finished running the 'buildrules-cs' report in parallel:
1>(buildrules-cs) Return Code = 0 (success).
1>(buildrules-cs) Runtime = 0.386s.
1>(buildrules-cs) Errors = 0.
1>(buildrules-cs) Baselined Errors = 0.
1>(buildrules-cs) Suppressed Errors = 0.
1>(buildrules-cs) Messages = 3.
1>TRACEREPORT : All reports have finished running in parallel. Printing buffered output in-order...
1>illegalprocess : Report will run if all of the following constraints are met:
1>illegalprocess :  - Constraint 'Trace was generated in a fre environment' is 'True' (EVALUATED TO TRUE)
1>illegalprocess :  - Constraint 'Trace was generated in a BuildXL environment' is 'False' (EVALUATED TO FALSE)
1>illegalprocess : This report will run. ('env=IC; arch=AMD64; type=Fre; isBuildXL=False')
1>restricteddependency : Report will run if all of the following constraints are met:
1>restricteddependency :  - Constraint 'Trace was generated during Timebuild' is 'True' (EVALUATED TO FALSE)
1>restricteddependency :  - Constraint 'Trace was generated in a fre environment' is 'True' (EVALUATED TO TRUE)
1>restricteddependency : This report will be skipped. Use 'forcerun' if you want it to run anyways. ('env=IC; arch=AMD64; type=Fre; isBuildXL=False')
1>multiwritestrict : Report will run if all of the following constraints are met:
1>multiwritestrict :  - Constraint 'Trace was generated in a fre environment' is 'True' (EVALUATED TO TRUE)
1>multiwritestrict :  - Constraint 'Trace was generated in a BuildXL environment' is 'False' (EVALUATED TO FALSE)
1>multiwritestrict : This report will run. ('env=IC; arch=AMD64; type=Fre; isBuildXL=False')
1>tools : Report will run if all of the following constraints are met:
1>tools :  - Constraint 'Trace was generated in a fre environment' is 'True' (EVALUATED TO TRUE)
1>tools :  - Constraint 'Trace was generated in a BuildXL environment' is 'False' (EVALUATED TO FALSE)
1>tools : This report will run. ('env=IC; arch=AMD64; type=Fre; isBuildXL=False')
1>chunkanalyzer : Report will run if all of the following constraints are met:
1>chunkanalyzer :  - Constraint 'Trace was generated during Timebuild' is 'True' (EVALUATED TO FALSE)
1>chunkanalyzer :  - Constraint 'Trace was generated in a fre environment' is 'True' (EVALUATED TO TRUE)
1>chunkanalyzer : This report will be skipped. Use 'forcerun' if you want it to run anyways. ('env=IC; arch=AMD64; type=Fre; isBuildXL=False')
1>includes : Report will run if all of the following constraints are met:
1>includes :  - Constraint 'Trace was generated in a fre environment' is 'True' (EVALUATED TO TRUE)
1>includes :  - Constraint 'Trace was generated in a BuildXL environment' is 'False' (EVALUATED TO FALSE)
1>includes : This report will run. ('env=IC; arch=AMD64; type=Fre; isBuildXL=False')
1>badpublish : Report will run if all of the following constraints are met:
1>badpublish :  - Constraint 'Trace was generated in a fre environment' is 'True' (EVALUATED TO TRUE)
1>badpublish :  - Constraint 'Trace was generated in a BuildXL environment' is 'False' (EVALUATED TO FALSE)
1>badpublish : This report will run. ('env=IC; arch=AMD64; type=Fre; isBuildXL=False')
1>unc : Report will run if all of the following constraints are met:
1>unc :  - Constraint 'Trace was generated in a fre environment' is 'True' (EVALUATED TO TRUE)
1>unc :  - Constraint 'Trace was generated in a BuildXL environment' is 'False' (EVALUATED TO FALSE)
1>unc : This report will run. ('env=IC; arch=AMD64; type=Fre; isBuildXL=False')
1>ostools : Report will run if all of the following constraints are met:
1>ostools :  - Constraint 'Trace was generated in a fre environment' is 'True' (EVALUATED TO TRUE)
1>ostools :  - Constraint 'Trace was generated in a BuildXL environment' is 'False' (EVALUATED TO FALSE)
1>ostools : This report will run. ('env=IC; arch=AMD64; type=Fre; isBuildXL=False')
1>sdxwrite : Report will run if all of the following constraints are met:
1>sdxwrite :  - Constraint 'Trace was generated in a fre environment' is 'True' (EVALUATED TO TRUE)
1>sdxwrite :  - Constraint 'Trace was generated in a BuildXL environment' is 'False' (EVALUATED TO FALSE)
1>sdxwrite : This report will run. ('env=IC; arch=AMD64; type=Fre; isBuildXL=False')
1>crosslayer : Report will run if all of the following constraints are met:
1>crosslayer :  - Constraint 'Trace was generated in a fre environment' is 'True' (EVALUATED TO TRUE)
1>crosslayer :  - Constraint 'Trace was generated in a BuildXL environment' is 'False' (EVALUATED TO FALSE)
1>crosslayer : This report will run. ('env=IC; arch=AMD64; type=Fre; isBuildXL=False')
1>nttree : Report will run if all of the following constraints are met:
1>nttree :  - Constraint 'Trace was generated in a fre environment' is 'True' (EVALUATED TO TRUE)
1>nttree :  - Constraint 'Trace was generated in a BuildXL environment' is 'False' (EVALUATED TO FALSE)
1>nttree : This report will run. ('env=IC; arch=AMD64; type=Fre; isBuildXL=False')
1>sourcesdep : No run constraints were specified for this report.
1>sourcesdep : This report will run. ('env=IC; arch=AMD64; type=Fre; isBuildXL=False')
1>sourcesdep : First Loop: 19 ms
1>sourcesdep : Time taken for SuiteSparse circular dependency checks in sources.dep data = 9 ms
1>sourcesdep : Second Loop: 40 ms
1>vpacknttree : No run constraints were specified for this report.
1>vpacknttree : This report will run. ('env=IC; arch=AMD64; type=Fre; isBuildXL=False')
1>externalaccess : Report will run if all of the following constraints are met:
1>externalaccess :  - Constraint 'Trace was generated in a fre environment' is 'True' (EVALUATED TO TRUE)
1>externalaccess :  - Constraint 'Trace was generated in a BuildXL environment' is 'False' (EVALUATED TO FALSE)
1>externalaccess : This report will run. ('env=IC; arch=AMD64; type=Fre; isBuildXL=False')
1>vpackstrict : No run constraints were specified for this report.
1>vpackstrict : This report will run. ('env=IC; arch=AMD64; type=Fre; isBuildXL=False')
1>vpackstrict : --------------------------------------------------------------
1>vpackstrict :         Total number of 'vpack pull' calls found: 0
1>vpackstrict : ...Calls from ValidateVpackPackage.ps1 (ignored): 0
1>vpackstrict : ..........Number of calls checked in this report: 0
1>vpackstrict : --------------------------------------------------------------
1>vpackstrict :        Number of calls without /StrictVersioning: 0
1>vpackstrict : .............................baselined (ignored): 0
1>vpackstrict : ..........................non-baselined (errors): 0
1>vpackstrict : --------------------------------------------------------------
1>objroot : Report will run if all of the following constraints are met:
1>objroot :  - Constraint 'Trace was generated in a fre environment' is 'True' (EVALUATED TO TRUE)
1>objroot :  - Constraint 'Trace was generated in a BuildXL environment' is 'False' (EVALUATED TO FALSE)
1>objroot : This report will run. ('env=IC; arch=AMD64; type=Fre; isBuildXL=False')
1>tempfiles : Report will run if all of the following constraints are met:
1>tempfiles :  - Constraint 'Trace was generated in a fre environment' is 'True' (EVALUATED TO TRUE)
1>tempfiles :  - Constraint 'Trace was generated in a BuildXL environment' is 'False' (EVALUATED TO FALSE)
1>tempfiles : This report will run. ('env=IC; arch=AMD64; type=Fre; isBuildXL=False')
1>buildrules : Report will run if all of the following constraints are met:
1>buildrules :  - Constraint 'Trace was generated in a fre environment' is 'True' (EVALUATED TO TRUE)
1>buildrules : This report will run. ('env=IC; arch=AMD64; type=Fre; isBuildXL=False')
1>TRACEREPORT : All buffered output has been printed.
1>TRACEREPORT : Time taken to run all reports in parallel = 0.387s
1>ANALYZER : TraceReport time = 3.899s
1>ANALYZER (_tmain): Analyzer has completed and is exiting with return code '0' indicating success.
1>ANALYZER : PageFaultCount:     194594
1>ANALYZER : PeakWorkingSetSize: 696741888
1>ANALYZER : PeakPagefileUsage:  672526336
1>ANALYZER : ProcessCycleTime:   14724999223
1>ANALYZER : KernelTime:         0.547
1>ANALYZER : UserTime:           5.047
Build layers enabled: [DesktopEditions,ShellCommon,OSClient,ClientCore,GameCore,OnecoreUAP]
Number of excluded directories, not in layer set: 0


    11 directories scanned
    53 files compiled
    7 libraries built
    3 executables built
    18 files binplaced
