/*++

Copyright (c) Microsoft Corporation. All rights reserved.

Module Name:

    RagServiceManager.cpp

Abstract:

    Implementation of the RAG Service Manager component that manages the lifecycle
    of the netrag.exe service and provides HTTP client functionality for tool
    registration and semantic search operations.

Author:

    <PERSON><PERSON><PERSON> (r<PERSON><PERSON>) 07/24/2025

--*/

#include "pch.hxx"
#include "StringUtils.h"
#include "AimxConstants.h"
#include <nlohmann/json.hpp>

#include "RagServiceManager.cpp.tmh"

#include <cpprest/http_client.h>
#include <cpprest/json.h>
#include <chrono>
#include <thread>

#include "RagServiceManager.h"

using namespace web;
using namespace web::http;
using namespace web::http::client;

// Static member definitions
RagServiceManager* RagServiceManager::s_instance = nullptr;
std::mutex RagServiceManager::s_instanceMutex;

// Default configuration values
static const std::wstring DEFAULT_RAG_SERVICE_PATH = L"netRag.exe";
static const std::wstring DEFAULT_RAG_SERVICE_ARGS = L"--service";
static const std::wstring DEFAULT_RAG_BASE_URL = L"http://localhost:5000";
static const DWORD DEFAULT_HEALTH_CHECK_TIMEOUT_MS = 5000;
static const DWORD DEFAULT_STARTUP_TIMEOUT_MS = 30000;
static const DWORD DEFAULT_SHUTDOWN_TIMEOUT_MS = 10000;

RagServiceManager::RagServiceManager()
    : m_serviceProcessHandle(INVALID_HANDLE_VALUE)
    , m_serviceProcessId(0)
    , m_initialized(false)
    , m_serviceRunning(false)
{
    // Initialize default configuration
    m_config.serviceExecutablePath = DEFAULT_RAG_SERVICE_PATH;
    m_config.serviceArguments = DEFAULT_RAG_SERVICE_ARGS;
    m_config.baseUrl = DEFAULT_RAG_BASE_URL;
    m_config.healthCheckTimeoutMs = DEFAULT_HEALTH_CHECK_TIMEOUT_MS;
    m_config.startupTimeoutMs = DEFAULT_STARTUP_TIMEOUT_MS;
    m_config.shutdownTimeoutMs = DEFAULT_SHUTDOWN_TIMEOUT_MS;
}

RagServiceManager::~RagServiceManager()
{
    if (m_serviceProcessHandle != INVALID_HANDLE_VALUE)
    {
        CloseHandle(m_serviceProcessHandle);
        m_serviceProcessHandle = INVALID_HANDLE_VALUE;
    }
}

HRESULT
RagServiceManager::Initialize()
/*++

Routine Description:
    Initialize the RAG Service Manager singleton instance.

Arguments:
    None.

Return Value:
    S_OK on success, or an error HRESULT on failure.

--*/
{
    std::lock_guard<std::mutex> lock(s_instanceMutex);

    TraceInfo(AimxRagServiceMgr, "Initializing RAG Service Manager");

    if (s_instance != nullptr)
    {
        TraceInfo(AimxRagServiceMgr, "RAG Service Manager already initialized");
        return AIMX_S_COMPONENT_ALREADY_INITIALIZED;
    }

    s_instance = new (std::nothrow) RagServiceManager();
    if (s_instance == nullptr)
    {
        TraceErr(AimxRagServiceMgr, "Failed to allocate RAG Service Manager instance");
        return E_OUTOFMEMORY;
    }

    HRESULT hr = s_instance->InitializeInternal();
    if (FAILED(hr))
    {
        TraceErr(AimxRagServiceMgr, "Failed to initialize RAG Service Manager internal state: %!HRESULT!", hr);
        delete s_instance;
        s_instance = nullptr;
        return hr;
    }

    s_instance->m_initialized = true;

    TraceInfo(AimxRagServiceMgr, "RAG Service Manager initialized successfully");
    return S_OK;
}

void
RagServiceManager::Uninitialize()
/*++

Routine Description:
    Uninitialize the RAG Service Manager and cleanup resources.

Arguments:
    None.

Return Value:
    None.

--*/
{
    std::lock_guard<std::mutex> lock(s_instanceMutex);
    
    TraceInfo(AimxRagServiceMgr, "Uninitializing RAG Service Manager");
    
    if (s_instance != nullptr)
    {
        // Stop the service if it's running
        if (s_instance->m_serviceRunning)
        {
            s_instance->StopRagService();
        }

        delete s_instance;
        s_instance = nullptr;
    }
    
    TraceInfo(AimxRagServiceMgr, "RAG Service Manager uninitialized");
}

HRESULT
RagServiceManager::InitializeInternal()
/*++

Routine Description:
    Internal initialization method that creates HTTP client.

Arguments:
    None.

Return Value:
    S_OK on success, or an error HRESULT on failure.

--*/
{
    TraceInfo(AimxRagServiceMgr, "Initializing RAG Service Manager HTTP client");

    try
    {
        // Create HTTP client configuration with timeout settings
        http_client_config config;
        config.set_timeout(std::chrono::milliseconds(m_config.healthCheckTimeoutMs));

        // Create HTTP client
        m_httpClient = std::make_unique<http_client>(m_config.baseUrl, config);

        TraceInfo(AimxRagServiceMgr, "RAG Service Manager HTTP client initialized successfully");
        return S_OK;
    }
    catch (const std::exception& e)
    {
        TraceErr(AimxRagServiceMgr, "Exception during RAG Service Manager initialization: %s", e.what());
        return E_FAIL;
    }
    catch (...)
    {
        TraceErr(AimxRagServiceMgr, "Unknown exception during RAG Service Manager initialization");
        return E_FAIL;
    }
}

HRESULT
RagServiceManager::StartRagService()
/*++

Routine Description:
    Start the RAG service process and wait for it to be ready.

Arguments:
    None.

Return Value:
    S_OK on success, or an error HRESULT on failure.

--*/
{
    if (s_instance == nullptr)
    {
        return AIMX_E_COMPONENT_NOT_INITIALIZED;
    }

    std::lock_guard<std::mutex> lock(s_instance->m_serviceMutex);

    TraceInfo(AimxRagServiceMgr, "Starting RAG service");

    if (s_instance->m_serviceRunning)
    {
        TraceInfo(AimxRagServiceMgr, "RAG service is already running");
        return S_OK;
    }

    HRESULT hr = s_instance->StartServiceProcess();
    if (FAILED(hr))
    {
        TraceErr(AimxRagServiceMgr, "Failed to start RAG service process: %!HRESULT!", hr);
        return hr;
    }

    hr = s_instance->WaitForServiceReady();
    if (FAILED(hr))
    {
        TraceErr(AimxRagServiceMgr, "RAG service failed to become ready: %!HRESULT!", hr);
        s_instance->TerminateServiceProcess();
        return hr;
    }

    s_instance->m_serviceRunning = true;
    TraceInfo(AimxRagServiceMgr, "RAG service started successfully");
    return S_OK;
}

HRESULT
RagServiceManager::StopRagService()
/*++

Routine Description:
    Stop the RAG service process.

Arguments:
    None.

Return Value:
    S_OK on success, or an error HRESULT on failure.

--*/
{
    if (s_instance == nullptr)
    {
        return AIMX_E_COMPONENT_NOT_INITIALIZED;
    }

    std::lock_guard<std::mutex> lock(s_instance->m_serviceMutex);

    TraceInfo(AimxRagServiceMgr, "Stopping RAG service");

    if (!s_instance->m_serviceRunning)
    {
        TraceInfo(AimxRagServiceMgr, "RAG service is not running");
        return S_OK;
    }

    HRESULT hr = s_instance->TerminateServiceProcess();
    if (FAILED(hr))
    {
        TraceWarn(AimxRagServiceMgr, "Failed to terminate RAG service process gracefully: %!HRESULT!", hr);
        // Continue anyway
    }

    s_instance->m_serviceRunning = false;
    TraceInfo(AimxRagServiceMgr, "RAG service stopped");
    return S_OK;
}

HRESULT
RagServiceManager::CheckServiceHealth()
/*++

Routine Description:
    Check if the RAG service is healthy by calling its health endpoint.

Arguments:
    None.

Return Value:
    S_OK if service is healthy, or an error HRESULT on failure.

--*/
{
    if (s_instance == nullptr)
    {
        return AIMX_E_COMPONENT_NOT_INITIALIZED;
    }

    TraceInfo(AimxRagServiceMgr, "Checking RAG service health");

    try
    {
        nlohmann::json response;
        HRESULT hr = s_instance->SendHttpGetRequest(L"/api/mcptools/health", response);
        if (FAILED(hr))
        {
            TraceErr(AimxRagServiceMgr, "Health check request failed: %!HRESULT!", hr);
            return hr;
        }

        // Check if response indicates healthy status
        if (response.contains("status") && response["status"] == "healthy")
        {
            TraceInfo(AimxRagServiceMgr, "RAG service is healthy");
            return S_OK;
        }
        else
        {
            TraceWarn(AimxRagServiceMgr, "RAG service health check returned non-healthy status");
            return E_FAIL;
        }
    }
    catch (const std::exception& e)
    {
        TraceErr(AimxRagServiceMgr, "Exception during health check: %s", e.what());
        return E_FAIL;
    }
}

// Server registration is no longer needed - we only register tools directly

HRESULT
RagServiceManager::RegisterTool(
    _In_ const MCP_TOOL_INFO& toolInfo
    )
/*++

Routine Description:
    Register a tool with the RAG service using simplified tool-centric approach.

Arguments:
    toolInfo - Tool information structure

Return Value:
    S_OK on success, or an error HRESULT on failure.

--*/
{
    if (s_instance == nullptr)
    {
        return AIMX_E_COMPONENT_NOT_INITIALIZED;
    }

    // Use the tool GUID directly as the RAG tool ID
    std::wstring ragToolId = GuidToString(toolInfo.toolGuid);
    TraceInfo(AimxRagServiceMgr, "Registering tool with RAG service: %ws (ID: %ws)",
             toolInfo.toolName.c_str(), ragToolId.c_str());

    try
    {
        // Create tool registration request
        nlohmann::json requestBody = {
            {"Id", WideToUtf8(ragToolId)},
            {"Name", WideToUtf8(toolInfo.toolName)},
            {"Description", WideToUtf8(toolInfo.description)},
            {"Category", "MCP Tool"},
            {"Version", "1.0.0"}
        };

        // Add input schema if available
        if (!toolInfo.inputSchema.is_null())
        {
            requestBody["InputSchema"] = toolInfo.inputSchema.dump();
        }

        // Add examples and tags based on tool name and description
        std::vector<std::string> examples = {
            WideToUtf8(L"Use " + toolInfo.toolName + L" to " + toolInfo.description)
        };
        requestBody["Examples"] = examples;

        std::vector<std::string> tags = {
            WideToUtf8(toolInfo.serverName),
            WideToUtf8(toolInfo.toolName),
            "mcp",
            "tool"
        };
        requestBody["Tags"] = tags;

        nlohmann::json response;
        HRESULT hr = s_instance->SendHttpRequest(L"/api/mcptools/register", methods::POST, requestBody, response);
        if (FAILED(hr))
        {
            TraceErr(AimxRagServiceMgr, "Tool registration request failed: %!HRESULT!", hr);
            return hr;
        }

        TraceInfo(AimxRagServiceMgr, "Tool registered successfully with RAG service: %ws", toolInfo.toolName.c_str());
        return S_OK;
    }
    catch (const std::exception& e)
    {
        TraceErr(AimxRagServiceMgr, "Exception during tool registration: %s", e.what());
        return E_FAIL;
    }
}

HRESULT
RagServiceManager::SearchTools(
    _In_ const std::wstring& query,
    _In_ int topK,
    _Out_ std::vector<RAG_TOOL_SEARCH_RESULT>& results
    )
/*++

Routine Description:
    Search for tools using semantic similarity.

Arguments:
    query - Search query string
    topK - Number of top results to return
    results - Output vector of search results

Return Value:
    S_OK on success, or an error HRESULT on failure.

--*/
{
    if (s_instance == nullptr)
    {
        return AIMX_E_COMPONENT_NOT_INITIALIZED;
    }

    TraceInfo(AimxRagServiceMgr, "Searching tools with query: %ws (topK: %d)", query.c_str(), topK);

    try
    {
        // Create search request
        nlohmann::json requestBody = {
            {"Query", WideToUtf8(query)},
            {"TopK", topK}
        };

        nlohmann::json response;
        HRESULT hr = s_instance->SendHttpRequest(L"/api/mcptools/search", methods::POST, requestBody, response);
        if (FAILED(hr))
        {
            TraceErr(AimxRagServiceMgr, "Tool search request failed: %!HRESULT!", hr);
            return hr;
        }

        // Parse search results
        results.clear();

        // Debug: dump the entire response to understand the structure
        TraceInfo(AimxRagServiceMgr, "RAG search response JSON: %s", response.dump(2).c_str());

        if (response.contains("tools") && response["tools"].is_array())
        {
            for (const auto& toolResult : response["tools"])
            {
                RAG_TOOL_SEARCH_RESULT result;

                // Debug: dump each tool result
                TraceInfo(AimxRagServiceMgr, "Processing tool result: %s", toolResult.dump(2).c_str());

                // The .NET service returns { "tool": {...}, "score": ..., "matchReason": ... }
                // We need to access the nested "tool" object
                if (toolResult.contains("tool") && toolResult["tool"].is_object())
                {
                    const auto& tool = toolResult["tool"];

                    if (tool.contains("id"))
                    {
                        result.toolId = Utf8ToWide(tool["id"].get<std::string>());
                    }

                    if (tool.contains("name"))
                    {
                        result.toolName = Utf8ToWide(tool["name"].get<std::string>());
                    }

                    if (tool.contains("description"))
                    {
                        result.description = Utf8ToWide(tool["description"].get<std::string>());
                    }
                }

                if (toolResult.contains("score"))
                {
                    result.score = toolResult["score"].get<double>();
                }

                results.push_back(result);
            }
        }

        TraceInfo(AimxRagServiceMgr, "Tool search completed successfully, found %d results", static_cast<int>(results.size()));
        return S_OK;
    }
    catch (const std::exception& e)
    {
        TraceErr(AimxRagServiceMgr, "Exception during tool search: %s", e.what());
        return E_FAIL;
    }
}

HRESULT
RagServiceManager::GetServiceStatistics(
    _Out_ nlohmann::json& statistics
    )
/*++

Routine Description:
    Get service statistics from the RAG service.

Arguments:
    statistics - Output JSON containing service statistics

Return Value:
    S_OK on success, or an error HRESULT on failure.

--*/
{
    if (s_instance == nullptr)
    {
        return AIMX_E_COMPONENT_NOT_INITIALIZED;
    }

    TraceInfo(AimxRagServiceMgr, "Getting RAG service statistics");

    try
    {
        HRESULT hr = s_instance->SendHttpGetRequest(L"/api/mcptools/statistics", statistics);
        if (FAILED(hr))
        {
            TraceErr(AimxRagServiceMgr, "Statistics request failed: %!HRESULT!", hr);
            return hr;
        }

        TraceInfo(AimxRagServiceMgr, "Service statistics retrieved successfully");
        return S_OK;
    }
    catch (const std::exception& e)
    {
        TraceErr(AimxRagServiceMgr, "Exception during statistics retrieval: %s", e.what());
        return E_FAIL;
    }
}

// Private helper methods implementation

HRESULT
RagServiceManager::SendHttpRequest(
    _In_ const std::wstring& endpoint,
    _In_ const http::method& method,
    _In_ const nlohmann::json& requestBody,
    _Out_ nlohmann::json& responseBody
    )
/*++

Routine Description:
    Send an HTTP request to the RAG service.

Arguments:
    endpoint - API endpoint path
    method - HTTP method (GET, POST, etc.)
    requestBody - JSON request body
    responseBody - Output JSON response body

Return Value:
    S_OK on success, or an error HRESULT on failure.

--*/
{
    try
    {
        // Send the request using method and endpoint path
        pplx::task<http_response> responseTask;

        if (method == methods::POST && !requestBody.is_null())
        {
            // For POST requests with body, create request object
            http_request request(method);
            request.set_request_uri(endpoint);
            request.headers().set_content_type(U("application/json"));

            std::string jsonStr = requestBody.dump();
            std::wstring jsonWide = Utf8ToWide(jsonStr);
            request.set_body(jsonWide, U("application/json"));

            responseTask = m_httpClient->request(request);
        }
        else
        {
            // For GET requests or POST without body, create request object
            http_request request(method);
            request.set_request_uri(endpoint);
            responseTask = m_httpClient->request(request);
        }
        
        auto response = responseTask.get();

        // Check status code
        if (response.status_code() != status_codes::OK)
        {
            TraceErr(AimxRagServiceMgr, "HTTP request failed with status code: %d", static_cast<int>(response.status_code()));
            return E_FAIL;
        }

        // Extract response body
        auto responseBodyTask = response.extract_string();
        std::wstring responseStr = responseBodyTask.get();
        std::string responseUtf8 = WideToUtf8(responseStr);

        // Parse JSON response
        responseBody = nlohmann::json::parse(responseUtf8);

        return S_OK;
    }
    catch (const std::exception& e)
    {
        TraceErr(AimxRagServiceMgr, "Exception during HTTP request: %s", e.what());
        return E_FAIL;
    }
}

HRESULT
RagServiceManager::SendHttpGetRequest(
    _In_ const std::wstring& endpoint,
    _Out_ nlohmann::json& responseBody
    )
/*++

Routine Description:
    Send an HTTP GET request to the RAG service.

Arguments:
    endpoint - API endpoint path
    responseBody - Output JSON response body

Return Value:
    S_OK on success, or an error HRESULT on failure.

--*/
{
    nlohmann::json emptyBody;
    return SendHttpRequest(endpoint, methods::GET, emptyBody, responseBody);
}

HRESULT
RagServiceManager::StartServiceProcess()
/*++

Routine Description:
    Start the RAG service process.

Arguments:
    None.

Return Value:
    S_OK on success, or an error HRESULT on failure.

--*/
{
    TraceInfo(AimxRagServiceMgr, "Starting RAG service process: %ws %ws",
             m_config.serviceExecutablePath.c_str(), m_config.serviceArguments.c_str());

    // Create command line
    std::wstring commandLine = m_config.serviceExecutablePath + L" " + m_config.serviceArguments;

    // Initialize process structures
    STARTUPINFO si = { 0 };
    PROCESS_INFORMATION pi = { 0 };
    si.cb = sizeof(si);

    // Create the process
    BOOL result = CreateProcess(
        nullptr,                    // Application name
        const_cast<LPWSTR>(commandLine.c_str()), // Command line
        nullptr,                    // Process security attributes
        nullptr,                    // Thread security attributes
        FALSE,                      // Inherit handles
        CREATE_NO_WINDOW,           // Creation flags
        nullptr,                    // Environment
        nullptr,                    // Current directory
        &si,                        // Startup info
        &pi                         // Process information
    );

    if (!result)
    {
        DWORD error = GetLastError();
        TraceErr(AimxRagServiceMgr, "Failed to create RAG service process: %!WINERROR!", error);
        return HRESULT_FROM_WIN32(error);
    }

    // Store process information
    m_serviceProcessHandle = pi.hProcess;
    m_serviceProcessId = pi.dwProcessId;

    // Close thread handle as we don't need it
    CloseHandle(pi.hThread);

    TraceInfo(AimxRagServiceMgr, "RAG service process started with PID: %d", m_serviceProcessId);
    return S_OK;
}

HRESULT
RagServiceManager::WaitForServiceReady()
/*++

Routine Description:
    Wait for the RAG service to become ready by polling its health endpoint.

Arguments:
    None.

Return Value:
    S_OK on success, or an error HRESULT on failure.

--*/
{
    TraceInfo(AimxRagServiceMgr, "Waiting for RAG service to become ready");

    DWORD startTime = GetTickCount();
    DWORD timeout = m_config.startupTimeoutMs;

    while (GetTickCount() - startTime < timeout)
    {
        // Check if process is still running
        DWORD exitCode;
        if (GetExitCodeProcess(m_serviceProcessHandle, &exitCode) && exitCode != STILL_ACTIVE)
        {
            TraceErr(AimxRagServiceMgr, "RAG service process exited unexpectedly with code: %d", exitCode);
            return E_FAIL;
        }

        // Try health check
        HRESULT hr = CheckServiceHealth();
        if (SUCCEEDED(hr))
        {
            TraceInfo(AimxRagServiceMgr, "RAG service is ready");
            return S_OK;
        }

        // Wait before next attempt
        Sleep(1000);
    }

    TraceErr(AimxRagServiceMgr, "Timeout waiting for RAG service to become ready");
    return E_FAIL;
}

HRESULT
RagServiceManager::TerminateServiceProcess()
/*++

Routine Description:
    Terminate the RAG service process.

Arguments:
    None.

Return Value:
    S_OK on success, or an error HRESULT on failure.

--*/
{
    if (m_serviceProcessHandle == INVALID_HANDLE_VALUE)
    {
        TraceInfo(AimxRagServiceMgr, "No RAG service process to terminate");
        return S_OK;
    }

    TraceInfo(AimxRagServiceMgr, "Terminating RAG service process (PID: %d)", m_serviceProcessId);

    // Try graceful termination first
    if (!TerminateProcess(m_serviceProcessHandle, 0))
    {
        DWORD error = GetLastError();
        TraceWarn(AimxRagServiceMgr, "Failed to terminate RAG service process: %!WINERROR!", error);
        return HRESULT_FROM_WIN32(error);
    }

    // Wait for process to exit
    DWORD waitResult = WaitForSingleObject(m_serviceProcessHandle, m_config.shutdownTimeoutMs);
    if (waitResult != WAIT_OBJECT_0)
    {
        TraceWarn(AimxRagServiceMgr, "RAG service process did not exit within timeout");
    }

    // Clean up handles
    CloseHandle(m_serviceProcessHandle);
    m_serviceProcessHandle = INVALID_HANDLE_VALUE;
    m_serviceProcessId = 0;

    TraceInfo(AimxRagServiceMgr, "RAG service process terminated");
    return S_OK;
}
