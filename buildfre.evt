ts=438 Merging config files using BUILD_CONFIG_FILE=e:\os\obj\amd64fre\objfre\amd64\build-exe-merged.config
ts=1047 queue pregraph command
ts=1047 run PreGraph commands build_pre_graph
ts=1734 queue prebuild command
ts=1734 run preprocess commands build_pre_process
ts=3109 initializing DBB query
ts=11453 reading parent chain of e:\os\src\onecore\ds\ds\src\aimx\prod
ts=11453 reading parent chain of e:\os\src\onecore\ds\ds\src\aimx
ts=11453 reading parent chain of e:\os\src\onecore\ds\ds\src
ts=11453 reading parent chain of e:\os\src\onecore\ds\ds
ts=11453 reading parent chain of e:\os\src\onecore\ds
ts=11453 reading parent chain of e:\os\src\onecore
ts=11453 reading parent chain of e:\os\src
ts=11453 scanning focus directory e:\os\src\onecore\ds\ds\src\aimx\prod
ts=11516 DELETE OUTPUT LIST: Invalid output entry e:\os\obj\amd64fre\temp\124a2453e3a9b802e5958c09e1867128\cl_1.rsp in e:\os\src\onecore\ds\ds\src\aimx\prod\aimxsrv\server (PASS1) 
ts=11531 DELETE OUTPUT LIST: Invalid output entry e:\os\obj\amd64fre\temp\124a2453e3a9b802e5958c09e1867128\tmp_18240_1753384535228296500.tmp in e:\os\src\onecore\ds\ds\src\aimx\prod\aimxsrv\server (PASS1) 
ts=11531 DELETE OUTPUT LIST: Invalid output entry e:\os\obj\amd64fre\temp\3758bf2cbabe78c95a06bc6102f6078a\cl_1.rsp in e:\os\src\onecore\ds\ds\src\aimx\prod\aimxsrv\server (PASS1) 
ts=11531 DELETE OUTPUT LIST: Invalid output entry e:\os\obj\amd64fre\temp\3758bf2cbabe78c95a06bc6102f6078a\tmp_7672_1753383565882754700.tmp in e:\os\src\onecore\ds\ds\src\aimx\prod\aimxsrv\server (PASS1) 
ts=11531 DELETE OUTPUT LIST: Invalid output entry e:\os\obj\amd64fre\temp\daf8c52851d63b9d99a386ca6725640a\cl_1.rsp in e:\os\src\onecore\ds\ds\src\aimx\prod\aimxsrv\server (PASS1) 
ts=11531 DELETE OUTPUT LIST: Invalid output entry e:\os\obj\amd64fre\temp\daf8c52851d63b9d99a386ca6725640a\tmp_36436_1753383439495145200.tmp in e:\os\src\onecore\ds\ds\src\aimx\prod\aimxsrv\server (PASS1) 
ts=11531 (PASS0) onecore\ds\ds\src\aimx\prod\admcpsvr(8): sent to build client (4).
ts=11547 (PASS0) onecore\ds\ds\src\aimx\prod\admcpsvr(8): distributed work started.
ts=11547 (PASS0) onecore\ds\ds\src\aimx\prod\admcpsvr(8): distributed work completed.
ts=11547 (PASS0) onecore\ds\ds\src\aimx\prod\aimxsrv\idl(16): sent to build client (1).
ts=11547 (PASS0) onecore\ds\ds\src\aimx\prod\aimxsrv\idl(16): distributed work started.
ts=11563 (PASS0) onecore\ds\ds\src\aimx\prod\aimxsrv\idl(16): distributed work completed.
ts=11594 DELETE OUTPUT LIST: Invalid output entry e:\os\obj\amd64fre\temp\0c6f847bdc92d37324c0b5ce0bfa4022\post_link_concurrent.log in e:\os\src\onecore\ds\ds\src\aimx\prod\aimxsrv\serviceinstaller (PASS2) 
ts=11594 DELETE OUTPUT LIST: Invalid output entry e:\os\obj\amd64fre\temp\e5028220d3e54c6d9f1c2b3aa639d000\post_link_concurrent.log in e:\os\src\onecore\ds\ds\src\aimx\prod\aimxsrv\serviceinstaller (PASS2) 
ts=11594 DELETE OUTPUT LIST: Invalid output entry e:\os\obj\amd64fre\temp\f691fe8856475893b780fa4024dc8718\post_link_concurrent.log in e:\os\src\onecore\ds\ds\src\aimx\prod\aimxsrv\serviceinstaller (PASS2) 
ts=11594 (PASS0) onecore\ds\ds\src\aimx\prod\mcpserversample\exe(22): sent to build client (4).
ts=11594 DELETE OUTPUT LIST: Invalid output entry e:\os\obj\amd64fre\temp\96466b4022d5aa4d3594607b20fa280d\cl_1.rsp in e:\os\src\onecore\ds\ds\src\aimx\prod\aimxsrv\dll (PASS1) 
ts=11594 DELETE OUTPUT LIST: Invalid output entry e:\os\obj\amd64fre\temp\96466b4022d5aa4d3594607b20fa280d\tmp_17820_1753383406307958600.tmp in e:\os\src\onecore\ds\ds\src\aimx\prod\aimxsrv\dll (PASS1) 
ts=11594 DELETE OUTPUT LIST: Invalid output entry e:\os\obj\amd64fre\temp\d158428e15fc790632c21142e4d63345\cl_1.rsp in e:\os\src\onecore\ds\ds\src\aimx\prod\aimxsrv\dll (PASS1) 
ts=11594 DELETE OUTPUT LIST: Invalid output entry e:\os\obj\amd64fre\temp\d158428e15fc790632c21142e4d63345\tmp_41500_1753383534584058400.tmp in e:\os\src\onecore\ds\ds\src\aimx\prod\aimxsrv\dll (PASS1) 
ts=11594 DELETE OUTPUT LIST: Invalid output entry e:\os\obj\amd64fre\temp\f872c2b0eb09c4f9c1ceb223e86ff57d\cl_1.rsp in e:\os\src\onecore\ds\ds\src\aimx\prod\aimxsrv\dll (PASS1) 
ts=11594 DELETE OUTPUT LIST: Invalid output entry e:\os\obj\amd64fre\temp\f872c2b0eb09c4f9c1ceb223e86ff57d\tmp_13396_1753384505879051800.tmp in e:\os\src\onecore\ds\ds\src\aimx\prod\aimxsrv\dll (PASS1) 
ts=11594 (PASS0) onecore\ds\ds\src\aimx\prod\aimxsrv\server(18): sent to build client (1).
ts=11594 (PASS0) onecore\ds\ds\src\aimx\prod\mcpserversample\exe(22): distributed work started.
ts=11594 (PASS0) onecore\ds\ds\src\aimx\prod\mcpserversample\exe(22): distributed work completed.
ts=11625 (PASS0) onecore\ds\ds\src\aimx\prod\aimxsrv\server(18): distributed work started.
ts=11625 (PASS0) onecore\ds\ds\src\aimx\prod\aimxsrv\server(18): distributed work completed.
ts=11953 (PASS0) onecore\ds\ds\src\aimx\prod\llmclientlib(11): sent to build client (5).
ts=11969 (PASS0) onecore\ds\ds\src\aimx\prod\llmclientlib(11): distributed work started.
ts=11969 (PASS0) onecore\ds\ds\src\aimx\prod\llmclientlib(11): distributed work completed.
ts=11969 (PASS0) onecore\ds\ds\src\aimx\prod\aimxsrv\serviceinstaller(19): sent to build client (1).
ts=11969 DELETE OUTPUT LIST: Invalid output entry e:\os\obj\amd64fre\temp\746fbf10d9010ff70525577245e25a98\_asmid.inc in e:\os\src\onecore\ds\ds\src\aimx\prod\aimxsrv\powershell (PASS0) 
ts=11969 DELETE OUTPUT LIST: Invalid output entry e:\os\obj\amd64fre\temp\746fbf10d9010ff70525577245e25a98\_asmid.xml in e:\os\src\onecore\ds\ds\src\aimx\prod\aimxsrv\powershell (PASS0) 
ts=11984 (PASS0) onecore\ds\ds\src\aimx\prod\aimxsrv\serviceinstaller(19): distributed work started.
ts=11984 (PASS0) onecore\ds\ds\src\aimx\prod\aimxsrv\serviceinstaller(19): distributed work completed.
ts=11984 (PASS0) onecore\ds\ds\src\aimx\prod\mcpprotocollib(12): sent to build client (5).
ts=11984 (PASS0) onecore\ds\ds\src\aimx\prod\mcpprotocollib(12): distributed work started.
ts=11984 (PASS0) onecore\ds\ds\src\aimx\prod\mcpprotocollib(12): distributed work completed.
ts=12000 (PASS0) onecore\ds\ds\src\aimx\prod\aimxsrv\dll(15): sent to build client (1).
ts=12016 (PASS0) onecore\ds\ds\src\aimx\prod\aimxsrv\client\lib(21): sent to build client (5).
ts=12016 (PASS0) onecore\ds\ds\src\aimx\prod\aimxsrv\client\lib(21): distributed work started.
ts=12016 (PASS0) onecore\ds\ds\src\aimx\prod\aimxsrv\dll(15): distributed work started.
ts=12016 (PASS0) onecore\ds\ds\src\aimx\prod\aimxsrv\client\lib(21): distributed work completed.
ts=12016 (PASS0) onecore\ds\ds\src\aimx\prod\aimxsrv\dll(15): distributed work completed.
ts=12031 DELETE OUTPUT LIST: Invalid output entry e:\os\obj\amd64fre\temp\9a53f73b30ce827c546f714301b78f94\post_link_concurrent.log in e:\os\src\onecore\ds\ds\src\aimx\prod\aimxsrv\client\dll (PASS2) 
ts=12047 DELETE OUTPUT LIST: Invalid output entry e:\os\obj\amd64fre\temp\ac58938a9757bfc9c937748bf0d181a9\_generated.cs in e:\os\src\onecore\ds\ds\src\aimx\prod\aimxsrv\powershell (PASS1) 
ts=12047 DELETE OUTPUT LIST: Invalid output entry e:\os\obj\amd64fre\temp\ac58938a9757bfc9c937748bf0d181a9\aimxpsh.asmmeta_temp in e:\os\src\onecore\ds\ds\src\aimx\prod\aimxsrv\powershell (PASS1) 
ts=12047 DELETE OUTPUT LIST: Invalid output entry e:\os\obj\amd64fre\temp\ac58938a9757bfc9c937748bf0d181a9\aimxpsh.metadata_dll in e:\os\src\onecore\ds\ds\src\aimx\prod\aimxsrv\powershell (PASS1) 
ts=12047 (PASS0) onecore\ds\ds\src\aimx\prod\aimxsrv\client\dll(20): sent to build client (4).
ts=12063 DELETE OUTPUT LIST: Invalid output entry e:\os\obj\amd64fre\temp\1534c67d2ac837082c681fee14f021b8\binp_2.rsp in e:\os\src\onecore\ds\ds\src\aimx\prod\aimxsrv\powershell (PASS2) 
ts=12063 DELETE OUTPUT LIST: Invalid output entry e:\os\obj\amd64fre\temp\1534c67d2ac837082c681fee14f021b8\post_link_concurrent.log in e:\os\src\onecore\ds\ds\src\aimx\prod\aimxsrv\powershell (PASS2) 
ts=12063 (PASS0) onecore\ds\ds\src\aimx\prod\mcpserversample\lib(23): sent to build client (1).
ts=12063 (PASS0) onecore\ds\ds\src\aimx\prod\aimxsrv\client\dll(20): distributed work started.
ts=12063 (PASS0) onecore\ds\ds\src\aimx\prod\aimxsrv\client\dll(20): distributed work completed.
ts=12078 (PASS0) onecore\ds\ds\src\aimx\prod\mcpserversample\lib(23): distributed work started.
ts=12078 (PASS0) onecore\ds\ds\src\aimx\prod\mcpserversample\lib(23): distributed work completed.
ts=12078 (PASS0) onecore\ds\ds\src\aimx\prod\aimxsrv\powershell(17): sent to build client (5).
ts=12078 (PASS0) onecore\ds\ds\src\aimx\prod\aimxsrv\powershell(17): distributed work started.
ts=12078 (PASS0) onecore\ds\ds\src\aimx\prod\aimxsrv\powershell(17): distributed work completed.
ts=12547 (PASS0) onecore\ds\ds\src\aimx\prod\cpprestsdk(10): sent to build client (1).
ts=14391 (PASS0) onecore\ds\ds\src\aimx\prod\cpprestsdk(10): distributed work started.
ts=14391 (PASS0) onecore\ds\ds\src\aimx\prod\cpprestsdk(10): distributed work completed.
ts=14438 scanning focus directory e:\os\src\onecore\ds\ds\src\aimx\prod
ts=14453 BUILD: Processing dependencies...
ts=14453 (PASS1) onecore\ds\ds\src\aimx\prod\aimxsrv\server(18): pre-build pending
ts=14453 (PASS1) onecore\ds\ds\src\aimx\prod\aimxsrv\dll(15): pre-build pending
ts=14453 BUILD: Scanning for circular dependencies...
ts=14453 BUILD: Processing dependencies complete
ts=14453 (onecore\ds\ds\src\aimx\prod\aimxsrv\server) e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\aimxsrv\server\objfre\amd64\_objects.mac created (133978596955111969), sources (133978544472720915), sources recorded (133978544472720915)
ts=14469 (onecore\ds\ds\src\aimx\prod\mcpprotocollib) e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\mcpprotocollib\objfre\amd64\_objects.mac created (133978596955288603), sources (133977770786641380), sources recorded (133977770786641380)
ts=14469 (onecore\ds\ds\src\aimx\prod\llmclientlib) e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\llmclientlib\objfre\amd64\_objects.mac created (133978596955306577), sources (133969941762565131), sources recorded (133969941762565131)
ts=14469 (onecore\ds\ds\src\aimx\prod\aimxsrv\serviceinstaller) e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\aimxsrv\serviceinstaller\objfre\amd64\_objects.mac created (133978596955306577), sources (133969941762565131), sources recorded (133969941762565131)
ts=14469 (onecore\ds\ds\src\aimx\prod\aimxsrv\dll) e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\aimxsrv\dll\objfre\amd64\_objects.mac created (133978596955306577), sources (133977770693216991), sources recorded (133977770693216991)
ts=14469 (onecore\ds\ds\src\aimx\prod\aimxsrv\idl) e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\aimxsrv\idl\objfre\amd64\_objects.mac created (133978596955306577), sources (133969941762565131), sources recorded (133969941762565131)
ts=14469 (onecore\ds\ds\src\aimx\prod\mcpserversample\lib) e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\mcpserversample\lib\objfre\amd64\_objects.mac created (133978596955306577), sources (133977770786641380), sources recorded (133977770786641380)
ts=14469 (onecore\ds\ds\src\aimx\prod\mcpserversample\exe) e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\mcpserversample\exe\objfre\amd64\_objects.mac created (133978596955306577), sources (133977770786641380), sources recorded (133977770786641380)
ts=14469 (onecore\ds\ds\src\aimx\prod\admcpsvr) e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\admcpsvr\objfre\amd64\_objects.mac created (133978596955306577), sources (133977770690027574), sources recorded (133977770690027574)
ts=14469 (onecore\ds\ds\src\aimx\prod\aimxsrv\client\dll) e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\aimxsrv\client\dll\objfre\amd64\_objects.mac created (133978596955306577), sources (133969941762565131), sources recorded (133969941762565131)
ts=14469 (onecore\ds\ds\src\aimx\prod\aimxsrv\client\lib) e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\aimxsrv\client\lib\objfre\amd64\_objects.mac created (133978596955306577), sources (133969941762565131), sources recorded (133969941762565131)
ts=14484 (onecore\ds\ds\src\aimx\prod\aimxsrv\powershell) e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\aimxsrv\powershell\objfre\amd64\_objects.mac created (133978596955426392), sources (133970660830610246), sources recorded (133970660830610246)
ts=14500 (PASS0) onecore\ds\ds\src\aimx\prod\mcpserversample(13): sent to build client (5).
ts=14500 (PASS0) onecore\ds\ds\src\aimx\prod\mcpserversample(13): distributed work started.
ts=14500 (PASS0) onecore\ds\ds\src\aimx\prod\mcpserversample(13): distributed work completed.
ts=14516 DELETE OUTPUT LIST: Invalid output entry e:\os\obj\amd64fre\temp\e305a6c75ac2d497116bc8a0e4e4c88e\lnk.rsp in e:\os\src\onecore\ds\ds\src\aimx\prod\semanticrag\examples (PASS2) 
ts=14531 (PASS0) onecore\ds\ds\src\aimx\prod\semanticrag\examples(0): sent to build client (1).
ts=14531 (PASS0) onecore\ds\ds\src\aimx\prod\semanticrag\examples(0): distributed work started.
ts=14531 (PASS0) onecore\ds\ds\src\aimx\prod\semanticrag\examples(0): distributed work completed.
ts=14547 DELETE OUTPUT LIST: Invalid output entry e:\os\obj\amd64fre\temp\e305a6c75ac2d497116bc8a0e4e4c88e\lnk.rsp in e:\os\src\onecore\ds\ds\src\aimx\prod\semanticrag\examples (PASS2) 
ts=14563 (PASS0) onecore\ds\ds\src\aimx\prod\semanticrag\examples(0): sent to build client (5).
ts=14563 (PASS0) onecore\ds\ds\src\aimx\prod\semanticrag\examples(0): distributed work started.
ts=14563 (PASS0) onecore\ds\ds\src\aimx\prod\semanticrag\examples(0): distributed work completed.
ts=14578 DELETE OUTPUT LIST: Invalid output entry e:\os\obj\amd64fre\temp\e305a6c75ac2d497116bc8a0e4e4c88e\lnk.rsp in e:\os\src\onecore\ds\ds\src\aimx\prod\semanticrag\examples (PASS2) 
ts=14578 (PASS0) onecore\ds\ds\src\aimx\prod\semanticrag\examples(0): sent to build client (1).
ts=14578 (PASS0) onecore\ds\ds\src\aimx\prod\semanticrag\examples(0): distributed work started.
ts=14578 (PASS0) onecore\ds\ds\src\aimx\prod\semanticrag\examples(0): distributed work completed.
ts=14859 (PASS0) onecore\ds\ds\src\aimx\prod\admcpsvr(8): in transit: passQ->distributedQ.
ts=14859 (PASS0) onecore\ds\ds\src\aimx\prod\admcpsvr(8): submitted to distributedQ.
ts=14859 (PASS0) onecore\ds\ds\src\aimx\prod\aimxsrv\idl(16): in transit: passQ->distributedQ.
ts=14859 (PASS0) onecore\ds\ds\src\aimx\prod\aimxsrv\idl(16): submitted to distributedQ.
ts=14859 (PASS0) onecore\ds\ds\src\aimx\prod\mcpserversample\exe(22): in transit: passQ->distributedQ.
ts=14859 (PASS0) onecore\ds\ds\src\aimx\prod\admcpsvr(8): sent to build client (5).
ts=14859 (PASS0) onecore\ds\ds\src\aimx\prod\mcpserversample\exe(22): submitted to distributedQ.
ts=14859 (PASS0) onecore\ds\ds\src\aimx\prod\aimxsrv\server(18): in transit: passQ->distributedQ.
ts=14859 (PASS0) onecore\ds\ds\src\aimx\prod\aimxsrv\idl(16): sent to build client (1).
ts=14859 (PASS0) onecore\ds\ds\src\aimx\prod\admcpsvr(8): distributed work started.
ts=14859 (PASS0) onecore\ds\ds\src\aimx\prod\aimxsrv\server(18): submitted to distributedQ.
ts=14859 (PASS0) onecore\ds\ds\src\aimx\prod\mcpserversample\exe(22): sent to build client (4).
ts=14859 (PASS0) onecore\ds\ds\src\aimx\prod\llmclientlib(11): in transit: passQ->distributedQ.
ts=14875 (PASS0) onecore\ds\ds\src\aimx\prod\llmclientlib(11): submitted to distributedQ.
ts=14875 (PASS0) onecore\ds\ds\src\aimx\prod\aimxsrv\client\lib(21): in transit: passQ->distributedQ.
ts=14875 (PASS0) onecore\ds\ds\src\aimx\prod\aimxsrv\client\lib(21): submitted to distributedQ.
ts=14875 (PASS0) onecore\ds\ds\src\aimx\prod\aimxsrv\dll(15): in transit: passQ->distributedQ.
ts=14875 (PASS0) onecore\ds\ds\src\aimx\prod\aimxsrv\server(18): sent to build client (6).
ts=14875 (PASS0) onecore\ds\ds\src\aimx\prod\aimxsrv\dll(15): submitted to distributedQ.
ts=14875 (PASS0) onecore\ds\ds\src\aimx\prod\aimxsrv\client\dll(20): in transit: passQ->distributedQ.
ts=14875 (PASS0) onecore\ds\ds\src\aimx\prod\llmclientlib(11): sent to build client (7).
ts=14875 (PASS0) onecore\ds\ds\src\aimx\prod\aimxsrv\client\dll(20): submitted to distributedQ.
ts=14875 (PASS0) onecore\ds\ds\src\aimx\prod\mcpserversample\lib(23): in transit: passQ->distributedQ.
ts=14875 (PASS0) onecore\ds\ds\src\aimx\prod\aimxsrv\idl(16): distributed work started.
ts=14875 (PASS0) onecore\ds\ds\src\aimx\prod\aimxsrv\client\lib(21): sent to build client (8).
ts=14875 (PASS0) onecore\ds\ds\src\aimx\prod\mcpserversample\exe(22): distributed work started.
ts=14875 (PASS0) onecore\ds\ds\src\aimx\prod\aimxsrv\server(18): distributed work started.
ts=14875 (PASS0) onecore\ds\ds\src\aimx\prod\llmclientlib(11): distributed work started.
ts=14875 (PASS0) onecore\ds\ds\src\aimx\prod\mcpserversample\lib(23): submitted to distributedQ.
ts=14875 (PASS0) onecore\ds\ds\src\aimx\prod\aimxsrv\client\lib(21): distributed work started.
ts=14875 (PASS0) onecore\ds\ds\src\aimx\prod\aimxsrv\dll(15): sent to build client (9).
ts=14875 (PASS0) onecore\ds\ds\src\aimx\prod\aimxsrv\powershell(17): in transit: passQ->distributedQ.
ts=14875 (PASS0) onecore\ds\ds\src\aimx\prod\aimxsrv\client\dll(20): sent to build client (10).
ts=14875 (PASS0) onecore\ds\ds\src\aimx\prod\aimxsrv\powershell(17): submitted to distributedQ.
ts=14875 (PASS0) onecore\ds\ds\src\aimx\prod\aimxsrv\dll(15): distributed work started.
ts=14875 (PASS0) onecore\ds\ds\src\aimx\prod\aimxsrv\client\dll(20): distributed work started.
ts=14875 (PASS0) onecore\ds\ds\src\aimx\prod\cpprestsdk(10): in transit: passQ->distributedQ.
ts=14875 (PASS0) onecore\ds\ds\src\aimx\prod\mcpserversample\lib(23): sent to build client (11).
ts=14891 (PASS0) onecore\ds\ds\src\aimx\prod\cpprestsdk(10): submitted to distributedQ.
ts=14891 (PASS0) onecore\ds\ds\src\aimx\prod\aimxsrv\powershell(17): sent to build client (12).
ts=14891 (PASS0) onecore\ds\ds\src\aimx\prod\mcpserversample\lib(23): distributed work started.
ts=14891 (PASS0) onecore\ds\ds\src\aimx\prod\aimxsrv\powershell(17): distributed work started.
ts=14891 (PASS0) onecore\ds\ds\src\aimx\prod\cpprestsdk(10): sent to build client (13).
ts=14891 (PASS0) onecore\ds\ds\src\aimx\prod\cpprestsdk(10): distributed work started.
ts=17281 (PASS0) onecore\ds\ds\src\aimx\prod\aimxsrv\server(18): distributed work completed.
ts=17281 (PASS0) onecore\ds\ds\src\aimx\prod\aimxsrv\server(18): operation completed.
ts=17313 (PASS0) onecore\ds\ds\src\aimx\prod\llmclientlib(11): distributed work completed.
ts=17313 (PASS0) onecore\ds\ds\src\aimx\prod\mcpserversample\exe(22): distributed work completed.
ts=17313 (PASS0) onecore\ds\ds\src\aimx\prod\admcpsvr(8): distributed work completed.
ts=17313 (PASS0) onecore\ds\ds\src\aimx\prod\llmclientlib(11): operation completed.
ts=17313 (PASS0) onecore\ds\ds\src\aimx\prod\aimxsrv\client\lib(21): distributed work completed.
ts=17313 (PASS0) onecore\ds\ds\src\aimx\prod\mcpserversample\exe(22): operation completed.
ts=17313 (PASS0) onecore\ds\ds\src\aimx\prod\admcpsvr(8): operation completed.
ts=17313 (PASS0) onecore\ds\ds\src\aimx\prod\aimxsrv\client\lib(21): operation completed.
ts=17344 (PASS0) onecore\ds\ds\src\aimx\prod\mcpserversample\lib(23): distributed work completed.
ts=17344 (PASS0) onecore\ds\ds\src\aimx\prod\mcpserversample\lib(23): operation completed.
ts=17406 (PASS0) onecore\ds\ds\src\aimx\prod\aimxsrv\dll(15): distributed work completed.
ts=17406 (PASS0) onecore\ds\ds\src\aimx\prod\aimxsrv\dll(15): operation completed.
ts=17500 (PASS0) onecore\ds\ds\src\aimx\prod\aimxsrv\client\dll(20): distributed work completed.
ts=17500 (PASS0) onecore\ds\ds\src\aimx\prod\aimxsrv\client\dll(20): operation completed.
ts=18063 (PASS0) onecore\ds\ds\src\aimx\prod\aimxsrv\idl(16): distributed work completed.
ts=18063 (PASS0) onecore\ds\ds\src\aimx\prod\aimxsrv\idl(16): operation completed.
ts=18422 (PASS0) onecore\ds\ds\src\aimx\prod\cpprestsdk(10): distributed work completed.
ts=18422 (PASS0) onecore\ds\ds\src\aimx\prod\cpprestsdk(10): operation completed.
ts=18891 (PASS0) onecore\ds\ds\src\aimx\prod\aimxsrv\powershell(17): distributed work completed.
ts=18891 (PASS0) onecore\ds\ds\src\aimx\prod\aimxsrv\powershell(17): operation completed.
ts=18906 (PASS0) processing complete.
ts=18906 (PASS1) onecore\ds\ds\src\aimx\prod\admcpsvr(8): in transit: passQ->distributedQ.
ts=18906 (PASS1) onecore\ds\ds\src\aimx\prod\admcpsvr(8): submitted to distributedQ.
ts=18906 (PASS1) onecore\ds\ds\src\aimx\prod\mcpserversample\exe(22): in transit: passQ->distributedQ.
ts=18906 (PASS1) onecore\ds\ds\src\aimx\prod\mcpserversample\exe(22): submitted to distributedQ.
ts=18906 (PASS1) onecore\ds\ds\src\aimx\prod\admcpsvr(8): sent to build client (14).
ts=18906 (PASS1) onecore\ds\ds\src\aimx\prod\llmclientlib(11): in transit: passQ->distributedQ.
ts=18906 (PASS1) onecore\ds\ds\src\aimx\prod\mcpserversample\exe(22): sent to build client (12).
ts=18906 (PASS1) onecore\ds\ds\src\aimx\prod\admcpsvr(8): distributed work started.
ts=18906 (PASS1) onecore\ds\ds\src\aimx\prod\llmclientlib(11): submitted to distributedQ.
ts=18922 (PASS1) onecore\ds\ds\src\aimx\prod\mcpserversample\exe(22): distributed work started.
ts=18922 (PASS1) onecore\ds\ds\src\aimx\prod\aimxsrv\serviceinstaller(19): in transit: passQ->distributedQ.
ts=18922 (PASS1) onecore\ds\ds\src\aimx\prod\llmclientlib(11): sent to build client (13).
ts=18922 (PASS1) onecore\ds\ds\src\aimx\prod\aimxsrv\serviceinstaller(19): submitted to distributedQ.
ts=18922 (PASS1) onecore\ds\ds\src\aimx\prod\llmclientlib(11): distributed work started.
ts=18922 (PASS1) onecore\ds\ds\src\aimx\prod\mcpprotocollib(12): in transit: passQ->distributedQ.
ts=18922 (PASS1) onecore\ds\ds\src\aimx\prod\aimxsrv\serviceinstaller(19): sent to build client (1).
ts=18922 (PASS1) onecore\ds\ds\src\aimx\prod\mcpprotocollib(12): submitted to distributedQ.
ts=18922 (PASS1) onecore\ds\ds\src\aimx\prod\aimxsrv\client\lib(21): in transit: passQ->distributedQ.
ts=18922 (PASS1) onecore\ds\ds\src\aimx\prod\aimxsrv\serviceinstaller(19): distributed work started.
ts=18922 (PASS1) onecore\ds\ds\src\aimx\prod\aimxsrv\client\lib(21): submitted to distributedQ.
ts=18922 (PASS1) onecore\ds\ds\src\aimx\prod\mcpprotocollib(12): sent to build client (10).
ts=18922 (PASS1) onecore\ds\ds\src\aimx\prod\aimxsrv\client\dll(20): in transit: passQ->distributedQ.
ts=18922 (PASS1) onecore\ds\ds\src\aimx\prod\aimxsrv\client\lib(21): sent to build client (9).
ts=18922 (PASS1) onecore\ds\ds\src\aimx\prod\aimxsrv\client\dll(20): submitted to distributedQ.
ts=18922 (PASS1) onecore\ds\ds\src\aimx\prod\mcpprotocollib(12): distributed work started.
ts=18922 (PASS1) onecore\ds\ds\src\aimx\prod\aimxsrv\client\lib(21): distributed work started.
ts=18922 (PASS1) onecore\ds\ds\src\aimx\prod\mcpserversample\lib(23): in transit: passQ->distributedQ.
ts=18922 (PASS1) onecore\ds\ds\src\aimx\prod\aimxsrv\client\dll(20): sent to build client (11).
ts=18922 (PASS1) onecore\ds\ds\src\aimx\prod\mcpserversample\lib(23): submitted to distributedQ.
ts=18922 (PASS1) onecore\ds\ds\src\aimx\prod\aimxsrv\powershell(17): in transit: passQ->distributedQ.
ts=18922 (PASS1) onecore\ds\ds\src\aimx\prod\aimxsrv\client\dll(20): distributed work started.
ts=18922 (PASS1) onecore\ds\ds\src\aimx\prod\aimxsrv\powershell(17): submitted to distributedQ.
ts=18922 (PASS1) onecore\ds\ds\src\aimx\prod\mcpserversample\lib(23): sent to build client (8).
ts=18922 (PASS1) onecore\ds\ds\src\aimx\prod\cpprestsdk(10): in transit: passQ->distributedQ.
ts=18922 (PASS1) onecore\ds\ds\src\aimx\prod\aimxsrv\powershell(17): sent to build client (5).
ts=18922 (PASS1) onecore\ds\ds\src\aimx\prod\cpprestsdk(10): submitted to distributedQ.
ts=18938 (PASS1) onecore\ds\ds\src\aimx\prod\cpprestsdk(10): sent to build client (4).
ts=18938 (PASS1) onecore\ds\ds\src\aimx\prod\mcpserversample\lib(23): distributed work started.
ts=18938 (PASS1) onecore\ds\ds\src\aimx\prod\aimxsrv\powershell(17): distributed work started.
ts=18938 (PASS1) onecore\ds\ds\src\aimx\prod\cpprestsdk(10): distributed work started.
ts=24063 (PASS1) onecore\ds\ds\src\aimx\prod\aimxsrv\serviceinstaller(19): distributed work completed.
ts=24063 (PASS1) onecore\ds\ds\src\aimx\prod\aimxsrv\serviceinstaller(19): operation completed.
ts=25828 (PASS1) onecore\ds\ds\src\aimx\prod\mcpserversample\lib(23): distributed work completed.
ts=25828 (PASS1) onecore\ds\ds\src\aimx\prod\mcpserversample\lib(23): operation completed.
ts=25906 (PASS1) onecore\ds\ds\src\aimx\prod\llmclientlib(11): distributed work completed.
ts=25906 (PASS1) onecore\ds\ds\src\aimx\prod\llmclientlib(11): operation completed.
ts=26609 (PASS1) onecore\ds\ds\src\aimx\prod\aimxsrv\powershell(17): distributed work completed.
ts=26609 (PASS1) onecore\ds\ds\src\aimx\prod\aimxsrv\powershell(17): operation completed.
ts=26828 (PASS1) onecore\ds\ds\src\aimx\prod\aimxsrv\client\lib(21): distributed work completed.
ts=26828 (PASS1) onecore\ds\ds\src\aimx\prod\aimxsrv\client\lib(21): operation completed.
ts=26828 (PASS1) onecore\ds\ds\src\aimx\prod\aimxsrv\client\dll(20): distributed work completed.
ts=26828 (PASS1) onecore\ds\ds\src\aimx\prod\aimxsrv\client\dll(20): operation completed.
ts=29063 (PASS1) onecore\ds\ds\src\aimx\prod\mcpprotocollib(12): distributed work completed.
ts=29063 (PASS1) onecore\ds\ds\src\aimx\prod\mcpprotocollib(12): operation completed.
ts=29297 (PASS1) onecore\ds\ds\src\aimx\prod\mcpserversample\exe(22): distributed work completed.
ts=29297 (PASS1) onecore\ds\ds\src\aimx\prod\mcpserversample\exe(22): operation completed.
ts=39625 (PASS1) onecore\ds\ds\src\aimx\prod\admcpsvr(8): distributed work completed.
ts=39625 (PASS1) onecore\ds\ds\src\aimx\prod\admcpsvr(8): operation completed.
ts=48734 (PASS1) onecore\ds\ds\src\aimx\prod\cpprestsdk(10): distributed work completed.
ts=48734 
READYQ INSERTED: e:\os\src\onecore\ds\ds\src\aimx\prod\aimxsrv\server (PASS1 RC:0, pending:1, queued:1)
READYQ INSERTED: e:\os\src\onecore\ds\ds\src\aimx\prod\aimxsrv\dll (PASS1 RC:0, pending:0, queued:2)
ts=48734 (PASS1) onecore\ds\ds\src\aimx\prod\cpprestsdk(10): operation completed.
ts=48734 (PASS1) onecore\ds\ds\src\aimx\prod\aimxsrv\dll(15): transition: readyQ->passQ.
ts=48734 (PASS1) onecore\ds\ds\src\aimx\prod\aimxsrv\server(18): transition: readyQ->passQ.
ts=48734 (PASS1) flush readyQ: 2 work items transitioned to pass queue.
ts=48734 (PASS1) onecore\ds\ds\src\aimx\prod\aimxsrv\dll(15): in transit: passQ->distributedQ.
ts=48734 (PASS1) onecore\ds\ds\src\aimx\prod\aimxsrv\dll(15): submitted to distributedQ.
ts=48734 (PASS1) onecore\ds\ds\src\aimx\prod\aimxsrv\server(18): in transit: passQ->distributedQ.
ts=48734 (PASS1) onecore\ds\ds\src\aimx\prod\aimxsrv\server(18): submitted to distributedQ.
ts=48734 (PASS1) onecore\ds\ds\src\aimx\prod\aimxsrv\dll(15): sent to build client (7).
ts=48734 (PASS1) onecore\ds\ds\src\aimx\prod\aimxsrv\dll(15): distributed work started.
ts=48734 (PASS1) onecore\ds\ds\src\aimx\prod\aimxsrv\server(18): sent to build client (4).
ts=48734 (PASS1) onecore\ds\ds\src\aimx\prod\aimxsrv\server(18): distributed work started.
ts=54625 (PASS1) onecore\ds\ds\src\aimx\prod\aimxsrv\dll(15): distributed work completed.
ts=54625 (PASS1) onecore\ds\ds\src\aimx\prod\aimxsrv\dll(15): operation completed.
ts=61406 (PASS1) onecore\ds\ds\src\aimx\prod\aimxsrv\server(18): distributed work completed.
ts=61406 (PASS1) onecore\ds\ds\src\aimx\prod\aimxsrv\server(18): operation completed.
ts=61859 (PASS1) processing complete.
ts=61859 (PASS2) onecore\ds\ds\src\aimx\prod\mcpserversample\exe(22): in transit: passQ->distributedQ.
ts=61859 (PASS2) onecore\ds\ds\src\aimx\prod\mcpserversample\exe(22): errors found in parent: terminating... 
ts=61859 (PASS2) onecore\ds\ds\src\aimx\prod\aimxsrv\serviceinstaller(19): in transit: passQ->distributedQ.
ts=61859 (PASS2) onecore\ds\ds\src\aimx\prod\aimxsrv\serviceinstaller(19): submitted to distributedQ.
ts=61859 (PASS2) onecore\ds\ds\src\aimx\prod\aimxsrv\dll(15): in transit: passQ->distributedQ.
ts=61859 (PASS2) onecore\ds\ds\src\aimx\prod\aimxsrv\serviceinstaller(19): sent to build client (14).
ts=61859 (PASS2) onecore\ds\ds\src\aimx\prod\aimxsrv\dll(15): errors found in parent: terminating... 
ts=61859 (PASS2) onecore\ds\ds\src\aimx\prod\aimxsrv\client\dll(20): in transit: passQ->distributedQ.
ts=61859 (PASS2) onecore\ds\ds\src\aimx\prod\aimxsrv\client\dll(20): submitted to distributedQ.
ts=61859 (PASS2) onecore\ds\ds\src\aimx\prod\aimxsrv\serviceinstaller(19): distributed work started.
ts=61859 (PASS2) onecore\ds\ds\src\aimx\prod\aimxsrv\powershell(17): in transit: passQ->distributedQ.
ts=61859 (PASS2) onecore\ds\ds\src\aimx\prod\aimxsrv\powershell(17): submitted to distributedQ.
ts=61859 (PASS2) onecore\ds\ds\src\aimx\prod\cpprestsdk(10): in transit: passQ->distributedQ.
ts=61859 (PASS2) onecore\ds\ds\src\aimx\prod\aimxsrv\client\dll(20): sent to build client (4).
ts=61859 (PASS2) onecore\ds\ds\src\aimx\prod\aimxsrv\powershell(17): sent to build client (7).
ts=61859 (PASS2) onecore\ds\ds\src\aimx\prod\cpprestsdk(10): submitted to distributedQ.
ts=61875 (PASS2) onecore\ds\ds\src\aimx\prod\aimxsrv\client\dll(20): distributed work started.
ts=61875 (PASS2) onecore\ds\ds\src\aimx\prod\aimxsrv\powershell(17): distributed work started.
ts=61875 (PASS2) onecore\ds\ds\src\aimx\prod\cpprestsdk(10): sent to build client (12).
ts=61875 (PASS2) onecore\ds\ds\src\aimx\prod\cpprestsdk(10): distributed work started.
ts=64344 (PASS2) onecore\ds\ds\src\aimx\prod\cpprestsdk(10): distributed work completed.
ts=64344 (PASS2) onecore\ds\ds\src\aimx\prod\cpprestsdk(10): operation completed.
ts=65453 (PASS2) onecore\ds\ds\src\aimx\prod\aimxsrv\powershell(17): distributed work completed.
ts=65453 (PASS2) onecore\ds\ds\src\aimx\prod\aimxsrv\powershell(17): operation completed.
ts=66484 (PASS2) onecore\ds\ds\src\aimx\prod\aimxsrv\client\dll(20): distributed work completed.
ts=66484 (PASS2) onecore\ds\ds\src\aimx\prod\aimxsrv\client\dll(20): operation completed.
ts=66891 (PASS2) onecore\ds\ds\src\aimx\prod\aimxsrv\serviceinstaller(19): distributed work completed.
ts=66891 (PASS2) onecore\ds\ds\src\aimx\prod\aimxsrv\serviceinstaller(19): operation completed.
ts=66922 (PASS2) processing complete.
ts=66922 PASS_INDEPENDENT processing complete.
ts=66922 PASS_INDEPENDENT processing complete.

DIR EVENTS: onecore\ds\ds\src\aimx\prod\aimxsrv\server (IN FOCUS)
  CREATED=1
  FOUND_IN_DIRS=1
  INITIAL_DIRECTORY=0
  FOUND_IN_DATABASE=1
  CLEAN_ALL=1
  CLEAN_ALL_FORCED=0
  CLEAN_PASS0=1
  CLEAN_PASS1=1
  CLEAN_PASS2=1
  CLEAN_PASS_INDEPENDENT=1
  ALLOC_WORKITEM_PASS0=1
  ALLOC_WORKITEM_PASS1=1
  ALLOC_WORKITEM_PASS2=0
  ALLOC_WORKITEM_PASS_INDEPENDENT=0
  SOURCES_READ=1
  NODE_DIR=1
  DIRS_PROJ_SKIPPED=0
  MSBUILD_PROJECT_FILE_READ=0
  MSBUILD_POST_BUILD_PROJECT_FILE_READ=0
  TEST_CODE=0
  SAMPLE_CODE=0
  TESTCODE_SKIPPED=0
  PASS0_NEEDED=1
  PASS1_NEEDED=1
  PASS2_NEEDED=0
  PASS3_NEEDED=0
  SOURCES_DEP_READ=0
  QUEUE_TO_PASS_LIST_1=1
  QUEUE_TO_PASS_LIST_2=1
  QUEUE_TO_PASS_LIST_3=0
  ADD_DEPENDENCIES_TO_GRAPH=1
  SET_NON_PRODUCT=0
  SET_NON_CRITICAL=0
  SET_CS_WINDOWS=0
  SET_CS_XBOX=0
  SET_CS_PHONE=0
  SET_CS_AZURE=0
  SCAN_COMPLETE=1

  BUILD PASS0 EVENTS: onecore\ds\ds\src\aimx\prod\aimxsrv\server
    BUILD_CREATED=1
    TASK_CREATED=0
    ADDED_TO_DEPENDENCY_GRAPH=1
    ADDED_TO_PASSQ=1
    ADDED_TO_TASKQ=0
    TRANSITION_STANDBYQ_TO_PASSQ=0
    ADDED_TO_PASS0_Q=1
    ADDED_TO_PASS1_Q=0
    ADDED_TO_PASS2_Q=0
    ADDED_TO_PASS3_Q=0
    ADDED_TO_PASS_INDEPENDENT_Q=0
    ADDED_TO_TASK_Q=0
    REMOVED_FROM_PASSQ=1
    DEPENDENT_MOVED_FROM_READYQ_TO_PASSQ=0
    NMAKE_COMMAND_LINE=1
    MSBUILD_COMMAND_LINE=0
    SUBMIT_TO_DISTRIBUTED_SCHEDULER=1
    DISTRIBUTED_SCHEDULER_WORK_STARTED=1
    DISTRIBUTED_SCHEDULER_WORK_COMPLETED=1
    ADDED_TO_READYQ=0
    BYPASSED=0
    SENT_TO_CLIENT=1
    INCREMENTAL=0
    BUILD_PASS_TERMINATED_FOR_ERRORS=0
    BUILD_OPERATION_COMPLETE=1

  BUILD PASS1 EVENTS: onecore\ds\ds\src\aimx\prod\aimxsrv\server
    BUILD_CREATED=1
    TASK_CREATED=0
    ADDED_TO_DEPENDENCY_GRAPH=1
    ADDED_TO_PASSQ=1
    ADDED_TO_TASKQ=0
    TRANSITION_STANDBYQ_TO_PASSQ=0
    ADDED_TO_PASS0_Q=0
    ADDED_TO_PASS1_Q=1
    ADDED_TO_PASS2_Q=0
    ADDED_TO_PASS3_Q=0
    ADDED_TO_PASS_INDEPENDENT_Q=0
    ADDED_TO_TASK_Q=0
    REMOVED_FROM_PASSQ=1
    DEPENDENT_MOVED_FROM_READYQ_TO_PASSQ=1
    NMAKE_COMMAND_LINE=1
    MSBUILD_COMMAND_LINE=0
    SUBMIT_TO_DISTRIBUTED_SCHEDULER=1
    DISTRIBUTED_SCHEDULER_WORK_STARTED=1
    DISTRIBUTED_SCHEDULER_WORK_COMPLETED=1
    ADDED_TO_READYQ=1
    BYPASSED=0
    SENT_TO_CLIENT=1
    INCREMENTAL=0
    BUILD_PASS_TERMINATED_FOR_ERRORS=0
    BUILD_OPERATION_COMPLETE=1

DIR EVENTS: onecore\ds\ds\src\aimx\prod\mcpprotocollib (IN FOCUS)
  CREATED=1
  FOUND_IN_DIRS=1
  INITIAL_DIRECTORY=0
  FOUND_IN_DATABASE=1
  CLEAN_ALL=1
  CLEAN_ALL_FORCED=0
  CLEAN_PASS0=1
  CLEAN_PASS1=1
  CLEAN_PASS2=1
  CLEAN_PASS_INDEPENDENT=1
  ALLOC_WORKITEM_PASS0=0
  ALLOC_WORKITEM_PASS1=1
  ALLOC_WORKITEM_PASS2=0
  ALLOC_WORKITEM_PASS_INDEPENDENT=0
  SOURCES_READ=1
  NODE_DIR=1
  DIRS_PROJ_SKIPPED=0
  MSBUILD_PROJECT_FILE_READ=0
  MSBUILD_POST_BUILD_PROJECT_FILE_READ=0
  TEST_CODE=0
  SAMPLE_CODE=0
  TESTCODE_SKIPPED=0
  PASS0_NEEDED=0
  PASS1_NEEDED=1
  PASS2_NEEDED=0
  PASS3_NEEDED=0
  SOURCES_DEP_READ=0
  QUEUE_TO_PASS_LIST_1=1
  QUEUE_TO_PASS_LIST_2=1
  QUEUE_TO_PASS_LIST_3=0
  ADD_DEPENDENCIES_TO_GRAPH=1
  SET_NON_PRODUCT=0
  SET_NON_CRITICAL=0
  SET_CS_WINDOWS=0
  SET_CS_XBOX=0
  SET_CS_PHONE=0
  SET_CS_AZURE=0
  SCAN_COMPLETE=1

  BUILD PASS1 EVENTS: onecore\ds\ds\src\aimx\prod\mcpprotocollib
    BUILD_CREATED=1
    TASK_CREATED=0
    ADDED_TO_DEPENDENCY_GRAPH=1
    ADDED_TO_PASSQ=1
    ADDED_TO_TASKQ=0
    TRANSITION_STANDBYQ_TO_PASSQ=0
    ADDED_TO_PASS0_Q=0
    ADDED_TO_PASS1_Q=1
    ADDED_TO_PASS2_Q=0
    ADDED_TO_PASS3_Q=0
    ADDED_TO_PASS_INDEPENDENT_Q=0
    ADDED_TO_TASK_Q=0
    REMOVED_FROM_PASSQ=1
    DEPENDENT_MOVED_FROM_READYQ_TO_PASSQ=0
    NMAKE_COMMAND_LINE=1
    MSBUILD_COMMAND_LINE=0
    SUBMIT_TO_DISTRIBUTED_SCHEDULER=1
    DISTRIBUTED_SCHEDULER_WORK_STARTED=1
    DISTRIBUTED_SCHEDULER_WORK_COMPLETED=1
    ADDED_TO_READYQ=0
    BYPASSED=0
    SENT_TO_CLIENT=1
    INCREMENTAL=0
    BUILD_PASS_TERMINATED_FOR_ERRORS=0
    BUILD_OPERATION_COMPLETE=1

DIR EVENTS: onecore\ds\ds\src\aimx\prod\llmclientlib (IN FOCUS)
  CREATED=1
  FOUND_IN_DIRS=1
  INITIAL_DIRECTORY=0
  FOUND_IN_DATABASE=1
  CLEAN_ALL=1
  CLEAN_ALL_FORCED=0
  CLEAN_PASS0=1
  CLEAN_PASS1=1
  CLEAN_PASS2=1
  CLEAN_PASS_INDEPENDENT=1
  ALLOC_WORKITEM_PASS0=1
  ALLOC_WORKITEM_PASS1=1
  ALLOC_WORKITEM_PASS2=0
  ALLOC_WORKITEM_PASS_INDEPENDENT=0
  SOURCES_READ=1
  NODE_DIR=1
  DIRS_PROJ_SKIPPED=0
  MSBUILD_PROJECT_FILE_READ=0
  MSBUILD_POST_BUILD_PROJECT_FILE_READ=0
  TEST_CODE=0
  SAMPLE_CODE=0
  TESTCODE_SKIPPED=0
  PASS0_NEEDED=1
  PASS1_NEEDED=1
  PASS2_NEEDED=0
  PASS3_NEEDED=0
  SOURCES_DEP_READ=0
  QUEUE_TO_PASS_LIST_1=1
  QUEUE_TO_PASS_LIST_2=1
  QUEUE_TO_PASS_LIST_3=0
  ADD_DEPENDENCIES_TO_GRAPH=1
  SET_NON_PRODUCT=0
  SET_NON_CRITICAL=0
  SET_CS_WINDOWS=0
  SET_CS_XBOX=0
  SET_CS_PHONE=0
  SET_CS_AZURE=0
  SCAN_COMPLETE=1

  BUILD PASS0 EVENTS: onecore\ds\ds\src\aimx\prod\llmclientlib
    BUILD_CREATED=1
    TASK_CREATED=0
    ADDED_TO_DEPENDENCY_GRAPH=1
    ADDED_TO_PASSQ=1
    ADDED_TO_TASKQ=0
    TRANSITION_STANDBYQ_TO_PASSQ=0
    ADDED_TO_PASS0_Q=1
    ADDED_TO_PASS1_Q=0
    ADDED_TO_PASS2_Q=0
    ADDED_TO_PASS3_Q=0
    ADDED_TO_PASS_INDEPENDENT_Q=0
    ADDED_TO_TASK_Q=0
    REMOVED_FROM_PASSQ=1
    DEPENDENT_MOVED_FROM_READYQ_TO_PASSQ=0
    NMAKE_COMMAND_LINE=1
    MSBUILD_COMMAND_LINE=0
    SUBMIT_TO_DISTRIBUTED_SCHEDULER=1
    DISTRIBUTED_SCHEDULER_WORK_STARTED=1
    DISTRIBUTED_SCHEDULER_WORK_COMPLETED=1
    ADDED_TO_READYQ=0
    BYPASSED=0
    SENT_TO_CLIENT=1
    INCREMENTAL=0
    BUILD_PASS_TERMINATED_FOR_ERRORS=0
    BUILD_OPERATION_COMPLETE=1

  BUILD PASS1 EVENTS: onecore\ds\ds\src\aimx\prod\llmclientlib
    BUILD_CREATED=1
    TASK_CREATED=0
    ADDED_TO_DEPENDENCY_GRAPH=1
    ADDED_TO_PASSQ=1
    ADDED_TO_TASKQ=0
    TRANSITION_STANDBYQ_TO_PASSQ=0
    ADDED_TO_PASS0_Q=0
    ADDED_TO_PASS1_Q=1
    ADDED_TO_PASS2_Q=0
    ADDED_TO_PASS3_Q=0
    ADDED_TO_PASS_INDEPENDENT_Q=0
    ADDED_TO_TASK_Q=0
    REMOVED_FROM_PASSQ=1
    DEPENDENT_MOVED_FROM_READYQ_TO_PASSQ=0
    NMAKE_COMMAND_LINE=1
    MSBUILD_COMMAND_LINE=0
    SUBMIT_TO_DISTRIBUTED_SCHEDULER=1
    DISTRIBUTED_SCHEDULER_WORK_STARTED=1
    DISTRIBUTED_SCHEDULER_WORK_COMPLETED=1
    ADDED_TO_READYQ=0
    BYPASSED=0
    SENT_TO_CLIENT=1
    INCREMENTAL=0
    BUILD_PASS_TERMINATED_FOR_ERRORS=0
    BUILD_OPERATION_COMPLETE=1

DIR EVENTS: onecore\ds\ds\src\aimx\prod\cpprestsdk (IN FOCUS)
  CREATED=1
  FOUND_IN_DIRS=1
  INITIAL_DIRECTORY=0
  FOUND_IN_DATABASE=1
  CLEAN_ALL=1
  CLEAN_ALL_FORCED=0
  CLEAN_PASS0=1
  CLEAN_PASS1=1
  CLEAN_PASS2=1
  CLEAN_PASS_INDEPENDENT=1
  ALLOC_WORKITEM_PASS0=1
  ALLOC_WORKITEM_PASS1=1
  ALLOC_WORKITEM_PASS2=1
  ALLOC_WORKITEM_PASS_INDEPENDENT=0
  SOURCES_READ=0
  NODE_DIR=1
  DIRS_PROJ_SKIPPED=0
  MSBUILD_PROJECT_FILE_READ=1
  MSBUILD_POST_BUILD_PROJECT_FILE_READ=0
  TEST_CODE=0
  SAMPLE_CODE=0
  TESTCODE_SKIPPED=0
  PASS0_NEEDED=1
  PASS1_NEEDED=1
  PASS2_NEEDED=1
  PASS3_NEEDED=0
  SOURCES_DEP_READ=0
  QUEUE_TO_PASS_LIST_1=1
  QUEUE_TO_PASS_LIST_2=1
  QUEUE_TO_PASS_LIST_3=0
  ADD_DEPENDENCIES_TO_GRAPH=1
  SET_NON_PRODUCT=0
  SET_NON_CRITICAL=0
  SET_CS_WINDOWS=0
  SET_CS_XBOX=0
  SET_CS_PHONE=0
  SET_CS_AZURE=0
  SCAN_COMPLETE=1

  BUILD PASS0 EVENTS: onecore\ds\ds\src\aimx\prod\cpprestsdk
    BUILD_CREATED=1
    TASK_CREATED=0
    ADDED_TO_DEPENDENCY_GRAPH=1
    ADDED_TO_PASSQ=1
    ADDED_TO_TASKQ=0
    TRANSITION_STANDBYQ_TO_PASSQ=0
    ADDED_TO_PASS0_Q=1
    ADDED_TO_PASS1_Q=0
    ADDED_TO_PASS2_Q=0
    ADDED_TO_PASS3_Q=0
    ADDED_TO_PASS_INDEPENDENT_Q=0
    ADDED_TO_TASK_Q=0
    REMOVED_FROM_PASSQ=1
    DEPENDENT_MOVED_FROM_READYQ_TO_PASSQ=0
    NMAKE_COMMAND_LINE=0
    MSBUILD_COMMAND_LINE=1
    SUBMIT_TO_DISTRIBUTED_SCHEDULER=1
    DISTRIBUTED_SCHEDULER_WORK_STARTED=1
    DISTRIBUTED_SCHEDULER_WORK_COMPLETED=1
    ADDED_TO_READYQ=0
    BYPASSED=0
    SENT_TO_CLIENT=1
    INCREMENTAL=0
    BUILD_PASS_TERMINATED_FOR_ERRORS=0
    BUILD_OPERATION_COMPLETE=1

  BUILD PASS1 EVENTS: onecore\ds\ds\src\aimx\prod\cpprestsdk
    BUILD_CREATED=1
    TASK_CREATED=0
    ADDED_TO_DEPENDENCY_GRAPH=1
    ADDED_TO_PASSQ=1
    ADDED_TO_TASKQ=0
    TRANSITION_STANDBYQ_TO_PASSQ=0
    ADDED_TO_PASS0_Q=0
    ADDED_TO_PASS1_Q=1
    ADDED_TO_PASS2_Q=0
    ADDED_TO_PASS3_Q=0
    ADDED_TO_PASS_INDEPENDENT_Q=0
    ADDED_TO_TASK_Q=0
    REMOVED_FROM_PASSQ=1
    DEPENDENT_MOVED_FROM_READYQ_TO_PASSQ=0
    NMAKE_COMMAND_LINE=0
    MSBUILD_COMMAND_LINE=1
    SUBMIT_TO_DISTRIBUTED_SCHEDULER=1
    DISTRIBUTED_SCHEDULER_WORK_STARTED=1
    DISTRIBUTED_SCHEDULER_WORK_COMPLETED=1
    ADDED_TO_READYQ=0
    BYPASSED=0
    SENT_TO_CLIENT=1
    INCREMENTAL=0
    BUILD_PASS_TERMINATED_FOR_ERRORS=0
    BUILD_OPERATION_COMPLETE=1

  BUILD PASS2 EVENTS: onecore\ds\ds\src\aimx\prod\cpprestsdk
    BUILD_CREATED=1
    TASK_CREATED=0
    ADDED_TO_DEPENDENCY_GRAPH=1
    ADDED_TO_PASSQ=1
    ADDED_TO_TASKQ=0
    TRANSITION_STANDBYQ_TO_PASSQ=0
    ADDED_TO_PASS0_Q=0
    ADDED_TO_PASS1_Q=0
    ADDED_TO_PASS2_Q=1
    ADDED_TO_PASS3_Q=0
    ADDED_TO_PASS_INDEPENDENT_Q=0
    ADDED_TO_TASK_Q=0
    REMOVED_FROM_PASSQ=1
    DEPENDENT_MOVED_FROM_READYQ_TO_PASSQ=0
    NMAKE_COMMAND_LINE=0
    MSBUILD_COMMAND_LINE=1
    SUBMIT_TO_DISTRIBUTED_SCHEDULER=1
    DISTRIBUTED_SCHEDULER_WORK_STARTED=1
    DISTRIBUTED_SCHEDULER_WORK_COMPLETED=1
    ADDED_TO_READYQ=0
    BYPASSED=0
    SENT_TO_CLIENT=1
    INCREMENTAL=0
    BUILD_PASS_TERMINATED_FOR_ERRORS=0
    BUILD_OPERATION_COMPLETE=1

DIR EVENTS: onecore\ds\ds\src\aimx\prod\aimxsrv\serviceinstaller (IN FOCUS)
  CREATED=1
  FOUND_IN_DIRS=1
  INITIAL_DIRECTORY=0
  FOUND_IN_DATABASE=1
  CLEAN_ALL=1
  CLEAN_ALL_FORCED=0
  CLEAN_PASS0=1
  CLEAN_PASS1=1
  CLEAN_PASS2=1
  CLEAN_PASS_INDEPENDENT=1
  ALLOC_WORKITEM_PASS0=0
  ALLOC_WORKITEM_PASS1=1
  ALLOC_WORKITEM_PASS2=1
  ALLOC_WORKITEM_PASS_INDEPENDENT=0
  SOURCES_READ=1
  NODE_DIR=1
  DIRS_PROJ_SKIPPED=0
  MSBUILD_PROJECT_FILE_READ=0
  MSBUILD_POST_BUILD_PROJECT_FILE_READ=0
  TEST_CODE=0
  SAMPLE_CODE=0
  TESTCODE_SKIPPED=0
  PASS0_NEEDED=0
  PASS1_NEEDED=1
  PASS2_NEEDED=1
  PASS3_NEEDED=0
  SOURCES_DEP_READ=0
  QUEUE_TO_PASS_LIST_1=1
  QUEUE_TO_PASS_LIST_2=1
  QUEUE_TO_PASS_LIST_3=0
  ADD_DEPENDENCIES_TO_GRAPH=1
  SET_NON_PRODUCT=0
  SET_NON_CRITICAL=0
  SET_CS_WINDOWS=0
  SET_CS_XBOX=0
  SET_CS_PHONE=0
  SET_CS_AZURE=0
  SCAN_COMPLETE=1

  BUILD PASS1 EVENTS: onecore\ds\ds\src\aimx\prod\aimxsrv\serviceinstaller
    BUILD_CREATED=1
    TASK_CREATED=0
    ADDED_TO_DEPENDENCY_GRAPH=1
    ADDED_TO_PASSQ=1
    ADDED_TO_TASKQ=0
    TRANSITION_STANDBYQ_TO_PASSQ=0
    ADDED_TO_PASS0_Q=0
    ADDED_TO_PASS1_Q=1
    ADDED_TO_PASS2_Q=0
    ADDED_TO_PASS3_Q=0
    ADDED_TO_PASS_INDEPENDENT_Q=0
    ADDED_TO_TASK_Q=0
    REMOVED_FROM_PASSQ=1
    DEPENDENT_MOVED_FROM_READYQ_TO_PASSQ=0
    NMAKE_COMMAND_LINE=1
    MSBUILD_COMMAND_LINE=0
    SUBMIT_TO_DISTRIBUTED_SCHEDULER=1
    DISTRIBUTED_SCHEDULER_WORK_STARTED=1
    DISTRIBUTED_SCHEDULER_WORK_COMPLETED=1
    ADDED_TO_READYQ=0
    BYPASSED=0
    SENT_TO_CLIENT=1
    INCREMENTAL=0
    BUILD_PASS_TERMINATED_FOR_ERRORS=0
    BUILD_OPERATION_COMPLETE=1

  BUILD PASS2 EVENTS: onecore\ds\ds\src\aimx\prod\aimxsrv\serviceinstaller
    BUILD_CREATED=1
    TASK_CREATED=0
    ADDED_TO_DEPENDENCY_GRAPH=1
    ADDED_TO_PASSQ=1
    ADDED_TO_TASKQ=0
    TRANSITION_STANDBYQ_TO_PASSQ=0
    ADDED_TO_PASS0_Q=0
    ADDED_TO_PASS1_Q=0
    ADDED_TO_PASS2_Q=1
    ADDED_TO_PASS3_Q=0
    ADDED_TO_PASS_INDEPENDENT_Q=0
    ADDED_TO_TASK_Q=0
    REMOVED_FROM_PASSQ=1
    DEPENDENT_MOVED_FROM_READYQ_TO_PASSQ=0
    NMAKE_COMMAND_LINE=1
    MSBUILD_COMMAND_LINE=0
    SUBMIT_TO_DISTRIBUTED_SCHEDULER=1
    DISTRIBUTED_SCHEDULER_WORK_STARTED=1
    DISTRIBUTED_SCHEDULER_WORK_COMPLETED=1
    ADDED_TO_READYQ=0
    BYPASSED=0
    SENT_TO_CLIENT=1
    INCREMENTAL=0
    BUILD_PASS_TERMINATED_FOR_ERRORS=0
    BUILD_OPERATION_COMPLETE=1

DIR EVENTS: onecore\ds\ds\src\aimx\prod\aimxsrv\dll (IN FOCUS)
  CREATED=1
  FOUND_IN_DIRS=1
  INITIAL_DIRECTORY=0
  FOUND_IN_DATABASE=1
  CLEAN_ALL=1
  CLEAN_ALL_FORCED=0
  CLEAN_PASS0=1
  CLEAN_PASS1=1
  CLEAN_PASS2=1
  CLEAN_PASS_INDEPENDENT=1
  ALLOC_WORKITEM_PASS0=1
  ALLOC_WORKITEM_PASS1=1
  ALLOC_WORKITEM_PASS2=1
  ALLOC_WORKITEM_PASS_INDEPENDENT=0
  SOURCES_READ=1
  NODE_DIR=1
  DIRS_PROJ_SKIPPED=0
  MSBUILD_PROJECT_FILE_READ=0
  MSBUILD_POST_BUILD_PROJECT_FILE_READ=0
  TEST_CODE=0
  SAMPLE_CODE=0
  TESTCODE_SKIPPED=0
  PASS0_NEEDED=1
  PASS1_NEEDED=1
  PASS2_NEEDED=1
  PASS3_NEEDED=0
  SOURCES_DEP_READ=0
  QUEUE_TO_PASS_LIST_1=1
  QUEUE_TO_PASS_LIST_2=1
  QUEUE_TO_PASS_LIST_3=0
  ADD_DEPENDENCIES_TO_GRAPH=1
  SET_NON_PRODUCT=0
  SET_NON_CRITICAL=0
  SET_CS_WINDOWS=0
  SET_CS_XBOX=0
  SET_CS_PHONE=0
  SET_CS_AZURE=0
  SCAN_COMPLETE=1

  BUILD PASS0 EVENTS: onecore\ds\ds\src\aimx\prod\aimxsrv\dll
    BUILD_CREATED=1
    TASK_CREATED=0
    ADDED_TO_DEPENDENCY_GRAPH=1
    ADDED_TO_PASSQ=1
    ADDED_TO_TASKQ=0
    TRANSITION_STANDBYQ_TO_PASSQ=0
    ADDED_TO_PASS0_Q=1
    ADDED_TO_PASS1_Q=0
    ADDED_TO_PASS2_Q=0
    ADDED_TO_PASS3_Q=0
    ADDED_TO_PASS_INDEPENDENT_Q=0
    ADDED_TO_TASK_Q=0
    REMOVED_FROM_PASSQ=1
    DEPENDENT_MOVED_FROM_READYQ_TO_PASSQ=0
    NMAKE_COMMAND_LINE=1
    MSBUILD_COMMAND_LINE=0
    SUBMIT_TO_DISTRIBUTED_SCHEDULER=1
    DISTRIBUTED_SCHEDULER_WORK_STARTED=1
    DISTRIBUTED_SCHEDULER_WORK_COMPLETED=1
    ADDED_TO_READYQ=0
    BYPASSED=0
    SENT_TO_CLIENT=1
    INCREMENTAL=0
    BUILD_PASS_TERMINATED_FOR_ERRORS=0
    BUILD_OPERATION_COMPLETE=1

  BUILD PASS1 EVENTS: onecore\ds\ds\src\aimx\prod\aimxsrv\dll
    BUILD_CREATED=1
    TASK_CREATED=0
    ADDED_TO_DEPENDENCY_GRAPH=1
    ADDED_TO_PASSQ=1
    ADDED_TO_TASKQ=0
    TRANSITION_STANDBYQ_TO_PASSQ=0
    ADDED_TO_PASS0_Q=0
    ADDED_TO_PASS1_Q=1
    ADDED_TO_PASS2_Q=0
    ADDED_TO_PASS3_Q=0
    ADDED_TO_PASS_INDEPENDENT_Q=0
    ADDED_TO_TASK_Q=0
    REMOVED_FROM_PASSQ=1
    DEPENDENT_MOVED_FROM_READYQ_TO_PASSQ=1
    NMAKE_COMMAND_LINE=1
    MSBUILD_COMMAND_LINE=0
    SUBMIT_TO_DISTRIBUTED_SCHEDULER=1
    DISTRIBUTED_SCHEDULER_WORK_STARTED=1
    DISTRIBUTED_SCHEDULER_WORK_COMPLETED=1
    ADDED_TO_READYQ=1
    BYPASSED=0
    SENT_TO_CLIENT=1
    INCREMENTAL=0
    BUILD_PASS_TERMINATED_FOR_ERRORS=0
    BUILD_OPERATION_COMPLETE=1

  BUILD PASS2 EVENTS: onecore\ds\ds\src\aimx\prod\aimxsrv\dll
    BUILD_CREATED=1
    TASK_CREATED=0
    ADDED_TO_DEPENDENCY_GRAPH=1
    ADDED_TO_PASSQ=1
    ADDED_TO_TASKQ=0
    TRANSITION_STANDBYQ_TO_PASSQ=0
    ADDED_TO_PASS0_Q=0
    ADDED_TO_PASS1_Q=0
    ADDED_TO_PASS2_Q=1
    ADDED_TO_PASS3_Q=0
    ADDED_TO_PASS_INDEPENDENT_Q=0
    ADDED_TO_TASK_Q=0
    REMOVED_FROM_PASSQ=1
    DEPENDENT_MOVED_FROM_READYQ_TO_PASSQ=0
    NMAKE_COMMAND_LINE=0
    MSBUILD_COMMAND_LINE=0
    SUBMIT_TO_DISTRIBUTED_SCHEDULER=0
    DISTRIBUTED_SCHEDULER_WORK_STARTED=0
    DISTRIBUTED_SCHEDULER_WORK_COMPLETED=0
    ADDED_TO_READYQ=0
    BYPASSED=0
    SENT_TO_CLIENT=0
    INCREMENTAL=0
    BUILD_PASS_TERMINATED_FOR_ERRORS=1
    BUILD_OPERATION_COMPLETE=1

DIR EVENTS: onecore\ds\ds\src\aimx\prod\aimxsrv\idl (IN FOCUS)
  CREATED=1
  FOUND_IN_DIRS=1
  INITIAL_DIRECTORY=0
  FOUND_IN_DATABASE=1
  CLEAN_ALL=1
  CLEAN_ALL_FORCED=0
  CLEAN_PASS0=1
  CLEAN_PASS1=1
  CLEAN_PASS2=1
  CLEAN_PASS_INDEPENDENT=1
  ALLOC_WORKITEM_PASS0=1
  ALLOC_WORKITEM_PASS1=0
  ALLOC_WORKITEM_PASS2=0
  ALLOC_WORKITEM_PASS_INDEPENDENT=0
  SOURCES_READ=1
  NODE_DIR=1
  DIRS_PROJ_SKIPPED=0
  MSBUILD_PROJECT_FILE_READ=0
  MSBUILD_POST_BUILD_PROJECT_FILE_READ=0
  TEST_CODE=0
  SAMPLE_CODE=0
  TESTCODE_SKIPPED=0
  PASS0_NEEDED=1
  PASS1_NEEDED=0
  PASS2_NEEDED=0
  PASS3_NEEDED=0
  SOURCES_DEP_READ=0
  QUEUE_TO_PASS_LIST_1=1
  QUEUE_TO_PASS_LIST_2=1
  QUEUE_TO_PASS_LIST_3=0
  ADD_DEPENDENCIES_TO_GRAPH=1
  SET_NON_PRODUCT=0
  SET_NON_CRITICAL=0
  SET_CS_WINDOWS=0
  SET_CS_XBOX=0
  SET_CS_PHONE=0
  SET_CS_AZURE=0
  SCAN_COMPLETE=1

  BUILD PASS0 EVENTS: onecore\ds\ds\src\aimx\prod\aimxsrv\idl
    BUILD_CREATED=1
    TASK_CREATED=0
    ADDED_TO_DEPENDENCY_GRAPH=1
    ADDED_TO_PASSQ=1
    ADDED_TO_TASKQ=0
    TRANSITION_STANDBYQ_TO_PASSQ=0
    ADDED_TO_PASS0_Q=1
    ADDED_TO_PASS1_Q=0
    ADDED_TO_PASS2_Q=0
    ADDED_TO_PASS3_Q=0
    ADDED_TO_PASS_INDEPENDENT_Q=0
    ADDED_TO_TASK_Q=0
    REMOVED_FROM_PASSQ=1
    DEPENDENT_MOVED_FROM_READYQ_TO_PASSQ=0
    NMAKE_COMMAND_LINE=1
    MSBUILD_COMMAND_LINE=0
    SUBMIT_TO_DISTRIBUTED_SCHEDULER=1
    DISTRIBUTED_SCHEDULER_WORK_STARTED=1
    DISTRIBUTED_SCHEDULER_WORK_COMPLETED=1
    ADDED_TO_READYQ=0
    BYPASSED=0
    SENT_TO_CLIENT=1
    INCREMENTAL=0
    BUILD_PASS_TERMINATED_FOR_ERRORS=0
    BUILD_OPERATION_COMPLETE=1

DIR EVENTS: onecore\ds\ds\src\aimx\prod\mcpserversample\lib (IN FOCUS)
  CREATED=1
  FOUND_IN_DIRS=1
  INITIAL_DIRECTORY=0
  FOUND_IN_DATABASE=1
  CLEAN_ALL=1
  CLEAN_ALL_FORCED=0
  CLEAN_PASS0=1
  CLEAN_PASS1=1
  CLEAN_PASS2=1
  CLEAN_PASS_INDEPENDENT=1
  ALLOC_WORKITEM_PASS0=1
  ALLOC_WORKITEM_PASS1=1
  ALLOC_WORKITEM_PASS2=0
  ALLOC_WORKITEM_PASS_INDEPENDENT=0
  SOURCES_READ=1
  NODE_DIR=1
  DIRS_PROJ_SKIPPED=0
  MSBUILD_PROJECT_FILE_READ=0
  MSBUILD_POST_BUILD_PROJECT_FILE_READ=0
  TEST_CODE=0
  SAMPLE_CODE=0
  TESTCODE_SKIPPED=0
  PASS0_NEEDED=1
  PASS1_NEEDED=1
  PASS2_NEEDED=0
  PASS3_NEEDED=0
  SOURCES_DEP_READ=0
  QUEUE_TO_PASS_LIST_1=1
  QUEUE_TO_PASS_LIST_2=1
  QUEUE_TO_PASS_LIST_3=0
  ADD_DEPENDENCIES_TO_GRAPH=1
  SET_NON_PRODUCT=0
  SET_NON_CRITICAL=0
  SET_CS_WINDOWS=0
  SET_CS_XBOX=0
  SET_CS_PHONE=0
  SET_CS_AZURE=0
  SCAN_COMPLETE=1

  BUILD PASS0 EVENTS: onecore\ds\ds\src\aimx\prod\mcpserversample\lib
    BUILD_CREATED=1
    TASK_CREATED=0
    ADDED_TO_DEPENDENCY_GRAPH=1
    ADDED_TO_PASSQ=1
    ADDED_TO_TASKQ=0
    TRANSITION_STANDBYQ_TO_PASSQ=0
    ADDED_TO_PASS0_Q=1
    ADDED_TO_PASS1_Q=0
    ADDED_TO_PASS2_Q=0
    ADDED_TO_PASS3_Q=0
    ADDED_TO_PASS_INDEPENDENT_Q=0
    ADDED_TO_TASK_Q=0
    REMOVED_FROM_PASSQ=1
    DEPENDENT_MOVED_FROM_READYQ_TO_PASSQ=0
    NMAKE_COMMAND_LINE=1
    MSBUILD_COMMAND_LINE=0
    SUBMIT_TO_DISTRIBUTED_SCHEDULER=1
    DISTRIBUTED_SCHEDULER_WORK_STARTED=1
    DISTRIBUTED_SCHEDULER_WORK_COMPLETED=1
    ADDED_TO_READYQ=0
    BYPASSED=0
    SENT_TO_CLIENT=1
    INCREMENTAL=0
    BUILD_PASS_TERMINATED_FOR_ERRORS=0
    BUILD_OPERATION_COMPLETE=1

  BUILD PASS1 EVENTS: onecore\ds\ds\src\aimx\prod\mcpserversample\lib
    BUILD_CREATED=1
    TASK_CREATED=0
    ADDED_TO_DEPENDENCY_GRAPH=1
    ADDED_TO_PASSQ=1
    ADDED_TO_TASKQ=0
    TRANSITION_STANDBYQ_TO_PASSQ=0
    ADDED_TO_PASS0_Q=0
    ADDED_TO_PASS1_Q=1
    ADDED_TO_PASS2_Q=0
    ADDED_TO_PASS3_Q=0
    ADDED_TO_PASS_INDEPENDENT_Q=0
    ADDED_TO_TASK_Q=0
    REMOVED_FROM_PASSQ=1
    DEPENDENT_MOVED_FROM_READYQ_TO_PASSQ=0
    NMAKE_COMMAND_LINE=1
    MSBUILD_COMMAND_LINE=0
    SUBMIT_TO_DISTRIBUTED_SCHEDULER=1
    DISTRIBUTED_SCHEDULER_WORK_STARTED=1
    DISTRIBUTED_SCHEDULER_WORK_COMPLETED=1
    ADDED_TO_READYQ=0
    BYPASSED=0
    SENT_TO_CLIENT=1
    INCREMENTAL=0
    BUILD_PASS_TERMINATED_FOR_ERRORS=0
    BUILD_OPERATION_COMPLETE=1

DIR EVENTS: onecore\ds\ds\src\aimx\prod\mcpserversample\exe (IN FOCUS)
  CREATED=1
  FOUND_IN_DIRS=1
  INITIAL_DIRECTORY=0
  FOUND_IN_DATABASE=1
  CLEAN_ALL=1
  CLEAN_ALL_FORCED=0
  CLEAN_PASS0=1
  CLEAN_PASS1=1
  CLEAN_PASS2=1
  CLEAN_PASS_INDEPENDENT=1
  ALLOC_WORKITEM_PASS0=1
  ALLOC_WORKITEM_PASS1=1
  ALLOC_WORKITEM_PASS2=1
  ALLOC_WORKITEM_PASS_INDEPENDENT=0
  SOURCES_READ=1
  NODE_DIR=1
  DIRS_PROJ_SKIPPED=0
  MSBUILD_PROJECT_FILE_READ=0
  MSBUILD_POST_BUILD_PROJECT_FILE_READ=0
  TEST_CODE=0
  SAMPLE_CODE=0
  TESTCODE_SKIPPED=0
  PASS0_NEEDED=1
  PASS1_NEEDED=1
  PASS2_NEEDED=1
  PASS3_NEEDED=0
  SOURCES_DEP_READ=0
  QUEUE_TO_PASS_LIST_1=1
  QUEUE_TO_PASS_LIST_2=1
  QUEUE_TO_PASS_LIST_3=0
  ADD_DEPENDENCIES_TO_GRAPH=1
  SET_NON_PRODUCT=0
  SET_NON_CRITICAL=0
  SET_CS_WINDOWS=0
  SET_CS_XBOX=0
  SET_CS_PHONE=0
  SET_CS_AZURE=0
  SCAN_COMPLETE=1

  BUILD PASS0 EVENTS: onecore\ds\ds\src\aimx\prod\mcpserversample\exe
    BUILD_CREATED=1
    TASK_CREATED=0
    ADDED_TO_DEPENDENCY_GRAPH=1
    ADDED_TO_PASSQ=1
    ADDED_TO_TASKQ=0
    TRANSITION_STANDBYQ_TO_PASSQ=0
    ADDED_TO_PASS0_Q=1
    ADDED_TO_PASS1_Q=0
    ADDED_TO_PASS2_Q=0
    ADDED_TO_PASS3_Q=0
    ADDED_TO_PASS_INDEPENDENT_Q=0
    ADDED_TO_TASK_Q=0
    REMOVED_FROM_PASSQ=1
    DEPENDENT_MOVED_FROM_READYQ_TO_PASSQ=0
    NMAKE_COMMAND_LINE=1
    MSBUILD_COMMAND_LINE=0
    SUBMIT_TO_DISTRIBUTED_SCHEDULER=1
    DISTRIBUTED_SCHEDULER_WORK_STARTED=1
    DISTRIBUTED_SCHEDULER_WORK_COMPLETED=1
    ADDED_TO_READYQ=0
    BYPASSED=0
    SENT_TO_CLIENT=1
    INCREMENTAL=0
    BUILD_PASS_TERMINATED_FOR_ERRORS=0
    BUILD_OPERATION_COMPLETE=1

  BUILD PASS1 EVENTS: onecore\ds\ds\src\aimx\prod\mcpserversample\exe
    BUILD_CREATED=1
    TASK_CREATED=0
    ADDED_TO_DEPENDENCY_GRAPH=1
    ADDED_TO_PASSQ=1
    ADDED_TO_TASKQ=0
    TRANSITION_STANDBYQ_TO_PASSQ=0
    ADDED_TO_PASS0_Q=0
    ADDED_TO_PASS1_Q=1
    ADDED_TO_PASS2_Q=0
    ADDED_TO_PASS3_Q=0
    ADDED_TO_PASS_INDEPENDENT_Q=0
    ADDED_TO_TASK_Q=0
    REMOVED_FROM_PASSQ=1
    DEPENDENT_MOVED_FROM_READYQ_TO_PASSQ=0
    NMAKE_COMMAND_LINE=1
    MSBUILD_COMMAND_LINE=0
    SUBMIT_TO_DISTRIBUTED_SCHEDULER=1
    DISTRIBUTED_SCHEDULER_WORK_STARTED=1
    DISTRIBUTED_SCHEDULER_WORK_COMPLETED=1
    ADDED_TO_READYQ=0
    BYPASSED=0
    SENT_TO_CLIENT=1
    INCREMENTAL=0
    BUILD_PASS_TERMINATED_FOR_ERRORS=0
    BUILD_OPERATION_COMPLETE=1

  BUILD PASS2 EVENTS: onecore\ds\ds\src\aimx\prod\mcpserversample\exe
    BUILD_CREATED=1
    TASK_CREATED=0
    ADDED_TO_DEPENDENCY_GRAPH=1
    ADDED_TO_PASSQ=1
    ADDED_TO_TASKQ=0
    TRANSITION_STANDBYQ_TO_PASSQ=0
    ADDED_TO_PASS0_Q=0
    ADDED_TO_PASS1_Q=0
    ADDED_TO_PASS2_Q=1
    ADDED_TO_PASS3_Q=0
    ADDED_TO_PASS_INDEPENDENT_Q=0
    ADDED_TO_TASK_Q=0
    REMOVED_FROM_PASSQ=1
    DEPENDENT_MOVED_FROM_READYQ_TO_PASSQ=0
    NMAKE_COMMAND_LINE=0
    MSBUILD_COMMAND_LINE=0
    SUBMIT_TO_DISTRIBUTED_SCHEDULER=0
    DISTRIBUTED_SCHEDULER_WORK_STARTED=0
    DISTRIBUTED_SCHEDULER_WORK_COMPLETED=0
    ADDED_TO_READYQ=0
    BYPASSED=0
    SENT_TO_CLIENT=0
    INCREMENTAL=0
    BUILD_PASS_TERMINATED_FOR_ERRORS=1
    BUILD_OPERATION_COMPLETE=1

DIR EVENTS: onecore\ds\ds\src\aimx\prod\admcpsvr (IN FOCUS)
  CREATED=1
  FOUND_IN_DIRS=1
  INITIAL_DIRECTORY=0
  FOUND_IN_DATABASE=1
  CLEAN_ALL=1
  CLEAN_ALL_FORCED=0
  CLEAN_PASS0=1
  CLEAN_PASS1=1
  CLEAN_PASS2=1
  CLEAN_PASS_INDEPENDENT=1
  ALLOC_WORKITEM_PASS0=1
  ALLOC_WORKITEM_PASS1=1
  ALLOC_WORKITEM_PASS2=0
  ALLOC_WORKITEM_PASS_INDEPENDENT=0
  SOURCES_READ=1
  NODE_DIR=1
  DIRS_PROJ_SKIPPED=0
  MSBUILD_PROJECT_FILE_READ=0
  MSBUILD_POST_BUILD_PROJECT_FILE_READ=0
  TEST_CODE=0
  SAMPLE_CODE=0
  TESTCODE_SKIPPED=0
  PASS0_NEEDED=1
  PASS1_NEEDED=1
  PASS2_NEEDED=0
  PASS3_NEEDED=0
  SOURCES_DEP_READ=0
  QUEUE_TO_PASS_LIST_1=1
  QUEUE_TO_PASS_LIST_2=1
  QUEUE_TO_PASS_LIST_3=0
  ADD_DEPENDENCIES_TO_GRAPH=1
  SET_NON_PRODUCT=0
  SET_NON_CRITICAL=0
  SET_CS_WINDOWS=0
  SET_CS_XBOX=0
  SET_CS_PHONE=0
  SET_CS_AZURE=0
  SCAN_COMPLETE=1

  BUILD PASS0 EVENTS: onecore\ds\ds\src\aimx\prod\admcpsvr
    BUILD_CREATED=1
    TASK_CREATED=0
    ADDED_TO_DEPENDENCY_GRAPH=1
    ADDED_TO_PASSQ=1
    ADDED_TO_TASKQ=0
    TRANSITION_STANDBYQ_TO_PASSQ=0
    ADDED_TO_PASS0_Q=1
    ADDED_TO_PASS1_Q=0
    ADDED_TO_PASS2_Q=0
    ADDED_TO_PASS3_Q=0
    ADDED_TO_PASS_INDEPENDENT_Q=0
    ADDED_TO_TASK_Q=0
    REMOVED_FROM_PASSQ=1
    DEPENDENT_MOVED_FROM_READYQ_TO_PASSQ=0
    NMAKE_COMMAND_LINE=1
    MSBUILD_COMMAND_LINE=0
    SUBMIT_TO_DISTRIBUTED_SCHEDULER=1
    DISTRIBUTED_SCHEDULER_WORK_STARTED=1
    DISTRIBUTED_SCHEDULER_WORK_COMPLETED=1
    ADDED_TO_READYQ=0
    BYPASSED=0
    SENT_TO_CLIENT=1
    INCREMENTAL=0
    BUILD_PASS_TERMINATED_FOR_ERRORS=0
    BUILD_OPERATION_COMPLETE=1

  BUILD PASS1 EVENTS: onecore\ds\ds\src\aimx\prod\admcpsvr
    BUILD_CREATED=1
    TASK_CREATED=0
    ADDED_TO_DEPENDENCY_GRAPH=1
    ADDED_TO_PASSQ=1
    ADDED_TO_TASKQ=0
    TRANSITION_STANDBYQ_TO_PASSQ=0
    ADDED_TO_PASS0_Q=0
    ADDED_TO_PASS1_Q=1
    ADDED_TO_PASS2_Q=0
    ADDED_TO_PASS3_Q=0
    ADDED_TO_PASS_INDEPENDENT_Q=0
    ADDED_TO_TASK_Q=0
    REMOVED_FROM_PASSQ=1
    DEPENDENT_MOVED_FROM_READYQ_TO_PASSQ=0
    NMAKE_COMMAND_LINE=1
    MSBUILD_COMMAND_LINE=0
    SUBMIT_TO_DISTRIBUTED_SCHEDULER=1
    DISTRIBUTED_SCHEDULER_WORK_STARTED=1
    DISTRIBUTED_SCHEDULER_WORK_COMPLETED=1
    ADDED_TO_READYQ=0
    BYPASSED=0
    SENT_TO_CLIENT=1
    INCREMENTAL=0
    BUILD_PASS_TERMINATED_FOR_ERRORS=0
    BUILD_OPERATION_COMPLETE=1

DIR EVENTS: onecore\ds\ds\src\aimx\prod\aimxsrv\client\dll (IN FOCUS)
  CREATED=1
  FOUND_IN_DIRS=1
  INITIAL_DIRECTORY=0
  FOUND_IN_DATABASE=1
  CLEAN_ALL=1
  CLEAN_ALL_FORCED=0
  CLEAN_PASS0=1
  CLEAN_PASS1=1
  CLEAN_PASS2=1
  CLEAN_PASS_INDEPENDENT=1
  ALLOC_WORKITEM_PASS0=1
  ALLOC_WORKITEM_PASS1=1
  ALLOC_WORKITEM_PASS2=1
  ALLOC_WORKITEM_PASS_INDEPENDENT=0
  SOURCES_READ=1
  NODE_DIR=1
  DIRS_PROJ_SKIPPED=0
  MSBUILD_PROJECT_FILE_READ=0
  MSBUILD_POST_BUILD_PROJECT_FILE_READ=0
  TEST_CODE=0
  SAMPLE_CODE=0
  TESTCODE_SKIPPED=0
  PASS0_NEEDED=1
  PASS1_NEEDED=1
  PASS2_NEEDED=1
  PASS3_NEEDED=0
  SOURCES_DEP_READ=0
  QUEUE_TO_PASS_LIST_1=1
  QUEUE_TO_PASS_LIST_2=1
  QUEUE_TO_PASS_LIST_3=0
  ADD_DEPENDENCIES_TO_GRAPH=1
  SET_NON_PRODUCT=0
  SET_NON_CRITICAL=0
  SET_CS_WINDOWS=0
  SET_CS_XBOX=0
  SET_CS_PHONE=0
  SET_CS_AZURE=0
  SCAN_COMPLETE=1

  BUILD PASS0 EVENTS: onecore\ds\ds\src\aimx\prod\aimxsrv\client\dll
    BUILD_CREATED=1
    TASK_CREATED=0
    ADDED_TO_DEPENDENCY_GRAPH=1
    ADDED_TO_PASSQ=1
    ADDED_TO_TASKQ=0
    TRANSITION_STANDBYQ_TO_PASSQ=0
    ADDED_TO_PASS0_Q=1
    ADDED_TO_PASS1_Q=0
    ADDED_TO_PASS2_Q=0
    ADDED_TO_PASS3_Q=0
    ADDED_TO_PASS_INDEPENDENT_Q=0
    ADDED_TO_TASK_Q=0
    REMOVED_FROM_PASSQ=1
    DEPENDENT_MOVED_FROM_READYQ_TO_PASSQ=0
    NMAKE_COMMAND_LINE=1
    MSBUILD_COMMAND_LINE=0
    SUBMIT_TO_DISTRIBUTED_SCHEDULER=1
    DISTRIBUTED_SCHEDULER_WORK_STARTED=1
    DISTRIBUTED_SCHEDULER_WORK_COMPLETED=1
    ADDED_TO_READYQ=0
    BYPASSED=0
    SENT_TO_CLIENT=1
    INCREMENTAL=0
    BUILD_PASS_TERMINATED_FOR_ERRORS=0
    BUILD_OPERATION_COMPLETE=1

  BUILD PASS1 EVENTS: onecore\ds\ds\src\aimx\prod\aimxsrv\client\dll
    BUILD_CREATED=1
    TASK_CREATED=0
    ADDED_TO_DEPENDENCY_GRAPH=1
    ADDED_TO_PASSQ=1
    ADDED_TO_TASKQ=0
    TRANSITION_STANDBYQ_TO_PASSQ=0
    ADDED_TO_PASS0_Q=0
    ADDED_TO_PASS1_Q=1
    ADDED_TO_PASS2_Q=0
    ADDED_TO_PASS3_Q=0
    ADDED_TO_PASS_INDEPENDENT_Q=0
    ADDED_TO_TASK_Q=0
    REMOVED_FROM_PASSQ=1
    DEPENDENT_MOVED_FROM_READYQ_TO_PASSQ=0
    NMAKE_COMMAND_LINE=1
    MSBUILD_COMMAND_LINE=0
    SUBMIT_TO_DISTRIBUTED_SCHEDULER=1
    DISTRIBUTED_SCHEDULER_WORK_STARTED=1
    DISTRIBUTED_SCHEDULER_WORK_COMPLETED=1
    ADDED_TO_READYQ=0
    BYPASSED=0
    SENT_TO_CLIENT=1
    INCREMENTAL=0
    BUILD_PASS_TERMINATED_FOR_ERRORS=0
    BUILD_OPERATION_COMPLETE=1

  BUILD PASS2 EVENTS: onecore\ds\ds\src\aimx\prod\aimxsrv\client\dll
    BUILD_CREATED=1
    TASK_CREATED=0
    ADDED_TO_DEPENDENCY_GRAPH=1
    ADDED_TO_PASSQ=1
    ADDED_TO_TASKQ=0
    TRANSITION_STANDBYQ_TO_PASSQ=0
    ADDED_TO_PASS0_Q=0
    ADDED_TO_PASS1_Q=0
    ADDED_TO_PASS2_Q=1
    ADDED_TO_PASS3_Q=0
    ADDED_TO_PASS_INDEPENDENT_Q=0
    ADDED_TO_TASK_Q=0
    REMOVED_FROM_PASSQ=1
    DEPENDENT_MOVED_FROM_READYQ_TO_PASSQ=0
    NMAKE_COMMAND_LINE=1
    MSBUILD_COMMAND_LINE=0
    SUBMIT_TO_DISTRIBUTED_SCHEDULER=1
    DISTRIBUTED_SCHEDULER_WORK_STARTED=1
    DISTRIBUTED_SCHEDULER_WORK_COMPLETED=1
    ADDED_TO_READYQ=0
    BYPASSED=0
    SENT_TO_CLIENT=1
    INCREMENTAL=0
    BUILD_PASS_TERMINATED_FOR_ERRORS=0
    BUILD_OPERATION_COMPLETE=1

DIR EVENTS: onecore\ds\ds\src\aimx\prod\aimxsrv\client\lib (IN FOCUS)
  CREATED=1
  FOUND_IN_DIRS=1
  INITIAL_DIRECTORY=0
  FOUND_IN_DATABASE=1
  CLEAN_ALL=1
  CLEAN_ALL_FORCED=0
  CLEAN_PASS0=1
  CLEAN_PASS1=1
  CLEAN_PASS2=1
  CLEAN_PASS_INDEPENDENT=1
  ALLOC_WORKITEM_PASS0=1
  ALLOC_WORKITEM_PASS1=1
  ALLOC_WORKITEM_PASS2=0
  ALLOC_WORKITEM_PASS_INDEPENDENT=0
  SOURCES_READ=1
  NODE_DIR=1
  DIRS_PROJ_SKIPPED=0
  MSBUILD_PROJECT_FILE_READ=0
  MSBUILD_POST_BUILD_PROJECT_FILE_READ=0
  TEST_CODE=0
  SAMPLE_CODE=0
  TESTCODE_SKIPPED=0
  PASS0_NEEDED=1
  PASS1_NEEDED=1
  PASS2_NEEDED=0
  PASS3_NEEDED=0
  SOURCES_DEP_READ=0
  QUEUE_TO_PASS_LIST_1=1
  QUEUE_TO_PASS_LIST_2=1
  QUEUE_TO_PASS_LIST_3=0
  ADD_DEPENDENCIES_TO_GRAPH=1
  SET_NON_PRODUCT=0
  SET_NON_CRITICAL=0
  SET_CS_WINDOWS=0
  SET_CS_XBOX=0
  SET_CS_PHONE=0
  SET_CS_AZURE=0
  SCAN_COMPLETE=1

  BUILD PASS0 EVENTS: onecore\ds\ds\src\aimx\prod\aimxsrv\client\lib
    BUILD_CREATED=1
    TASK_CREATED=0
    ADDED_TO_DEPENDENCY_GRAPH=1
    ADDED_TO_PASSQ=1
    ADDED_TO_TASKQ=0
    TRANSITION_STANDBYQ_TO_PASSQ=0
    ADDED_TO_PASS0_Q=1
    ADDED_TO_PASS1_Q=0
    ADDED_TO_PASS2_Q=0
    ADDED_TO_PASS3_Q=0
    ADDED_TO_PASS_INDEPENDENT_Q=0
    ADDED_TO_TASK_Q=0
    REMOVED_FROM_PASSQ=1
    DEPENDENT_MOVED_FROM_READYQ_TO_PASSQ=0
    NMAKE_COMMAND_LINE=1
    MSBUILD_COMMAND_LINE=0
    SUBMIT_TO_DISTRIBUTED_SCHEDULER=1
    DISTRIBUTED_SCHEDULER_WORK_STARTED=1
    DISTRIBUTED_SCHEDULER_WORK_COMPLETED=1
    ADDED_TO_READYQ=0
    BYPASSED=0
    SENT_TO_CLIENT=1
    INCREMENTAL=0
    BUILD_PASS_TERMINATED_FOR_ERRORS=0
    BUILD_OPERATION_COMPLETE=1

  BUILD PASS1 EVENTS: onecore\ds\ds\src\aimx\prod\aimxsrv\client\lib
    BUILD_CREATED=1
    TASK_CREATED=0
    ADDED_TO_DEPENDENCY_GRAPH=1
    ADDED_TO_PASSQ=1
    ADDED_TO_TASKQ=0
    TRANSITION_STANDBYQ_TO_PASSQ=0
    ADDED_TO_PASS0_Q=0
    ADDED_TO_PASS1_Q=1
    ADDED_TO_PASS2_Q=0
    ADDED_TO_PASS3_Q=0
    ADDED_TO_PASS_INDEPENDENT_Q=0
    ADDED_TO_TASK_Q=0
    REMOVED_FROM_PASSQ=1
    DEPENDENT_MOVED_FROM_READYQ_TO_PASSQ=0
    NMAKE_COMMAND_LINE=1
    MSBUILD_COMMAND_LINE=0
    SUBMIT_TO_DISTRIBUTED_SCHEDULER=1
    DISTRIBUTED_SCHEDULER_WORK_STARTED=1
    DISTRIBUTED_SCHEDULER_WORK_COMPLETED=1
    ADDED_TO_READYQ=0
    BYPASSED=0
    SENT_TO_CLIENT=1
    INCREMENTAL=0
    BUILD_PASS_TERMINATED_FOR_ERRORS=0
    BUILD_OPERATION_COMPLETE=1

DIR EVENTS: onecore\ds\ds\src\aimx\prod\aimxsrv\powershell (IN FOCUS)
  CREATED=1
  FOUND_IN_DIRS=1
  INITIAL_DIRECTORY=0
  FOUND_IN_DATABASE=1
  CLEAN_ALL=1
  CLEAN_ALL_FORCED=0
  CLEAN_PASS0=1
  CLEAN_PASS1=1
  CLEAN_PASS2=1
  CLEAN_PASS_INDEPENDENT=1
  ALLOC_WORKITEM_PASS0=1
  ALLOC_WORKITEM_PASS1=1
  ALLOC_WORKITEM_PASS2=1
  ALLOC_WORKITEM_PASS_INDEPENDENT=0
  SOURCES_READ=1
  NODE_DIR=1
  DIRS_PROJ_SKIPPED=0
  MSBUILD_PROJECT_FILE_READ=0
  MSBUILD_POST_BUILD_PROJECT_FILE_READ=0
  TEST_CODE=0
  SAMPLE_CODE=0
  TESTCODE_SKIPPED=0
  PASS0_NEEDED=1
  PASS1_NEEDED=1
  PASS2_NEEDED=1
  PASS3_NEEDED=0
  SOURCES_DEP_READ=0
  QUEUE_TO_PASS_LIST_1=1
  QUEUE_TO_PASS_LIST_2=1
  QUEUE_TO_PASS_LIST_3=0
  ADD_DEPENDENCIES_TO_GRAPH=1
  SET_NON_PRODUCT=0
  SET_NON_CRITICAL=0
  SET_CS_WINDOWS=0
  SET_CS_XBOX=0
  SET_CS_PHONE=0
  SET_CS_AZURE=0
  SCAN_COMPLETE=1

  BUILD PASS0 EVENTS: onecore\ds\ds\src\aimx\prod\aimxsrv\powershell
    BUILD_CREATED=1
    TASK_CREATED=0
    ADDED_TO_DEPENDENCY_GRAPH=1
    ADDED_TO_PASSQ=1
    ADDED_TO_TASKQ=0
    TRANSITION_STANDBYQ_TO_PASSQ=0
    ADDED_TO_PASS0_Q=1
    ADDED_TO_PASS1_Q=0
    ADDED_TO_PASS2_Q=0
    ADDED_TO_PASS3_Q=0
    ADDED_TO_PASS_INDEPENDENT_Q=0
    ADDED_TO_TASK_Q=0
    REMOVED_FROM_PASSQ=1
    DEPENDENT_MOVED_FROM_READYQ_TO_PASSQ=0
    NMAKE_COMMAND_LINE=1
    MSBUILD_COMMAND_LINE=0
    SUBMIT_TO_DISTRIBUTED_SCHEDULER=1
    DISTRIBUTED_SCHEDULER_WORK_STARTED=1
    DISTRIBUTED_SCHEDULER_WORK_COMPLETED=1
    ADDED_TO_READYQ=0
    BYPASSED=0
    SENT_TO_CLIENT=1
    INCREMENTAL=0
    BUILD_PASS_TERMINATED_FOR_ERRORS=0
    BUILD_OPERATION_COMPLETE=1

  BUILD PASS1 EVENTS: onecore\ds\ds\src\aimx\prod\aimxsrv\powershell
    BUILD_CREATED=1
    TASK_CREATED=0
    ADDED_TO_DEPENDENCY_GRAPH=1
    ADDED_TO_PASSQ=1
    ADDED_TO_TASKQ=0
    TRANSITION_STANDBYQ_TO_PASSQ=0
    ADDED_TO_PASS0_Q=0
    ADDED_TO_PASS1_Q=1
    ADDED_TO_PASS2_Q=0
    ADDED_TO_PASS3_Q=0
    ADDED_TO_PASS_INDEPENDENT_Q=0
    ADDED_TO_TASK_Q=0
    REMOVED_FROM_PASSQ=1
    DEPENDENT_MOVED_FROM_READYQ_TO_PASSQ=0
    NMAKE_COMMAND_LINE=1
    MSBUILD_COMMAND_LINE=0
    SUBMIT_TO_DISTRIBUTED_SCHEDULER=1
    DISTRIBUTED_SCHEDULER_WORK_STARTED=1
    DISTRIBUTED_SCHEDULER_WORK_COMPLETED=1
    ADDED_TO_READYQ=0
    BYPASSED=0
    SENT_TO_CLIENT=1
    INCREMENTAL=0
    BUILD_PASS_TERMINATED_FOR_ERRORS=0
    BUILD_OPERATION_COMPLETE=1

  BUILD PASS2 EVENTS: onecore\ds\ds\src\aimx\prod\aimxsrv\powershell
    BUILD_CREATED=1
    TASK_CREATED=0
    ADDED_TO_DEPENDENCY_GRAPH=1
    ADDED_TO_PASSQ=1
    ADDED_TO_TASKQ=0
    TRANSITION_STANDBYQ_TO_PASSQ=0
    ADDED_TO_PASS0_Q=0
    ADDED_TO_PASS1_Q=0
    ADDED_TO_PASS2_Q=1
    ADDED_TO_PASS3_Q=0
    ADDED_TO_PASS_INDEPENDENT_Q=0
    ADDED_TO_TASK_Q=0
    REMOVED_FROM_PASSQ=1
    DEPENDENT_MOVED_FROM_READYQ_TO_PASSQ=0
    NMAKE_COMMAND_LINE=1
    MSBUILD_COMMAND_LINE=0
    SUBMIT_TO_DISTRIBUTED_SCHEDULER=1
    DISTRIBUTED_SCHEDULER_WORK_STARTED=1
    DISTRIBUTED_SCHEDULER_WORK_COMPLETED=1
    ADDED_TO_READYQ=0
    BYPASSED=0
    SENT_TO_CLIENT=1
    INCREMENTAL=0
    BUILD_PASS_TERMINATED_FOR_ERRORS=0
    BUILD_OPERATION_COMPLETE=1
