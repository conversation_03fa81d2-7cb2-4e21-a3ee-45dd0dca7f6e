/*++

Copyright (c) Microsoft Corporation. All rights reserved.

Module Name:

    ConversationManager.cpp

Abstract:

    Implementation of the Conversation Session Manager that manages conversation sessions
    using existing AIMX context IDs. 

Author:

    <PERSON><PERSON><PERSON> (rizhang) 07/13/2025

--*/

#include "pch.hxx"
#include "ConversationManager.h"
#include "StringUtils.h"
#include "AimxConstants.h"
#include "ConversationManager.cpp.tmh"

extern "C" void* __RPC_USER MIDL_user_allocate(size_t NumBytes);
extern "C" void __RPC_USER MIDL_user_free(void* p);

// Static member definitions
ConversationSessionManager* ConversationSessionManager::s_instance = nullptr;
std::mutex ConversationSessionManager::s_instanceMutex;

// External declarations from existing code
extern std::unordered_map<GUID, std::shared_ptr<AIMX_OPERATION>, GuidHash, GuidEqual> g_OperationMap;
extern std::mutex g_OperationMapMutex;

// Borrow a GUID_NULL
#ifndef GUID_NULL
struct __declspec(uuid("00000000-0000-0000-0000-000000000000")) GUID_NULL;
#define GUID_NULL __uuidof(struct GUID_NULL)
#endif

HRESULT
ConversationSessionManager::Initialize()
/*++

Routine Description:
    Initialize the Conversation Session Manager component.

Arguments:
    None.

Return Value:
    S_OK on success, or an error HRESULT on failure.

--*/
{
    std::lock_guard<std::mutex> lock(s_instanceMutex);

    TraceInfo(AimxConversationMgr, "Initializing Conversation Session Manager component");

    if (s_instance != nullptr)
    {
        TraceInfo(AimxConversationMgr, "Conversation Session Manager component already initialized");
        return AIMX_S_COMPONENT_ALREADY_INITIALIZED;
    }

    s_instance = new (std::nothrow) ConversationSessionManager();
    if (s_instance == nullptr)
    {
        TraceErr(AimxConversationMgr, "Failed to allocate Conversation Session Manager instance");
        return E_OUTOFMEMORY;
    }

    HRESULT hr = s_instance->InitializeInternal();
    if (FAILED(hr))
    {
        TraceErr(AimxConversationMgr, "Failed to initialize Conversation Session Manager internal state: %!HRESULT!", hr);
        delete s_instance;
        s_instance = nullptr;
        return hr;
    }

    s_instance->m_initialized = true;

    TraceInfo(AimxConversationMgr, "Conversation Session Manager component initialized successfully");
    return S_OK;
}

HRESULT
ConversationSessionManager::InitializeInternal()
/*++

Routine Description:
    Internal initialization method that sets up the session manager.

Arguments:
    None.

Return Value:
    S_OK on success, or an error HRESULT on failure.

--*/
{
    TraceInfo(AimxConversationMgr, "Initializing Conversation Session Manager internal state");

    try
    {
        // Initialize session map (already done in constructor)
        TraceInfo(AimxConversationMgr, "Conversation Session Manager internal state initialized successfully");
        return S_OK;
    }
    catch (...)
    {
        TraceErr(AimxConversationMgr, "Exception during Conversation Session Manager initialization");
        return E_FAIL;
    }
}

void
ConversationSessionManager::Uninitialize()
/*++

Routine Description:
    Uninitialize the Conversation Session Manager component and cleanup resources.

Arguments:
    None.

Return Value:
    None.

--*/
{
    std::lock_guard<std::mutex> lock(s_instanceMutex);
    
    TraceInfo(AimxConversationMgr, "Uninitializing Conversation Session Manager component");
    
    if (s_instance != nullptr)
    {
        // Close all sessions
        {
            std::lock_guard<std::mutex> sessionLock(s_instance->m_sessionsMutex);
            s_instance->m_sessions.clear();
        }

        delete s_instance;
        s_instance = nullptr;
    }
    
    TraceInfo(AimxConversationMgr, "Conversation Session Manager component uninitialized");
}

ConversationSessionManager*
ConversationSessionManager::GetInstance()
/*++

Routine Description:
    Get the singleton instance of the Conversation Session Manager.

Arguments:
    None.

Return Value:
    Pointer to the singleton instance, or nullptr if not initialized.

--*/
{
    std::lock_guard<std::mutex> lock(s_instanceMutex);
    return s_instance;
}

HRESULT
ConversationSessionManager::GetOrCreateSession(
    _In_ const GUID& contextId,
    _Out_ std::shared_ptr<ConversationSession>& session
    )
/*++

Routine Description:
    Get existing session or create new session for the given context ID.

Arguments:
    contextId - The AIMX context ID to use as session ID
    session - Output session object

Return Value:
    S_OK on success, or an error HRESULT on failure.

--*/
{
    if (s_instance == nullptr)
    {
        return AIMX_E_COMPONENT_NOT_INITIALIZED;
    }
    
    TraceInfo(AimxConversationMgr, "Getting or creating session for context: %!GUID!", &contextId);
    
    std::lock_guard<std::mutex> lock(s_instance->m_sessionsMutex);
    
    auto it = s_instance->m_sessions.find(contextId);
    if (it != s_instance->m_sessions.end())
    {
        session = it->second;
        TraceInfo(AimxConversationMgr, "Found existing session for context: %!GUID!", &contextId);
        return S_OK;
    }

    // Create new session using context ID
    session = std::make_shared<ConversationSession>(contextId);
    HRESULT hr = session->Initialize();
    if (FAILED(hr))
    {
        TraceErr(AimxConversationMgr, "Failed to initialize new session: %!HRESULT!", hr);
        return hr;
    }

    s_instance->m_sessions[contextId] = session;
    TraceInfo(AimxConversationMgr, "Created new conversation session for context: %!GUID!", &contextId);
    
    return S_OK;
}

HRESULT
ConversationSessionManager::GetSession(
    _In_ const GUID& contextId,
    _Out_ std::shared_ptr<ConversationSession>& session
    )
/*++

Routine Description:
    Get existing session for the given context ID.

Arguments:
    contextId - The AIMX context ID to use as session ID
    session - Output session object

Return Value:
    S_OK on success, or an error HRESULT on failure.

--*/
{
    if (s_instance == nullptr)
    {
        return AIMX_E_COMPONENT_NOT_INITIALIZED;
    }
    
    std::lock_guard<std::mutex> lock(s_instance->m_sessionsMutex);
    
    auto it = s_instance->m_sessions.find(contextId);
    if (it == s_instance->m_sessions.end())
    {
        return HRESULT_FROM_WIN32(ERROR_NOT_FOUND);
    }

    session = it->second;
    return S_OK;
}

HRESULT
ConversationSessionManager::GetSessionByOperationId(
    _In_ const GUID& operationId,
    _Out_ std::shared_ptr<ConversationSession>& session
    )
/*++

Routine Description:
    Get existing session for the given operation ID.

Arguments:
    operationId - The operation ID to look up
    session - Output session object

Return Value:
    S_OK on success, or an error HRESULT on failure.

--*/
{
    if (s_instance == nullptr)
    {
        return AIMX_E_COMPONENT_NOT_INITIALIZED;
    }

    std::lock_guard<std::mutex> lock(s_instance->m_sessionsMutex);

    // Find context ID for this operation
    auto opIt = s_instance->m_operationToContextMap.find(operationId);
    if (opIt == s_instance->m_operationToContextMap.end())
    {
        return HRESULT_FROM_WIN32(ERROR_NOT_FOUND);
    }

    GUID contextId = opIt->second;

    // Find session for this context
    auto sessionIt = s_instance->m_sessions.find(contextId);
    if (sessionIt == s_instance->m_sessions.end())
    {
        return HRESULT_FROM_WIN32(ERROR_NOT_FOUND);
    }

    session = sessionIt->second;
    return S_OK;
}

HRESULT
ConversationSessionManager::CloseSession(
    _In_ const GUID& contextId
    )
/*++

Routine Description:
    Close session for the given context ID.

Arguments:
    contextId - The AIMX context ID to close

Return Value:
    S_OK on success, or an error HRESULT on failure.

--*/
{
    if (s_instance == nullptr)
    {
        return AIMX_E_COMPONENT_NOT_INITIALIZED;
    }
    
    std::lock_guard<std::mutex> lock(s_instance->m_sessionsMutex);
    
    auto it = s_instance->m_sessions.find(contextId);
    if (it != s_instance->m_sessions.end())
    {
        it->second->Shutdown();
        s_instance->m_sessions.erase(it);
        TraceInfo(AimxConversationMgr, "Closed conversation session for context: %!GUID!", &contextId);
    }
    
    return S_OK;
}

HRESULT
ConversationSessionManager::NotifyOperationStarted(
    _In_ const GUID& contextId,
    _In_ const GUID& operationId
    )
/*++

Routine Description:
    Notify that an operation has started for the given context.

Arguments:
    contextId - The AIMX context ID
    operationId - The operation ID

Return Value:
    S_OK on success, or an error HRESULT on failure.

--*/
{
    std::shared_ptr<ConversationSession> session;
    HRESULT hr = GetOrCreateSession(contextId, session);
    if (FAILED(hr))
    {
        return hr;
    }

    // Store operation to context mapping
    {
        std::lock_guard<std::mutex> lock(s_instance->m_sessionsMutex);
        s_instance->m_operationToContextMap[operationId] = contextId;
    }

    session->SetCurrentOperation(operationId);

    // Set initial stage and send progress update
    std::wstring initialStage = AimxConstants::MessageStages::AIMX_STAGE_PLANNING_START;
    std::wstring initialMessage = AimxConstants::Messages::AIMX_MSG_OPERATION_STARTED;
    session->UpdateStatus(initialStage, initialMessage);

    return S_OK;
}

HRESULT
ConversationSessionManager::NotifyOperationProgress(
    _In_ const GUID& operationId,
    _In_ const std::wstring& message
    )
/*++

Routine Description:
    Notify progress for an operation.

Arguments:
    operationId - The operation ID
    message - Progress message

Return Value:
    S_OK on success, or an error HRESULT on failure.

--*/
{
    if (s_instance == nullptr)
    {
        return AIMX_E_COMPONENT_NOT_INITIALIZED;
    }

    // Find context ID for this operation
    GUID contextId;
    {
        std::lock_guard<std::mutex> lock(s_instance->m_sessionsMutex);
        auto it = s_instance->m_operationToContextMap.find(operationId);
        if (it == s_instance->m_operationToContextMap.end())
        {
            TraceInfo(AimxConversationMgr, "Operation not found in context map: %!GUID!", &operationId);
            return S_OK; // Not an error, operation might not have a conversation session
        }
        contextId = it->second;
    }

    std::shared_ptr<ConversationSession> session;
    HRESULT hr = GetSession(contextId, session);
    if (SUCCEEDED(hr) && session->IsActive())
    {
        // Extract stage from message if present (format: [STAGE] description)
        std::wstring stage;
        std::wstring description;
        ExtractStageFromMessage(message, stage, description);

        if (!stage.empty())
        {
            // Update the session's current stage
            TraceInfo(AimxConversationMgr, "Updating session stage to: %ws, description: %ws", stage.c_str(), description.c_str());
            session->UpdateStatus(stage, description);
        }
        else
        {
            // No stage in message, just send as progress update
            TraceInfo(AimxConversationMgr, "No stage found in message, sending as progress update: %ws", message.c_str());
            session->SendProgressUpdate(message);
        }
    }

    return S_OK;
}



HRESULT
ConversationSessionManager::NotifyOperationCompleted(
    _In_ const GUID& operationId,
    _In_ const std::wstring& result
    )
/*++

Routine Description:
    Notify that an operation has completed.

Arguments:
    operationId - The operation ID
    result - Operation result

Return Value:
    S_OK on success, or an error HRESULT on failure.

--*/
{
    TraceInfo(AimxConversationMgr, "NotifyOperationCompleted called for operation: %!GUID! with result: %ws",
             &operationId, result.c_str());

    if (s_instance == nullptr)
    {
        TraceErr(AimxConversationMgr, "ConversationSessionManager not initialized");
        return AIMX_E_COMPONENT_NOT_INITIALIZED;
    }

    // Find context ID for this operation
    GUID contextId;
    {
        std::lock_guard<std::mutex> lock(s_instance->m_sessionsMutex);
        auto it = s_instance->m_operationToContextMap.find(operationId);
        if (it == s_instance->m_operationToContextMap.end())
        {
            TraceInfo(AimxConversationMgr, "Operation not found in context map: %!GUID!", &operationId);
            return S_OK; // Not an error, operation might not have a conversation session
        }
        contextId = it->second;
        TraceInfo(AimxConversationMgr, "Found context ID: %!GUID! for operation: %!GUID!", &contextId, &operationId);

        // Remove from operation map
        s_instance->m_operationToContextMap.erase(it);
    }

    std::shared_ptr<ConversationSession> session;
    HRESULT hr = GetSession(contextId, session);
    if (SUCCEEDED(hr) && session->IsActive())
    {
        TraceInfo(AimxConversationMgr, "Sending assistant response to active session");

        // Extract stage from result if present, otherwise use completion stage
        std::wstring stage;
        std::wstring description;
        ExtractStageFromMessage(result, stage, description);

        if (stage.empty())
        {
            stage = L"COMPLETED";
            description = result;
        }

        // Send final assistant response (this will be the completion message)
        // Don't send a separate status update to avoid duplication with tool result messages
        session->SendMessage(AIMX_MSG_ASSISTANT_RESPONSE, description);
        session->SetState(AIMX_SESSION_COMPLETED);
        TraceInfo(AimxConversationMgr, "Successfully sent completion notification to frontend");
    }
    else
    {
        TraceWarn(AimxConversationMgr, "Session not found or not active for context: %!GUID!", &contextId);
    }

    return S_OK;
}

HRESULT
ConversationSessionManager::NotifyOperationFailed(
    _In_ const GUID& operationId,
    _In_ const std::wstring& error
    )
/*++

Routine Description:
    Notify that an operation has failed.

Arguments:
    operationId - The operation ID
    error - Error message

Return Value:
    S_OK on success, or an error HRESULT on failure.

--*/
{
    if (s_instance == nullptr)
    {
        return AIMX_E_COMPONENT_NOT_INITIALIZED;
    }

    // Find context ID for this operation
    GUID contextId;
    {
        std::lock_guard<std::mutex> lock(s_instance->m_sessionsMutex);
        auto it = s_instance->m_operationToContextMap.find(operationId);
        if (it == s_instance->m_operationToContextMap.end())
        {
            TraceInfo(AimxConversationMgr, "Operation not found in context map: %!GUID!", &operationId);
            return S_OK; // Not an error, operation might not have a conversation session
        }
        contextId = it->second;

        // Remove from operation map
        s_instance->m_operationToContextMap.erase(it);
    }

    std::shared_ptr<ConversationSession> session;
    HRESULT hr = GetSession(contextId, session);
    if (SUCCEEDED(hr) && session->IsActive())
    {
        // Extract stage from error if present, otherwise use error stage
        std::wstring stage;
        std::wstring description;
        ExtractStageFromMessage(error, stage, description);

        if (stage.empty())
        {
            stage = L"ERROR";
            description = error;
        }

        // Update status to error stage
        session->UpdateStatus(stage, description);
        session->SendMessage(AIMX_MSG_ERROR_MESSAGE, error);
        session->SetState(AIMX_SESSION_ERROR);
    }

    return S_OK;
}

//
// ConversationSessionManager Private Methods
//

ConversationSessionManager::ConversationSessionManager()
    : m_initialized(false)
{
    TraceInfo(AimxConversationMgr, "ConversationSessionManager constructor");
}

ConversationSessionManager::~ConversationSessionManager()
{
    TraceInfo(AimxConversationMgr, "ConversationSessionManager destructor");
}

//
// ConversationSession Implementation
//

ConversationSession::ConversationSession(const GUID& contextId)
    : m_ContextId(contextId)
    , m_State(AIMX_SESSION_ACTIVE)
    , m_IsActive(true)
    , m_CurrentOperationId(GUID_NULL)
{
    TraceInfo(AimxConversationMgr, "ConversationSession created for context: %!GUID!", &contextId);
}

ConversationSession::~ConversationSession()
{
    Shutdown();
    TraceInfo(AimxConversationMgr, "ConversationSession destroyed for context: %!GUID!", &m_ContextId);
}

HRESULT
ConversationSession::Initialize()
/*++

Routine Description:
    Initialize the conversation session.

Arguments:
    None.

Return Value:
    S_OK on success, or an error HRESULT on failure.

--*/
{
    std::lock_guard<std::mutex> lock(m_SessionMutex);
    m_IsActive = true;
    m_State = AIMX_SESSION_ACTIVE;
    return S_OK;
}

HRESULT
ConversationSession::Shutdown()
/*++

Routine Description:
    Shutdown the conversation session and cleanup resources.

Arguments:
    None.

Return Value:
    S_OK on success, or an error HRESULT on failure.

--*/
{
    std::lock_guard<std::mutex> lock(m_SessionMutex);
    m_IsActive = false;
    m_QueuedMessages.clear(); // Clear any pending messages
    return S_OK;
}

HRESULT
ConversationSession::AddMessage(
    _In_ const AIMX_CONVERSATION_MESSAGE& message
    )
/*++

Routine Description:
    Add a message to the conversation history.

Arguments:
    message - The message to add

Return Value:
    S_OK on success, or an error HRESULT on failure.

--*/
{
    std::lock_guard<std::mutex> lock(m_SessionMutex);
    m_Messages.push_back(message);
    return S_OK;
}

HRESULT
ConversationSession::SendMessage(
    _In_ AIMX_CONVERSATION_MESSAGE_TYPE type,
    _In_ const std::wstring& content,
    _In_ const nlohmann::json& metadata
    )
/*++

Routine Description:
    Send a message add to history.

Arguments:
    type - Message type
    content - Message content
    metadata - Message metadata

Return Value:
    S_OK on success, or an error HRESULT on failure.

--*/
{
    AIMX_CONVERSATION_MESSAGE message;
    HRESULT hr = CreateConversationMessage(type, content, metadata, message);
    if (FAILED(hr))
    {
        return hr;
    }

    // Add to history
    hr = AddMessage(message);
    if (FAILED(hr))
    {
        return hr;
    }

    // Queue message for polling
    {
        std::lock_guard<std::mutex> lock(m_SessionMutex);
        AIMX_QUEUED_MESSAGE queuedMessage;
        queuedMessage.Message = message;
        queuedMessage.IsDelivered = false;
        m_QueuedMessages.push_back(queuedMessage);

        TraceInfo(AimxConversationMgr, "Message queued for polling. Queue size: %Iu", m_QueuedMessages.size());
    }

    return S_OK;
}

HRESULT
ConversationSession::PollMessages(
    _Out_ std::vector<AIMX_CONVERSATION_MESSAGE>& newMessages
    )
/*++

Routine Description:
    Poll for new conversation messages

Arguments:
    newMessages - Vector to receive new messages

Return Value:
    S_OK on success, or an error HRESULT on failure.

--*/
{
    std::lock_guard<std::mutex> lock(m_SessionMutex);

    newMessages.clear();

    // Collect undelivered messages
    for (auto& queuedMessage : m_QueuedMessages)
    {
        if (!queuedMessage.IsDelivered)
        {
            newMessages.push_back(queuedMessage.Message);
            queuedMessage.IsDelivered = true;
        }
    }

    // Clean up delivered messages periodically
    if (m_QueuedMessages.size() > 100) // Keep last 100 messages
    {
        auto it = std::remove_if(m_QueuedMessages.begin(), m_QueuedMessages.end(),
            [](const AIMX_QUEUED_MESSAGE& msg) { return msg.IsDelivered; });
        m_QueuedMessages.erase(it, m_QueuedMessages.end());
    }

    // Log if there are new messages
    if (newMessages.size() > 0)
    {
        TraceInfo(AimxConversationMgr, "Polled %Iu new messages", newMessages.size());
    }

    return S_OK;
}

HRESULT
ConversationSession::GetCurrentStatus(
    _Out_ std::wstring& statusDescription,
    _Out_ std::wstring& currentStage,
    _Out_ bool& isActive
    )
/*++

Routine Description:
    Get the current status and stage of the conversation session.

Arguments:
    statusDescription - Detailed status description
    currentStage - Current processing stage
    isActive - Whether the session is actively processing

Return Value:
    S_OK on success, or an error HRESULT on failure.

--*/
{
    std::lock_guard<std::mutex> lock(m_SessionMutex);

    statusDescription = m_StatusDescription;
    currentStage = m_CurrentStage;
    isActive = m_IsActive.load();

    // If no explicit status, derive from current state
    if (statusDescription.empty())
    {
        if (!isActive)
        {
            statusDescription = AimxConstants::Messages::AIMX_MSG_SESSION_INACTIVE;
            currentStage = AimxConstants::Messages::AIMX_MSG_STAGE_IDLE;
        }
        else
        {
            statusDescription = AimxConstants::Messages::AIMX_MSG_PROCESSING_REQUEST;
            if (currentStage.empty())
            {
                currentStage = AimxConstants::Messages::AIMX_MSG_STAGE_UNKNOWN;
            }
        }
    }

    TraceInfo(AimxConversationMgr, "Status: %ws, Stage: %ws, Active: %d",
              statusDescription.c_str(), currentStage.c_str(), isActive);

    return S_OK;
}

void
ConversationSession::UpdateStatus(
    _In_ const std::wstring& stage,
    _In_ const std::wstring& description
    )
/*++

Routine Description:
    Update the current status and stage of the conversation session.

Arguments:
    stage - Current processing stage
    description - Detailed status description

Return Value:
    None.

--*/
{
    std::lock_guard<std::mutex> lock(m_SessionMutex);

    m_CurrentStage = stage;
    m_StatusDescription = description;
    GetSystemTimeAsFileTime(&m_LastActivityTime);

    // Send status update message
    AIMX_CONVERSATION_MESSAGE statusMessage;
    statusMessage.Type = AIMX_MSG_STATUS_UPDATE;
    statusMessage.Content = L"[" + stage + L"] " + description;
    statusMessage.Timestamp = m_LastActivityTime;
    statusMessage.Metadata = nlohmann::json::object();
    statusMessage.Metadata["stage"] = WideToUtf8(stage);
    statusMessage.Metadata["description"] = WideToUtf8(description);

    AIMX_QUEUED_MESSAGE queuedMessage;
    queuedMessage.Message = statusMessage;
    queuedMessage.IsDelivered = false;
    m_QueuedMessages.push_back(queuedMessage);

    TraceInfo(AimxConversationMgr, "Status updated: %ws - %ws", stage.c_str(), description.c_str());
}

HRESULT
ConversationSession::SendProgressUpdate(
    _In_ const std::wstring& message
    )
/*++

Routine Description:
    Send a progress update message.

Arguments:
    message - Progress message

Return Value:
    S_OK on success, or an error HRESULT on failure.

--*/
{
    return SendMessage(AIMX_MSG_PROGRESS_UPDATE, message);
}



HRESULT
ConversationSession::SetState(
    _In_ AIMX_CONVERSATION_SESSION_STATE newState
    )
/*++

Routine Description:
    Set the session state.

Arguments:
    newState - New session state

Return Value:
    S_OK on success, or an error HRESULT on failure.

--*/
{
    std::lock_guard<std::mutex> lock(m_SessionMutex);
    m_State = newState;
    return S_OK;
}

HRESULT
ConversationSession::SetCurrentOperation(
    _In_ const GUID& operationId
    )
/*++

Routine Description:
    Set the current operation ID.

Arguments:
    operationId - The operation ID

Return Value:
    S_OK on success, or an error HRESULT on failure.

--*/
{
    std::lock_guard<std::mutex> lock(m_SessionMutex);
    m_CurrentOperationId = operationId;
    return S_OK;
}

//
// Helper Functions
//

HRESULT
CreateConversationMessage(
    _In_ AIMX_CONVERSATION_MESSAGE_TYPE type,
    _In_ const std::wstring& content,
    _In_ const nlohmann::json& metadata,
    _Out_ AIMX_CONVERSATION_MESSAGE& message
    )
/*++

Routine Description:
    Create a conversation message with the specified parameters.

Arguments:
    type - Message type
    content - Message content
    metadata - Message metadata
    message - Output message structure

Return Value:
    S_OK on success, or an error HRESULT on failure.

--*/
{
    HRESULT hr = CoCreateGuid(&message.MessageId);
    if (FAILED(hr))
    {
        return hr;
    }

    message.Type = type;
    message.Content = content;
    message.Metadata = metadata;
    GetSystemTimeAsFileTime(&message.Timestamp);

    return S_OK;
}

std::wstring
ConversationMessageTypeToString(
    _In_ AIMX_CONVERSATION_MESSAGE_TYPE type
    )
/*++

Routine Description:
    Convert conversation message type enum to a meaningful string representation.

Arguments:
    type - The message type enum value

Return Value:
    Wide string representation of the message type.

--*/
{
    switch (type)
    {
        case AIMX_MSG_USER_INPUT:
            return AimxConstants::Messages::AIMX_MSG_TYPE_USER_INPUT;
        case AIMX_MSG_ASSISTANT_RESPONSE:
            return AimxConstants::Messages::AIMX_MSG_TYPE_ASSISTANT_RESPONSE;
        case AIMX_MSG_PROGRESS_UPDATE:
            return AimxConstants::Messages::AIMX_MSG_TYPE_PROGRESS_UPDATE;
        case AIMX_MSG_TASK_BREAKDOWN:
            return AimxConstants::Messages::AIMX_MSG_TYPE_TASK_BREAKDOWN;
        case AIMX_MSG_APPROVAL_REQUEST:
            return AimxConstants::Messages::AIMX_MSG_TYPE_APPROVAL_REQUEST;
        case AIMX_MSG_TOOL_EXECUTION:
            return AimxConstants::Messages::AIMX_MSG_TYPE_TOOL_EXECUTION;
        case AIMX_MSG_ERROR_MESSAGE:
            return AimxConstants::Messages::AIMX_MSG_TYPE_ERROR_MESSAGE;
        case AIMX_MSG_LLM_INFERENCE:
            return AimxConstants::Messages::AIMX_MSG_TYPE_LLM_INFERENCE;
        case AIMX_MSG_LLM_RESPONSE:
            return AimxConstants::Messages::AIMX_MSG_TYPE_LLM_RESPONSE;
        case AIMX_MSG_TOOL_ANALYSIS:
            return AimxConstants::Messages::AIMX_MSG_TYPE_TOOL_ANALYSIS;
        case AIMX_MSG_EXECUTION_PLAN:
            return AimxConstants::Messages::AIMX_MSG_TYPE_EXECUTION_PLAN;
        case AIMX_MSG_TOOL_EXECUTION_START:
            return AimxConstants::Messages::AIMX_MSG_TYPE_TOOL_EXECUTION_START;
        case AIMX_MSG_TOOL_EXECUTION_RESULT:
            return AimxConstants::Messages::AIMX_MSG_TYPE_TOOL_EXECUTION_RESULT;
        case AIMX_MSG_STATUS_UPDATE:
            return AimxConstants::Messages::AIMX_MSG_TYPE_STATUS_UPDATE;
        default:
            return AimxConstants::Messages::AIMX_MSG_TYPE_UNKNOWN;
    }
}

HRESULT
FormatMessageForCallback(
    _In_ const AIMX_CONVERSATION_MESSAGE& internalMessage,
    _Out_ LPWSTR* messageString
    )
/*++

Routine Description:
    Format a conversation message as a single string for PowerShell callbacks.
    PowerShell frees with AimxFree.

Arguments:
    internalMessage - The internal C++ message structure
    messageString - Output allocated string (PowerShell must free with AimxFree)

Return Value:
    S_OK on success, or an error HRESULT on failure.

--*/
{
    if (!messageString)
    {
        return E_INVALIDARG;
    }

    *messageString = nullptr;

    try
    {
        // Get message type as string
        std::wstring typeStr = ConversationMessageTypeToString(internalMessage.Type);

        // Format message as simple text (PowerShell just displays it)
        std::wstring formattedMessage = L"[" + typeStr + L"] " + internalMessage.Content;

        // Add metadata if present
        if (!internalMessage.Metadata.empty())
        {
            std::string metadataUtf8 = internalMessage.Metadata.dump();
            std::wstring metadataWide = Utf8ToWide(metadataUtf8);
            formattedMessage += L" (Metadata: " + metadataWide + L")";
        }

        size_t messageLen = formattedMessage.length() + 1;
        LPWSTR allocatedString = (LPWSTR)MIDL_user_allocate(messageLen * sizeof(wchar_t));
        if (!allocatedString)
        {
            TraceErr(AimxConversationMgr, "Failed to allocate message string");
            return E_OUTOFMEMORY;
        }

        wcscpy_s(allocatedString, messageLen, formattedMessage.c_str());
        *messageString = allocatedString;

        return S_OK;
    }
    catch (const std::exception& ex)
    {
        TraceErr(AimxConversationMgr, "Exception in FormatMessageForCallback: %s", ex.what());
        return E_FAIL;
    }
    catch (...)
    {
        TraceErr(AimxConversationMgr, "Unknown exception in FormatMessageForCallback");
        return E_FAIL;
    }
}

HRESULT
FormatMessagesForPolling(
    _In_ const std::vector<AIMX_CONVERSATION_MESSAGE>& messages,
    _Out_ LPWSTR* messagesString
    )
/*++

Routine Description:
    Format multiple conversation messages as a JSON string for PowerShell polling.
    PowerShell frees with AimxFree.

Arguments:
    messages - Vector of messages to format
    messagesString - Output allocated string (PowerShell must free with AimxFree)

Return Value:
    S_OK on success, or an error HRESULT on failure.

--*/
{
    if (!messagesString)
    {
        return E_INVALIDARG;
    }

    *messagesString = nullptr;

    try
    {
        nlohmann::json messagesJson = nlohmann::json::array();

        for (const auto& message : messages)
        {
            nlohmann::json messageJson;

            // Convert message type to UTF-8 string properly
            std::wstring typeWide = ConversationMessageTypeToString(message.Type);
            std::string typeUtf8 = WideToUtf8(typeWide);
            messageJson["type"] = typeUtf8;

            messageJson["content"] = WideToUtf8(message.Content);

            if (!message.Metadata.empty())
            {
                messageJson["metadata"] = message.Metadata;
            }

            // Convert FILETIME to ISO string
            SYSTEMTIME st;
            if (FileTimeToSystemTime(&message.Timestamp, &st))
            {
                char timeStr[64];
                sprintf_s(timeStr, "%04d-%02d-%02dT%02d:%02d:%02d.%03dZ",
                    st.wYear, st.wMonth, st.wDay,
                    st.wHour, st.wMinute, st.wSecond, st.wMilliseconds);
                messageJson["timestamp"] = timeStr;
            }

            messagesJson.push_back(messageJson);
        }

        // Convert to string
        std::string jsonUtf8 = messagesJson.dump();
        std::wstring jsonWide = Utf8ToWide(jsonUtf8);
        
        size_t messageLen = jsonWide.length() + 1;
        LPWSTR allocatedString = (LPWSTR)MIDL_user_allocate(messageLen * sizeof(wchar_t));
        if (!allocatedString)
        {
            TraceErr(AimxConversationMgr, "Failed to allocate messages string");
            return E_OUTOFMEMORY;
        }

        wcscpy_s(allocatedString, messageLen, jsonWide.c_str());
        *messagesString = allocatedString;

        return S_OK;
    }
    catch (const std::exception& ex)
    {
        TraceErr(AimxConversationMgr, "Exception in FormatMessagesForPolling: %s", ex.what());
        return E_FAIL;
    }
    catch (...)
    {
        TraceErr(AimxConversationMgr, "Unknown exception in FormatMessagesForPolling");
        return E_FAIL;
    }
}

void
ExtractStageFromMessage(
    _In_ const std::wstring& message,
    _Out_ std::wstring& stage,
    _Out_ std::wstring& description
    )
/*++

Routine Description:
    Extract stage and description from a formatted message.
    Expected format: [STAGE] description

Arguments:
    message - The formatted message
    stage - Output stage name
    description - Output description

Return Value:
    None. If no stage is found, stage will be empty and description will be the full message.

--*/
{
    stage.clear();
    description = message;

    size_t startPos = message.find(L'[');
    size_t endPos = message.find(L']');

    if (startPos != std::wstring::npos && endPos != std::wstring::npos && endPos > startPos)
    {
        stage = message.substr(startPos + 1, endPos - startPos - 1);
        if (endPos + 2 < message.length())
        {
            description = message.substr(endPos + 2); // Skip "] "
        }
        else
        {
            description = L""; // No description after stage
        }
    }
}
