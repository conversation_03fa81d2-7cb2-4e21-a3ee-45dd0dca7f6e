#!/usr/bin/env pwsh
<#
.SYNOPSIS
    Setup script for PowerShell MCP Server (AdPsMcpSvr)
    
.DESCRIPTION
    This script configures the PowerShell MCP Server by adding it to the Aimx
    MCP server configuration file. It handles creating the configuration entry,
    backing up existing configuration, and validating the setup.

.AUTHOR
    Rup<PERSON> (rizhang) 07-21-2025

#>

[CmdletBinding()]
param(
    [string]$ServerName = "PSMcpServer",
    
    [string]$Description = "PowerShell MCP Server - provides PowerShell command execution and system tools",
    
    [switch]$Force,
    
    [bool]$BackupConfig = $true
)

# Configuration paths
$ConfigPath = "C:\ProgramData\Microsoft\AIMX\aimx_mcpsvr.json"
$ConfigDir = Split-Path $ConfigPath -Parent
$ScriptPath = $PSScriptRoot
$ServerScriptPath = Join-Path $ScriptPath "Start-McpServer.ps1"

Write-Host "=== PowerShell MCP Server Setup ===" -ForegroundColor Green
Write-Host ""

# Validate that the server script exists
if (-not (Test-Path $ServerScriptPath)) {
    Write-Host "ERROR: Server script not found at: $ServerScriptPath" -ForegroundColor Red
    Write-Host "Please run this setup script from the AdPsMcpSvr directory." -ForegroundColor Yellow
    exit 1
}

Write-Host "Found server script: $ServerScriptPath" -ForegroundColor Green

# Create configuration directory if it doesn't exist
if (-not (Test-Path $ConfigDir)) {
    Write-Host "Creating configuration directory: $ConfigDir" -ForegroundColor Yellow
    try {
        New-Item -ItemType Directory -Path $ConfigDir -Force | Out-Null
        Write-Host "Configuration directory created" -ForegroundColor Green
    }
    catch {
        Write-Host "ERROR: Failed to create configuration directory: $($_.Exception.Message)" -ForegroundColor Red
        exit 1
    }
}

# Load existing configuration or create new one
$config = @{}
if (Test-Path $ConfigPath) {
    Write-Host "Loading existing configuration: $ConfigPath" -ForegroundColor Yellow
    try {
        $configContent = Get-Content $ConfigPath -Raw -Encoding UTF8
        $config = $configContent | ConvertFrom-Json -AsHashtable
        Write-Host "Existing configuration loaded" -ForegroundColor Green
    }
    catch {
        Write-Host "ERROR: Failed to parse existing configuration: $($_.Exception.Message)" -ForegroundColor Red
        Write-Host "The configuration file may be corrupted." -ForegroundColor Yellow
        exit 1
    }
    
    # Create backup if requested
    if ($BackupConfig) {
        $backupPath = $ConfigPath + ".backup." + (Get-Date -Format "yyyyMMdd-HHmmss")
        try {
            Copy-Item $ConfigPath $backupPath
            Write-Host "Configuration backed up to: $backupPath" -ForegroundColor Green
        }
        catch {
            Write-Host "WARNING: Failed to create backup: $($_.Exception.Message)" -ForegroundColor Yellow
        }
    }
}
else {
    Write-Host "Creating new configuration file" -ForegroundColor Yellow
    $config = @{
        mcpServers = @{}
    }
}

# Ensure mcpServers section exists
if (-not $config.ContainsKey("mcpServers")) {
    $config.mcpServers = @{}
}

# Check if server already exists
if ($config.mcpServers.ContainsKey($ServerName)) {
    if (-not $Force) {
        Write-Host "ERROR: Server '$ServerName' already exists in configuration." -ForegroundColor Red
        Write-Host "Use -Force to overwrite the existing configuration." -ForegroundColor Yellow
        exit 1
    }
    else {
        Write-Host "WARNING: Overwriting existing server configuration for '$ServerName'" -ForegroundColor Yellow
    }
}

# Create server configuration
$serverConfig = @{
    enabled = $true
    type = "stdio"
    version = "1.0.0"
    description = $Description
    command = "powershell.exe"
    args = @(
        "-ExecutionPolicy",
        "Bypass",
        "-NoProfile",
        "-File",
        $ServerScriptPath  # ConvertTo-Json will handle escaping automatically
    )
    workingDirectory = $ScriptPath  # ConvertTo-Json will handle escaping automatically
    env = @{
        MCP_SERVER_MODE = "stdio"
        LOG_LEVEL = "Info"
    }
}

# Add server to configuration
$config.mcpServers[$ServerName] = $serverConfig

Write-Host "Adding server configuration:" -ForegroundColor Yellow
Write-Host "  Name: $ServerName" -ForegroundColor Cyan
Write-Host "  Description: $Description" -ForegroundColor Cyan
Write-Host "  Command: powershell.exe" -ForegroundColor Cyan
Write-Host "  Script: $ServerScriptPath" -ForegroundColor Cyan
Write-Host "  Working Directory: $ScriptPath" -ForegroundColor Cyan

# Save configuration
try {
    $configJson = $config | ConvertTo-Json -Depth 10
    $configJson | Set-Content $ConfigPath -Encoding UTF8
    Write-Host "Configuration saved to: $ConfigPath" -ForegroundColor Green
}
catch {
    Write-Host "ERROR: Failed to save configuration: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}

# Validate the configuration
Write-Host ""
Write-Host "Validating configuration..." -ForegroundColor Yellow
try {
    $testConfig = Get-Content $ConfigPath -Raw | ConvertFrom-Json
    if ($testConfig.mcpServers.$ServerName) {
        Write-Host "Configuration validation successful" -ForegroundColor Green
    }
    else {
        Write-Host "ERROR: Server not found in saved configuration" -ForegroundColor Red
        exit 1
    }
}
catch {
    Write-Host "ERROR: Configuration validation failed: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}

Write-Host ""
Write-Host "=== Setup Complete ===" -ForegroundColor Green
Write-Host ""
Write-Host "PowerShell MCP Server has been configured successfully!" -ForegroundColor White
Write-Host ""
Write-Host "Next steps:" -ForegroundColor Yellow
Write-Host "1. Restart the Aimx service to load the new configuration" -ForegroundColor Cyan
Write-Host "2. The server will be available as '$ServerName' in Aimx" -ForegroundColor Cyan
Write-Host "3. Check Aimx logs for connection status" -ForegroundColor Cyan
Write-Host ""
Write-Host "Configuration details:" -ForegroundColor Yellow
Write-Host "  Config file: $ConfigPath" -ForegroundColor Cyan
Write-Host "  Server name: $ServerName" -ForegroundColor Cyan
Write-Host "  Server script: $ServerScriptPath" -ForegroundColor Cyan
Write-Host ""
Write-Host "To remove this server later, delete the '$ServerName' entry from the config file." -ForegroundColor Gray
