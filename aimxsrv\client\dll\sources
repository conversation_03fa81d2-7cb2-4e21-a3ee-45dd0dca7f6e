TARGETNAME=aimxclient
TARGETTYPE=<PERSON><PERSON><PERSON>INK
TARGET_DESTINATION=retail

DEFFILE=aimxclient.def

USE_MSVCRT=1
USE_UNICRT=1
USE_STL=1
STL_VER=STL_VER_CURRENT
USE_ATL=1
ATL_VER=ATL_VER_CURRENT

C_DEFINES=$(C_DEFINES) -DWIN32 -D_WIN32 -DUNICODE -D_UNICODE -DAIMXCLIENT_EXPORTS

# /Zo adds improved debugging of optimized code.
USER_C_FLAGS=$(USER_C_FLAGS) /Zo

PRECOMPILED_INCLUDE=..\pch.hxx
COMPILE_CXX = 1

# Enable exception handling unwind semantics(/EHsc)
USE_NATIVE_EH = 1 

DLLENTRY = _DllMainCRTStartup

SOURCES = \
    ..\aimxclient.cpp \
    ..\aimxrpcclient.cpp \
    ..\memory.cpp \

INCLUDES=\
    ..\; \
    ..\..\..\common; \
    ..\..\..\common\nlohmann; \
    $(OBJ_PATH); \
    $(OBJ_PATH)\$(O); \
    $(OBJ_PATH)\..\..\idl\$(O); \
    $(DS_INC_PATH); \
    $(BASE_INC_PATH); \
    $(SECURITY_INC); \
    $(ONECORE_EXTERNAL_SDK_INC_PATH_L); \
    $(ONECOREDS_INTERNAL_INC_PATH_L); \
    $(ONECOREDS_PRIVATE_INC_PATH_L)\security\base; \
    $(ONECOREBASE_PRIVATE_INC_PATH_L); \
    $(ONECOREBASE_INTERNAL_INC_PATH_L); \
    $(MINWIN_INTERNAL_PRIV_SDK_INC_PATH_L); \
    $(MINWIN_INTERNAL_PRIV_SDK_INC_PATH_L)\lsa; \
    $(MINWIN_INTERNAL_PRIV_SDK_INC_PATH_L)\apiset; \
    $(MINWIN_INTERNAL_PRIV_SDK_INC_PATH_L)\ntos; \
    $(MINWIN_PRIVATE_PRIV_SDK_INC_PATH_L); \
    $(MINWIN_PRIVATE_PRIV_SDK_INC_PATH_L)\lsa; \
    $(MINCORE_INTERNAL_PRIV_SDK_INC_PATH_L); \
    $(INTERNAL_SDK_INC_PATH); \
    $(ONECORE_INTERNAL_SDK_INC_PATH); \
    $(ONECORE_INTERNAL_PRIV_SDK_INC_PATH_L); \
    $(PROJECT_ROOT)\ds\src\adai\proto\win32\aimxsrv\inc; \
    $(PROJECT_ROOT)\ds\src\adai\proto\win32\aimxsrv\client; \

TARGETLIBS_LEGACY=\
    $(MINWIN_EXTERNAL_SDK_LIB_PATH_L)\ntdll.lib                 \
    $(MINWIN_EXTERNAL_SDK_LIB_PATH_L)\rpcrt4.lib                \

TARGETLIBS_APISETS=\
    $(ONECORE_EXTERNAL_SDK_LIB_PATH)\onecore.lib \
    $(MINWIN_INTERNAL_SDK_LIB_VPATH_L)\api-ms-win-security-sddl-l1.lib                \
    $(MINWIN_INTERNAL_SDK_LIB_VPATH_L)\api-ms-win-security-base-l1.lib                \

TARGETLIBS=\
    $(TARGETLIBS_LEGACY) \
    $(TARGETLIBS_APISETS) \

RUN_WPP = \
    -ext:.cpp.h.hxx                                                   \
    -preserveext:.cpp.h.hxx                                           \
    -scan:..\clientwpp.h                                            \
    -DWPP_CHECK_INIT                                              \
    -p:AIMXCLIENT                                                   \
    -func:TraceCrit{LEVEL=TRACE_LEVEL_CRITICAL}(FLAGS,MSG,...)    \
    -func:TraceErr{LEVEL=TRACE_LEVEL_ERROR}(FLAGS,MSG,...)        \
    -func:TraceWarn{LEVEL=TRACE_LEVEL_WARNING}(FLAGS,MSG,...)     \
    -func:TraceInfo{LEVEL=TRACE_LEVEL_INFORMATION}(FLAGS,MSG,...) \
    -func:TraceVerb{LEVEL=TRACE_LEVEL_VERBOSE}(FLAGS,MSG,...)     \
    $(SOURCES)