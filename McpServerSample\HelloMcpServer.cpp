/*++

Copyright (c) Microsoft Corporation. All rights reserved.

Module Name:

    HelloMcpServer.cpp

Abstract:

    Implementation of Hello in-process MCP server.
    Simple sample demonstrating JSON blob interface with direct API calls.

Author:

    <PERSON><PERSON><PERSON> (r<PERSON><PERSON>) 07/12/2025

--*/

#include "HelloMCPServer.h"
#include "StringUtils.h"

HelloMcpServer::HelloMcpServer()
    : InProcessMcpServerBase(
        L"Hello",
        L"Provides a simple greeting tool as a sample in-process MCP server",
        L"1.0.0"
    )
{
}

HelloMcpServer::~HelloMcpServer()
{
}

HRESULT HelloMcpServer::OnInitialize()
/*++

Routine Description:
    Initialize the server by registering the hello tool.

Return Value:
    S_OK on success, error HRESULT on failure

--*/
{
    // Register single hello tool
    HRESULT hr = REGISTER_MCP_TOOL(HelloFromMcpTool, "Returns a friendly greeting message from the MCP server", HelloFromMcpTool);
    return hr;
}

HRESULT HelloMcpServer::HelloFromMcpTool(
    _In_ const nlohmann::json& parameters,
    _Out_ nlohmann::json& result
    )
/*++

Routine Description:
    Simple hello greeting tool implementation.

Arguments:
    parameters - Input parameters (can contain optional "name" field)
    result - Receives greeting message as JSON

Return Value:
    S_OK on success, error HRESULT on failure

--*/
{
    try
    {
        result = nlohmann::json::object();

        // Check if a name was provided in parameters
        std::string name = "World";
        if (parameters.contains("name") && parameters["name"].is_string())
        {
            name = parameters["name"].get<std::string>();
        }

        // Create greeting message
        std::string greeting = "Hello, " + name + "!" +
            "! This is a greeting from the Hello MCP Server. ";

        result["message"] = greeting;
        result["server"] = "Hello MCP Server";
        result["version"] = "1.0.0";

        return S_OK;
    }
    catch (const std::exception& ex)
    {
        result = CreateErrorResponse(L"Failed to generate greeting: " + Utf8ToWide(ex.what()), L"greeting_error");
        return E_FAIL;
    }
}

// Export functions for registration
extern "C" __declspec(dllexport) HRESULT RegisterHelloMcpServer()
{
    auto server = std::make_shared<HelloMcpServer>();
    return McpSvrMgr::RegisterInProcessServer(server);
}

extern "C" __declspec(dllexport) HRESULT UnregisterHelloMcpServer()
{
    return McpSvrMgr::UnregisterInProcessServer(L"Hello");
}

// Register the server factory
REGISTER_INPROCESS_MCP_SERVER(HelloMcpServer)
