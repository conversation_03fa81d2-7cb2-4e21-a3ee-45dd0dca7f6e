/*++

Copyright (c) Microsoft Corporation. All rights reserved.

Module Name:

    McpServerUtils.h

Abstract:

    High-level utilities for creating MCP server executables.
    Provides base classes and helper functions that can be used by
    any MCP server implementation to handle JSON-RPC protocol.

Author:

    <PERSON><PERSON><PERSON> (r<PERSON><PERSON>) 07/19/2025

--*/

#pragma once

#include "McpJsonRpc.h"
#include <windows.h>
#include <string>
#include <map>
#include <functional>
#include <memory>

// Forward declaration for in-process server base
class InProcessMcpServerBase;

namespace McpProtocol
{
    // Server-side utilities for MCP protocol handling
    namespace Server
    {
        // Request handler function signature
        typedef std::function<nlohmann::json(const nlohmann::json&)> RequestHandler;

        // Tool function signature for template-based tool registry
        template<typename ServerType>
        using ToolFunction = HRESULT(ServerType::*)(const nlohmann::json&, nlohmann::json&);

        // Process a complete JSON-RPC request and return response
        std::string ProcessRequest(
            _In_ const std::string& requestJson,
            _In_ const std::map<std::string, RequestHandler>& methodHandlers
        );

        // Create standard MCP method handlers for a server instance
        template<typename ServerType>
        std::map<std::string, RequestHandler> CreateStandardHandlers(
            _In_ ServerType* server,
            _In_ const std::map<std::string, ToolFunction<ServerType>>& toolRegistry
        );

        // Handle initialize request using any server type that has Initialize() method
        template<typename ServerType>
        nlohmann::json HandleInitialize(
            _In_ const nlohmann::json& params,
            _In_ ServerType* server
        );

        // Handle tools/list request using any server type that has ListTools() method
        template<typename ServerType>
        nlohmann::json HandleListTools(
            _In_ const nlohmann::json& params,
            _In_ ServerType* server
        );

        // Handle tools/call request using tool registry
        template<typename ServerType>
        nlohmann::json HandleCallTool(
            _In_ const nlohmann::json& params,
            _In_ ServerType* server,
            _In_ const std::map<std::string, ToolFunction<ServerType>>& toolRegistry
        );

        // Handle shutdown request
        nlohmann::json HandleShutdown(
            _In_ const nlohmann::json& params
        );

        // Server initialization helper
        template<typename ServerType>
        HRESULT InitializeServer(
            _Inout_ std::unique_ptr<ServerType>& server
        );

        // Main entry point for MCP server executables
        template<typename ServerType>
        int RunMcpServerExecutable(
            _In_ int argc,
            _In_ wchar_t* argv[],
            _In_ const std::map<std::string, ToolFunction<ServerType>>& toolRegistry
        );
    }

    // Client-side utilities for MCP protocol handling
    namespace Client
    {
        // Response handler function signature
        typedef std::function<void(const nlohmann::json&)> ResponseHandler;
        typedef std::function<void(const nlohmann::json&)> ErrorHandler;

        // Send request and handle response asynchronously
        class AsyncRequestManager
        {
        public:
            // Send a request with response/error handlers
            void SendRequest(
                _In_ const nlohmann::json& request,
                _In_ ResponseHandler onResponse,
                _In_ ErrorHandler onError
            );

            // Process incoming response
            void ProcessResponse(
                _In_ const nlohmann::json& response
            );

        private:
            std::map<nlohmann::json, std::pair<ResponseHandler, ErrorHandler>> m_pendingRequests;
        };

        // Create standard MCP client requests
        nlohmann::json CreateInitializeRequest(
            _In_ const std::string& clientName,
            _In_ const std::string& clientVersion = "1.0.0"
        );

        nlohmann::json CreateListToolsRequest();

        nlohmann::json CreateCallToolRequest(
            _In_ const std::string& toolName,
            _In_ const nlohmann::json& arguments
        );

        nlohmann::json CreateShutdownRequest();
    }
}

// Template implementations (must be in header)

template<typename ServerType>
std::map<std::string, McpProtocol::Server::RequestHandler> 
McpProtocol::Server::CreateStandardHandlers(
    _In_ ServerType* server,
    _In_ const std::map<std::string, ToolFunction<ServerType>>& toolRegistry
)
{
    return {
        {Mcp::Methods::INITIALIZE, [server](const nlohmann::json& params) {
            return HandleInitialize(params, server);
        }},
        {Mcp::Methods::TOOLS_LIST, [server](const nlohmann::json& params) {
            return HandleListTools(params, server);
        }},
        {Mcp::Methods::TOOLS_CALL, [server, &toolRegistry](const nlohmann::json& params) {
            return HandleCallTool(params, server, toolRegistry);
        }},
        {Mcp::Methods::SHUTDOWN, [](const nlohmann::json& params) {
            return HandleShutdown(params);
        }}
    };
}

template<typename ServerType>
nlohmann::json McpProtocol::Server::HandleInitialize(
    _In_ const nlohmann::json& params,
    _In_ ServerType* server
)
{
    // Extract client info (optional validation)
    std::string clientName = "Unknown Client";
    std::string clientVersion = "1.0.0";
    
    if (params.contains("clientInfo"))
    {
        const auto& clientInfo = params["clientInfo"];
        if (clientInfo.contains("name") && clientInfo["name"].is_string())
        {
            clientName = clientInfo["name"].get<std::string>();
        }
        if (clientInfo.contains("version") && clientInfo["version"].is_string())
        {
            clientVersion = clientInfo["version"].get<std::string>();
        }
    }

    // Initialize server if it has an Initialize method
    // Try to call Initialize if it exists - will fail at compile time if not available
    if constexpr (true)
    {
        HRESULT hr = server->Initialize();
        if (FAILED(hr))
        {
            throw std::runtime_error("Server initialization failed");
        }
    }

    // Create response with server info
    return {
        {"protocolVersion", Mcp::PROTOCOL_VERSION},
        {"capabilities", nlohmann::json::object()},
        {"serverInfo", {
            {"name", "MCP Server"},
            {"version", "1.0.0"}
        }}
    };
}

template<typename ServerType>
nlohmann::json McpProtocol::Server::HandleListTools(
    _In_ const nlohmann::json& params,
    _In_ ServerType* server
)
{
    UNREFERENCED_PARAMETER(params);

    if (!server)
    {
        throw std::runtime_error("Server not initialized");
    }

    // Use server's ListTools method
    nlohmann::json toolsResponse;
    HRESULT hr = server->ListTools(toolsResponse);
    if (FAILED(hr))
    {
        throw std::runtime_error("Failed to get tool list from server");
    }

    // Extract tools array if it exists
    if (toolsResponse.contains("tools"))
    {
        return {
            {"tools", toolsResponse["tools"]}
        };
    }
    else if (toolsResponse.contains("result") && toolsResponse["result"].contains("tools"))
    {
        return {
            {"tools", toolsResponse["result"]["tools"]}
        };
    }
    else
    {
        return toolsResponse;
    }
}

template<typename ServerType>
nlohmann::json McpProtocol::Server::HandleCallTool(
    _In_ const nlohmann::json& params,
    _In_ ServerType* server,
    _In_ const std::map<std::string, ToolFunction<ServerType>>& toolRegistry
)
{
    if (!params.contains("name") || !params["name"].is_string())
    {
        throw std::runtime_error("Missing or invalid 'name' parameter");
    }

    std::string toolName = params["name"].get<std::string>();
    
    // Look up the tool in the registry
    auto toolIt = toolRegistry.find(toolName);
    if (toolIt == toolRegistry.end())
    {
        throw std::runtime_error("Unknown tool: " + toolName);
    }
    
    // Extract arguments
    nlohmann::json arguments = params.value("arguments", nlohmann::json::object());
    
    // Call the tool function using the function pointer from the registry
    auto toolFunc = toolIt->second;
    nlohmann::json result;
    HRESULT hr = (server->*toolFunc)(arguments, result);
    if (FAILED(hr))
    {
        throw std::runtime_error("Tool execution failed");
    }
    
    // Modify the result to indicate stdio mode
    if (result.contains("server"))
    {
        std::string serverName = result["server"].get<std::string>();
        result["server"] = serverName + " (stdio)";
    }
    if (!result.contains("mode"))
    {
        result["mode"] = "out-of-process";
    }

    // Return in MCP protocol format
    return Mcp::WrapToolContent(
        Mcp::CreateToolContentResponse(result.dump(2))
    );
}

template<typename ServerType>
HRESULT McpProtocol::Server::InitializeServer(
    _Inout_ std::unique_ptr<ServerType>& server
)
{
    if (!server)
    {
        server = std::make_unique<ServerType>();
        
        if constexpr (true)
        {
            HRESULT hr = server->Initialize();
            if (FAILED(hr))
            {
                server.reset();
                return hr;
            }
        }
    }
    return S_OK;
}
