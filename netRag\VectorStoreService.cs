using Microsoft.Extensions.Logging;
using System.Collections.Concurrent;
using System.Numerics.Tensors;

namespace netRag;

public class VectorStoreService
{
    private readonly ConcurrentDictionary<string, VectorPoint> _vectors;
    private readonly ILogger<VectorStoreService> _logger;
    private readonly object _lockObject = new();
    private int _vectorSize;

    public VectorStoreService(ILogger<VectorStoreService> logger)
    {
        _vectors = new ConcurrentDictionary<string, VectorPoint>();
        _logger = logger;
    }

    public Task InitializeAsync(int vectorSize = 768)
    {
        _vectorSize = vectorSize;
        _logger.LogInformation("In-memory vector store initialized with vector size {VectorSize}", vectorSize);
        return Task.CompletedTask;
    }

    public Task UpsertAsync(string id, ReadOnlyMemory<float> embedding, Dictionary<string, object> metadata)
    {
        if (embedding.Length != _vectorSize)
        {
            throw new ArgumentException($"Embedding size {embedding.Length} does not match expected size {_vectorSize}");
        }

        var point = new VectorPoint
        {
            Id = id,
            Vector = embedding.ToArray(),
            Metadata = new Dictionary<string, object>(metadata),
            Timestamp = DateTime.UtcNow
        };

        _vectors.AddOrUpdate(id, point, (key, oldValue) => point);
        _logger.LogDebug("Upserted point with ID: {Id}", id);

        return Task.CompletedTask;
    }

    public Task<List<SearchResult>> SearchAsync(ReadOnlyMemory<float> queryEmbedding, int limit = 3)
    {
        if (queryEmbedding.Length != _vectorSize)
        {
            throw new ArgumentException($"Query embedding size {queryEmbedding.Length} does not match expected size {_vectorSize}");
        }

        var queryVector = queryEmbedding.ToArray();
        var results = new List<(VectorPoint point, float score)>();

        // Calculate cosine similarity for all vectors
        foreach (var kvp in _vectors)
        {
            var point = kvp.Value;
            var similarity = CalculateCosineSimilarity(queryVector, point.Vector);
            results.Add((point, similarity));
        }

        // Sort by similarity (descending) and take top results
        var topResults = results
            .OrderByDescending(r => r.score)
            .Take(limit)
            .Select(r => new SearchResult
            {
                Id = r.point.Id,
                Score = r.score,
                Metadata = r.point.Metadata
            })
            .ToList();

        _logger.LogDebug("Found {Count} similar points from {TotalCount} vectors", topResults.Count, _vectors.Count);
        return Task.FromResult(topResults);
    }

    public Task ClearAsync()
    {
        var count = _vectors.Count;
        _vectors.Clear();
        _logger.LogInformation("Cleared {Count} vectors from in-memory store", count);
        return Task.CompletedTask;
    }

    public Task<int> GetVectorCountAsync()
    {
        return Task.FromResult(_vectors.Count);
    }

    public Task<bool> RemoveAsync(string id)
    {
        if (string.IsNullOrEmpty(id))
        {
            _logger.LogWarning("Cannot remove vector with empty ID");
            return Task.FromResult(false);
        }

        var removed = _vectors.TryRemove(id, out var removedPoint);

        if (removed)
        {
            _logger.LogDebug("Removed vector with ID: {Id}", id);
        }
        else
        {
            _logger.LogDebug("Vector with ID {Id} not found for removal", id);
        }

        return Task.FromResult(removed);
    }

    private static float CalculateCosineSimilarity(float[] vector1, float[] vector2)
    {
        if (vector1.Length != vector2.Length)
        {
            throw new ArgumentException("Vectors must have the same length");
        }

        // Use System.Numerics.Tensors for optimized operations
        var span1 = vector1.AsSpan();
        var span2 = vector2.AsSpan();

        var dotProduct = TensorPrimitives.Dot(span1, span2);
        var magnitude1 = MathF.Sqrt(TensorPrimitives.Dot(span1, span1));
        var magnitude2 = MathF.Sqrt(TensorPrimitives.Dot(span2, span2));

        if (magnitude1 == 0 || magnitude2 == 0)
        {
            return 0;
        }

        return dotProduct / (magnitude1 * magnitude2);
    }
}

public class VectorPoint
{
    public string Id { get; set; } = string.Empty;
    public float[] Vector { get; set; } = Array.Empty<float>();
    public Dictionary<string, object> Metadata { get; set; } = new();
    public DateTime Timestamp { get; set; }
}

public class SearchResult
{
    public string Id { get; set; } = string.Empty;
    public float Score { get; set; }
    public Dictionary<string, object> Metadata { get; set; } = new();
}
