<#
.SYNOPSIS
    Test script for direct PowerShell execution in AIMX MCP Tool Manager
    
.DESCRIPTION
    This script tests the new direct PowerShell execution functionality that bypasses
    the MCP server protocol for PowerShell-based tools. It demonstrates how PowerShell
    cmdlets can be executed directly using CreateProcess instead of the MCP server.
    
.EXAMPLE
    .\Test-DirectPowerShellExecution.ps1
    
.NOTES
    This script simulates what the AIMX MCP Tool Manager does when executing
    PowerShell tools directly.
#>

[CmdletBinding()]
param()

function Test-DirectPowerShellExecution {
    param(
        [string]$ToolName,
        [hashtable]$Parameters = @{}
    )
    
    Write-Host "Testing direct PowerShell execution for: $ToolName" -ForegroundColor Cyan
    
    # Build PowerShell command (similar to what McpToolManager does)
    $psCommand = "powershell.exe"
    $psArgs = "-ExecutionPolicy Bypass -NoProfile -Command `""
    
    # Add the cmdlet name
    $psArgs += $ToolName
    
    # Add parameters
    foreach ($key in $Parameters.Keys) {
        $value = $Parameters[$key]
        $psArgs += " -$key"
        
        if ($value -is [string]) {
            $psArgs += " '$value'"
        } elseif ($value -is [bool]) {
            $psArgs += if ($value) { " `$true" } else { " `$false" }
        } else {
            $psArgs += " $value"
        }
    }
    
    $psArgs += "`""
    
    Write-Host "Command: $psCommand $psArgs" -ForegroundColor Yellow
    
    try {
        # Execute the command
        $startInfo = New-Object System.Diagnostics.ProcessStartInfo
        $startInfo.FileName = $psCommand
        $startInfo.Arguments = $psArgs
        $startInfo.RedirectStandardOutput = $true
        $startInfo.RedirectStandardError = $true
        $startInfo.UseShellExecute = $false
        $startInfo.CreateNoWindow = $true
        
        $process = New-Object System.Diagnostics.Process
        $process.StartInfo = $startInfo
        
        $stdout = New-Object System.Text.StringBuilder
        $stderr = New-Object System.Text.StringBuilder
        
        # Event handlers for output
        $stdoutEvent = Register-ObjectEvent -InputObject $process -EventName OutputDataReceived -Action {
            if ($Event.SourceEventArgs.Data) {
                $Event.MessageData.AppendLine($Event.SourceEventArgs.Data)
            }
        } -MessageData $stdout
        
        $stderrEvent = Register-ObjectEvent -InputObject $process -EventName ErrorDataReceived -Action {
            if ($Event.SourceEventArgs.Data) {
                $Event.MessageData.AppendLine($Event.SourceEventArgs.Data)
            }
        } -MessageData $stderr
        
        # Start the process
        $process.Start()
        $process.BeginOutputReadLine()
        $process.BeginErrorReadLine()
        
        # Wait for completion with timeout
        $completed = $process.WaitForExit(30000) # 30 second timeout
        
        if (-not $completed) {
            Write-Host "Process timed out!" -ForegroundColor Red
            $process.Kill()
            return $false
        }
        
        # Cleanup event handlers
        Unregister-Event -SourceIdentifier $stdoutEvent.Name
        Unregister-Event -SourceIdentifier $stderrEvent.Name
        
        $exitCode = $process.ExitCode
        $stdoutText = $stdout.ToString().Trim()
        $stderrText = $stderr.ToString().Trim()
        
        Write-Host "Exit Code: $exitCode" -ForegroundColor $(if ($exitCode -eq 0) { "Green" } else { "Red" })
        
        if ($stdoutText) {
            Write-Host "Output:" -ForegroundColor Green
            Write-Host $stdoutText -ForegroundColor White
        }
        
        if ($stderrText) {
            Write-Host "Error:" -ForegroundColor Red
            Write-Host $stderrText -ForegroundColor Yellow
        }
        
        return $exitCode -eq 0
    }
    catch {
        Write-Host "Exception: $($_.Exception.Message)" -ForegroundColor Red
        return $false
    }
    finally {
        if ($process) {
            $process.Dispose()
        }
    }
}

# Test cases
Write-Host "=== Direct PowerShell Execution Test Suite ===" -ForegroundColor Magenta
Write-Host ""

$testCases = @(
    @{
        Name = "Get-Date"
        Parameters = @{}
        Description = "Simple cmdlet with no parameters"
    },
    @{
        Name = "Get-Process"
        Parameters = @{ Name = "powershell" }
        Description = "Cmdlet with string parameter"
    },
    @{
        Name = "Get-ChildItem"
        Parameters = @{ Path = "C:\Windows"; Recurse = $false }
        Description = "Cmdlet with path and boolean parameters"
    },
    @{
        Name = "Get-Service"
        Parameters = @{ Name = "Spooler" }
        Description = "Service information cmdlet"
    },
    @{
        Name = "Get-ComputerInfo"
        Parameters = @{}
        Description = "System information cmdlet"
    }
)

$passedTests = 0
$totalTests = $testCases.Count

foreach ($testCase in $testCases) {
    Write-Host "Test: $($testCase.Description)" -ForegroundColor Cyan
    Write-Host "Cmdlet: $($testCase.Name)" -ForegroundColor White
    
    $success = Test-DirectPowerShellExecution -ToolName $testCase.Name -Parameters $testCase.Parameters
    
    if ($success) {
        Write-Host "[PASS]" -ForegroundColor Green
        $passedTests++
    } else {
        Write-Host "[FAIL]" -ForegroundColor Red
    }
    
    Write-Host ("-" * 60) -ForegroundColor Gray
    Write-Host ""
}

Write-Host "=== Test Results ===" -ForegroundColor Magenta
Write-Host "Passed: $passedTests/$totalTests" -ForegroundColor $(if ($passedTests -eq $totalTests) { "Green" } else { "Yellow" })

if ($passedTests -eq $totalTests) {
    Write-Host "All tests passed! Direct PowerShell execution is working correctly." -ForegroundColor Green
} else {
    Write-Host "Some tests failed. Check the implementation." -ForegroundColor Yellow
}

Write-Host ""
Write-Host "This demonstrates that PowerShell cmdlets can be executed directly" -ForegroundColor Cyan
Write-Host "without the overhead of the MCP server protocol." -ForegroundColor Cyan
