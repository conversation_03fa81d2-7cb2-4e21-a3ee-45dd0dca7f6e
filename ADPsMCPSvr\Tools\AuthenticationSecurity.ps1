<#
.SYNOPSIS
    Active Directory Authentication and Security Management Tools for MCP Server
    
.DESCRIPTION
    This module registers MCP tools for Active Directory authentication and security management operations.
    Each tool is a direct wrapper around the corresponding AD PowerShell cmdlet with
    exact parameter passthrough and no output formatting.

.AUTHOR
    Rup<PERSON> Zhang (rizhang)
#>

# Import required modules
Import-Module ActiveDirectory -ErrorAction SilentlyContinue

function Register-AuthenticationSecurityTools {
    [CmdletBinding()]
    param()

    # Get-ADAuthenticationPolicy - Gets one or more Active Directory Domain Services authentication policies
    Register-McpTool -Name "Get-ADAuthenticationPolicy" -Description "Gets one or more Active Directory Domain Services authentication policies." -ScriptBlock {
        param($Arguments)
        
        $params = @{}
        if ($Arguments.Filter) { $params.Filter = $Arguments.Filter }
        if ($Arguments.Identity) { $params.Identity = $Arguments.Identity }
        if ($Arguments.LDAPFilter) { $params.LDAPFilter = $Arguments.LDAPFilter }
        if ($Arguments.Properties) { $params.Properties = $Arguments.Properties }
        if ($Arguments.ResultPageSize) { $params.ResultPageSize = $Arguments.ResultPageSize }
        if ($Arguments.ResultSetSize) { $params.ResultSetSize = $Arguments.ResultSetSize }
        if ($Arguments.SearchBase) { $params.SearchBase = $Arguments.SearchBase }
        if ($Arguments.SearchScope) { $params.SearchScope = $Arguments.SearchScope }
        if ($Arguments.Server) { $params.Server = $Arguments.Server }
        if ($Arguments.AuthType) { $params.AuthType = $Arguments.AuthType }
        if ($Arguments.Credential) { $params.Credential = $Arguments.Credential }
        
        Get-ADAuthenticationPolicy @params
    } -InputSchema @{
        type = "object"
        properties = @{
            Filter = @{ type = "string"; description = "PowerShell Expression Language filter string" }
            Identity = @{ type = "string"; description = "Authentication policy identity (DN, GUID, or name)" }
            LDAPFilter = @{ type = "string"; description = "LDAP query string for filtering" }
            Properties = @{ type = "array"; items = @{ type = "string" }; description = "Additional properties to retrieve" }
            ResultPageSize = @{ type = "integer"; description = "Number of objects per page" }
            ResultSetSize = @{ type = "integer"; description = "Maximum number of objects to return" }
            SearchBase = @{ type = "string"; description = "Active Directory path to search under" }
            SearchScope = @{ type = "string"; enum = @("Base", "OneLevel", "Subtree"); description = "Scope of the search" }
            Server = @{ type = "string"; description = "Domain controller to connect to" }
            AuthType = @{ type = "string"; enum = @("Negotiate", "Basic"); description = "Authentication method" }
            Credential = @{ type = "string"; description = "User credentials for authentication" }
        }
    }

    # New-ADAuthenticationPolicy - Creates an Active Directory Domain Services authentication policy object
    Register-McpTool -Name "New-ADAuthenticationPolicy" -Description "Creates a new Active Directory Domain Services authentication policy object." -ScriptBlock {
        param($Arguments)
        
        $params = @{}
        if ($Arguments.Name) { $params.Name = $Arguments.Name }
        if ($Arguments.ComputerTGTLifetimeMins) { $params.ComputerTGTLifetimeMins = $Arguments.ComputerTGTLifetimeMins }
        if ($Arguments.Enforce) { $params.Enforce = $Arguments.Enforce }
        if ($Arguments.ServiceTGTLifetimeMins) { $params.ServiceTGTLifetimeMins = $Arguments.ServiceTGTLifetimeMins }
        if ($Arguments.UserTGTLifetimeMins) { $params.UserTGTLifetimeMins = $Arguments.UserTGTLifetimeMins }
        if ($Arguments.Description) { $params.Description = $Arguments.Description }
        if ($Arguments.ProtectedFromAccidentalDeletion) { $params.ProtectedFromAccidentalDeletion = $Arguments.ProtectedFromAccidentalDeletion }
        if ($Arguments.OtherAttributes) { $params.OtherAttributes = $Arguments.OtherAttributes }
        if ($Arguments.Server) { $params.Server = $Arguments.Server }
        if ($Arguments.AuthType) { $params.AuthType = $Arguments.AuthType }
        if ($Arguments.Credential) { $params.Credential = $Arguments.Credential }
        if ($Arguments.PassThru) { $params.PassThru = $Arguments.PassThru }
        
        New-ADAuthenticationPolicy @params
    } -InputSchema @{
        type = "object"
        properties = @{
            Name = @{ type = "string"; description = "Name of the authentication policy (required)" }
            ComputerTGTLifetimeMins = @{ type = "integer"; description = "Computer TGT lifetime in minutes" }
            Enforce = @{ type = "boolean"; description = "Enforce the authentication policy" }
            ServiceTGTLifetimeMins = @{ type = "integer"; description = "Service TGT lifetime in minutes" }
            UserTGTLifetimeMins = @{ type = "integer"; description = "User TGT lifetime in minutes" }
            Description = @{ type = "string"; description = "Description of the authentication policy" }
            ProtectedFromAccidentalDeletion = @{ type = "boolean"; description = "Protect from accidental deletion" }
            OtherAttributes = @{ type = "object"; description = "Additional attributes as hashtable" }
            Server = @{ type = "string"; description = "Domain controller to connect to" }
            AuthType = @{ type = "string"; enum = @("Negotiate", "Basic"); description = "Authentication method" }
            Credential = @{ type = "string"; description = "User credentials for authentication" }
            PassThru = @{ type = "boolean"; description = "Return the created authentication policy object" }
        }
        required = @("Name")
    }

    # Set-ADAuthenticationPolicy - Modifies an Active Directory Domain Services authentication policy object
    Register-McpTool -Name "Set-ADAuthenticationPolicy" -Description "Modifies an Active Directory Domain Services authentication policy object." -ScriptBlock {
        param($Arguments)
        
        $params = @{}
        if ($Arguments.Identity) { $params.Identity = $Arguments.Identity }
        if ($Arguments.Add) { $params.Add = $Arguments.Add }
        if ($Arguments.Clear) { $params.Clear = $Arguments.Clear }
        if ($Arguments.Remove) { $params.Remove = $Arguments.Remove }
        if ($Arguments.Replace) { $params.Replace = $Arguments.Replace }
        if ($Arguments.ComputerTGTLifetimeMins) { $params.ComputerTGTLifetimeMins = $Arguments.ComputerTGTLifetimeMins }
        if ($Arguments.Enforce) { $params.Enforce = $Arguments.Enforce }
        if ($Arguments.ServiceTGTLifetimeMins) { $params.ServiceTGTLifetimeMins = $Arguments.ServiceTGTLifetimeMins }
        if ($Arguments.UserTGTLifetimeMins) { $params.UserTGTLifetimeMins = $Arguments.UserTGTLifetimeMins }
        if ($Arguments.Description) { $params.Description = $Arguments.Description }
        if ($Arguments.ProtectedFromAccidentalDeletion) { $params.ProtectedFromAccidentalDeletion = $Arguments.ProtectedFromAccidentalDeletion }
        if ($Arguments.Server) { $params.Server = $Arguments.Server }
        if ($Arguments.AuthType) { $params.AuthType = $Arguments.AuthType }
        if ($Arguments.Credential) { $params.Credential = $Arguments.Credential }
        if ($Arguments.PassThru) { $params.PassThru = $Arguments.PassThru }
        
        Set-ADAuthenticationPolicy @params
    } -InputSchema @{
        type = "object"
        properties = @{
            Identity = @{ type = "string"; description = "Authentication policy identity (DN, GUID, or name)" }
            Add = @{ type = "object"; description = "Attributes to add as hashtable" }
            Clear = @{ type = "array"; items = @{ type = "string" }; description = "Attributes to clear" }
            Remove = @{ type = "object"; description = "Attributes to remove as hashtable" }
            Replace = @{ type = "object"; description = "Attributes to replace as hashtable" }
            ComputerTGTLifetimeMins = @{ type = "integer"; description = "Computer TGT lifetime in minutes" }
            Enforce = @{ type = "boolean"; description = "Enforce the authentication policy" }
            ServiceTGTLifetimeMins = @{ type = "integer"; description = "Service TGT lifetime in minutes" }
            UserTGTLifetimeMins = @{ type = "integer"; description = "User TGT lifetime in minutes" }
            Description = @{ type = "string"; description = "Description of the authentication policy" }
            ProtectedFromAccidentalDeletion = @{ type = "boolean"; description = "Protect from accidental deletion" }
            Server = @{ type = "string"; description = "Domain controller to connect to" }
            AuthType = @{ type = "string"; enum = @("Negotiate", "Basic"); description = "Authentication method" }
            Credential = @{ type = "string"; description = "User credentials for authentication" }
            PassThru = @{ type = "boolean"; description = "Return the modified authentication policy object" }
        }
        required = @("Identity")
    }

    # Remove-ADAuthenticationPolicy - Removes an Active Directory Domain Services authentication policy object
    Register-McpTool -Name "Remove-ADAuthenticationPolicy" -Description "Removes an Active Directory Domain Services authentication policy object." -ScriptBlock {
        param($Arguments)
        
        $params = @{}
        if ($Arguments.Identity) { $params.Identity = $Arguments.Identity }
        if ($Arguments.Server) { $params.Server = $Arguments.Server }
        if ($Arguments.AuthType) { $params.AuthType = $Arguments.AuthType }
        if ($Arguments.Credential) { $params.Credential = $Arguments.Credential }
        if ($Arguments.Confirm) { $params.Confirm = $Arguments.Confirm }
        if ($Arguments.WhatIf) { $params.WhatIf = $Arguments.WhatIf }
        
        Remove-ADAuthenticationPolicy @params
    } -InputSchema @{
        type = "object"
        properties = @{
            Identity = @{ type = "string"; description = "Authentication policy identity (DN, GUID, or name)" }
            Server = @{ type = "string"; description = "Domain controller to connect to" }
            AuthType = @{ type = "string"; enum = @("Negotiate", "Basic"); description = "Authentication method" }
            Credential = @{ type = "string"; description = "User credentials for authentication" }
            Confirm = @{ type = "boolean"; description = "Prompt for confirmation before removing" }
            WhatIf = @{ type = "boolean"; description = "Show what would happen without executing" }
        }
        required = @("Identity")
    }

    # Get-ADAuthenticationPolicySilo - Gets one or more Active Directory Domain Services authentication policy silos
    Register-McpTool -Name "Get-ADAuthenticationPolicySilo" -Description "Gets one or more Active Directory Domain Services authentication policy silos." -ScriptBlock {
        param($Arguments)
        
        $params = @{}
        if ($Arguments.Filter) { $params.Filter = $Arguments.Filter }
        if ($Arguments.Identity) { $params.Identity = $Arguments.Identity }
        if ($Arguments.LDAPFilter) { $params.LDAPFilter = $Arguments.LDAPFilter }
        if ($Arguments.Properties) { $params.Properties = $Arguments.Properties }
        if ($Arguments.ResultPageSize) { $params.ResultPageSize = $Arguments.ResultPageSize }
        if ($Arguments.ResultSetSize) { $params.ResultSetSize = $Arguments.ResultSetSize }
        if ($Arguments.SearchBase) { $params.SearchBase = $Arguments.SearchBase }
        if ($Arguments.SearchScope) { $params.SearchScope = $Arguments.SearchScope }
        if ($Arguments.Server) { $params.Server = $Arguments.Server }
        if ($Arguments.AuthType) { $params.AuthType = $Arguments.AuthType }
        if ($Arguments.Credential) { $params.Credential = $Arguments.Credential }
        
        Get-ADAuthenticationPolicySilo @params
    } -InputSchema @{
        type = "object"
        properties = @{
            Filter = @{ type = "string"; description = "PowerShell Expression Language filter string" }
            Identity = @{ type = "string"; description = "Authentication policy silo identity (DN, GUID, or name)" }
            LDAPFilter = @{ type = "string"; description = "LDAP query string for filtering" }
            Properties = @{ type = "array"; items = @{ type = "string" }; description = "Additional properties to retrieve" }
            ResultPageSize = @{ type = "integer"; description = "Number of objects per page" }
            ResultSetSize = @{ type = "integer"; description = "Maximum number of objects to return" }
            SearchBase = @{ type = "string"; description = "Active Directory path to search under" }
            SearchScope = @{ type = "string"; enum = @("Base", "OneLevel", "Subtree"); description = "Scope of the search" }
            Server = @{ type = "string"; description = "Domain controller to connect to" }
            AuthType = @{ type = "string"; enum = @("Negotiate", "Basic"); description = "Authentication method" }
            Credential = @{ type = "string"; description = "User credentials for authentication" }
        }
    }

    # New-ADAuthenticationPolicySilo - Creates an Active Directory Domain Services authentication policy silo object
    Register-McpTool -Name "New-ADAuthenticationPolicySilo" -Description "Creates a new Active Directory Domain Services authentication policy silo object." -ScriptBlock {
        param($Arguments)
        
        $params = @{}
        if ($Arguments.Name) { $params.Name = $Arguments.Name }
        if ($Arguments.ComputerAuthenticationPolicy) { $params.ComputerAuthenticationPolicy = $Arguments.ComputerAuthenticationPolicy }
        if ($Arguments.Enforce) { $params.Enforce = $Arguments.Enforce }
        if ($Arguments.ServiceAuthenticationPolicy) { $params.ServiceAuthenticationPolicy = $Arguments.ServiceAuthenticationPolicy }
        if ($Arguments.UserAuthenticationPolicy) { $params.UserAuthenticationPolicy = $Arguments.UserAuthenticationPolicy }
        if ($Arguments.Description) { $params.Description = $Arguments.Description }
        if ($Arguments.ProtectedFromAccidentalDeletion) { $params.ProtectedFromAccidentalDeletion = $Arguments.ProtectedFromAccidentalDeletion }
        if ($Arguments.OtherAttributes) { $params.OtherAttributes = $Arguments.OtherAttributes }
        if ($Arguments.Server) { $params.Server = $Arguments.Server }
        if ($Arguments.AuthType) { $params.AuthType = $Arguments.AuthType }
        if ($Arguments.Credential) { $params.Credential = $Arguments.Credential }
        if ($Arguments.PassThru) { $params.PassThru = $Arguments.PassThru }
        
        New-ADAuthenticationPolicySilo @params
    } -InputSchema @{
        type = "object"
        properties = @{
            Name = @{ type = "string"; description = "Name of the authentication policy silo (required)" }
            ComputerAuthenticationPolicy = @{ type = "string"; description = "Computer authentication policy to assign" }
            Enforce = @{ type = "boolean"; description = "Enforce the authentication policy silo" }
            ServiceAuthenticationPolicy = @{ type = "string"; description = "Service authentication policy to assign" }
            UserAuthenticationPolicy = @{ type = "string"; description = "User authentication policy to assign" }
            Description = @{ type = "string"; description = "Description of the authentication policy silo" }
            ProtectedFromAccidentalDeletion = @{ type = "boolean"; description = "Protect from accidental deletion" }
            OtherAttributes = @{ type = "object"; description = "Additional attributes as hashtable" }
            Server = @{ type = "string"; description = "Domain controller to connect to" }
            AuthType = @{ type = "string"; enum = @("Negotiate", "Basic"); description = "Authentication method" }
            Credential = @{ type = "string"; description = "User credentials for authentication" }
            PassThru = @{ type = "boolean"; description = "Return the created authentication policy silo object" }
        }
        required = @("Name")
    }

    # Set-ADAuthenticationPolicySilo - Modifies an Active Directory Domain Services authentication policy silo object
    Register-McpTool -Name "Set-ADAuthenticationPolicySilo" -Description "Modifies an Active Directory Domain Services authentication policy silo object." -ScriptBlock {
        param($Arguments)
        
        $params = @{}
        if ($Arguments.Identity) { $params.Identity = $Arguments.Identity }
        if ($Arguments.Add) { $params.Add = $Arguments.Add }
        if ($Arguments.Clear) { $params.Clear = $Arguments.Clear }
        if ($Arguments.Remove) { $params.Remove = $Arguments.Remove }
        if ($Arguments.Replace) { $params.Replace = $Arguments.Replace }
        if ($Arguments.ComputerAuthenticationPolicy) { $params.ComputerAuthenticationPolicy = $Arguments.ComputerAuthenticationPolicy }
        if ($Arguments.Enforce) { $params.Enforce = $Arguments.Enforce }
        if ($Arguments.ServiceAuthenticationPolicy) { $params.ServiceAuthenticationPolicy = $Arguments.ServiceAuthenticationPolicy }
        if ($Arguments.UserAuthenticationPolicy) { $params.UserAuthenticationPolicy = $Arguments.UserAuthenticationPolicy }
        if ($Arguments.Description) { $params.Description = $Arguments.Description }
        if ($Arguments.ProtectedFromAccidentalDeletion) { $params.ProtectedFromAccidentalDeletion = $Arguments.ProtectedFromAccidentalDeletion }
        if ($Arguments.Server) { $params.Server = $Arguments.Server }
        if ($Arguments.AuthType) { $params.AuthType = $Arguments.AuthType }
        if ($Arguments.Credential) { $params.Credential = $Arguments.Credential }
        if ($Arguments.PassThru) { $params.PassThru = $Arguments.PassThru }
        
        Set-ADAuthenticationPolicySilo @params
    } -InputSchema @{
        type = "object"
        properties = @{
            Identity = @{ type = "string"; description = "Authentication policy silo identity (DN, GUID, or name)" }
            Add = @{ type = "object"; description = "Attributes to add as hashtable" }
            Clear = @{ type = "array"; items = @{ type = "string" }; description = "Attributes to clear" }
            Remove = @{ type = "object"; description = "Attributes to remove as hashtable" }
            Replace = @{ type = "object"; description = "Attributes to replace as hashtable" }
            ComputerAuthenticationPolicy = @{ type = "string"; description = "Computer authentication policy to assign" }
            Enforce = @{ type = "boolean"; description = "Enforce the authentication policy silo" }
            ServiceAuthenticationPolicy = @{ type = "string"; description = "Service authentication policy to assign" }
            UserAuthenticationPolicy = @{ type = "string"; description = "User authentication policy to assign" }
            Description = @{ type = "string"; description = "Description of the authentication policy silo" }
            ProtectedFromAccidentalDeletion = @{ type = "boolean"; description = "Protect from accidental deletion" }
            Server = @{ type = "string"; description = "Domain controller to connect to" }
            AuthType = @{ type = "string"; enum = @("Negotiate", "Basic"); description = "Authentication method" }
            Credential = @{ type = "string"; description = "User credentials for authentication" }
            PassThru = @{ type = "boolean"; description = "Return the modified authentication policy silo object" }
        }
        required = @("Identity")
    }

    # Remove-ADAuthenticationPolicySilo - Removes an Active Directory Domain Services authentication policy silo object
    Register-McpTool -Name "Remove-ADAuthenticationPolicySilo" -Description "Removes an Active Directory Domain Services authentication policy silo object." -ScriptBlock {
        param($Arguments)
        
        $params = @{}
        if ($Arguments.Identity) { $params.Identity = $Arguments.Identity }
        if ($Arguments.Server) { $params.Server = $Arguments.Server }
        if ($Arguments.AuthType) { $params.AuthType = $Arguments.AuthType }
        if ($Arguments.Credential) { $params.Credential = $Arguments.Credential }
        if ($Arguments.Confirm) { $params.Confirm = $Arguments.Confirm }
        if ($Arguments.WhatIf) { $params.WhatIf = $Arguments.WhatIf }
        
        Remove-ADAuthenticationPolicySilo @params
    } -InputSchema @{
        type = "object"
        properties = @{
            Identity = @{ type = "string"; description = "Authentication policy silo identity (DN, GUID, or name)" }
            Server = @{ type = "string"; description = "Domain controller to connect to" }
            AuthType = @{ type = "string"; enum = @("Negotiate", "Basic"); description = "Authentication method" }
            Credential = @{ type = "string"; description = "User credentials for authentication" }
            Confirm = @{ type = "boolean"; description = "Prompt for confirmation before removing" }
            WhatIf = @{ type = "boolean"; description = "Show what would happen without executing" }
        }
        required = @("Identity")
    }

    # Grant-ADAuthenticationPolicySiloAccess - Grants permission to join an authentication policy silo
    Register-McpTool -Name "Grant-ADAuthenticationPolicySiloAccess" -Description "Grants permission to join an authentication policy silo." -ScriptBlock {
        param($Arguments)

        $params = @{}
        if ($Arguments.Identity) { $params.Identity = $Arguments.Identity }
        if ($Arguments.Account) { $params.Account = $Arguments.Account }
        if ($Arguments.Server) { $params.Server = $Arguments.Server }
        if ($Arguments.AuthType) { $params.AuthType = $Arguments.AuthType }
        if ($Arguments.Credential) { $params.Credential = $Arguments.Credential }
        if ($Arguments.PassThru) { $params.PassThru = $Arguments.PassThru }

        Grant-ADAuthenticationPolicySiloAccess @params
    } -InputSchema @{
        type = "object"
        properties = @{
            Identity = @{ type = "string"; description = "Authentication policy silo identity (DN, GUID, or name)" }
            Account = @{ type = "array"; items = @{ type = "string" }; description = "Array of account identities to grant access" }
            Server = @{ type = "string"; description = "Domain controller to connect to" }
            AuthType = @{ type = "string"; enum = @("Negotiate", "Basic"); description = "Authentication method" }
            Credential = @{ type = "string"; description = "User credentials for authentication" }
            PassThru = @{ type = "boolean"; description = "Return the authentication policy silo object" }
        }
        required = @("Identity", "Account")
    }

    # Revoke-ADAuthenticationPolicySiloAccess - Revokes membership in an authentication policy silo for the specified account
    Register-McpTool -Name "Revoke-ADAuthenticationPolicySiloAccess" -Description "Revokes membership in an authentication policy silo for the specified account." -ScriptBlock {
        param($Arguments)

        $params = @{}
        if ($Arguments.Identity) { $params.Identity = $Arguments.Identity }
        if ($Arguments.Account) { $params.Account = $Arguments.Account }
        if ($Arguments.Server) { $params.Server = $Arguments.Server }
        if ($Arguments.AuthType) { $params.AuthType = $Arguments.AuthType }
        if ($Arguments.Credential) { $params.Credential = $Arguments.Credential }
        if ($Arguments.PassThru) { $params.PassThru = $Arguments.PassThru }

        Revoke-ADAuthenticationPolicySiloAccess @params
    } -InputSchema @{
        type = "object"
        properties = @{
            Identity = @{ type = "string"; description = "Authentication policy silo identity (DN, GUID, or name)" }
            Account = @{ type = "array"; items = @{ type = "string" }; description = "Array of account identities to revoke access" }
            Server = @{ type = "string"; description = "Domain controller to connect to" }
            AuthType = @{ type = "string"; enum = @("Negotiate", "Basic"); description = "Authentication method" }
            Credential = @{ type = "string"; description = "User credentials for authentication" }
            PassThru = @{ type = "boolean"; description = "Return the authentication policy silo object" }
        }
        required = @("Identity", "Account")
    }

    # Set-ADAccountAuthenticationPolicySilo - Modifies the authentication policy or authentication policy silo of an account
    Register-McpTool -Name "Set-ADAccountAuthenticationPolicySilo" -Description "Modifies the authentication policy or authentication policy silo of an account." -ScriptBlock {
        param($Arguments)

        $params = @{}
        if ($Arguments.Identity) { $params.Identity = $Arguments.Identity }
        if ($Arguments.AuthenticationPolicy) { $params.AuthenticationPolicy = $Arguments.AuthenticationPolicy }
        if ($Arguments.AuthenticationPolicySilo) { $params.AuthenticationPolicySilo = $Arguments.AuthenticationPolicySilo }
        if ($Arguments.Server) { $params.Server = $Arguments.Server }
        if ($Arguments.AuthType) { $params.AuthType = $Arguments.AuthType }
        if ($Arguments.Credential) { $params.Credential = $Arguments.Credential }
        if ($Arguments.PassThru) { $params.PassThru = $Arguments.PassThru }

        Set-ADAccountAuthenticationPolicySilo @params
    } -InputSchema @{
        type = "object"
        properties = @{
            Identity = @{ type = "string"; description = "Account identity (DN, GUID, SID, or SAM account name)" }
            AuthenticationPolicy = @{ type = "string"; description = "Authentication policy to assign to the account" }
            AuthenticationPolicySilo = @{ type = "string"; description = "Authentication policy silo to assign to the account" }
            Server = @{ type = "string"; description = "Domain controller to connect to" }
            AuthType = @{ type = "string"; enum = @("Negotiate", "Basic"); description = "Authentication method" }
            Credential = @{ type = "string"; description = "User credentials for authentication" }
            PassThru = @{ type = "boolean"; description = "Return the account object" }
        }
        required = @("Identity")
    }

    # Show-ADAuthenticationPolicyExpression - Displays the Edit Access Control Conditions window update or create security descriptor definition language (SDDL) security descriptors
    Register-McpTool -Name "Show-ADAuthenticationPolicyExpression" -Description "Displays the Edit Access Control Conditions window to update or create SDDL security descriptors." -ScriptBlock {
        param($Arguments)

        $params = @{}
        if ($Arguments.AllowedToAuthenticateFrom) { $params.AllowedToAuthenticateFrom = $Arguments.AllowedToAuthenticateFrom }
        if ($Arguments.AllowedToAuthenticateTo) { $params.AllowedToAuthenticateTo = $Arguments.AllowedToAuthenticateTo }
        if ($Arguments.Title) { $params.Title = $Arguments.Title }

        Show-ADAuthenticationPolicyExpression @params
    } -InputSchema @{
        type = "object"
        properties = @{
            AllowedToAuthenticateFrom = @{ type = "string"; description = "SDDL string for allowed to authenticate from conditions" }
            AllowedToAuthenticateTo = @{ type = "string"; description = "SDDL string for allowed to authenticate to conditions" }
            Title = @{ type = "string"; description = "Title for the dialog window" }
        }
    }

    # Get-ADCentralAccessPolicy - Retrieves central access policies from Active Directory
    Register-McpTool -Name "Get-ADCentralAccessPolicy" -Description "Retrieves central access policies from Active Directory." -ScriptBlock {
        param($Arguments)

        $params = @{}
        if ($Arguments.Filter) { $params.Filter = $Arguments.Filter }
        if ($Arguments.Identity) { $params.Identity = $Arguments.Identity }
        if ($Arguments.LDAPFilter) { $params.LDAPFilter = $Arguments.LDAPFilter }
        if ($Arguments.Properties) { $params.Properties = $Arguments.Properties }
        if ($Arguments.ResultPageSize) { $params.ResultPageSize = $Arguments.ResultPageSize }
        if ($Arguments.ResultSetSize) { $params.ResultSetSize = $Arguments.ResultSetSize }
        if ($Arguments.SearchBase) { $params.SearchBase = $Arguments.SearchBase }
        if ($Arguments.SearchScope) { $params.SearchScope = $Arguments.SearchScope }
        if ($Arguments.Server) { $params.Server = $Arguments.Server }
        if ($Arguments.AuthType) { $params.AuthType = $Arguments.AuthType }
        if ($Arguments.Credential) { $params.Credential = $Arguments.Credential }

        Get-ADCentralAccessPolicy @params
    } -InputSchema @{
        type = "object"
        properties = @{
            Filter = @{ type = "string"; description = "PowerShell Expression Language filter string" }
            Identity = @{ type = "string"; description = "Central access policy identity (DN, GUID, or name)" }
            LDAPFilter = @{ type = "string"; description = "LDAP query string for filtering" }
            Properties = @{ type = "array"; items = @{ type = "string" }; description = "Additional properties to retrieve" }
            ResultPageSize = @{ type = "integer"; description = "Number of objects per page" }
            ResultSetSize = @{ type = "integer"; description = "Maximum number of objects to return" }
            SearchBase = @{ type = "string"; description = "Active Directory path to search under" }
            SearchScope = @{ type = "string"; enum = @("Base", "OneLevel", "Subtree"); description = "Scope of the search" }
            Server = @{ type = "string"; description = "Domain controller to connect to" }
            AuthType = @{ type = "string"; enum = @("Negotiate", "Basic"); description = "Authentication method" }
            Credential = @{ type = "string"; description = "User credentials for authentication" }
        }
    }

    # New-ADCentralAccessPolicy - Creates a new central access policy in Active Directory containing a set of central access rules
    Register-McpTool -Name "New-ADCentralAccessPolicy" -Description "Creates a new central access policy in Active Directory containing a set of central access rules." -ScriptBlock {
        param($Arguments)

        $params = @{}
        if ($Arguments.Name) { $params.Name = $Arguments.Name }
        if ($Arguments.Description) { $params.Description = $Arguments.Description }
        if ($Arguments.ProtectedFromAccidentalDeletion) { $params.ProtectedFromAccidentalDeletion = $Arguments.ProtectedFromAccidentalDeletion }
        if ($Arguments.OtherAttributes) { $params.OtherAttributes = $Arguments.OtherAttributes }
        if ($Arguments.Server) { $params.Server = $Arguments.Server }
        if ($Arguments.AuthType) { $params.AuthType = $Arguments.AuthType }
        if ($Arguments.Credential) { $params.Credential = $Arguments.Credential }
        if ($Arguments.PassThru) { $params.PassThru = $Arguments.PassThru }

        New-ADCentralAccessPolicy @params
    } -InputSchema @{
        type = "object"
        properties = @{
            Name = @{ type = "string"; description = "Name of the central access policy (required)" }
            Description = @{ type = "string"; description = "Description of the central access policy" }
            ProtectedFromAccidentalDeletion = @{ type = "boolean"; description = "Protect from accidental deletion" }
            OtherAttributes = @{ type = "object"; description = "Additional attributes as hashtable" }
            Server = @{ type = "string"; description = "Domain controller to connect to" }
            AuthType = @{ type = "string"; enum = @("Negotiate", "Basic"); description = "Authentication method" }
            Credential = @{ type = "string"; description = "User credentials for authentication" }
            PassThru = @{ type = "boolean"; description = "Return the created central access policy object" }
        }
        required = @("Name")
    }

    # Set-ADCentralAccessPolicy - Modifies a central access policy in Active Directory
    Register-McpTool -Name "Set-ADCentralAccessPolicy" -Description "Modifies a central access policy in Active Directory." -ScriptBlock {
        param($Arguments)

        $params = @{}
        if ($Arguments.Identity) { $params.Identity = $Arguments.Identity }
        if ($Arguments.Add) { $params.Add = $Arguments.Add }
        if ($Arguments.Clear) { $params.Clear = $Arguments.Clear }
        if ($Arguments.Remove) { $params.Remove = $Arguments.Remove }
        if ($Arguments.Replace) { $params.Replace = $Arguments.Replace }
        if ($Arguments.Description) { $params.Description = $Arguments.Description }
        if ($Arguments.ProtectedFromAccidentalDeletion) { $params.ProtectedFromAccidentalDeletion = $Arguments.ProtectedFromAccidentalDeletion }
        if ($Arguments.Server) { $params.Server = $Arguments.Server }
        if ($Arguments.AuthType) { $params.AuthType = $Arguments.AuthType }
        if ($Arguments.Credential) { $params.Credential = $Arguments.Credential }
        if ($Arguments.PassThru) { $params.PassThru = $Arguments.PassThru }

        Set-ADCentralAccessPolicy @params
    } -InputSchema @{
        type = "object"
        properties = @{
            Identity = @{ type = "string"; description = "Central access policy identity (DN, GUID, or name)" }
            Add = @{ type = "object"; description = "Attributes to add as hashtable" }
            Clear = @{ type = "array"; items = @{ type = "string" }; description = "Attributes to clear" }
            Remove = @{ type = "object"; description = "Attributes to remove as hashtable" }
            Replace = @{ type = "object"; description = "Attributes to replace as hashtable" }
            Description = @{ type = "string"; description = "Description of the central access policy" }
            ProtectedFromAccidentalDeletion = @{ type = "boolean"; description = "Protect from accidental deletion" }
            Server = @{ type = "string"; description = "Domain controller to connect to" }
            AuthType = @{ type = "string"; enum = @("Negotiate", "Basic"); description = "Authentication method" }
            Credential = @{ type = "string"; description = "User credentials for authentication" }
            PassThru = @{ type = "boolean"; description = "Return the modified central access policy object" }
        }
        required = @("Identity")
    }

    # Remove-ADCentralAccessPolicy - Removes a central access policy from Active Directory
    Register-McpTool -Name "Remove-ADCentralAccessPolicy" -Description "Removes a central access policy from Active Directory." -ScriptBlock {
        param($Arguments)

        $params = @{}
        if ($Arguments.Identity) { $params.Identity = $Arguments.Identity }
        if ($Arguments.Server) { $params.Server = $Arguments.Server }
        if ($Arguments.AuthType) { $params.AuthType = $Arguments.AuthType }
        if ($Arguments.Credential) { $params.Credential = $Arguments.Credential }
        if ($Arguments.Confirm) { $params.Confirm = $Arguments.Confirm }
        if ($Arguments.WhatIf) { $params.WhatIf = $Arguments.WhatIf }

        Remove-ADCentralAccessPolicy @params
    } -InputSchema @{
        type = "object"
        properties = @{
            Identity = @{ type = "string"; description = "Central access policy identity (DN, GUID, or name)" }
            Server = @{ type = "string"; description = "Domain controller to connect to" }
            AuthType = @{ type = "string"; enum = @("Negotiate", "Basic"); description = "Authentication method" }
            Credential = @{ type = "string"; description = "User credentials for authentication" }
            Confirm = @{ type = "boolean"; description = "Prompt for confirmation before removing" }
            WhatIf = @{ type = "boolean"; description = "Show what would happen without executing" }
        }
        required = @("Identity")
    }
}

# Function is available after dot-sourcing
