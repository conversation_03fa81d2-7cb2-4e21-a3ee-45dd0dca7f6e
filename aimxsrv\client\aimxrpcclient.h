/*++

Copyright (c) Microsoft Corporation. All rights reserved.

Module Name:
    aimxrpcclient.h

Abstract:
    AIMXSRV RPC client interface declarations.

Author:
    <PERSON> (SNAKE FIGHTER) (lindakup) 06/10/2025

--*/

#pragma once
#include "aimxrpc.h"

class AimxRpcClient
{
public:
    HRESULT AimxConnect(_Out_ GUID* pContextId);
    HRESULT AimxClose(_In_ GUID contextId);
    HRESULT AimxProcessPrompt(_In_ GUID contextId, _In_ LPCWSTR InputPrompt, _Out_ LPWSTR* Response);
    HRESULT AimxPollConversationMessages(_In_ GUID contextId, _Out_ LPWSTR* messages);
    HRESULT AimxGetConversationStatus(_In_ GUID contextId, _Out_ LPWSTR* statusJson);
    HRESULT AimxStartConversation(_In_ GUID contextId, _In_ LPCWSTR query, _In_ LONG executionMode);
    HRESULT AimxGetLlmStatus(_In_ GUID contextId, _Out_ LPWSTR* statusJson);
    HRESULT AimxGetMcpServerInfo(_In_ GUID contextId, _Out_ LPWSTR* serverInfoJson);
    AimxRpcClient();
    ~AimxRpcClient();

private:

    static _Success_(return)
    BOOL
    CreateAimxRpcClientHandle(
        _Outptr_result_nullonfailure_ HANDLE* pRpcHandle
    );

};
