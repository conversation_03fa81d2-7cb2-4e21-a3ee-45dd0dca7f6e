<#
.SYNOPSIS
    Active Directory PowerShell MCP Server
    
.DESCRIPTION
    A comprehensive MCP (Model Context Protocol) server wrapper for all Active Directory 
    PowerShell cmdlets. Built on the McpPshFx framework, this server provides MCP tool 
    wrappers for complete Active Directory management operations.
    
    Each AD cmdlet is exposed as an MCP tool with exact parameter passthrough and no 
    output formatting, allowing AI assistants to interact with Active Directory through 
    the MCP protocol.

.AUTHOR
    Rupo Zhang (rizhang)

.EXAMPLE
    .\Start-ADPsMCPSvr.ps1
    
.NOTES
    Requires:
    - Windows PowerShell 5.1 or PowerShell 7+
    - Active Directory PowerShell module (RSAT-AD-PowerShell feature)
    - McpPshFx framework
#>

[CmdletBinding()]
param()

# Suppress warnings to keep stdout clean for JSON-RPC communication
$WarningPreference = 'SilentlyContinue'

# Get script directory
$ScriptRoot = $PSScriptRoot

# Import McpPshFx framework modules
$McpPshFxPath = Join-Path (Split-Path $ScriptRoot -Parent) "McpPshFx"
Import-Module "$McpPshFxPath\Modules\McpConstants.psm1" -Force -WarningAction SilentlyContinue
Import-Module "$McpPshFxPath\Modules\McpProtocol.psm1" -Force -WarningAction SilentlyContinue
Import-Module "$McpPshFxPath\Modules\McpToolRegistry.psm1" -Force -WarningAction SilentlyContinue

# Import Active Directory module
try {
    Import-Module ActiveDirectory -ErrorAction Stop
}
catch {
    Write-Error "Active Directory PowerShell module is required but not available. Please install RSAT-AD-PowerShell feature."
    exit 1
}

# Import all AD tool modules
$ToolsPath = Join-Path $ScriptRoot "Tools"

# Import User Management tools
. "$ToolsPath\UserManagement.ps1"

# Import Group Management tools  
. "$ToolsPath\GroupManagement.ps1"

# Import Computer Management tools
. "$ToolsPath\ComputerManagement.ps1"

# Import OU Management tools
. "$ToolsPath\OUManagement.ps1"

# Import Domain and Forest Management tools
. "$ToolsPath\DomainForestManagement.ps1"

# Import Object Management tools
. "$ToolsPath\ObjectManagement.ps1"

# Import Account and Password Management tools
. "$ToolsPath\AccountPasswordManagement.ps1"

# Import Replication Management tools
. "$ToolsPath\ReplicationManagement.ps1"

# Import Authentication and Security tools
. "$ToolsPath\AuthenticationSecurity.ps1"

# Import Domain Controller Management tools
. "$ToolsPath\DomainControllerManagement.ps1"

# Import Central Access Policy Management tools
. "$ToolsPath\CentralAccessPolicyManagement.ps1"

# Import Resource Property Management tools
. "$ToolsPath\ResourcePropertyManagement.ps1"

# Import Service Account Migration tools
. "$ToolsPath\ServiceAccountMigration.ps1"

try {
    # Initialize the MCP server
    $server = New-McpServer

    # Register all Active Directory tools
    Write-Verbose "Registering User Management tools..."
    Register-UserManagementTools
    
    Write-Verbose "Registering Group Management tools..."
    Register-GroupManagementTools
    
    Write-Verbose "Registering Computer Management tools..."
    Register-ComputerManagementTools
    
    Write-Verbose "Registering OU Management tools..."
    Register-OUManagementTools

    Write-Verbose "Registering Domain and Forest Management tools..."
    Register-DomainForestManagementTools

    Write-Verbose "Registering Object Management tools..."
    Register-ObjectManagementTools

    Write-Verbose "Registering Account and Password Management tools..."
    Register-AccountPasswordManagementTools

    Write-Verbose "Registering Replication Management tools..."
    Register-ReplicationManagementTools

    Write-Verbose "Registering Authentication and Security tools..."
    Register-AuthenticationSecurityTools

    Write-Verbose "Registering Domain Controller Management tools..."
    Register-DomainControllerManagementTools

    Write-Verbose "Registering Central Access Policy Management tools..."
    Register-CentralAccessPolicyManagementTools

    Write-Verbose "Registering Resource Property Management tools..."
    Register-ResourcePropertyManagementTools

    Write-Verbose "Registering Service Account Migration tools..."
    Register-ServiceAccountMigrationTools

    # Get registered tool count for logging
    $registeredTools = Get-RegisteredTools
    Write-Verbose "Successfully registered $($registeredTools.Count) Active Directory tools"

    # Start the MCP server (this will block and handle stdio communication)
    Write-Verbose "Starting ADPsMCPSvr server..."
    Start-McpServer -Server $server
}
catch {
    # Log error details for debugging (to stderr, not stdout)
    Write-Error "Failed to start ADPsMCPSvr: $($_.Exception.Message)" -ErrorAction Continue
    Write-Error "Stack trace: $($_.ScriptStackTrace)" -ErrorAction Continue
    
    # Exit silently on error to avoid contaminating stdout
    exit 1
}
