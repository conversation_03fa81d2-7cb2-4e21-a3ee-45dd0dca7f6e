<?xml version='1.0' encoding='utf-8' standalone='yes'?>
<assembly
    xmlns="urn:schemas-microsoft-com:asm.v3"
    xmlns:xsd="http://www.w3.org/2001/XMLSchema"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    manifestVersion="1.0"
    >
  <assemblyIdentity
      buildType="$(build.buildType)"
      language="neutral"
      name="Microsoft-Windows-AIMXSrv"
      processorArchitecture="$(build.arch)"
      publicKeyToken="$(Build.WindowsPublicKeyToken)"
      version="$(build.version)"
      versionScope="nonSxS"
      />
  <file
      destinationPath="$(runtime.system32)"
      importPath="$(build.nttree)\"
      name="AIMXSrv.dll"
      sourceName="AIMXSrv.dll"
      sourcePath=".\"
      />
  <dependency
      discoverable="false"
      optional="false"
      resourceType="resources"
      >
    <dependentAssembly>
      <assemblyIdentity
          buildType="$(build.buildType)"
          language="*"
          name="Microsoft-Windows-AIMXSrv.Resources"
          processorArchitecture="$(build.arch)"
          publicKeyToken="$(Build.WindowsPublicKeyToken)"
          version="$(build.version)"
          />
    </dependentAssembly>
  </dependency>
  <memberships>
    <categoryMembership>
      <id
          name="Microsoft.Windows.Categories.Services"
          publicKeyToken="$(build.WindowsPublicKeyToken)"
          typeName="Service"
          version="$(build.version)"
          />
      <categoryInstance subcategory="AIMXSrv">
        <serviceData
            dependOnService="RpcSs"
            description="@%systemroot%\system32\AIMXSrv.dll,-101"
            displayName="@%systemroot%\system32\AIMXSrv.dll,-100"
            errorControl="normal"
            imagePath="%SystemRoot%\system32\svchost.exe -k LocalSystemNetworkRestricted -p"
            name="AIMXSrv"
            objectName="LocalSystem"
            requiredPrivileges="SeImpersonatePrivilege"
            sidType="restricted"
            start="disabled"
            type="win32ShareProcess"
            >
          <failureActions resetPeriod="86400">
            <actions>
              <action
                  delay="60000"
                  type="restartService"
                  />
              <action
                  delay="120000"
                  type="restartService"
                  />
              <action
                  delay="0"
                  type="none"
                  />
            </actions>
          </failureActions>
        </serviceData>
      </categoryInstance>
    </categoryMembership>
    <categoryMembership>
      <id
          name="Microsoft.Windows.Categories"
          publicKeyToken="365143bb27e7ac8b"
          typeName="SvcHost"
          version="*******"
          />
      <categoryInstance subcategory="LocalSystemNetworkRestricted">
        <serviceGroup
            position="last"
            serviceName="AIMXSrv"
            />
      </categoryInstance>
    </categoryMembership>
    <categoryMembership>
      <id
          name="Microsoft.Windows.Categories"
          publicKeyToken="365143bb27e7ac8b"
          typeName="BootRecovery"
          version="*******"
          />
    </categoryMembership>
  </memberships>
  <registryKeys>
    <registryKey keyName="HKEY_LOCAL_MACHINE\System\CurrentControlSet\Services\AIMXSrv\Parameters">
      <registryValue
          name="ServiceDll"
          value="%SystemRoot%\System32\AIMXSrv.dll"
          valueType="REG_EXPAND_SZ"
          />
      <registryValue
          name="ServiceDllUnloadOnStop"
          value="1"
          valueType="REG_DWORD"
          />
      <registryValue
          name="ServiceMain"
          value="AimxServiceMain"
          valueType="REG_SZ"
          />
    <securityDescriptor name="WRP_REGKEY_DEFAULT_SDDL"/>
    </registryKey>
  </registryKeys>
  <trustInfo>
    <security>
      <accessControl>
        <securityDescriptorDefinitions>
          <securityDescriptorDefinition
              name="WRP_REGKEY_DEFAULT_SDDL"
              sddl="$(build.wrpRegKeySddl)"
              />
          <securityDescriptorDefinition
              name="WRP_FILE_DEFAULT_SDDL"
              sddl="$(build.wrpFileSddl)"
              />
        </securityDescriptorDefinitions>
      </accessControl>
    </security>
  </trustInfo>
</assembly>
