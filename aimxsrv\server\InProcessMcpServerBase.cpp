/*++

Copyright (c) Microsoft Corporation. All rights reserved.

Module Name:

    InProcessMcpServerBase.cpp

Abstract:

    Implementation of base class for in-process MCP servers.
    Provides common functionality while maintaining JSON blob interface
    for full MCP protocol compliance with direct API calls.

Author:

    <PERSON><PERSON><PERSON> (rizhang) 07/12/2025

--*/

#include "pch.hxx"
#include "InProcessMcpServerBase.h"
#include "InProcessMcpUtils.h"
#include "StringUtils.h"
#include "AimxConstants.h"

InProcessMcpServerBase::InProcessMcpServerBase(
    _In_ const std::wstring& serverName,
    _In_ const std::wstring& description,
    _In_ const std::wstring& version
    )
    : m_serverName(serverName)
    , m_description(description)
    , m_version(version)
    , m_initialized(false)
    , m_totalExecutions(0)
    , m_successfulExecutions(0)
    , m_failedExecutions(0)
{
}

InProcessMcpServerBase::~InProcessMcpServerBase()
{
    if (m_initialized)
    {
        Uninitialize();
    }
}

HRESULT InProcessMcpServerBase::ListTools(
    _Out_ nlohmann::json& toolsResponse
    )
/*++

Routine Description:
    Returns MCP-compliant tools/list response with all registered tools.

Arguments:
    toolsResponse - Receives JSON response matching MCP protocol

Return Value:
    S_OK on success, error HRESULT on failure

--*/
{
    try
    {
        std::shared_lock<std::shared_mutex> lock(m_toolsMutex);
        
        std::vector<nlohmann::json> tools;
        tools.reserve(m_registeredTools.size());
        
        for (const auto& [toolName, toolDef] : m_registeredTools)
        {
            nlohmann::json tool;
            tool[AimxConstants::Protocol::AIMX_FIELD_NAME] = WideToUtf8(toolDef.name);
            tool[AimxConstants::Protocol::AIMX_FIELD_DESCRIPTION] = WideToUtf8(toolDef.description);

            if (!toolDef.inputSchema.empty())
            {
                tool[AimxConstants::Protocol::AIMX_FIELD_INPUT_SCHEMA] = toolDef.inputSchema;
            }

            tools.push_back(tool);
        }
        
        toolsResponse = InProcessMcpUtils::CreateToolsListResponse(tools);
        return S_OK;
    }
    catch (const std::exception& ex)
    {
        toolsResponse = InProcessMcpUtils::ExceptionToMcpError(ex);
        return E_FAIL;
    }
}

HRESULT InProcessMcpServerBase::CallTool(
    _In_ const nlohmann::json& callRequest,
    _Out_ nlohmann::json& callResponse
    )
/*++

Routine Description:
    Executes a tool based on MCP tools/call request format.

Arguments:
    callRequest - MCP tools/call request JSON
    callResponse - Receives MCP-compliant response JSON

Return Value:
    S_OK on success, error HRESULT on failure

--*/
{
    DWORD startTime = GetTickCount();
    
    try
    {
        // Parse the request
        std::string toolName;
        nlohmann::json parameters;
        HRESULT hr = InProcessMcpUtils::ParseToolCallRequest(callRequest, toolName, parameters);
        if (FAILED(hr))
        {
            callResponse = InProcessMcpUtils::CreateMcpErrorResponse(
                McpProtocol::ErrorCodes::INVALID_PARAMS, "Invalid parameters");
            return hr;
        }
        
        std::wstring toolNameWide = Utf8ToWide(toolName);
        
        // Execute the tool
        nlohmann::json result;
        hr = ExecuteToolInternal(toolNameWide, parameters, result);
        
        DWORD executionTime = GetTickCount() - startTime;
        LogToolExecution(toolNameWide, parameters, hr, executionTime);
        
        if (SUCCEEDED(hr))
        {
            callResponse = InProcessMcpUtils::CreateToolCallSuccessResponse(result);
            UpdateExecutionStatistics(true);
        }
        else
        {
            callResponse = InProcessMcpUtils::CreateMcpErrorResponse(
                McpProtocol::ErrorCodes::INTERNAL_ERROR, "Tool execution failed");
            UpdateExecutionStatistics(false);
        }
        
        return hr;
    }
    catch (const std::exception& ex)
    {
        callResponse = InProcessMcpUtils::ExceptionToMcpError(ex);
        UpdateExecutionStatistics(false);
        return E_FAIL;
    }
}

std::wstring InProcessMcpServerBase::GetServerName() const
{
    return m_serverName;
}

std::wstring InProcessMcpServerBase::GetServerDescription() const
{
    return m_description;
}

std::wstring InProcessMcpServerBase::GetServerVersion() const
{
    return m_version;
}

HRESULT InProcessMcpServerBase::Initialize()
{
    if (m_initialized)
    {
        return S_OK;
    }
    
    HRESULT hr = OnInitialize();
    if (SUCCEEDED(hr))
    {
        m_initialized = true;
    }
    
    return hr;
}

void InProcessMcpServerBase::Uninitialize()
{
    if (!m_initialized)
    {
        return;
    }
    
    OnUninitialize();
    
    {
        std::unique_lock<std::shared_mutex> lock(m_toolsMutex);
        m_registeredTools.clear();
    }
    
    m_initialized = false;
}

HRESULT InProcessMcpServerBase::RegisterTool(
    _In_ const MCP_TOOL_DEFINITION& toolDefinition
    )
/*++

Routine Description:
    Registers a tool with the server.

Arguments:
    toolDefinition - Tool definition including handler

Return Value:
    S_OK on success, error HRESULT on failure

--*/
{
    if (toolDefinition.name.empty() || !toolDefinition.handler)
    {
        return E_INVALIDARG;
    }
    
    std::unique_lock<std::shared_mutex> lock(m_toolsMutex);
    m_registeredTools[toolDefinition.name] = toolDefinition;
    
    return S_OK;
}

HRESULT InProcessMcpServerBase::RegisterSimpleTool(
    _In_ const std::wstring& name,
    _In_ const std::wstring& description,
    _In_ McpToolHandler handler
    )
/*++

Routine Description:
    Registers a simple tool with name, description and handler.

Arguments:
    name - Tool name
    description - Tool description  
    handler - Tool handler function

Return Value:
    S_OK on success, error HRESULT on failure

--*/
{
    MCP_TOOL_DEFINITION toolDef;
    toolDef.name = name;
    toolDef.description = description;
    toolDef.handler = handler;
    toolDef.requiresElevation = false;
    toolDef.estimatedExecutionTimeMs = 1000;
    toolDef.inputSchema = nlohmann::json::object();
    toolDef.outputSchema = nlohmann::json::object();
    
    return RegisterTool(toolDef);
}

HRESULT InProcessMcpServerBase::ExecuteToolInternal(
    _In_ const std::wstring& toolName,
    _In_ const nlohmann::json& parameters,
    _Out_ nlohmann::json& result
    )
/*++

Routine Description:
    Internal method to execute a registered tool.

Arguments:
    toolName - Name of tool to execute
    parameters - Tool parameters
    result - Receives tool execution result

Return Value:
    S_OK on success, error HRESULT on failure

--*/
{
    std::shared_lock<std::shared_mutex> lock(m_toolsMutex);
    
    auto it = m_registeredTools.find(toolName);
    if (it == m_registeredTools.end())
    {
        return E_INVALIDARG;
    }
    
    const auto& toolDef = it->second;
    lock.unlock();
    
    // Call pre-execution hook
    HRESULT hr = OnBeforeToolExecution(toolName, parameters);
    if (FAILED(hr))
    {
        return hr;
    }
    
    // Execute the tool
    hr = toolDef.handler(parameters, result);
    
    // Call post-execution hook
    OnAfterToolExecution(toolName, parameters, result, hr);
    
    return hr;
}

void InProcessMcpServerBase::UpdateExecutionStatistics(
    _In_ bool success
    )
{
    m_totalExecutions++;
    if (success)
    {
        m_successfulExecutions++;
    }
    else
    {
        m_failedExecutions++;
    }
}

nlohmann::json InProcessMcpServerBase::CreateSuccessResponse(
    _In_ const nlohmann::json& content
    ) const
/*++

Routine Description:
    Creates a standard success response JSON structure.

Arguments:
    content - The content to include in the response

Return Value:
    JSON success response

--*/
{
    return InProcessMcpUtils::CreateToolCallSuccessResponse(content);
}

nlohmann::json InProcessMcpServerBase::CreateErrorResponse(
    _In_ const std::wstring& errorMessage,
    _In_ const std::wstring& errorCode
    ) const
/*++

Routine Description:
    Creates a standard error response JSON structure.

Arguments:
    errorMessage - The error message
    errorCode - The error code (optional, defaults to "execution_error")

Return Value:
    JSON error response

--*/
{
    std::string errorMessageUtf8 = WideToUtf8(errorMessage);
    std::string errorCodeUtf8 = WideToUtf8(errorCode);

    nlohmann::json errorData;
    errorData["code"] = errorCodeUtf8;

    return InProcessMcpUtils::CreateMcpErrorResponse(McpProtocol::ErrorCodes::INTERNAL_ERROR, errorMessageUtf8, &errorData);
}

void InProcessMcpServerBase::LogToolExecution(
    _In_ const std::wstring& toolName,
    _In_ const nlohmann::json& parameters,
    _In_ HRESULT result,
    _In_ DWORD executionTimeMs
    ) const
{
    UNREFERENCED_PARAMETER(toolName);
    UNREFERENCED_PARAMETER(parameters);
    UNREFERENCED_PARAMETER(result);
    UNREFERENCED_PARAMETER(executionTimeMs);

    // Log tool execution for debugging/monitoring
    // Implementation would use appropriate logging mechanism
}
