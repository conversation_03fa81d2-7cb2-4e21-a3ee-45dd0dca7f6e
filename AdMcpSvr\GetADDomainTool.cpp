/*++

Copyright (c) Microsoft Corporation. All rights reserved.

Module Name:

    GetADDomainTool.cpp

Abstract:

    Implementation of Get-ADDomain tool for Active Directory MCP server.
    Provides comprehensive AD domain query functionality using native Win32 LDAP APIs
    while maintaining PowerShell cmdlet parameter compatibility.

Author:

    <PERSON><PERSON><PERSON> (rizhang) 2025-7-16

--*/

#include "AdMcpSvr.h"
#include "StringUtils.h"
#include "AimxConstants.h"
#include "../aimxsrv/inc/wpp.h"

// WPP tracing
#include "GetADDomainTool.cpp.tmh"

HRESULT AdMcpSvr::GetADDomainTool(
    _In_ const nlohmann::json& parameters,
    _Out_ nlohmann::json& result
    )
/*++

Routine Description:
    Implementation of Get-ADDomain tool.

Arguments:
    parameters - Input parameters matching PowerShell Get-ADDomain cmdlet
    result - Receives domain information as JSON

Return Value:
    S_OK on success, error HRESULT on failure

--*/
{
    TraceInfo(AdMcpSvr, L"AdMcpSvr::GetADDomainTool called");

    try
    {
        result = nlohmann::json::object();

        // Initialize LDAP connection if needed
        std::wstring serverName;
        if (parameters.contains("Server") && parameters["Server"].is_string())
        {
            serverName = Utf8ToWide(parameters["Server"].get<std::string>());
        }

        HRESULT hr = InitializeLdapConnection(serverName);
        if (FAILED(hr))
        {
            return CreateLdapErrorResponse(hr, L"Failed to initialize LDAP connection", result);
        }

        // Get domain attributes
        std::vector<std::wstring> attributes = GetDefaultDomainAttributes();

        // Execute LDAP search for domain object
        std::vector<nlohmann::json> domains;
        hr = ExecuteLdapSearch(m_defaultNamingContext, L"(objectClass=domainDNS)", attributes, LDAP_SCOPE_BASE, domains);
        if (FAILED(hr))
        {
            return CreateLdapErrorResponse(hr, L"LDAP search for domain failed", result);
        }

        if (!domains.empty())
        {
            result["domain"] = domains[0];
        }
        else
        {
            result["domain"] = nlohmann::json::object();
        }

        result["server"] = WideToUtf8(m_domainController);

        TraceInfo(AdMcpSvr, L"AdMcpSvr::GetADDomainTool completed successfully");
        return S_OK;
    }
    catch (const std::exception& ex)
    {
        TraceErr(AdMcpSvr, L"Exception in GetADDomainTool: %s", ex.what());
        result = CreateErrorResponse(L"Failed to execute Get-ADDomain: " + Utf8ToWide(ex.what()), L"execution_error");
        return E_FAIL;
    }
}

std::vector<std::wstring> AdMcpSvr::GetDefaultDomainAttributes()
/*++

Routine Description:
    Get default attributes for domain objects.

Return Value:
    Vector of default domain attribute names

--*/
{
    return {
        L"distinguishedName", L"objectGUID", L"objectSid", L"name",
        L"dnsRoot", L"netBIOSName", L"domainFunctionality", L"forestFunctionality",
        L"fSMORoleOwner", L"infrastructureMaster", L"rIDMaster", L"pDCEmulator",
        L"objectClass", L"whenCreated", L"whenChanged", L"msDS-Behavior-Version"
    };
}
